<html lang="en" data-lt-installed="true"><head>
    <meta charset="utf-8">
    
    
    <!-- Google Optimize -->
<script type="text/javascript" async="" src="https://widget.intercom.io/widget/lsvujawt"></script><script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/identify_ecbed230.js"></script><script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/main.MTc3NWUxZTAxMA.js" data-id="CEGINNRC77UFTJ8GGB9G"></script><script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=CEGINNRC77UFTJ8GGB9G&amp;lib=ttq"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-MKJC2T5"></script><script async="" src="https://www.googleoptimize.com/optimize.js?id=OPT-WLVG3BP"></script>

<!-- Google tag (gtag.js) (Google Analytics) 
<script async src="https://www.googletagmanager.com/gtag/js?id=G-9CHTJBNSSD"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-9CHTJBNSSD');
</script>
-->

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-MKJC2T5');</script>


<!-- Hotjar Tracking Code for Popless - Prod -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:2688343,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script><script async="" src="https://static.hotjar.com/c/hotjar-2688343.js?sv=6"></script>

<!-- OpenReplay Tracking Code -->
<!-- OpenReplay Tracking Code for Popless - Prd -->
<script>
  var  projectKey = window.location.host==='fe.dev.popless.com' ? 'aDdqPaN8EUCpRu3nr1AA' : "k8xXUcLSdYaBbzENYuXN";
  var initOpts = {
    projectKey: projectKey,
    defaultInputMode: 0,
    obscureTextNumbers: false,
    obscureTextEmails: true,
  };
  var startOpts = { userID: "" };
  (function(A,s,a,y,e,r){
    r=window.OpenReplay=[e,r,y,[s-1, e]];
    s=document.createElement('script');s.src=A;s.async=!a;
    document.getElementsByTagName('head')[0].appendChild(s);
    r.start=function(v){r.push([0])};
    r.stop=function(v){r.push([1])};
    r.setUserID=function(id){r.push([2,id])};
    r.setUserAnonymousID=function(id){r.push([3,id])};
    r.setMetadata=function(k,v){r.push([4,k,v])};
    r.event=function(k,p,i){r.push([5,k,p,i])};
    r.issue=function(k,p){r.push([6,k,p])};
    r.isActive=function(){return false};
    r.getSessionToken=function(){};
  })("//static.openreplay.com/latest/openreplay.js",1,0,initOpts,startOpts);
</script><script src="//static.openreplay.com/latest/openreplay.js" async=""></script>
    <!-- End of headStart -->
    <meta name="viewport" content="width=device-width">
    <meta name="generator" content="Framer 2bdc57c">
    <title>Popless | The all-in-one tutor platform for private tutors and group classes</title>
    <meta name="description" content="Popless is the best tutoring platform to manage and grow your tutoring business. Power your teaching and students from an all-in-one dashboard. Spend more time teaching and engaging students.">
    <meta name="framer-search-index" content="limit-reached">
    <link rel="icon" href="https://framerusercontent.com/modules/W4hVCnkMB6ND8hXTkGlk/p3Q71W5Klx6mT88rlrNR/assets/AnCV8qCUo5cbDyJncVhFY1rMqIo.png">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Popless | The all-in-one tutor platform for private tutors and group classes">
    <meta property="og:description" content="Popless is the best tutoring platform to manage and grow your tutoring business. Power your teaching and students from an all-in-one dashboard. Spend more time teaching and engaging students.">
    <meta property="og:image" content="https://framerusercontent.com/modules/W4hVCnkMB6ND8hXTkGlk/p3Q71W5Klx6mT88rlrNR/assets/FsysPmdg07zDIFM8LnIlXvGY3x0.jpg">
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Popless | The all-in-one tutor platform for private tutors and group classes">
    <meta name="twitter:description" content="Popless is the best tutoring platform to manage and grow your tutoring business. Power your teaching and students from an all-in-one dashboard. Spend more time teaching and engaging students.">
    <meta name="twitter:image" content="https://framerusercontent.com/modules/W4hVCnkMB6ND8hXTkGlk/p3Q71W5Klx6mT88rlrNR/assets/FsysPmdg07zDIFM8LnIlXvGY3x0.jpg">
    
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-9CHTJBNSSD"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){window.dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-9CHTJBNSSD');
    </script>
    
    
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin=""><link rel="canonical" href="https://popless.framer.website/"><link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-KEXRGOII.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-5F276QAW.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-OIST4OYN.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/7yASPuX6VEnA5BcpoNeIqFyVT2RaoWq09Kcl0-rccLQ.5S3VDIZ4.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-45BP7KOO.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-7OSKSMWJ.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-VWWF2A63.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-QDKAFU6T.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-BI4OMGMN.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-GGAC7NPY.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-7FSZCHHR.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-LRG5EHGX.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-RCVHWUUU.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-Y45OGLEC.mjs">
<link rel="modulepreload" href="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/chunk-42U43NKG.mjs"><style data-framer-css-ssr-minified="" data-framer-components="">html,body,#main{height:100%;margin:0;padding:0;box-sizing:border-box}:root{-webkit-font-smoothing:antialiased}*{box-sizing:border-box;-webkit-font-smoothing:inherit}h1,h2,h3,h4,h5,h6,p,figure{margin:0}body,input,textarea,select,button{font-size:12px;font-family:sans-serif}body{--token-7b040a80-5f57-4f9a-a734-c0f3178785ca: rgb(3, 104, 224);--token-15b486ba-2fc9-40ec-b282-d340ae737628: rgb(19, 22, 18);--token-b2ddaf18-fb8e-49c5-bfe2-e80a4e95b118: rgb(44, 126, 234);--token-840e2253-3db7-4fe4-9c04-053d2f50f134: rgb(168, 168, 168);--token-c50781d2-1475-401d-9e71-7a371e78ed14: rgb(112, 167, 240);--token-cd156118-158a-47d3-8fb6-822a4bbc99ee: rgb(224, 225, 227);--token-a1e13941-d14a-4692-855b-e794eebdfa6f: rgb(229, 232, 232);--token-ce5164cd-4223-4bb7-8552-21eb990c41c0: rgb(245, 245, 245);--token-36a54893-9e5b-4622-8567-34f1e61e2ee9: rgb(255, 255, 255);--token-e3946f44-aa22-4470-a550-0689952f6cfd: rgb(0, 136, 255);--token-8699c4f3-d3b7-4764-bab8-47a373c0f26f: rgb(109, 22, 223);--token-672a3040-6e9a-4dff-8a40-eed3170f29ae: rgb(90, 90, 91);--token-b614b0f1-f502-471d-908b-7ee324a58d01: rgb(0, 24, 182);--token-dc74b930-0ddb-4cd5-84c0-a49f71740644: rgb(234, 104, 115);--token-d1f3fbd0-f3ba-41ec-a0d6-879c7df665da: rgb(23, 46, 20);--token-8e2e3d7a-18d7-4c35-8f04-9fea5742be2b: rgb(69, 105, 64)}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7SUc.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7SUc.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7SUc.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7SUc.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7SUc.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7SUc.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu72xKOzY.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu5mxKOzY.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7mxKOzY.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4WxKOzY.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7WxKOzY.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7GxKOzY.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Roboto;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7SUc.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7SUc.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7SUc.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7SUc.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7SUc.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7SUc.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7SUc.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7SUc.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7SUc.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Meow Script;font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ERcp2fwyfA.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Meow Script;font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ERco2fwyfA.woff2) format("woff2");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Meow Script;font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ERcm2fw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter Placeholder;ascent-override:90%;descent-override:22.43%;line-gap-override:0%;size-adjust:107.64%;src:local("Arial")}@font-face{font-family:Roboto Placeholder;ascent-override:92.49%;descent-override:24.34%;line-gap-override:0%;size-adjust:100.3%;src:local("Arial")}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.cyrillic-ext-BHDA65O4.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.cyrillic-Q5IXHU2O.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.greek-ext-VVOQA2NE.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.greek-E3WNEUH6.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.latin-ext-5RC4NRHN.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.latin-GJ7CRGHG.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:100;src:url(https://app.framerstatic.com/Inter-Thin.vietnamese-Z3Y7DPWO.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.cyrillic-ext-7NWKXNAT.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.cyrillic-IBWUD6RJ.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.greek-ext-HUMDTRBU.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.greek-ZLLHEEN3.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.latin-ext-72JE5FGU.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.latin-2JHDAFAQ.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLight.vietnamese-VKRCA4VC.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.cyrillic-ext-G3OTPKE4.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.cyrillic-JO7ZJTP6.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.greek-ext-N63XCCK3.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.greek-5GVUXSXZ.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.latin-ext-RREJIMQ3.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.latin-EKF76FXR.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:300;src:url(https://app.framerstatic.com/Inter-Light.vietnamese-GVC2UOFS.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.cyrillic-ext-CFTLRB35.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.cyrillic-KKLZBALH.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.greek-ext-ULEBLIFV.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.greek-IRHSNFQB.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.latin-ext-VZDUGU3Q.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.latin-JLQMKCHE.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:400;src:url(https://app.framerstatic.com/Inter-Regular.vietnamese-QK7VSWXK.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.cyrillic-ext-M4WHNGTS.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.cyrillic-JVU2PANX.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.greek-ext-4KCQBEIZ.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.greek-DPOQGN7L.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.latin-ext-J4DBSW7F.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.latin-Y3IVPL46.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:500;src:url(https://app.framerstatic.com/Inter-Medium.vietnamese-PJV76O4P.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.cyrillic-ext-C7KWUKA7.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.cyrillic-JWV7SOZ6.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.greek-ext-FBKSFTSU.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.greek-EQ3PSENU.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.latin-ext-ULRSO3ZR.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.latin-RDYY2AG2.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBold.vietnamese-ESQNSEQ3.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.cyrillic-ext-XOTVL7ZR.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.cyrillic-6LOMBC2V.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.greek-ext-WXWSJXLB.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.greek-YRST7ODZ.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.latin-ext-BASA5UL3.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.latin-UCM45LQF.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:swap;font-family:Inter;font-style:normal;font-weight:700;src:url(https://app.framerstatic.com/Inter-Bold.vietnamese-OEVJMXEP.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.cyrillic-ext-7Q6SVIPE.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.cyrillic-JSLPE6KW.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.greek-ext-6OYGJJV7.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.greek-SHW2FPC4.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.latin-ext-A5DUFOP6.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.latin-OW4UGSRU.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBold.vietnamese-IBBC7NGV.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.cyrillic-ext-TU4ITVTR.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.cyrillic-JX7CGTYD.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.greek-ext-LS3GCBFI.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.greek-ZWCJHBP5.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.latin-ext-BZLEUMX6.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.latin-TETRYDF7.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:normal;font-weight:900;src:url(https://app.framerstatic.com/Inter-Black.vietnamese-RXQCC3EJ.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.cyrillic-ext-2RGKWUBV.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.cyrillic-TDYIP5HV.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.greek-ext-WR4TIDYZ.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.greek-V3WZMSP7.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.latin-ext-TXFTJONQ.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.latin-RVEBKP6O.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:100;src:url(https://app.framerstatic.com/Inter-ThinItalic.vietnamese-WCBPP4MD.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.cyrillic-ext-OVCHMVPD.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.cyrillic-BRDZE5UH.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.greek-ext-YV64YFFH.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.greek-EJVCLASM.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.latin-ext-2MHTM56A.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.latin-JB3CJMMM.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:200;src:url(https://app.framerstatic.com/Inter-ExtraLightItalic.vietnamese-3EJ3IQYS.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.cyrillic-ext-C2S5XS3D.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.cyrillic-E7CYPW5D.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.greek-ext-ROSAFPGE.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.greek-PAWWH37Z.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.latin-ext-N2Z67Z45.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.latin-SLSTLWEU.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:300;src:url(https://app.framerstatic.com/Inter-LightItalic.vietnamese-RLGM2D3Y.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.cyrillic-ext-YDGMJOJO.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.cyrillic-BFOVMAQB.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.greek-ext-4KOU3AHC.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.greek-OJTBJNE6.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.latin-ext-H4B22QN6.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.latin-2DWX32EN.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:400;src:url(https://app.framerstatic.com/Inter-Italic.vietnamese-TYMT6CKW.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.cyrillic-ext-QYBZQ2NF.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.cyrillic-ZHAJHZCC.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.greek-ext-W5ABYGZR.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.greek-RGNSYVNV.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.latin-ext-7DZEPSAS.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.latin-SKPQAMBJ.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:500;src:url(https://app.framerstatic.com/Inter-MediumItalic.vietnamese-23WIFZV7.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.cyrillic-ext-MEHHCDC3.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.cyrillic-YACNRNDE.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.greek-ext-GFL7KADI.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.greek-5W77OPRT.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.latin-ext-OYJJ2W6R.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.latin-KBLJMBDH.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:600;src:url(https://app.framerstatic.com/Inter-SemiBoldItalic.vietnamese-5ZFOV65G.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.cyrillic-ext-PEYDHC3S.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.cyrillic-7EIL6JWG.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.greek-ext-3DJOYQMH.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.greek-TJBTLTT7.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.latin-ext-FVPCPRBJ.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.latin-5ZFQS4XK.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:swap;font-family:Inter;font-style:italic;font-weight:700;src:url(https://app.framerstatic.com/Inter-BoldItalic.vietnamese-W2625PGF.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.cyrillic-ext-ACWDZ3VD.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.cyrillic-ZKBSDAI2.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.greek-ext-3CY5DPTP.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.greek-YL5CC63W.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.latin-ext-7IZFJI4D.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.latin-O5HH4IX3.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:800;src:url(https://app.framerstatic.com/Inter-ExtraBoldItalic.vietnamese-UW3XUJOD.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.cyrillic-ext-TRM4ITYR.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.cyrillic-FPHIQVZS.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.greek-ext-JTGUUSP5.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.greek-LUNA3RFO.woff2) format("woff2");unicode-range:U+0370-03FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.latin-ext-KU7ICFYH.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.latin-FRVXWQSB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-display:block;font-family:Inter;font-style:italic;font-weight:900;src:url(https://app.framerstatic.com/Inter-BlackItalic.vietnamese-2Q7MQKJX.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB}[data-framer-component-type]{position:absolute}[data-framer-component-type=Text]{cursor:inherit}[data-framer-component-text-autosized] *{white-space:pre}[data-framer-component-type=Text]>*{text-align:var(--framer-text-alignment, start)}[data-framer-component-type=Text] span span,[data-framer-component-type=Text] p span,[data-framer-component-type=Text] h1 span,[data-framer-component-type=Text] h2 span,[data-framer-component-type=Text] h3 span,[data-framer-component-type=Text] h4 span,[data-framer-component-type=Text] h5 span,[data-framer-component-type=Text] h6 span{display:block}[data-framer-component-type=Text] span span span,[data-framer-component-type=Text] p span span,[data-framer-component-type=Text] h1 span span,[data-framer-component-type=Text] h2 span span,[data-framer-component-type=Text] h3 span span,[data-framer-component-type=Text] h4 span span,[data-framer-component-type=Text] h5 span span,[data-framer-component-type=Text] h6 span span{display:unset}[data-framer-component-type=Text] div div span,[data-framer-component-type=Text] a div span,[data-framer-component-type=Text] span span span,[data-framer-component-type=Text] p span span,[data-framer-component-type=Text] h1 span span,[data-framer-component-type=Text] h2 span span,[data-framer-component-type=Text] h3 span span,[data-framer-component-type=Text] h4 span span,[data-framer-component-type=Text] h5 span span,[data-framer-component-type=Text] h6 span span,[data-framer-component-type=Text] a{font-family:var(--font-family);font-style:var(--font-style);font-weight:min(calc(var(--framer-font-weight-increase, 0) + var(--font-weight, 400)),900);color:var(--text-color);letter-spacing:var(--letter-spacing);font-size:var(--font-size);text-transform:var(--text-transform);text-decoration:var(--text-decoration);line-height:var(--line-height)}[data-framer-component-type=Text] div div span,[data-framer-component-type=Text] a div span,[data-framer-component-type=Text] span span span,[data-framer-component-type=Text] p span span,[data-framer-component-type=Text] h1 span span,[data-framer-component-type=Text] h2 span span,[data-framer-component-type=Text] h3 span span,[data-framer-component-type=Text] h4 span span,[data-framer-component-type=Text] h5 span span,[data-framer-component-type=Text] h6 span span,[data-framer-component-type=Text] a{--font-family: var(--framer-font-family);--font-style: var(--framer-font-style);--font-weight: var(--framer-font-weight);--text-color: var(--framer-text-color);--letter-spacing: var(--framer-letter-spacing);--font-size: var(--framer-font-size);--text-transform: var(--framer-text-transform);--text-decoration: var(--framer-text-decoration);--line-height: var(--framer-line-height)}[data-framer-component-type=Text] a,[data-framer-component-type=Text] a div span,[data-framer-component-type=Text] a span span span,[data-framer-component-type=Text] a p span span,[data-framer-component-type=Text] a h1 span span,[data-framer-component-type=Text] a h2 span span,[data-framer-component-type=Text] a h3 span span,[data-framer-component-type=Text] a h4 span span,[data-framer-component-type=Text] a h5 span span,[data-framer-component-type=Text] a h6 span span{--font-family: var(--framer-link-font-family, var(--framer-font-family));--font-style: var(--framer-link-font-style, var(--framer-font-style));--font-weight: var(--framer-link-font-weight, var(--framer-font-weight));--text-color: var(--framer-link-text-color, var(--framer-text-color));--font-size: var(--framer-link-font-size, var(--framer-font-size));--text-transform: var(--framer-link-text-transform, var(--framer-text-transform));--text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration))}[data-framer-component-type=Text] a:hover,[data-framer-component-type=Text] a div span:hover,[data-framer-component-type=Text] a span span span:hover,[data-framer-component-type=Text] a p span span:hover,[data-framer-component-type=Text] a h1 span span:hover,[data-framer-component-type=Text] a h2 span span:hover,[data-framer-component-type=Text] a h3 span span:hover,[data-framer-component-type=Text] a h4 span span:hover,[data-framer-component-type=Text] a h5 span span:hover,[data-framer-component-type=Text] a h6 span span:hover{--font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family)));--font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style)));--font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));--text-color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color)));--font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size)));--text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));--text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration)))}[data-framer-component-type=Text].isCurrent a,[data-framer-component-type=Text].isCurrent a div span,[data-framer-component-type=Text].isCurrent a span span span,[data-framer-component-type=Text].isCurrent a p span span,[data-framer-component-type=Text].isCurrent a h1 span span,[data-framer-component-type=Text].isCurrent a h2 span span,[data-framer-component-type=Text].isCurrent a h3 span span,[data-framer-component-type=Text].isCurrent a h4 span span,[data-framer-component-type=Text].isCurrent a h5 span span,[data-framer-component-type=Text].isCurrent a h6 span span{--font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family)));--font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style)));--font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));--text-color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color)));--font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size)));--text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));--text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration)))}p.framer-text,div.framer-text,h1.framer-text,h2.framer-text,h3.framer-text,h4.framer-text,h5.framer-text,h6.framer-text,ol.framer-text,ul.framer-text{margin:0;padding:0}p.framer-text,div.framer-text,h1.framer-text,h2.framer-text,h3.framer-text,h4.framer-text,h5.framer-text,h6.framer-text,li.framer-text,ol.framer-text,ul.framer-text,span.framer-text:not([data-text-fill]){font-family:var(--framer-font-family, Inter, Inter Placeholder, sans-serif);font-style:var(--framer-font-style, normal);font-weight:var(--framer-font-weight, 400);color:var(--framer-text-color, #000);font-size:calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));letter-spacing:var(--framer-letter-spacing, 0);text-transform:var(--framer-text-transform, none);text-decoration:var(--framer-text-decoration, none);line-height:var(--framer-line-height, 1.2em);text-align:var(--framer-text-alignment, start)}strong.framer-text{font-weight:bolder}em.framer-text{font-style:italic}p.framer-text:not(:first-child),div.framer-text:not(:first-child),h1.framer-text:not(:first-child),h2.framer-text:not(:first-child),h3.framer-text:not(:first-child),h4.framer-text:not(:first-child),h5.framer-text:not(:first-child),h6.framer-text:not(:first-child),ol.framer-text:not(:first-child),ul.framer-text:not(:first-child),.framer-image.framer-text:not(:first-child){margin-top:var(--framer-paragraph-spacing, 0)}li.framer-text>ul.framer-text:nth-child(2),li.framer-text>ol.framer-text:nth-child(2){margin-top:0}.framer-text[data-text-fill]{display:inline-block;background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:max(0em,calc(calc(1.3em - var(--framer-line-height, 1.3em)) / 2));margin:min(0em,calc(calc(1.3em - var(--framer-line-height, 1.3em)) / -2))}code.framer-text,code.framer-text span.framer-text:not([data-text-fill]){font-family:var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-code-font-style, var(--framer-font-style, normal));font-weight:var(--framer-code-font-weight, var(--framer-font-weight, 400));color:var(--framer-code-text-color, var(--framer-text-color, #000));font-size:calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));letter-spacing:var(--framer-letter-spacing, 0);line-height:var(--framer-line-height, 1.2em)}a.framer-text,a.framer-text span.framer-text:not([data-text-fill]){font-family:var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-link-font-style, var(--framer-font-style, normal));font-weight:var(--framer-link-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-text-color, var(--framer-text-color, #000));font-size:calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1));text-transform:var(--framer-link-text-transform, var(--framer-text-transform, none));text-decoration:var(--framer-link-text-decoration, var(--framer-text-decoration, none));cursor:var(--framer-custom-cursors, pointer)}code.framer-text a.framer-text,code.framer-text a.framer-text span.framer-text:not([data-text-fill]){font-family:var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-code-font-style, var(--framer-font-style, normal));font-weight:var(--framer-code-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)));font-size:calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1))}a.framer-text:hover,a.framer-text:hover span.framer-text:not([data-text-fill]){font-family:var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));font-style:var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));font-weight:var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));color:var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));font-size:calc(var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));text-transform:var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));text-decoration:var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))}code.framer-text a.framer-text:hover,code.framer-text a.framer-text:hover span.framer-text:not([data-text-fill]){font-family:var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-code-font-style, var(--framer-font-style, normal));font-weight:var(--framer-code-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));font-size:calc(var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1))}a.framer-text[data-framer-page-link-current],a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]){font-family:var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));font-style:var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));font-weight:var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));color:var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));font-size:calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));text-transform:var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));text-decoration:var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))}code.framer-text a.framer-text[data-framer-page-link-current],code.framer-text a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]){font-family:var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-code-font-style, var(--framer-font-style, normal));font-weight:var(--framer-code-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));font-size:calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1))}a.framer-text[data-framer-page-link-current]:hover,a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]){font-family:var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));font-style:var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));font-weight:var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));color:var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));font-size:calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))) * var(--framer-font-size-scale, 1));text-transform:var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));text-decoration:var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))))}code.framer-text a.framer-text[data-framer-page-link-current]:hover,code.framer-text a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]){font-family:var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-code-font-style, var(--framer-font-style, normal));font-weight:var(--framer-code-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))));font-size:calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))) * var(--framer-font-size-scale, 1))}.framer-image.framer-text{display:block;max-width:100%;height:auto}.text-styles-preset-reset.framer-text{--framer-font-family: Inter, Inter Placeholder, sans-serif;--framer-font-style: normal;--framer-font-weight: 500;--framer-text-color: #000;--framer-font-size: 16px;--framer-letter-spacing: 0;--framer-text-transform: none;--framer-text-decoration: none;--framer-line-height: 1.2em;--framer-text-alignment: start}ul.framer-text,ol.framer-text{display:table;width:100%}li.framer-text{display:table-row;counter-increment:list-item;list-style:none}ol.framer-text>li.framer-text:before{display:table-cell;width:2.25ch;box-sizing:border-box;padding-right:.75ch;content:counter(list-item) ".";white-space:nowrap}ul.framer-text>li.framer-text:before{display:table-cell;width:2.25ch;box-sizing:border-box;padding-right:.75ch;content:"\2022"}.framer-text-module[style*=aspect-ratio]>:first-child{width:100%}@supports not (aspect-ratio: 1){.framer-text-module[style*=aspect-ratio]{position:relative}}@supports not (aspect-ratio: 1){.framer-text-module[style*=aspect-ratio]:before{content:"";display:block;padding-bottom:calc(100% / calc(var(--aspect-ratio)))}}@supports not (aspect-ratio: 1){.framer-text-module[style*=aspect-ratio]>:first-child{position:absolute;top:0;left:0;height:100%}}[data-framer-component-type=DeprecatedRichText]{cursor:inherit}[data-framer-component-type=DeprecatedRichText] .text-styles-preset-reset{--framer-font-family: Inter, Inter Placeholder, sans-serif;--framer-font-style: normal;--framer-font-weight: 500;--framer-text-color: #000;--framer-font-size: 16px;--framer-letter-spacing: 0;--framer-text-transform: none;--framer-text-decoration: none;--framer-line-height: 1.2em;--framer-text-alignment: start}[data-framer-component-type=DeprecatedRichText] p,[data-framer-component-type=DeprecatedRichText] div,[data-framer-component-type=DeprecatedRichText] h1,[data-framer-component-type=DeprecatedRichText] h2,[data-framer-component-type=DeprecatedRichText] h3,[data-framer-component-type=DeprecatedRichText] h4,[data-framer-component-type=DeprecatedRichText] h5,[data-framer-component-type=DeprecatedRichText] h6{margin:0;padding:0}[data-framer-component-type=DeprecatedRichText] p,[data-framer-component-type=DeprecatedRichText] div,[data-framer-component-type=DeprecatedRichText] h1,[data-framer-component-type=DeprecatedRichText] h2,[data-framer-component-type=DeprecatedRichText] h3,[data-framer-component-type=DeprecatedRichText] h4,[data-framer-component-type=DeprecatedRichText] h5,[data-framer-component-type=DeprecatedRichText] h6,[data-framer-component-type=DeprecatedRichText] li,[data-framer-component-type=DeprecatedRichText] ol,[data-framer-component-type=DeprecatedRichText] ul,[data-framer-component-type=DeprecatedRichText] span:not([data-text-fill]){font-family:var(--framer-font-family, Inter, Inter Placeholder, sans-serif);font-style:var(--framer-font-style, normal);font-weight:var(--framer-font-weight, 400);color:var(--framer-text-color, #000);font-size:var(--framer-font-size, 16px);letter-spacing:var(--framer-letter-spacing, 0);text-transform:var(--framer-text-transform, none);text-decoration:var(--framer-text-decoration, none);line-height:var(--framer-line-height, 1.2em);text-align:var(--framer-text-alignment, start)}[data-framer-component-type=DeprecatedRichText] p:not(:first-child),[data-framer-component-type=DeprecatedRichText] div:not(:first-child),[data-framer-component-type=DeprecatedRichText] h1:not(:first-child),[data-framer-component-type=DeprecatedRichText] h2:not(:first-child),[data-framer-component-type=DeprecatedRichText] h3:not(:first-child),[data-framer-component-type=DeprecatedRichText] h4:not(:first-child),[data-framer-component-type=DeprecatedRichText] h5:not(:first-child),[data-framer-component-type=DeprecatedRichText] h6:not(:first-child),[data-framer-component-type=DeprecatedRichText] ol:not(:first-child),[data-framer-component-type=DeprecatedRichText] ul:not(:first-child),[data-framer-component-type=DeprecatedRichText] .framer-image:not(:first-child){margin-top:var(--framer-paragraph-spacing, 0)}[data-framer-component-type=DeprecatedRichText] span[data-text-fill]{display:inline-block;background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent}[data-framer-component-type=DeprecatedRichText] a,[data-framer-component-type=DeprecatedRichText] a span:not([data-text-fill]){font-family:var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));font-style:var(--framer-link-font-style, var(--framer-font-style, normal));font-weight:var(--framer-link-font-weight, var(--framer-font-weight, 400));color:var(--framer-link-text-color, var(--framer-text-color, #000));font-size:var(--framer-link-font-size, var(--framer-font-size, 16px));text-transform:var(--framer-link-text-transform, var(--framer-text-transform, none));text-decoration:var(--framer-link-text-decoration, var(--framer-text-decoration, none))}[data-framer-component-type=DeprecatedRichText] a:hover,[data-framer-component-type=DeprecatedRichText] a:hover span:not([data-text-fill]){font-family:var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));font-style:var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));font-weight:var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));color:var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));font-size:var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));text-transform:var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));text-decoration:var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))}a[data-framer-page-link-current],a[data-framer-page-link-current] span:not([data-text-fill]){font-family:var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));font-style:var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));font-weight:var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));color:var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));font-size:var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));text-transform:var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));text-decoration:var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))}a[data-framer-page-link-current]:hover,a[data-framer-page-link-current]:hover span:not([data-text-fill]){font-family:var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));font-style:var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));font-weight:var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));color:var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));font-size:var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))));text-transform:var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));text-decoration:var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))))}[data-framer-component-type=DeprecatedRichText] strong{font-weight:bolder}[data-framer-component-type=DeprecatedRichText] em{font-style:italic}[data-framer-component-type=DeprecatedRichText] .framer-image{display:block;max-width:100%;height:auto}[data-framer-component-type=DeprecatedRichText] ul,[data-framer-component-type=DeprecatedRichText] ol{display:table;width:100%;padding-left:0;margin:0}[data-framer-component-type=DeprecatedRichText] li{display:table-row;counter-increment:list-item;list-style:none}[data-framer-component-type=DeprecatedRichText] ol>li:before{display:table-cell;width:2.25ch;box-sizing:border-box;padding-right:.75ch;content:counter(list-item) ".";white-space:nowrap}[data-framer-component-type=DeprecatedRichText] ul>li:before{display:table-cell;width:2.25ch;box-sizing:border-box;padding-right:.75ch;content:"\2022"}:not([data-framer-generated])>[data-framer-stack-content-wrapper]>*,:not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-component-type],:not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-legacy-stack-gap-enabled]>*,:not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-legacy-stack-gap-enabled]>[data-framer-component-type]{position:relative}.flexbox-gap-not-supported [data-framer-legacy-stack-gap-enabled=true]>*,[data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]{margin-top:calc(var(--stack-gap-y) / 2);margin-bottom:calc(var(--stack-gap-y) / 2);margin-right:calc(var(--stack-gap-x) / 2);margin-left:calc(var(--stack-gap-x) / 2)}[data-framer-stack-content-wrapper][data-framer-stack-gap-enabled=true]{row-gap:var(--stack-native-row-gap);column-gap:var(--stack-native-column-gap)}.flexbox-gap-not-supported [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled=true]{row-gap:unset;column-gap:unset}.flexbox-gap-not-supported [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true]>*:first-child,[data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>*:first-child,.flexbox-gap-not-supported [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true]>*:last-child,[data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>*:last-child{margin-top:0;margin-left:0}.flexbox-gap-not-supported [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true]>*:last-child,[data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>*:last-child,.flexbox-gap-not-supported [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true]>*:first-child,[data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>*:first-child{margin-right:0;margin-bottom:0}NavigationContainer [data-framer-component-type=NavigationContainer]>*,[data-framer-component-type=NavigationContainer]>[data-framer-component-type]{position:relative}[data-framer-component-type=Scroll]::-webkit-scrollbar{display:none}[data-framer-component-type=ScrollContentWrapper]>*{position:relative}[data-framer-component-type=NativeScroll]{-webkit-overflow-scrolling:touch}[data-framer-component-type=NativeScroll]>*{position:relative}[data-framer-component-type=NativeScroll].direction-both{overflow-x:scroll;overflow-y:scroll}[data-framer-component-type=NativeScroll].direction-vertical{overflow-x:hidden;overflow-y:scroll}[data-framer-component-type=NativeScroll].direction-horizontal{overflow-x:scroll;overflow-y:hidden}[data-framer-component-type=NativeScroll].direction-vertical>*{width:100%!important}[data-framer-component-type=NativeScroll].direction-horizontal>*{height:100%!important}[data-framer-component-type=NativeScroll].scrollbar-hidden::-webkit-scrollbar{display:none}[data-framer-component-type=PageContentWrapper]>*,[data-framer-component-type=PageContentWrapper]>[data-framer-component-type]{position:relative}[data-framer-component-type=DeviceComponent].no-device>*{width:100%!important;height:100%!important}[data-is-present=false],[data-is-present=false] *{pointer-events:none!important}[data-framer-cursor=pointer]{cursor:pointer}[data-framer-cursor=grab]{cursor:grab}[data-framer-cursor=grab]:active{cursor:grabbing}[data-framer-component-type=Frame] *,[data-framer-component-type=Stack] *{pointer-events:auto}[data-framer-generated] *{pointer-events:unset}.svgContainer svg{display:block}[data-reset=button]{border-width:0;padding:0;background:none}[data-hide-scrollbars=true]::-webkit-scrollbar{width:0px;height:0px}[data-hide-scrollbars=true]::-webkit-scrollbar-thumb{background:transparent}
</style>
    
    <!-- End of headEnd -->
<script id="dataslayerLaunchMonitors">
// console.log('** dataslayer: injecting Launch monitors **');
window._satellite = window._satellite || {};
window._satellite._monitors = window._satellite._monitors || [];
window._satellite._monitors.push({
  ruleTriggered: function(e) {
    window.parent.postMessage(
      {
        type: 'dataslayer_launchruletriggered',
        url: window == window.parent ? window.location.href : 'iframe',
        data: JSON.parse(JSON.stringify(e.rule)),
      },
      '*'
    );
  },
  ruleCompleted: function(e) {
    // console.log('** dataslayer: Launch rule completed **', e.rule);
    var rule = JSON.parse(JSON.stringify(e.rule));
    var sendRule = function() {
        if (window.dataslayer) {
            window.parent.postMessage(
                {
                    type: 'dataslayer_launchrulecompleted',
                    url: window == window.parent ? window.location.href : 'iframe',
                    data: rule,
                },
                '*'
            );       
            // console.log('** dataslayer: posted rule '+rule+'**'); 
        } else {
            if (document.readyState === 'complete') {
                // console.log('** dataslayer: giving up on launch **');
                window._dataslayerQueue = window._dataslayerQueue || [];
                window._dataslayerQueue.push({
                  type: 'dataslayer_launchrulecompleted',
                  url: window == window.parent ? window.location.href : 'iframe',
                  data: rule,
                });
                return;
            } else {
                // console.log('** dataslayer: waiting 250ms to repost rule **');
                window.setTimeout(sendRule, 250);
            }
        }
    }
    sendRule();
    if (
      window._satellite &&
      window._satellite._container &&
      window._satellite._container.dataElements &&
      true
    ) {
      var elementNames = Object.keys(
        window._satellite._container.dataElements
      ).sort(function(a, b) {
        var nameA = a.toUpperCase();
        var nameB = b.toUpperCase();

        if (nameA < nameB) {
          return -1;
        } else if (nameA > nameB) {
          return 1;
        } else {
          return 0;
        }
      });

      let launchElements = {};

      for (const elementName of elementNames) {
        var newElement = JSON.parse(
          JSON.stringify(
            window._satellite._container.dataElements[elementName]
          )
        );
  
        let cleanValue = window._satellite.getVar(elementName);
        if (typeof cleanValue === 'function') {
          cleanValue = '(function)';
        } else if (
          cleanValue !== null &&
          typeof cleanValue === 'object' &&
          typeof cleanValue.then === 'function'
        ) {
          cleanValue = '(Promise)';
        }
        launchElements[elementName] = cleanValue;
        // launchElements.push({
        //   key: elementNames[i],
        //   value: cleanValue,
        //   element: newElement,
        // });
      }
      try {
        window.parent.postMessage(
          {
            type: 'dataslayer_launchdataelements',
            data: 'found',
            url: window == window.parent ? window.location.href : 'iframe',
            elements: launchElements
          },
          '*'
        );
      } catch (e) {
        console.warn(e);
      }
  
    }
  },
  ruleConditionFailed: function(e) {
    window.parent.postMessage(
      {
        type: 'dataslayer_launchrulefailed',
        url: window == window.parent ? window.location.href : 'iframe',
        data: JSON.parse(JSON.stringify(e.rule)),
      },
      '*'
    );
  },
});
</script><style>.base-arrow-GfAHTK {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAyCAYAAACtd6CrAAAACXBIWXMAAC4jAAAuIwF4pT92AAAC9ElEQVR4nL2YSWtUQRSFS23nhRsXgmsFE/+AmwRFURQnRMURdSMSp2iCnXQwA3ZMSIKJRMRNHFDEefaXqP/An2Ack/Yc320pz+skPdx44TSv3ynq49Z7r+pWZerr1qwLIdyFVkA3oPYPHz8VwixEBroDrbL/WWjZ2vq6ptkAErZS7p2CJgE84w0k7BZ0Qe43QQUAz3oCCWuFlkNHxTsNTQDY7AXMoCMO2Qlcz4GOiH8OIqjZBcYfAJnBcVzOgw5Km/PwJtCmxQUWATmUzPCAtLsIr4A2rS4wAc6F9kvbFssw6wIz4C90ejgkGe4T+5Jl2OYCi4CHQvIM94idhTeJNjkXWATks3sE7Ra73TLscIEZ8Cc65bN7DO0SO2fPsNMFJsAn0A6xL9uQdrvADPgDne7F5VNou9hdNqQ9LjABPoO2id1tQ5p3gRnwOzrl2/kc2ir2Fcuw1wUmwBfQFrHzlmG/C8yA39ApP4eX0Gax+ww46AIT4Ctok9gDNqRDLjADfkWnO3H5Gtoo9qBlOOwCE+AbaIPY1yzDEReYAcfRKb+/t9D6EkBmOOoCE+A7qDGyuHpchzfuBjPgF3TKD/491CDAq66wmcIVhqyWhmQYG8Ri0dTmBgNoSUjeyEaxCGL9OeYCi0D6JjJYd47yomYYQItDMovoN1YEjRT/1ASLQDp7MFri2aMmGECLQjLz67zIaNV5sWpYBNIZn5EtNeNXBQNoYUhWa13LGLmp1rKKYRFIV2lGx3SrdEUwgBaEpMLS+oPROVP9UTYsAmllxegqp7IqCwbQ/JBUxVozMnrKrRlnhEUgrYYZ+Uqq4WlhANF7GNJ1PqO30jp/SpiBHoT0DobRV80OpiTMQPdDem/G6K92b5aCAcT92L2Q3nUyBmvZdf4Di0C6n2YM1bqf/gsz0O2QPilgDHucFPyBAcTN+lhIn4EwuBbp6U/1MGggpE93GFxd3U53irCTJe7zKND13KoI+wytju7dhNxP5IqwY+E/HW7+BpJoVXj80aQPAAAAAElFTkSuQmCC);
  width: 16px;
  height: 16px;
  position: absolute;
  top: 16px;
  left: 16px;
  cursor: pointer;
  background-size: contain;
  background-repeat: no-repeat;
}
.base-navigation-tab-Ahvn3N {
  width: 24px;
  height: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.base-navigation-label-vUENtU {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  line-height: 18px;
  color: rgba(0, 0, 0, 0.87);
}
.shoop-de-tooltip-rXf0XP {
  position: relative;
}
.shoop-de-tooltip__text-block-VXptk_ {
  height: 136px;
  background: #e35144;
  position: absolute;
  top: -148px;
  width: 302px;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-size: 15px;
  line-height: 21px;
  font-weight: normal;
  letter-spacing: -0.24px;
  color: #FFF;
  display: flex;
  align-items: flex-end;
}
.shoop-de-tooltip__polygon-a7JmmB {
  position: absolute;
  width: 15px;
  height: 15px;
  top: -20px;
  left: 17px;
  border-radius: 2px;
  transform: rotate(45deg);
  background: #e35144;
}
</style><style>.shoop-de-serp-beA_vX {
  z-index: 999999999999;
  width: max-content;
  font-size: 15px;
  line-height: 21px;
  font-weight: bold;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}
.shoop-de-serp-beA_vX:hover .shoop-de-serp__tooltip-jznGdA {
  opacity: 1;
}
.shoop-de-serp__logo-VNEyj1 {
  width: 30px;
  height: 20px;
  background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOC42NTg4IDYuNjUwMjFDMjMuODkxIDYuNjUwMjEgMTkuODUyMyA5Ljc3MTY0IDE4LjQ3NDggMTQuMDgyNEMxNi41NjQ4IDEyLjI5OSAxNC4wMDA0IDExLjIwNzUgMTEuMTgxMSAxMS4yMDc1QzUuMjc3NSAxMS4yMDc1IDAuNDkxNzkgMTUuOTkzMiAwLjQ5MTc5IDIxLjg5NjhDMC40OTE3OSAyNy44MDA0IDUuMjc3NSAzMi41ODYxIDExLjE4MTEgMzIuNTg2MUMxNS45NDg5IDMyLjU4NjEgMTkuOTg3NiAyOS40NjQ2IDIxLjM2NTEgMjUuMTUzOEMyMy4yNzUgMjYuOTM3MyAyNS44Mzk0IDI4LjAyODggMjguNjU4OCAyOC4wMjg4QzM0LjU2MjQgMjguMDI4OCAzOS4zNDgxIDIzLjI0MzEgMzkuMzQ4MSAxNy4zMzk1QzM5LjM0ODEgMTEuNDM1OSAzNC41NjI0IDYuNjUwMjEgMjguNjU4OCA2LjY1MDIxWk0yOC42NTg4IDIyLjI5NTJDMjUuOTIxNyAyMi4yOTUyIDIzLjcwMzEgMjAuMDc2NiAyMy43MDMxIDE3LjMzOTVDMjMuNzAzMSAxNC42MDI0IDI1LjkyMTcgMTIuMzgzOCAyOC42NTg4IDEyLjM4MzhDMzEuMzk1OSAxMi4zODM4IDMzLjYxNDUgMTQuNjAyNCAzMy42MTQ1IDE3LjMzOTVDMzMuNjE0NSAyMC4wNzY2IDMxLjM5NTkgMjIuMjk1MiAyOC42NTg4IDIyLjI5NTJaTTExLjE4MTEgMjYuODUyNUM4LjQ0MzkzIDI2Ljg1MjUgNi4yMjUzNiAyNC42MzM5IDYuMjI1MzYgMjEuODk2OEM2LjIyNTM2IDE5LjE1OTYgOC40NDM5MyAxNi45NDExIDExLjE4MTEgMTYuOTQxMUMxMy45MTgyIDE2Ljk0MTEgMTYuMTM2OCAxOS4xNTk2IDE2LjEzNjggMjEuODk2OEMxNi4xMzY4IDI0LjYzMzkgMTMuOTE4MiAyNi44NTI1IDExLjE4MTEgMjYuODUyNVoiIGZpbGw9IiMxRDdCQ0UiLz4KPC9zdmc+Cg==) no-repeat;
  background-size: contain;
}
.shoop-de-serp__premium-ZDFq9y {
  background-image: url(data:image/svg+xml;base64,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);
  width: 16px;
  height: 16px;
  margin-right: 10px;
}
.shoop-de-serp__description-pzXVxP {
  color: #1D7BCE;
  font-weight: 600;
}
.shoop-de-serp__container-HDQc_M {
  display: flex;
  width: 100%;
}
</style><meta name="shoop-toolbar" version="********"><script async="" src="https://script.hotjar.com/modules.3128f1ee3ce5b65c4961.js" charset="utf-8"></script><style type="text/css" data-framer-css="true"></style><meta name="mydealz-browser-extension" version="12.2.0"><style id="shoop-styles">@import url("chrome-extension://hacngjmphfcjdfpmfmlngemhddjdncpe/content/styles.css");
    @import url("chrome-extension://hacngjmphfcjdfpmfmlngemhddjdncpe/fonts.css");</style></head>

<body>
    <script async="" src="https://events.framer.com/script" data-fid="faae23a5ce522943dcc4961de2807183885fc892d4f0e32fa5f7abacae513cc6"></script>

    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MKJC2T5" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    <!-- End of bodyStart -->
    <div id="main"><div class="framer-2J8KY framer-Iyk50" style="display: contents;"><div class="framer-yi5wyj" style="min-height: 100%; width: auto;"><main class="framer-bdmtry" data-framer-name="Main" name="Main"><div class="framer-11zlnui-container" data-framer-name="Navigation" name="Navigation"><div class="framer-mCoPO framer-v-1xtks3u" tabindex="0" style="display: contents;"><div name="Navigation" class="framer-1xtks3u" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); width: 100%; box-shadow: none; transform: none; transform-origin: 50% 50% 0px; opacity: 1;"><div class="framer-7n1rqr" data-framer-name="Header" style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;"><a class="framer-1df91dr framer-9l99o0" href="./" data-framer-page-link-current="true" style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;"><div class="framer-phv7f5-container" style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;"><div data-framer-generated="true" class="framer-su7oC framer-v-1o77pht" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1o77pht framer-1pi3ej6" data-framer-name="Logomark" data-highlight="true" href="./" tabindex="0" style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><div data-framer-component-type="SVG" class="framer-1ip3699" style="image-rendering: pixelated; flex-shrink: 0; background-size: 100% 100%; background-image: url(&quot;data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20viewBox%3D%220%200%2032%2032%22%3E%3Cpath%20d%3D%22M%2012.9%2031%20L%205.7%2026.2%20C%205.44%2026.025%205.23%2025.785%205.09%2025.505%20C%204.95%2025.225%204.884%2024.913%204.9%2024.6%20L%204.9%201.9%20C%204.897%201.701%204.949%201.505%205.051%201.334%20C%205.152%201.163%205.299%201.023%205.475%200.93%20C%205.65%200.837%205.849%200.794%206.047%200.806%20C%206.246%200.819%206.437%200.886%206.6%201%20L%2013.8%205.8%20C%2014.06%205.975%2014.27%206.215%2014.41%206.495%20C%2014.55%206.775%2014.616%207.087%2014.6%207.4%20L%2014.6%2030.2%20C%2014.582%2030.389%2014.516%2030.57%2014.407%2030.726%20C%2014.298%2030.881%2014.151%2031.005%2013.979%2031.086%20C%2013.807%2031.167%2013.617%2031.201%2013.428%2031.186%20C%2013.239%2031.171%2013.057%2031.107%2012.9%2031%20Z%22%20fill%3D%22rgb(0%2C0%2C0)%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M%2025.5%2018.7%20L%2018.3%2013.9%20C%2018.04%2013.725%2017.83%2013.485%2017.69%2013.205%20C%2017.55%2012.925%2017.484%2012.613%2017.5%2012.3%20L%2017.5%201.9%20C%2017.497%201.701%2017.549%201.505%2017.651%201.334%20C%2017.752%201.163%2017.899%201.023%2018.075%200.93%20C%2018.25%200.837%2018.448%200.794%2018.647%200.806%20C%2018.846%200.819%2019.037%200.886%2019.2%201%20L%2026.4%205.8%20C%2026.66%205.975%2026.87%206.215%2027.01%206.495%20C%2027.15%206.775%2027.216%207.087%2027.2%207.4%20L%2027.2%2017.8%20C%2027.193%2017.995%2027.135%2018.184%2027.031%2018.35%20C%2026.928%2018.515%2026.782%2018.65%2026.61%2018.741%20C%2026.438%2018.832%2026.244%2018.877%2026.049%2018.869%20C%2025.855%2018.862%2025.665%2018.804%2025.5%2018.7%20Z%22%20fill%3D%22rgb(0%2C0%2C0)%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E&quot;); transform: none; transform-origin: 50% 50% 0px;"></div><div class="framer-7qhj7p" style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;"><div data-framer-component-type="Text" class="framer-16gi991" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-ExtraBold&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-weight: 800; --framer-letter-spacing: -0.2px; --framer-line-height: 1em; --framer-link-hover-text-color: #222; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; transform-origin: 50% 50% 0px;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Popless</span><br></span></span></div></div></a></div></div></a><div class="framer-1frl3w7" data-framer-name="Links" style="opacity: 1;"><div class="framer-wdeyfh" style="opacity: 1;"><div class="framer-1xsd5m9-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-NxZit framer-v-13iaam4" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-13iaam4" data-framer-name="Default" data-highlight="true" style="background-color: rgba(0, 0, 0, 0); height: 100%; border-radius: 6px; opacity: 1;"><div class="framer-1sjhzl9" style="background-color: rgb(255, 255, 255); border-radius: 6px; opacity: 1;"><div class="framer-tmd0b8" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-letter-spacing: -0.2px; --framer-line-height: 2.4em; --framer-text-alignment: center; --framer-text-color: var(--extracted-r6o4lv);">Use cases</p></div><div class="framer-p22jzm" style="opacity: 1;"><div data-framer-component-type="SVG" class="framer-15gfcyx" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 15 15" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-1471486003_194"></use></svg></div></div></div></div></div></div></div><div class="framer-38ok9r-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-NxZit framer-v-13iaam4" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-13iaam4" data-framer-name="Default" data-highlight="true" style="background-color: rgba(0, 0, 0, 0); height: 100%; border-radius: 6px; opacity: 1;"><div class="framer-1sjhzl9" style="background-color: rgb(255, 255, 255); border-radius: 6px; opacity: 1;"><div class="framer-tmd0b8" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-letter-spacing: -0.2px; --framer-line-height: 2.4em; --framer-text-alignment: center; --framer-text-color: var(--extracted-r6o4lv);">Features</p></div><div class="framer-p22jzm" style="opacity: 1;"><div data-framer-component-type="SVG" class="framer-15gfcyx" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 15 15" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-1471486003_194"></use></svg></div></div></div></div></div></div></div><div class="framer-m7556w-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-9JZMP framer-v-1b7gv89" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1b7gv89 framer-14eeasz" data-framer-name="Default" data-highlight="true" href="https://www.popless.com/explore" target="_blank" rel="noopener" tabindex="0" style="background-color: rgb(255, 255, 255); height: 100%; border-radius: 6px; opacity: 1;"><div class="framer-lv43im" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-letter-spacing: -0.2px; --framer-line-height: 2.4em; --framer-text-alignment: center; --framer-text-color: var(--extracted-r6o4lv);">Explore</p></div></a></div></div><div class="framer-1fekpn9-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-9JZMP framer-v-1vq2r5t" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1b7gv89 framer-14eeasz" data-framer-name="Login" data-highlight="true" href="https://www.popless.com/login" target="_blank" rel="noopener" tabindex="0" style="background-color: rgb(255, 255, 255); height: 100%; border-radius: 6px; opacity: 1;"><div class="framer-1uwu8gn-container" style="opacity: 1;"><a class="p-login-link" href="/login" target="_blank">Login</a><style>
    .p-login-link{
        color: #202124;
        font-size: 16px;
        font-family: "Inter";
        font-weight: 500;
        text-decoration: none;
        line-height: 2.4em;
    }
</style></div></a></div></div></div><div class="framer-oanszr" style="opacity: 1;"><div class="framer-1908hzl-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px; opacity: 1;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Get started</span><br></span></span></div></a></div></div></div></div></div></div></div></div><div class="framer-pmc1c2" data-framer-name="impact" name="impact"><div class="framer-vim8kp"><div class="framer-19ivupa"><div class="framer-7ragbc"><div class="framer-1upx9es" data-framer-name="The all-in-one platform" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-weight: 500; --framer-line-height: 150%; --framer-text-alignment: left; --framer-text-color: rgb(200, 200, 200); --framer-font-size: 20px;">Tutor on a trusted platform.</p></div><div class="framer-g0e7mo" data-framer-name="Supercharge how you teach." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-line-height: 1.1em;"><span class="framer-text" style="--framer-font-size: 70px;">Powerful</span><br class="framer-text"><span class="framer-text" style="--framer-font-size: 70px;">tutoring and</span><br class="framer-text"><span class="framer-text" style="--framer-font-size: 70px;">classes.</span></p></div></div></div><div class="framer-jrk6w0" data-framer-name="The easiest way to manage and grow your tutoring business." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 20px; --framer-line-height: 135%; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255);">Set your own rate, we'll connect you with you with students, you earn money on your schedule.</p></div><div class="framer-1oipifc-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1nr57ge" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(255, 255, 255); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;" data-framer-name="Button - White"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Get started today</span><br></span></span></div></a></div></div><div class="framer-6qewkf-container"></div></div><div class="framer-nb8xh9"><div class="framer-f5z1k4" data-framer-name="Messages" name="Messages" style="opacity: 1; transform: none;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/0XK2wgaHskIE0XvlI8000WaXusA.png" alt="" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div><div class="framer-1w2j9nd" data-framer-name="Profile" name="Profile" style="opacity: 1; transform: none;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png" alt="" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div><div class="framer-6h2fvt" style="opacity: 1; transform: translateY(-50%);"><div class="framer-14he46i" data-framer-name="Total_Earnings" name="Total_Earnings"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/BM7YOwABbWREv6GVwox81dWBNn8.png" alt="" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div><div class="framer-9ny7dv" data-framer-name="Date_Selection" name="Date_Selection"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/mgWhKiMSujgr8Qx4CKdAUGmzRpc.png" alt="" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div><div class="framer-1p818kr" data-framer-name="Checkout_Meets" name="Checkout_Meets"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/MqDX17oIrwlazxq1yev0XXhIU.png" alt="" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div></div></div></div><div class="framer-8plkry" data-framer-name="impact" name="impact"><div class="framer-1li8nea"><div class="framer-1efq1ib"><div class="framer-gd4g2c" data-framer-name="Power your teaching and students from an all-in-one dashboard." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 58px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Power your tutoring and students from an all-in-one dashboard.</p></div><div class="framer-3jlv5e" data-framer-name="Spend more time teaching and engaging students." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--framer-text-alignment: center;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 24px;">Spend more time teaching and less time managing admin.</span></p></div></div><div class="framer-137suh2"><div class="framer-15kh8nv-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Get started today</span><br></span></span></div></a></div></div><div class="framer-ji3zr1-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1rm2apr" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line - Black Hover" data-highlight="true" data-border="true" href="https://calendly.com/popless/intro" rel="noopener" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Request a demo</span><br></span></span></div></a></div></div></div></div><div class="framer-16euffb"><div class="framer-i4ewrd" data-framer-name="1" name="1" style="opacity: 1; transform: none;"><div class="framer-1s8lcwf" data-framer-name="Frame 56058" name="Frame 56058"><div class="framer-rkbnv4" data-framer-name="Scheduling" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36); --framer-font-size: 18px;">Set your price and schedule</p></div><div class="framer-u7lr41" data-framer-name="Advanced scheduling tools to  help you manage your time." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Choose when you tutor and <br class="framer-text">how much you charge.</p></div></div><div class="framer-1rdi3we" data-framer-name="image 1137" name="image 1137"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/sGVAlIleLngGdt5ONlDKn4bbI.png" alt="" srcset="https://framerusercontent.com/images/sGVAlIleLngGdt5ONlDKn4bbI.png?scale-down-to=512 512w, https://framerusercontent.com/images/sGVAlIleLngGdt5ONlDKn4bbI.png 876w" sizes="384px" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div></div></div><div class="framer-1brbpm6" data-framer-name="2" name="2" style="opacity: 1; transform: none;"><div class="framer-1grq1e7" data-framer-name="Frame 56058" name="Frame 56058"><div data-framer-component-type="Text" data-framer-name="Instant payments and payouts" class="framer-18nl1oz" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: center; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Automated payments and payouts</span><br></span></span></div><div class="framer-1n6afnn" data-framer-name="Accept payments in 45 currencies.  Get paid after your meeting." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Accept payments in 45 currencies.<br class="framer-text">Get paid straight after your meeting.</p></div></div><div class="framer-uzmybr"><div class="framer-12uknh7" data-framer-name="Transaction Items" name="Transaction Items"><div class="framer-1vbrzj8" data-framer-name="Group 30090" name="Group 30090"><div data-framer-component-type="Text" data-framer-name="Jul 23, 2022" class="framer-1210zlv" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Jul 23, 2022</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="+ $80.00" class="framer-yu9a87" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: right; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">+ $80.00</span><br></span></span></div><div class="framer-1ek6lm" data-framer-name="Frame 55313" name="Frame 55313" style="transform: translateY(-50%);"><div data-framer-component-type="Text" data-framer-name="Earnings" class="framer-r619mv" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Earnings</span><br></span></span></div></div><div data-framer-component-type="Text" data-framer-name="Andrew Hawkins" class="framer-1eacu8t" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Andrew Hawkins</span><br></span></span></div><div class="framer-nrzljs" data-framer-name="Ellipse 430" name="Ellipse 430"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/2FIoO1BEuZ8RE7dUvGhzjVHyg.png" alt="" srcset="https://framerusercontent.com/images/2FIoO1BEuZ8RE7dUvGhzjVHyg.png?scale-down-to=512 503w, https://framerusercontent.com/images/2FIoO1BEuZ8RE7dUvGhzjVHyg.png?scale-down-to=1024 1007w, https://framerusercontent.com/images/2FIoO1BEuZ8RE7dUvGhzjVHyg.png 1076w" sizes="32px" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div><div class="framer-36gs25" data-framer-name="Transaction Items" name="Transaction Items"><div class="framer-1ezsorj" data-framer-name="Group 30090" name="Group 30090"><div data-framer-component-type="Text" data-framer-name="Jul 21, 2022" class="framer-lwl56d" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Jul 21, 2022</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="- $90.00" class="framer-fw1xq2" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: right; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">- $90.00</span><br></span></span></div><div class="framer-4n4q1m" data-framer-name="Frame 55311" name="Frame 55311" style="transform: translateY(-50%);"><div data-framer-component-type="Text" data-framer-name="Payout" class="framer-133c5yy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Payout</span><br></span></span></div></div><div data-framer-component-type="Text" data-framer-name="Popless payout" class="framer-15wmplz" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Popless payout</span><br></span></span></div><div class="framer-15hiidr" data-framer-name="Group 31039" name="Group 31039"><div class="framer-1d2wnu8" data-framer-name="Ellipse 430" name="Ellipse 430"></div><div data-framer-component-type="SVG" class="framer-1hgmpkn" style="image-rendering: pixelated; flex-shrink: 0; fill: rgb(0, 0, 0); color: rgb(0, 0, 0);"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 18 18" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-1948094006_1423"></use></svg></div></div></div></div></div><div class="framer-ilv2si" data-framer-name="Transaction Items" name="Transaction Items"><div class="framer-1q7ele9" data-framer-name="Group 30090" name="Group 30090"><div data-framer-component-type="Text" data-framer-name="Jul 18, 2022" class="framer-4j1rfg" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Jul 18, 2022</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="+ $70.00" class="framer-1yv6zo7" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: right; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">+ $70.00</span><br></span></span></div><div class="framer-9lyni2" data-framer-name="Frame 55312" name="Frame 55312" style="transform: translateY(-50%);"><div data-framer-component-type="Text" data-framer-name="Earnings" class="framer-d1m54c" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Earnings</span><br></span></span></div></div><div data-framer-component-type="Text" data-framer-name="Samantha Jane" class="framer-62hidm" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: left; transform: none;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Samantha Jane</span><br></span></span></div><div class="framer-2fhb38" data-framer-name="Ellipse 430" name="Ellipse 430"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/Qo0kTzmWZ2qjy3PRKERLjsxNY0.png" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div></div></div><div class="framer-r75a4z" data-framer-name="3" name="3" style="opacity: 1; transform: none;"><div class="framer-1mtrmim" data-framer-name="Frame 56059" name="Frame 56059"><div class="framer-1oks7do" data-framer-name="Frame 56058" name="Frame 56058"><div class="framer-6syby" data-framer-name="Private and group messaging" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36); --framer-font-size: 18px;">Private and group messaging</p></div><div class="framer-gtgbxo" data-framer-name="Securely send messages,  photos, videos, and documents." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Securely send messages,<br class="framer-text">photos, videos, and documents.</p></div></div></div><div class="framer-ryhwyv" data-framer-name="image 1141" name="image 1141"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/dX34Z5nGLKcjM2iqt7tUjG6fDk.jpg" alt="" srcset="https://framerusercontent.com/images/dX34Z5nGLKcjM2iqt7tUjG6fDk.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/dX34Z5nGLKcjM2iqt7tUjG6fDk.jpg?scale-down-to=1024 1024w, https://framerusercontent.com/images/dX34Z5nGLKcjM2iqt7tUjG6fDk.jpg 1941w" sizes="344px" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div></div><div class="framer-4tl70a" data-framer-name="impact" name="impact"><div class="framer-5vtt9"><div class="framer-o3ho91"><div class="framer-6dfkf" data-framer-name="Power your teaching and students from an all-in-one dashboard." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Integrated whiteboard, messaging, and document sharing. </p></div><div class="framer-i27pp4" data-framer-name="Spend more time teaching and engaging students." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-text-alignment: center; --framer-font-size: 24px;">Interactive online features makes learning engaging. Bring tricky concepts to life with interactive exercises, drawing diagrams, and annotating homework and practice questions.</p></div></div><div class="framer-1xgknqg"><div class="framer-3olhvc-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Sign up for free</span><br></span></span></div></a></div></div><div class="framer-1mv0jqt-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1rm2apr" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line - Black Hover" data-highlight="true" data-border="true" href="./updates" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">View all features</span><br></span></span></div></a></div></div></div></div><div class="framer-l5720k" style="opacity: 1; transform: none;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/e911izya8tyq4pB77V3niPLOd9A.png" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div><div class="framer-54acze" data-framer-name="impact" name="impact"><div class="framer-1k39h5x"><div class="framer-vd69qr"><div class="framer-5wi7v9" data-framer-name="Power your teaching and students from an all-in-one dashboard." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 58px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);"><span class="framer-text" style="--framer-font-size: 60px;">Expand your tutoring income</span><br class="framer-text"><span class="framer-text" style="--framer-font-size: 60px;">with group classes.</span></p></div><div class="framer-67ii8u" data-framer-name="Spend more time teaching and engaging students." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-text-alignment: center; --framer-font-size: 24px;">Tutor your students in a group setting. Perfect for<br class="framer-text">group tutoring before finals and teaching small classes.</p></div></div><div class="framer-juci64"><div class="framer-1visvkx-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="./group-classes" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Learn more about classes</span><br></span></span></div></a></div></div><div class="framer-3ea4k1-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-milg9a" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line" data-highlight="true" data-border="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Become a tutor</span><br></span></span></div></a></div></div></div></div><div class="framer-ubi8fo"><div class="framer-1p1co0z" data-framer-name="tut_hero" name="tut_hero" style="opacity: 1; transform: none;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png" alt="" srcset="https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=512 512w, https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=1024 1024w, https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=2048 2048w, https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=4096 4096w, https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png 5760w" sizes="max(100vw - 80px, 1840px)" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div><div class="framer-1v5tnxd" data-framer-name="impact" name="impact"><div class="framer-1qfr4gh"><div class="framer-qixt01"><div class="framer-1goth4" data-framer-name="Power your teaching and students from an all-in-one dashboard." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Take better notes, view your students in one place, and stay on track.</p></div><div class="framer-1mrh0zl" data-framer-name="Spend more time teaching and engaging students." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 1; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-text-alignment: center; --framer-font-size: 24px;">Featuring powerful note taking and contact management tools to help you stay organized and on top of your tutoring schedule.</p></div></div><div class="framer-1iox7ue"><div class="framer-1yb2uv4-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Sign up for free</span><br></span></span></div></a></div></div><div class="framer-16oo8jz-container" style="opacity: 1; transform: none;"><div data-framer-generated="true" class="framer-cKThw framer-v-1rm2apr" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line - Black Hover" data-highlight="true" data-border="true" href="https://calendly.com/popless/intro" rel="noopener" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Request a demo</span><br></span></span></div></a></div></div></div></div><div class="framer-54zyx5" style="opacity: 1; transform: none;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/41JSuJQBk1wyo1nqNlipP1epQwM.png?scale-down-to=2048" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div></div><div class="framer-1jvpjuk" data-framer-name="impact" name="impact"><div class="framer-2kxcur"><div class="framer-1yawrco" data-framer-name="Frame 42560" name="Frame 42560" style="opacity: 1; transform: none;"><div class="framer-17pm2ea"><div class="framer-fv4dwt"><div class="framer-1hsw75p" data-framer-name="A platform ready to grow as you do." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--framer-text-alignment: center;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-color: rgb(32, 33, 36);">Access your schedule, meetings, and messages on the go.</span></p></div><div class="framer-hiy60r" data-framer-name="Features to help your tutoring business thrive." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 1.4em; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36); --framer-font-size: 20px;">Designed for tutors who want simple and powerful tutoring.</p></div></div><div class="framer-x5yfjb" data-framer-name="Frame 60054" name="Frame 60054"><div class="framer-1i57x2z-container" data-framer-appear-id="drfgcy" style="opacity: 1; transform: perspective(1200px) translateX(0px) translateY(0px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-cKThw framer-v-1e84z8s" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid" data-highlight="true" href="./updates" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(0, 0, 0); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">View all features</span><br></span></span></div></a></div></div><div class="framer-18g3mrn-container" data-framer-appear-id="1jj3tuk" style="opacity: 1; transform: perspective(1200px) translateX(0px) translateY(0px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-cKThw framer-v-milg9a" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line" data-highlight="true" data-border="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Become a tutor</span><br></span></span></div></a></div></div></div></div><div class="framer-109i3p1" style="transform: none;"><div class="framer-1p2d8wt"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/E6jIayRQgiXA0hftkTFvm8gRz80.png" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div><div class="framer-fjsr2c"><div class="framer-1uz98hs" data-framer-name="Frame 42561" name="Frame 42561" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-htd3vn" data-framer-name="Analytics to help you understand how you’re doing." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNjAw; --framer-font-size: 65px; --framer-font-weight: 600; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255);">Analytics </p><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNjAw; --framer-font-size: 65px; --framer-font-weight: 600; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255);">to help you understand </p><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNjAw; --framer-font-size: 65px; --framer-font-weight: 600; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255);">how you’re</p><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNjAw; --framer-font-size: 65px; --framer-font-weight: 600; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255);">doing.</p></div><div class="framer-16sv6zg" data-framer-name="Frame 56067" name="Frame 56067"><div class="framer-bo2ge2" data-framer-name="Rectangle 4321" name="Rectangle 4321"></div><div class="framer-18jrkcy" data-framer-name="Rectangle 4322" name="Rectangle 4322"></div><div class="framer-dux3zc" data-framer-name="Rectangle 4323" name="Rectangle 4323"></div><div class="framer-x996u8" data-framer-name="Rectangle 4324" name="Rectangle 4324"></div><div class="framer-1v8lk05" data-framer-name="Rectangle 4325" name="Rectangle 4325"></div></div></div><div class="framer-ynto98" data-framer-name="Frame 42562" name="Frame 42562" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-1uwbm5g" data-framer-name="Features to keep students engaged." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 70px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: rgb(32, 33, 36);">Features <br class="framer-text">to build an income with flexibility.</p></div><div class="framer-1vg5inv" data-border="true" data-framer-name="image 1136" name="image 1136"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/wQtN0QiibWUYDLAwWIp8xvq2E.png" alt="" srcset="https://framerusercontent.com/images/wQtN0QiibWUYDLAwWIp8xvq2E.png?scale-down-to=512 314w, https://framerusercontent.com/images/wQtN0QiibWUYDLAwWIp8xvq2E.png?scale-down-to=1024 628w, https://framerusercontent.com/images/wQtN0QiibWUYDLAwWIp8xvq2E.png 1209w" sizes="445px" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: contain; image-rendering: auto;"></div></div></div></div></div></div></div><div class="framer-v76c69" data-framer-name="impact" name="impact"><div class="framer-11toto3"><div class="framer-19mfrvt" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><p class="framer-text" style="--framer-line-height: 1.4em; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 24px;">The all-in-one tutoring platform.</p></div><div class="framer-11eckbu" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><p class="framer-text" style="--font-selector: SW50ZXItQm9sZA==; --framer-font-family: &quot;Inter-Bold&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 58px; --framer-font-weight: 700; --framer-letter-spacing: -1px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36));">Powerful tools to build a reliable <br class="framer-text">and flexible tutoring income.</p></div></div><div class="framer-1ro7r0d"><div class="framer-1j8kbow"><div class="framer-1pz17jn" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-m16w7c" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg482518259_258"></use></svg></div></div><div class="framer-j1dks7" data-framer-name="text" name="text"><div class="framer-16kd9l2" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Set your price</p></div><div class="framer-7x7ft" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Set how much you want to<br class="framer-text">charge and offer discounts.</p></div></div></div><div class="framer-19b7ffq" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1b5imla" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1776301866_407"></use></svg></div></div><div class="framer-10idl8n" data-framer-name="text" name="text"><div class="framer-p4gpv1" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Scheduling</p></div><div class="framer-o7rv8y" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Advanced scheduling tools to help you manage your time.</p></div></div></div><div class="framer-1fx6vyf" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1ob8xgy" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-187085573_399"></use></svg></div></div><div class="framer-bov8qc" data-framer-name="text" name="text"><div class="framer-aeurab" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Global payments</p></div><div class="framer-1jlt5l5" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Accept payments in 45 <br class="framer-text">currencies from 140 counties.</p></div></div></div><div class="framer-1q212y8" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-vjzeb" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg138610326_1740"></use></svg></div></div><div class="framer-21cc7o" data-framer-name="text" name="text"><div class="framer-dgw50c" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">All-in-one dashboard</p></div><div class="framer-1vs1yrn" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">The only platform designed for tutoring professionals.</p></div></div></div><div class="framer-1yfhw5l" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-uuftbv" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-410979178_970"></use></svg></div></div><div class="framer-1fp5pyb" data-framer-name="text" name="text"><div class="framer-12v3pwt" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Meeting packages</p></div><div class="framer-15uz755" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Offer discounts to people who<br class="framer-text">pre-pay for multiple meetings.</p></div></div></div><div class="framer-15jzchh" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1jhxl03" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1576715579_278"></use></svg></div></div><div class="framer-1diaj7k" data-framer-name="text" name="text"><div class="framer-tt5t9a" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Messaging</p></div><div class="framer-3bwlgp" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Securely send messages,<br class="framer-text">photos, videos, and documents.</p></div></div></div><div class="framer-1guap2v" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1iv3l05" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1591810958_423"></use></svg></div></div><div class="framer-1t1q1vd" data-framer-name="text" name="text"><div class="framer-1cq3xk0" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Video calls</p></div><div class="framer-zm9486" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Support for up to 200 people with breakout rooms and whiteboards.</p></div></div></div><div class="framer-96frgn" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1dqcsxt" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1688372684_414"></use></svg></div></div><div class="framer-vvyqbx" data-framer-name="text" name="text"><div class="framer-uvp8lt" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Coupon discounts</p></div><div class="framer-xmeoz0" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Create personalized coupons that can be applied at checkout.</p></div></div></div><div class="framer-k2640m" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-quwo9w" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg529086851_361"></use></svg></div></div><div class="framer-1kh90lm" data-framer-name="text" name="text"><div class="framer-10pwcqd" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Calendar integration</p></div><div class="framer-95afxg" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Connect Google Calendar to easily managing your availability.</p></div></div></div><div class="framer-q4bxb9" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-f5rm55" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-129555426_539"></use></svg></div></div><div class="framer-1y2lbxw" data-framer-name="text" name="text"><div class="framer-1vp3xsm" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Instant booking</p></div><div class="framer-emx0lv" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Accept bookings from students automatically or one-by-one.</p></div></div></div><div class="framer-2bugyz" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1bhs8m2" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-399729834_291"></use></svg></div></div><div class="framer-h7vupa" data-framer-name="text" name="text"><div class="framer-1b57mx3" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Analytics</p></div><div class="framer-17mb5ud" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Track your earnings, bookings, minutes tutored, and more.</p></div></div></div><div class="framer-4u923w" data-framer-name="Features" name="Features" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-8qt2fa" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 36 36" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1055447456_467"></use></svg></div></div><div class="framer-1b3rfnl" data-framer-name="text" name="text"><div class="framer-6vk0v8" data-framer-name="All in one solution" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 18px; --framer-font-weight: 500; --framer-line-height: 135%; --framer-text-alignment: center; --framer-text-color: rgb(32, 33, 36);">Cancelation policies</p></div><div class="framer-gjtegf" data-framer-name="Teach online from wherever  and set your own prices." data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-line-height: 125%; --framer-text-alignment: center; --framer-text-color: rgb(95, 99, 104);">Choose a cancelation policy<br class="framer-text">that works for you.</p></div></div></div></div></div></div><header class="framer-thslyp" data-framer-name="Stack" name="Stack"><div class="framer-1oa7cyl"><div class="framer-1edh616" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><h1 class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36));">Be discovered <br class="framer-text">in the Popless marketplace.</h1></div><div class="framer-10rsf2h" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 24px; --framer-line-height: 1.4em; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36));">Opt-in to be found on the Popless marketplace by 1000’s of students looking for help.</p></div></div><div class="framer-ijlo09-container" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-cKThw framer-v-1rm2apr" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line - Black Hover" data-highlight="true" data-border="true" href="./marketplace" tabindex="0" style="--border-bottom-width: 1px; --border-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(255, 255, 255); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Learn more</span><br></span></span></div></a></div></div><div class="framer-15d8ixs hidden-1wqr76h" data-framer-name="Cards" name="Cards"><div class="framer-123mgbl" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-101gd40-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/7voIAUYlLejHnb7TFFJkGm7q4.jpg" alt="" srcset="https://framerusercontent.com/images/7voIAUYlLejHnb7TFFJkGm7q4.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/7voIAUYlLejHnb7TFFJkGm7q4.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Artist | Explore modern art</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Michael K.</span><br></span></span></div></div></div></div><div class="framer-11qwcnc-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/lDHylh6Aw5nXUwrbAfMRaiYL4k.jpg" alt="" srcset="https://framerusercontent.com/images/lDHylh6Aw5nXUwrbAfMRaiYL4k.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/lDHylh6Aw5nXUwrbAfMRaiYL4k.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">with an artist</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Learn drawing</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-1w5ket5-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/GSRYxDQtkehRTowq52QbjgE9n4.jpg" alt="" srcset="https://framerusercontent.com/images/GSRYxDQtkehRTowq52QbjgE9n4.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/GSRYxDQtkehRTowq52QbjgE9n4.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Horticulturalist | Intro to horticulture</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Kathryn M.</span><br></span></span></div></div></div></div><div class="framer-1dtr0r1-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/RzKTmX6ya9TRbFOEX5QOcghTm0.jpg" alt="" srcset="https://framerusercontent.com/images/RzKTmX6ya9TRbFOEX5QOcghTm0.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/RzKTmX6ya9TRbFOEX5QOcghTm0.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">and mentorship</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Peer support</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-u79oqa-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/JpRbCCDMMWDdja1kr1r5qBG0wk.jpg" alt="" srcset="https://framerusercontent.com/images/JpRbCCDMMWDdja1kr1r5qBG0wk.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/JpRbCCDMMWDdja1kr1r5qBG0wk.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Peer mentor | Balance college and wellness</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Amelia and Stephanie</span><br></span></span></div></div></div></div></div><div class="framer-wzin9k" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-1t26hhx-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/tgwtfXR6gl5FN1DAuy1XqEgCw7I.jpg" alt="" srcset="https://framerusercontent.com/images/tgwtfXR6gl5FN1DAuy1XqEgCw7I.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/tgwtfXR6gl5FN1DAuy1XqEgCw7I.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Stanford University | Intro to robotics</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Charlotte A.</span><br></span></span></div></div></div></div><div class="framer-1ggmtpa-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/6yLrvitV3Wv1kc9UgnguNoaur5c.jpg" alt="" srcset="https://framerusercontent.com/images/6yLrvitV3Wv1kc9UgnguNoaur5c.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/6yLrvitV3Wv1kc9UgnguNoaur5c.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Musician | Learn guitar</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Cici M.</span><br></span></span></div></div></div></div><div class="framer-xgh9x-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/zzSb6x9H7eIZ9I03LstvtRNxrM.jpg" alt="" srcset="https://framerusercontent.com/images/zzSb6x9H7eIZ9I03LstvtRNxrM.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/zzSb6x9H7eIZ9I03LstvtRNxrM.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">SAT and ACT</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Study for the</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-ltq859-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/G0nSSXpLD7r7WuRL67OS26L4nxU.jpg" alt="" srcset="https://framerusercontent.com/images/G0nSSXpLD7r7WuRL67OS26L4nxU.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/G0nSSXpLD7r7WuRL67OS26L4nxU.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">college and course</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Tutors for your</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div></div><div class="framer-11wut2b" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-xwe3dg-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/QXZh7UZPopwBPa2nDY2PHusHOE.jpg" alt="" srcset="https://framerusercontent.com/images/QXZh7UZPopwBPa2nDY2PHusHOE.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/QXZh7UZPopwBPa2nDY2PHusHOE.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">your first story</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Write and publish</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-1ktrkvg-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/9oVxaPoWTbw7u8qsFIRXezWJI.jpg" alt="" srcset="https://framerusercontent.com/images/9oVxaPoWTbw7u8qsFIRXezWJI.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/9oVxaPoWTbw7u8qsFIRXezWJI.jpg?scale-down-to=1024 1024w, https://framerusercontent.com/images/9oVxaPoWTbw7u8qsFIRXezWJI.jpg 1362w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">in wellness</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Make progress</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-1p3c0ea-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1bnfmlb" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="Default - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/GXG64ps05DJRIcF1QwPc8WyLQ.jpg" alt="" srcset="https://framerusercontent.com/images/GXG64ps05DJRIcF1QwPc8WyLQ.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/GXG64ps05DJRIcF1QwPc8WyLQ.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-1jcmebn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">admissions</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Prepare for college</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Collection</span><br></span></span></div><div class="framer-1o8m76h-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-qeSWs framer-v-6ncvr9" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-6ncvr9" data-framer-name="Default" style="background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); border-radius: 6px; opacity: 1;"><div data-framer-component-type="Text" class="framer-88skih" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-style: normal; --framer-font-weight: 500; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 14px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 22px; --framer-text-alignment: center; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Show all</span><br></span></span></div></div></div></div></div></div></div><div class="framer-157arvj-container"><div data-framer-generated="true" class="framer-oyyBb framer-v-1li6tw4" tabindex="0" style="display: contents;"><div class="framer-18xv3n2" data-framer-name="No Button - No Link" style="border-radius: 12px; opacity: 1;"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/cSocu6VMiqO76TdwOlv5Byqu4.jpg" alt="" srcset="https://framerusercontent.com/images/cSocu6VMiqO76TdwOlv5Byqu4.jpg?scale-down-to=512 512w, https://framerusercontent.com/images/cSocu6VMiqO76TdwOlv5Byqu4.jpg 908w" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: fill; image-rendering: auto;"></div></div><div data-framer-component-type="Text" data-framer-name="Heading" class="framer-590qhw" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: 0px; --framer-line-height: 24px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Writers | Your first article for the NYT</span><br></span></span></div><div data-framer-component-type="Text" data-framer-name="Sub Heading" class="framer-1apdwzd" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-text-alignment: left; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="font-size: 0"><span style="">Matt and Kelly</span><br></span></span></div></div></div></div></div></div></header><div class="framer-1d2vuwy" data-framer-name="impact" name="impact"><div class="framer-1djsyyh"><div class="framer-1wv67wh" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><p class="framer-text" style="--framer-line-height: 1.4em; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-font-size: 20px;">Trusted by tutors from leading colleges and companies</p></div></div><div class="framer-4l67jf" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-component-type="SVG" class="framer-1hl8dds" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 300 101" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-255222499_1857"></use></svg></div></div><div data-framer-component-type="SVG" class="framer-epp6bv" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 842 220" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1897297650_22348"></use></svg></div></div><div data-framer-component-type="SVG" class="framer-1j2tp7b" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 1668 376" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-1859938347_3781"></use></svg></div></div><div data-framer-component-type="SVG" class="framer-13je2n" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 616 164" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1197562992_17469"></use></svg></div></div><div class="framer-1kh50bz" data-framer-name="stanford" name="stanford"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/NXdh3pIjRuyiIVb3ZsOhyQnrCDo.svg" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div></div><div data-framer-component-type="SVG" class="framer-u31zgk" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 934 204" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg-1115871797_6019"></use></svg></div></div><div data-framer-component-type="SVG" class="framer-19bdyf2" style="image-rendering: pixelated; flex-shrink: 0; fill: black; color: black;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%" viewBox="0 0 300 144" preserveAspectRatio="none" width="100%" height="100%"><use href="#svg1375198502_1037"></use></svg></div></div></div></div><section class="framer-5gf187"><header class="framer-1xiraov" data-framer-name="Stack" name="Stack"><div data-framer-component-type="Text" class="framer-6sseeu" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; --framer-text-alignment: center; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><h1 style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">In case you</span><br></span><span style="direction: ltr; font-size: 0"><span style="">missed anything.</span><br></span></h1></div></header><div class="framer-nhnfxk-container" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-8zmNM framer-v-1ykstih" tabindex="0" style="display: contents;"><div class="framer-brfeq6" data-framer-name="Tutoring - Desktop - v2" style="width: 100%; opacity: 1;"><div class="framer-11mmp6z-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-1k5sum" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Medium - Desktop - Closed" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">What is Popless?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div><div class="framer-1vpa8yo" data-framer-name="Divider" style="background-color: rgb(238, 238, 238); opacity: 1;"></div><div class="framer-veui21-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-1c4jtpn" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Tutoring - Q2 - Closed D - v2" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">How do payments work?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div><div class="framer-1xi9x3o" data-framer-name="Divider" style="background-color: rgb(238, 238, 238); opacity: 1;"></div><div class="framer-16u58oc-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-zu1i8i" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Tutoring - Q3 - Closed D - v2" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">How long does it take to get set up?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div><div class="framer-1qzk6xm" data-framer-name="Divider" style="background-color: rgb(238, 238, 238); opacity: 1;"></div><div class="framer-js53b3-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-1k5sum" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Medium - Desktop - Closed" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Can I tutor my existing students on Popless?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div><div class="framer-5nhjsa" data-framer-name="Divider" style="background-color: rgb(238, 238, 238); opacity: 1;"></div><div class="framer-1itx7o6-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-1k5sum" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Medium - Desktop - Closed" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">How do I set when I’m available for tutoring?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div><div class="framer-1qab86k" data-framer-name="Divider" style="background-color: rgb(238, 238, 238); opacity: 1;"></div><div class="framer-1cn2t0z-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-v2yJr framer-v-1k5sum" tabindex="0" style="display: contents;"><div class="framer-1bbqg76" data-framer-name="Medium - Desktop - Closed" data-highlight="true" tabindex="0" style="width: 100%; opacity: 1;"><div class="framer-1owhqhr" data-framer-name="Title" data-highlight="true" tabindex="0" style="opacity: 1;"><div data-framer-component-type="Text" class="framer-1y30chy" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; --framer-text-alignment: start; --framer-text-color: rgb(0, 0, 0); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">How does Popless make money?</span><br></span></span></div><div class="framer-zuog2c" style="opacity: 1; transform: rotate(180deg) translateZ(0px);"><div class="framer-99gyaw" data-framer-name="Chevron" style="transform: rotate(180deg) translateZ(0px); opacity: 1;"><div data-framer-component-type="SVG" class="framer-14ycicj" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg2378593587"></use></svg></div></div></div></div></div></div></div></div></div></div></div></section><section class="framer-miwidy"><div class="framer-1sb57bb"><div class="framer-1klkasr"><div class="framer-1lh0t4x" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><p class="framer-text" style="--font-selector: SW50ZXItTWVkaXVt; --framer-font-family: &quot;Inter-Medium&quot;, &quot;Inter&quot;, sans-serif; --framer-font-size: 24px; --framer-font-weight: 500; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-letter-spacing: -1px;">We're here to help.</p></div><div class="framer-1stqs02" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><h2 class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 60px; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-text-alignment: center; --framer-text-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)); --framer-line-height: 66px;">Share your knowledge and teach on your terms.</h2></div></div><div class="framer-16x8qm8"><div class="framer-1gzzllz-container" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-cKThw framer-v-1nr57ge" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Button - White" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: rgb(255, 255, 255); height: 100%; opacity: 1; border-radius: 6px; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Get started</span><br></span></span></div></a></div></div><div class="framer-1ihkchc-container" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-generated="true" class="framer-cKThw framer-v-1apr27j" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Line - White" data-highlight="true" data-border="true" href="https://calendly.com/popless/intro" rel="noopener" tabindex="0" style="--border-bottom-width: 1px; --border-color: rgba(255, 255, 255, 1); --border-left-width: 1px; --border-right-width: 1px; --border-style: solid; --border-top-width: 1px; background-color: rgb(0, 0, 0); box-shadow: none; height: 100%; opacity: 1; border-radius: 6px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Request a demo</span><br></span></span></div></a></div></div></div></div><div class="framer-1lfv9sp"><div class="framer-cxd0xl" data-border="true" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div class="framer-1k7pbls"><div class="framer-gkma7r" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><h4 class="framer-text" style="--font-selector: R0Y7SW50ZXItNzAw; --framer-font-size: 45px; --framer-font-weight: 700; --framer-letter-spacing: -0.8px; --framer-line-height: 1em; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36));">Claim your username.</h4></div><div data-framer-component-type="Text" class="framer-174cmtn" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; line-height: 1px; font-size: 0px; transform: none;"><p style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Get started to claim your uniqe username for your tutoring profile.</span><br></span></p></div></div><div class="framer-2tucme"><div class="framer-1uc17lz"><div class="framer-6klexh"><div class="framer-1n12lt6-container"><video autoplay="" poster="https://misc.framerstatic.com/components/video-poster.jpg" playsinline="" style="width: 100%; height: 100%; border-radius: 8px; display: block; object-fit: contain; background-color: rgba(0, 0, 0, 0); object-position: 50% 50%;" src="blob:https://www.popless.com/a10bd2af-017b-4839-a3ff-664adee3396f"></video></div></div></div><div class="framer-195oywt-container"><div data-framer-generated="true" class="framer-cKThw framer-v-1erbgvf" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1e84z8s framer-4srpbw" data-framer-name="Solid - Round Bottom" data-highlight="true" href="https://www.popless.com/signup" rel="noopener" tabindex="0" style="--border-bottom-width: 0px; --border-color: rgba(0, 0, 0, 0); --border-left-width: 0px; --border-right-width: 0px; --border-style: solid; --border-top-width: 0px; background-color: var(--token-dc74b930-0ddb-4cd5-84c0-a49f71740644, rgb(234, 104, 115)); border-radius: 0px 0px 6px 6px; height: 100%; width: 100%; opacity: 1; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px;"><div data-framer-component-type="Text" data-framer-name="Button" class="framer-338jv4" style="outline: none; display: flex; flex-direction: column; justify-content: center; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 16px; --framer-text-alignment: center; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><span style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit"><span style="direction: ltr; font-size: 0"><span style="">Claim username</span><br></span></span></div></a></div></div></div></div><div class="framer-7wbxt7" data-border="true" style="opacity: 0; transform: perspective(0px) translateX(0px) translateY(20px) scale(1) rotate(0deg) rotateX(0deg) rotateY(0deg) translateZ(0px);"><div data-framer-background-image-wrapper="true" style="position: absolute; pointer-events: none; user-select: none; border-radius: inherit; inset: 0px;"><div style="display: contents; border-radius: inherit; pointer-events: none;"><img src="https://framerusercontent.com/images/XsKdVaUYaDb8GAIPJUPJ13jrF8.jpg" alt="" loading="lazy" style="pointer-events: none; user-select: none; display: block; width: 100%; height: 100%; border-radius: inherit; object-position: center center; object-fit: cover; image-rendering: auto;"></div></div><div class="framer-12exdlg"><div class="framer-1mhxnky" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><h3 class="framer-text framer-styles-preset-24blvi" data-styles-preset="EciIwMErV" style="--framer-text-alignment: left;">Sophia</h3></div><div class="framer-1pcjfdr" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 1.4em; --framer-text-alignment: left;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255));">Design Tutor | New York</span></p></div></div></div></div></section></main><div class="framer-1q47if1-container"><div data-framer-generated="true" class="framer-n5YHk framer-qPr25 framer-v-112s07e" tabindex="0" style="display: contents;"><div class="framer-112s07e" data-framer-name="Desktop" style="background-color: rgb(0, 0, 0); width: 100%; opacity: 1;"><div class="framer-16a15kt" style="opacity: 1;"><div class="framer-yhtyou" style="opacity: 1;"><div class="framer-1ybi6cv-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-su7oC framer-v-1doy51y" tabindex="0" style="display: contents; pointer-events: auto;"><a class="framer-1o77pht framer-1pi3ej6" data-framer-name="Logo Reversed" data-highlight="true" href="./" tabindex="0" style="height: 100%; width: 100%; opacity: 1;"><div data-framer-component-type="SVG" class="framer-1ip3699" style="image-rendering: pixelated; flex-shrink: 0; background-size: 100% 100%; background-image: url(&quot;data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20viewBox%3D%220%200%2032%2032%22%3E%3Cpath%20d%3D%22M%2012.9%2031%20L%205.7%2026.2%20C%205.44%2026.025%205.23%2025.785%205.09%2025.505%20C%204.95%2025.225%204.884%2024.913%204.9%2024.6%20L%204.9%201.9%20C%204.897%201.701%204.949%201.505%205.051%201.334%20C%205.152%201.163%205.299%201.023%205.475%200.93%20C%205.65%200.837%205.849%200.794%206.047%200.806%20C%206.246%200.819%206.437%200.886%206.6%201%20L%2013.8%205.8%20C%2014.06%205.975%2014.27%206.215%2014.41%206.495%20C%2014.55%206.775%2014.616%207.087%2014.6%207.4%20L%2014.6%2030.2%20C%2014.582%2030.389%2014.516%2030.57%2014.407%2030.726%20C%2014.298%2030.881%2014.151%2031.005%2013.979%2031.086%20C%2013.807%2031.167%2013.617%2031.201%2013.428%2031.186%20C%2013.239%2031.171%2013.057%2031.107%2012.9%2031%20Z%22%20fill%3D%22%23FFFFFF%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M%2025.5%2018.7%20L%2018.3%2013.9%20C%2018.04%2013.725%2017.83%2013.485%2017.69%2013.205%20C%2017.55%2012.925%2017.484%2012.613%2017.5%2012.3%20L%2017.5%201.9%20C%2017.497%201.701%2017.549%201.505%2017.651%201.334%20C%2017.752%201.163%2017.899%201.023%2018.075%200.93%20C%2018.25%200.837%2018.448%200.794%2018.647%200.806%20C%2018.846%200.819%2019.037%200.886%2019.2%201%20L%2026.4%205.8%20C%2026.66%205.975%2026.87%206.215%2027.01%206.495%20C%2027.15%206.775%2027.216%207.087%2027.2%207.4%20L%2027.2%2017.8%20C%2027.193%2017.995%2027.135%2018.184%2027.031%2018.35%20C%2026.928%2018.515%2026.782%2018.65%2026.61%2018.741%20C%2026.438%2018.832%2026.244%2018.877%2026.049%2018.869%20C%2025.855%2018.862%2025.665%2018.804%2025.5%2018.7%20Z%22%20fill%3D%22%23FFFFFF%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E&quot;); opacity: 1;"></div></a></div></div><div class="framer-1s9n895" style="opacity: 1;"><div class="framer-y0amv1" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-link-hover-text-color: rgba(255, 255, 255, 0.8); --framer-link-text-decoration: none; --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 14px; --framer-font-weight: 500; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);">Support</p></div><div class="framer-14yi8l" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./faq">FAQs</a></p></div><div class="framer-1fp5w0o" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./accessibility">Accessibility</a></p></div><div class="framer-agco53" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./support">Support center</a></p></div><div class="framer-quedik" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./discord">Community</a></p></div><div class="framer-1y8zfja-container" style="opacity: 1;"><div data-framer-generated="true" class="framer-1BGCh framer-v-y5w3le" tabindex="0" style="display: contents; pointer-events: auto;"><div class="framer-y5w3le" data-framer-name="Ask a Question - Footer" style="opacity: 1;"><div data-framer-component-type="DeprecatedRichText" class="framer-139vnnw rich-text-wrapper" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-1w3ko1f: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: translate(-50%, -50%); opacity: 1;"><p style="--framer-font-size:14px; --framer-line-height:22px;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:400; --font-selector:R0Y7SW50ZXItcmVndWxhcg==; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:14px;">Ask a question</span></p></div></div></div></div></div><div class="framer-1dstxc4" style="opacity: 1;"><div class="framer-1mnx26r" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-link-hover-text-color: rgba(255, 255, 255, 0.8); --framer-link-text-decoration: none; --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 14px; --framer-font-weight: 500; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);">Tutoring</p></div><div class="framer-16djjs3" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./tutoring">Become a tutor</a></p></div><div class="framer-ucccz3" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./group-classes">Teach a class</a></p></div><div class="framer-1z11s15" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./marketplace">Find new students</a></p></div><div class="framer-17me7hs" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./refer">Refer program</a></p></div><div class="framer-1mct1k9" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./pricing">Pricing</a></p></div></div><div class="framer-yo16e2" style="opacity: 1;"><div class="framer-k4n9x6" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-link-hover-text-color: rgba(255, 255, 255, 0.8); --framer-link-text-decoration: none; --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 14px; --framer-font-weight: 500; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);">Popless</p></div><div class="framer-hv9ytq" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./" data-framer-page-link-current="true">Tutor platform</a></p></div><div class="framer-1a6zdoi" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./updates">Feature releases</a></p></div><div class="framer-1bqxovp" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./marketplace">How it works</a></p></div><div class="framer-1b9a349" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="https://calendly.com/popless/intro" rel="noopener">Request a demo</a></p></div></div><div class="framer-15qsfkm" style="opacity: 1;"><div class="framer-1mr3ez9" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-link-hover-text-color: rgba(255, 255, 255, 0.8); --framer-link-text-decoration: none; --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItNTAw; --framer-font-size: 14px; --framer-font-weight: 500; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);">About</p></div><div class="framer-19lnh9a" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./blog">Blog</a></p></div><div class="framer-2ic09p" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./mission">Mission</a></p></div><div class="framer-hh4uwx" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-r6o4lv: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-size: 14px; --framer-line-height: 22px; --framer-text-color: var(--extracted-r6o4lv);"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./careers">Careers</a></p></div><div class="framer-53oyn0" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-hl0iuy: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 22px;"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./contact"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--extracted-hl0iuy);">Contact</span></a></p></div></div></div></div><div class="framer-1uw5sv0" style="opacity: 1;"><div class="framer-1faobnr" style="background-color: rgb(116, 116, 116); border-radius: 100px; opacity: 1;"></div><div class="framer-1bvflhh" style="opacity: 1;"><div class="framer-1mdh93q" style="opacity: 1;"><div class="framer-hw0kuw" style="opacity: 1;"><div class="framer-4uykzm" style="opacity: 1;"><div class="framer-4g2085" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-1w3ko1f: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 22px;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--extracted-1w3ko1f);">Copyright © 2022 Popless</span></p></div><div class="framer-1oiqhik" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-1w3ko1f: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 22px;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--extracted-1w3ko1f);">·</span></p></div><div data-framer-component-type="Text" class="framer-701nfs" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-link-hover-text-color: rgba(255, 255, 255, 0.8); --framer-link-text-decoration: none; --framer-text-alignment: start; --framer-text-color: rgb(255, 255, 255); --framer-text-decoration: none; --framer-text-transform: none; line-height: 1px; font-size: 0px; transform: none; opacity: 1;"><a style="font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit" href="./legal/privacy" data-framer-page-link-target="caI63G_Mf" data-framer-page-link-path-variables="L4KcAaijt=privacy"><span><span style="direction: ltr; font-size: 0"><span style="">Privacy</span><br></span></span></a></div><div class="framer-19yn1j8" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-1w3ko1f: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 22px;"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--extracted-1w3ko1f);">·</span></p></div><div class="framer-1i8wo2r" data-framer-component-type="RichTextContainer" style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --extracted-hl0iuy: rgb(255, 255, 255); --framer-paragraph-spacing: 0px; transform: none; opacity: 1;"><p class="framer-text" style="--framer-font-size: 14px; --framer-line-height: 22px;"><a class="framer-text framer-styles-preset-1vs0812" data-styles-preset="GUvozIcPY" href="./legal/terms"><span class="framer-text" style="--font-selector: R0Y7SW50ZXItcmVndWxhcg==; --framer-font-family: &quot;Inter&quot;, sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--extracted-hl0iuy);">Terms</span></a></p></div></div><div class="framer-1wqxfsb-container" data-framer-name="Socials" name="Socials" style="opacity: 1;"><div data-framer-generated="true" class="framer-jdoxF framer-v-aafqwg" tabindex="0" style="display: contents; pointer-events: auto;"><a name="Socials" class="framer-3s2vxh framer-1gpju0" data-framer-name="Twitter White" href="https://twitter.com/popless_hq" target="_blank" rel="noopener" style="opacity: 1;"><div data-framer-component-type="SVG" class="framer-1kwu6ex" style="image-rendering: pixelated; flex-shrink: 0; opacity: 1;"><div class="svgContainer" style="width: 100%; height: 100%; aspect-ratio: inherit;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"><use href="#svg739324438"></use></svg></div></div></a></div></div></div></div></div></div></div></div></div></div><div id="overlay"></div></div></div>
    

    <script data-framer-appear-animation="no-preference"></script>
    <script type="module" data-framer-bundle="" src="https://framerusercontent.com/sites/2jWTrYXqW5Tn0I8PyCeMwk/_script0.OEVLCHSR.mjs"></script>
    
    <!-- End of bodyEnd --><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe>


	<script type="text/javascript" id="" charset="">!function(d,g,e){d.TiktokAnalyticsObject=e;var a=d[e]=d[e]||[];a.methods="page track identify instances debug on off once ready alias group enableCookie disableCookie".split(" ");a.setAndDefer=function(b,c){b[c]=function(){b.push([c].concat(Array.prototype.slice.call(arguments,0)))}};for(d=0;d<a.methods.length;d++)a.setAndDefer(a,a.methods[d]);a.instance=function(b){b=a._i[b]||[];for(var c=0;c<a.methods.length;c++)a.setAndDefer(b,a.methods[c]);return b};a.load=function(b,c){var f="https://analytics.tiktok.com/i18n/pixel/events.js";
a._i=a._i||{};a._i[b]=[];a._i[b]._u=f;a._t=a._t||{};a._t[b]=+new Date;a._o=a._o||{};a._o[b]=c||{};c=document.createElement("script");c.type="text/javascript";c.async=!0;c.src=f+"?sdkid\x3d"+b+"\x26lib\x3d"+e;b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(c,b)};a.load("CEGINNRC77UFTJ8GGB9G");a.page()}(window,document,"ttq");</script>
	<div id="svg-templates" style="position: absolute; top: 0px; left: 0px; width: 0px; height: 0px; overflow: hidden;"><div id="container_svg-1948094006_1423"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-1948094006_1423">
<path d="M7.45957 16.2925L4.00357 14.0065C3.87943 13.9234 3.77776 13.8109 3.70762 13.679C3.63749 13.5471 3.60105 13.3999 3.60157 13.2505V2.31854C3.60196 2.22515 3.62769 2.13361 3.67601 2.05369C3.72434 1.97377 3.79345 1.90846 3.87598 1.86474C3.9585 1.82101 4.05135 1.8005 4.14462 1.8054C4.23788 1.81029 4.32807 1.84041 4.40557 1.89254L7.86157 4.18454C7.98644 4.26734 8.089 4.37964 8.16016 4.51149C8.23132 4.64335 8.2689 4.79071 8.26957 4.94054V15.8665C8.2679 15.9601 8.24113 16.0514 8.19206 16.1311C8.14299 16.2107 8.07342 16.2757 7.99063 16.3192C7.90784 16.3628 7.81487 16.3833 7.72144 16.3786C7.62802 16.3739 7.53757 16.3442 7.45957 16.2925Z" fill="white"></path>
<path d="M13.5242 10.3754L10.0682 8.08338C9.94375 8.00153 9.84144 7.89027 9.77027 7.75945C9.69911 7.62864 9.66129 7.4823 9.66016 7.33338V2.31738C9.66182 2.22385 9.68859 2.13249 9.73766 2.05285C9.78674 1.97322 9.8563 1.90823 9.93909 1.86469C10.0219 1.82114 10.1149 1.80065 10.2083 1.80535C10.3017 1.81004 10.3921 1.83976 10.4702 1.89138L13.9262 4.18338C14.0503 4.26648 14.152 4.37899 14.2221 4.5109C14.2922 4.6428 14.3287 4.78999 14.3282 4.93938V9.94938C14.3278 10.0428 14.302 10.1343 14.2537 10.2142C14.2054 10.2942 14.1363 10.3595 14.0537 10.4032C13.9712 10.4469 13.8784 10.4674 13.7851 10.4625C13.6918 10.4576 13.6017 10.4275 13.5242 10.3754Z" fill="white"></path>
</svg></div><div id="container_svg482518259_258"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg482518259_258"><rect x="3" y="9" width="30" height="18" rx="2" stroke="#202124" stroke-width="2"></rect><path d="M20 22h-3m11 0h-3M3 14h30" stroke="#202124" stroke-width="2" stroke-linecap="round"></path></svg></div><div id="container_svg1776301866_407"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1776301866_407"><path d="M5 29.25A3.75 3.75 0 0 0 8.75 33h22.5M5 29.25a3.75 3.75 0 0 1 3.75-3.75h20.5a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v24.25Z" stroke="#202124" stroke-width="2" stroke-linecap="round"></path><path d="M31.25 33H8.75a3.75 3.75 0 0 1 0-7.5h9.375M12.5 8.625h11.25" stroke="#202124" stroke-width="2" stroke-linecap="round"></path></svg></div><div id="container_svg-187085573_399"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-187085573_399"><rect x="3" y="13.333" width="30" height="16.667" rx="1" stroke="#202124" stroke-width="2"></rect><circle cx="18.001" cy="21.667" r="2.667" stroke="#202124" stroke-width="2"></circle><path d="M4.666 13.333 26.167 5.37a1 1 0 0 1 1.305.65l2.194 7.313" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="bevel"></path></svg></div><div id="container_svg138610326_1740"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg138610326_1740"><circle cx="18" cy="18" r="15" stroke="#202124" stroke-width="2"></circle><path d="M3 18.002h30" stroke="#202124" stroke-width="2"></path><path d="M17.998 3c.482.876.483.876.483.875h.002l.002-.001.003-.002a5.37 5.37 0 0 0-.666.521c-.446.409-1.066 1.086-1.696 2.125C14.87 8.591 13.543 12.157 13.543 18h-2c0-6.157 1.402-10.091 2.873-12.518.733-1.21 1.477-2.034 2.054-2.563.288-.264.535-.455.716-.583a4.62 4.62 0 0 1 .323-.208l.004-.002.002-.001s.001-.001.483.875Zm-4.455 15c0 5.843 1.327 9.409 2.583 11.482.63 1.04 1.25 1.716 1.695 2.125a5.276 5.276 0 0 0 .645.508l.022.013c-.001 0-.002 0-.003-.002h-.002l-.002-.002-.483.876c-.482.876-.483.876-.483.875h-.002a.825.825 0 0 0-.004-.002l-.008-.005a1.712 1.712 0 0 1-.093-.056 4.563 4.563 0 0 1-.222-.148 7.35 7.35 0 0 1-.716-.583c-.577-.53-1.32-1.352-2.054-2.563-1.472-2.427-2.873-6.361-2.873-12.518h2Z" fill="#202124"></path><path d="M17.998 3c-.482.876-.483.876-.483.875h-.002l-.002-.001-.002-.002c-.002 0-.002 0 0 0l.021.013a5.276 5.276 0 0 1 .645.508c.445.409 1.065 1.086 1.695 2.125 1.256 2.073 2.583 5.639 2.583 11.482h2c0-6.157-1.401-10.091-2.872-12.518-.734-1.21-1.477-2.034-2.055-2.563a7.353 7.353 0 0 0-.716-.583 4.62 4.62 0 0 0-.291-.19l-.023-.014a.776.776 0 0 0-.01-.004l-.003-.002-.002-.001s-.001-.001-.483.875Zm4.455 15c0 5.843-1.327 9.409-2.583 11.482-.63 1.04-1.25 1.716-1.695 2.125a5.373 5.373 0 0 1-.52.424c-.058.041-.1.069-.125.084l-.022.013c.001 0 .002 0 .003-.002h.003v-.002c.001 0 .002 0 .484.876s.483.876.483.875h.002a.825.825 0 0 1 .004-.002 1.769 1.769 0 0 0 .102-.06c.055-.036.13-.085.22-.149.182-.128.429-.319.717-.583.578-.53 1.32-1.352 2.055-2.563 1.47-2.427 2.872-6.361 2.872-12.518h-2Z" fill="#202124"></path></svg></div><div id="container_svg-410979178_970"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-410979178_970"><path d="M33 27.778v-5.673c0-.648-.368-1.297-.987-1.503a3.134 3.134 0 0 1 0-5.953c.619-.206.987-.84.987-1.503V7.473C33 6.663 32.337 6 31.526 6H4.473C3.663 6 3 6.663 3 7.473v5.673c0 .649.368 1.297.987 1.503a3.134 3.134 0 0 1 0 5.953c-.619.206-.987.84-.987 1.503v5.673c0 .81.663 1.474 1.473 1.474h27.053A1.48 1.48 0 0 0 33 27.778Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="m18.782 11.82.972 2.991a.81.81 0 0 0 .78.56h3.14c.795 0 1.12 1.017.486 1.474l-2.55 1.842a.811.811 0 0 0-.294.913l.972 2.991c.25.752-.619 1.385-1.252.914l-2.55-1.842a.819.819 0 0 0-.957 0l-2.55 1.842c-.633.471-1.502-.162-1.252-.914l.973-2.99c.103-.34 0-.708-.295-.914l-2.549-1.842c-.633-.472-.31-1.474.486-1.474h3.139a.823.823 0 0 0 .78-.56l.973-2.99c.25-.752 1.312-.752 1.562 0h-.014Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg1576715579_278"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1576715579_278"><rect x="3" y="7.5" width="30" height="21" rx="1" stroke="#202124" stroke-width="2"></rect><path d="M4.5 9 18 18l13.5-9" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg1591810958_423"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1591810958_423"><path d="M6.2 8.857h15.886c1.767 0 3.2 1.206 3.2 2.694v3.906L33 10.43v15.714l-7.714-5.029v3.906c0 1.488-1.433 2.694-3.2 2.694H6.2c-1.767 0-3.2-1.206-3.2-2.694v-13.47c0-1.488 1.433-2.694 3.2-2.694Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11.143 8v18.857" stroke="#202124" stroke-width="2"></path></svg></div><div id="container_svg1688372684_414"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1688372684_414"><path d="M16.97 5.226a5 5 0 0 1 3.375-1.462l8.056-.26A3 3 0 0 1 31.496 6.6l-.26 8.056a5 5 0 0 1-1.462 3.374L16.37 31.435a2 2 0 0 1-2.829 0L3.565 21.46a2 2 0 0 1 0-2.829L16.971 5.226Z" stroke="#202124" stroke-width="2"></path><circle cx="24.344" cy="10.656" r="2.018" transform="rotate(45 24.344 10.656)" stroke="#202124" stroke-width="2"></circle></svg></div><div id="container_svg529086851_361"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg529086851_361"><path d="M3 7a1 1 0 0 1 1-1h28a1 1 0 0 1 1 1v25a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7Z" stroke="#202124" stroke-width="2"></path><path d="M11 2v3.048M25 2v3.048M3 12.662h30m-23 7.626h4m8 0h4m-16 6.089h4m8 0h4" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg-129555426_539"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-129555426_539"><path d="M31.526 8.669H4.473C3.66 8.669 3 9.329 3 10.142v18.89c0 .814.66 1.474 1.473 1.474h27.053c.814 0 1.474-.66 1.474-1.474v-18.89c0-.813-.66-1.473-1.474-1.473Zm-17.254 9.744H8.039m3.109 5.003h-3.11" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="m18.78 19.605 3.182 3.404 5.437-6.808M10.5 6v5.334M26.654 6v5.334M18.574 6v5.334" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg-399729834_291"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-399729834_291"><path d="M3 5v23.531c0 .87.707 1.592 1.591 1.592H33M8.244 24.877v-6.07m6.293 6.071V12.47m6.307 12.407v-9.239m6.291 9.239V9.287" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg1055447456_467"><svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1055447456_467"><path d="m14.002 18.71 8.572 8.571m-.002-8.571-8.571 8.571" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3 7a1 1 0 0 1 1-1h28a1 1 0 0 1 1 1v25a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7Z" stroke="#202124" stroke-width="2"></path><path d="M11 2v3.048M25 2v3.048M3 12.662h30" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg-255222499_1857"><svg width="300" height="101" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-255222499_1857"><path d="M74.58 35.97H39.63v10.29H64.5c-1.2 14.4-13.2 20.55-24.51 20.55-14.49 0-27.12-11.4-27.12-27.36 0-15.57 12.03-27.54 27.15-27.54 11.64 0 18.51 7.44 18.51 7.44l7.2-7.44S56.49 1.62 39.66 1.62C18.18 1.59 1.59 19.71 1.59 39.24c0 19.17 15.63 37.86 38.61 37.86 20.22 0 35.01-13.86 35.01-34.32 0-4.32-.63-6.81-.63-6.81Z" fill="#4285F4"></path><path d="M104.13 28.53c-14.22 0-24.39 11.1-24.39 24.06 0 13.14 9.87 24.48 24.57 24.48 13.29 0 24.18-10.17 24.18-24.18 0-16.11-12.66-24.36-24.36-24.36Zm.15 9.51c6.99 0 13.62 5.64 13.62 14.76 0 8.91-6.6 14.73-13.65 14.73-7.74 0-13.86-6.21-13.86-14.79 0-8.4 6.03-14.7 13.89-14.7Z" fill="#EA4335"></path><path d="M156.9 28.53c-14.22 0-24.39 11.1-24.39 24.06 0 13.14 9.87 24.48 24.57 24.48 13.29 0 24.18-10.17 24.18-24.18 0-16.11-12.69-24.36-24.36-24.36Zm.15 9.51c6.99 0 13.62 5.64 13.62 14.76 0 8.91-6.6 14.73-13.65 14.73-7.74 0-13.86-6.21-13.86-14.79 0-8.4 6.03-14.7 13.89-14.7Z" fill="#FBBC05"></path><path d="M208.95 28.53c-13.05 0-23.31 11.43-23.31 24.24 0 14.61 11.88 24.3 23.07 24.3 6.93 0 10.59-2.76 13.32-5.91v4.8c0 8.37-5.07 13.38-12.75 13.38-7.41 0-11.13-5.52-12.42-8.64l-9.33 3.9c3.3 6.99 9.96 14.28 21.84 14.28 12.96 0 22.86-8.16 22.86-25.29V29.4h-10.17v4.71c-3.18-3.36-7.44-5.58-13.11-5.58Zm.93 9.54c6.39 0 12.96 5.46 12.96 14.79 0 9.48-6.54 14.7-13.11 14.7-6.96 0-13.41-5.64-13.41-14.61 0-9.33 6.72-14.88 13.56-14.88Z" fill="#4285F4"></path><path d="M276.54 28.47c-12.3 0-22.62 9.78-22.62 24.24 0 15.27 11.52 24.36 23.82 24.36 10.26 0 16.56-5.61 20.34-10.65l-8.4-5.58c-2.19 3.39-5.82 6.69-11.88 6.69-6.81 0-9.96-3.75-11.91-7.35l32.55-13.5-1.68-3.96c-3.18-7.77-10.53-14.22-20.22-14.25Zm.42 9.36c4.44 0 7.62 2.37 8.97 5.19l-21.72 9.09c-.93-7.05 5.73-14.28 12.75-14.28Z" fill="#EA4335"></path><path d="M248.94 4.11h-9.96v71.52h9.96V4.11Z" fill="#34A853"></path></svg></div><div id="container_svg1897297650_22348"><svg version="1.1" id="svg1897297650_22348" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 841.9 220.2" style="enable-background:new 0 0 841.9 220.2" xml:space="preserve"><style>.st3{fill:#fff}.st4,.st5{fill:none;stroke:#000;stroke-width:1.000000e-02}.st5{stroke-width:1.200000e-02}</style><path d="M344.4 149.2c0-1.7-1.1-3-3.4-3.4v-1.1c1.3 0 5.4.2 6.6.2 1 0 5.1-.2 6.3-.2v1.1c-2.1.4-3.2 1.7-3.2 3.4v21.6c0 6.8 3.8 10.7 11.6 10.7 6.8 0 12-3 12-10.2v-22.2c0-1.6-1-2.9-3.5-3.4v-1.1c.9 0 4.4.2 5.6.2 1 0 4.6-.2 5.6-.2v1.1c-2.4.6-3.5 1.7-3.5 3.4v21c0 9.4-5.5 14.8-17.3 14.8-11 0-16.7-5-16.7-13.9v-21.8h-.1zm47.2 34c2.6-.5 3.6-1.8 3.6-3.4V150c0-2.5-1.3-3.7-3.6-4.3v-1.1c1.3.1 3.5.2 4.6.2 1 0 2.6 0 3.6-.2l25.9 30.4v-26c0-1.6-1-2.9-3.5-3.4v-1.1c.9 0 4.3.2 5.4.2 1 0 4.6-.2 5.5-.2v1.1c-2.5.6-3.7 1.7-3.7 3.4v35.8h-1.6c-1-.2-2.8-1.3-5.7-4.4l-23.1-27v26.3c0 1.6.9 2.9 3.4 3.4v1.1c-.9 0-4.3-.2-5.4-.2-1 0-4.5.2-5.5.2v-1h.1zm204.2-8.1h1.1c.6 2.2 1.9 4.3 3.6 5.4 1.9 1.3 4.8 1.9 7.1 1.9 5.2 0 8.5-2.6 8.5-6.7 0-4.2-4.2-6.4-9.3-8.9-3.8-1.9-11.2-5-11.2-12.1 0-6.7 5.1-10.8 13.2-10.8 5.4 0 7.9 1.3 11.5 1.1l-.1 8.1h-1.1c-1.4-4.8-5.8-6.4-10.5-6.4-5.3 0-7.3 3-7.3 6.1 0 4.4 4.3 6.6 9.2 8.8 5.5 2.5 11.5 5.5 11.5 12.4 0 6.5-5.1 11.2-14.4 11.2-5.5 0-8.9-1.6-11.8-1.9v-8.2zm117.2-8.9v13.5c0 1.3 1 3 3.3 3.4v1.1c-1.3 0-5.5-.2-6.7-.2-1 0-5 .2-6.5.2v-1.1c2.3-.4 3.4-2.1 3.4-3.4v-12.9m.4 1.1L696.4 151c-1.8-3-3.2-4.5-5.2-5.3v-1.1c1.4 0 5.3.2 6.5.2 1 0 5.2-.2 6.7-.2v1.1c-1.3.2-2 .9-2 1.9 0 .9.6 2 1.2 3.1.6 1 7.8 12.9 7.8 12.9l7.5-12.9c.8-1.3 1.3-2.6 1.3-3.4 0-.8-.5-1.4-1.8-1.6v-1.1c1.3 0 3.9.2 4.8.2s3.9-.2 5.2-.2v1c-2.9 1.2-4.7 3.7-6.4 6.6l-9.6 16m-201.2-23.6h24.7c.8 0 2.1-.1 2.9-.4h1.1l-.2 8h-1.1c-.9-2.5-1.9-4.5-6.7-4.5h-10.7v14h8.3c1.2 0 2.7-.1 3.3-.3 1.9-.4 2.7-1.4 3.1-3h1.1c-.1 1.2-.1 4-.1 4.8s.1 3.7.1 5h-1.1c-.5-1.6-1.3-2.8-3.1-3.2-.6-.2-2.1-.3-3.3-.3h-8.3v16.4h11.7c5.1 0 6.6-2.1 7.8-5.1l1.1.3-1.1 8.2c-3.8-.3-6.1-.3-10.4-.3H518c-1.2 0-5.5.2-6.8.2v-1.1c2.2-.4 3.5-1.5 3.5-3.7v-30.3c0-1.9-1-3.1-3.5-3.5v-1.2zm-63.1 4.8c0-2-1.2-3.2-3.6-3.7v-1.1c1.3 0 5.6.2 6.9.2 1 0 5.5-.2 6.8-.2v1.1c-2.4.5-3.6 1.5-3.6 3.7v30.1c0 2 1.2 3.1 3.6 3.6v1.1c-1.3 0-5.6-.2-6.8-.2-1.1 0-5.5.2-6.9.2v-1.1c2.4-.5 3.6-1.5 3.6-3.6v-30.1zm187.4 0c0-2-1.2-3.2-3.6-3.7v-1.1c1.3 0 5.6.2 6.9.2 1.1 0 5.5-.2 6.8-.2v1.1c-2.4.5-3.6 1.5-3.6 3.7v30.1c0 2 1.2 3.1 3.6 3.6v1.1c-1.3 0-5.5-.2-6.8-.2-1 0-5.5.2-6.9.2v-1.1c2.4-.5 3.6-1.5 3.6-3.6v-30.1zm34.3-4.6c-13.5 0-15.7-.3-18.5-.4v8.2h1.1c1-3.6 3-4.5 5.6-4.5h8.5v31.5c0 2.1-1.2 3.2-3.6 3.6v1.1c1.3 0 5.8-.2 6.9-.2 1.2 0 5.5.2 6.8.2v-1.1c-2.4-.5-3.6-1.6-3.6-3.6v-31.5h8.4c2.7 0 4.6.9 5.7 4.5h1.1v-8.2c-2.6.1-4.8.4-18.4.4m-108 18.6c1.3.2 3.1.3 5 .3 7.4 0 9.8-4.3 9.8-8.1 0-5.4-3.6-8.7-10.1-8.7-1.3 0-3.3.2-4.7.4v16.1zm0 16.1c0 2 1.3 3.1 3.7 3.6v1.1c-1.3 0-5.7-.2-6.9-.2-1.1 0-5.3.2-6.6.2v-1.1c2.2-.4 3.5-1.5 3.5-3.6v-30.4c0-1.9-1-3.1-3.5-3.5v-1.1c1.3 0 5.5.2 6.7.2 1.9 0 7.4-.3 9.6-.3 9.5 0 14.8 3.9 14.8 10.7 0 5.3-3.9 8.9-10 10.3 0 0 1.4.8 2.8 2.4 1.7 2 6.2 8.5 8.1 10.7 1.9 2.3 4.3 4.8 6.8 4.9v.9c-1.2.3-3.2.5-4.7.5-3.2 0-5.8-1.1-8.3-4.2-2.2-2.6-5.8-8.1-8-10.9-1.4-1.8-3.1-3.1-6.1-3.6l-1.8-.3-.1 13.7zm-94.1-30c-.9-1.9-2.2-3.3-4.5-3.8v-1.1c1.4 0 6.3.2 7.4.2 1 0 5.2-.2 6.7-.2v1.1c-1.3.2-2.8 1-2.8 2.3 0 .9.6 2.2 1.7 4.6l10.2 23.3 9.7-23.3c.8-1.9 1.3-3.3 1.3-4.5s-1.1-2.1-2.7-2.4v-1.1c1.3 0 4.4.2 5.4.2.9 0 4.6-.2 6-.2v1.1c-2.4.7-3.9 2-5.2 5.1l-14.2 33.6h-3.2l-15.8-34.9zm36.5-105.9c-1.6-3.6-4.2-6.3-8.5-7.2v-2.1c2.7 0 11.5.3 13.5.3 1.9 0 10.4-.3 13.2-.3v2.1c-2.4.5-5.2 1.8-5.2 4.3 0 1.8 1.1 4.2 3.1 8.8l19.4 44.1 18.4-44.2c1.4-3.6 2.5-6.3 2.5-8.4 0-2.3-2.2-3.9-5.2-4.6v-2.1c2.4 0 8.3.3 10.2.3 1.7 0 8.7-.3 11.4-.3v2c-4.6 1.2-7.4 3.9-9.9 9.8l-27 63.7H534l-29.8-66.2zm-261.1 63.7c3.5-.8 6-2.4 6-6.4V42.6c0-3.8-2.3-5.4-6-6.4v-2.1c1.8 0 9.8.3 11.8.3 2.2 0 10.6-.3 12.4-.3v2.1c-3.5.8-6.1 2.4-6.1 6.4V67h43.5V42.6c0-4-2.3-5.6-6-6.4v-2.1c1.8 0 10 .3 12.3.3 1.9 0 10.1-.3 11.9-.3v2.1c-3.5.8-6.1 2.4-6.1 6.4V101c0 3.8 2.5 5.4 6.1 6.4v1.9c-1.8 0-10-.2-11.9-.2-2.2 0-10.4.2-12.3.2v-1.9c3.5-.8 6-2.4 6-6.4V73.4h-43.5V101c0 4 2.4 5.4 6.1 6.4v1.9c-1.8 0-10.2-.2-12.4-.2-1.9 0-9.9.2-11.8.2v-2zm521.9-3.8c2.7.5 7.8.8 11.7.8 22.5 0 34.2-13.5 34.2-32.4 0-19.6-12.3-33.3-33.3-33.3-4.3 0-9.7.3-12.5.8v64.1h-.1zm-19 3.8c4.5-.8 7-2.6 7-6.7v-58c0-4-2.5-5.6-6.8-6.4v-2.1c2.5 0 10.7.3 13 .3 3.7 0 13.9-.8 21.8-.8 26.7 0 43 14.9 43 37.6 0 22.1-16.9 38.4-44.5 38.4-8 0-17.4-.6-21.1-.6-2.2 0-9.9.3-12.4.3v-2zM443.2 69.8c2.4.3 5.9.5 9.4.5 14.1 0 18.5-8.1 18.5-15.2 0-10.2-6.8-16.4-19.1-16.4-2.5 0-6.2.3-8.8.8v30.3zm0 30.6c0 3.9 2.5 6 7 6.9v2.1c-2.5 0-10.7-.3-13-.3-2 0-10 .3-12.5.3v-2.1c4.2-.8 6.7-2.9 6.7-6.9V42.8c0-3.5-1.9-5.8-6.6-6.6v-2.1c2.5 0 10.4.3 12.7.3 3.7 0 14.1-.6 18.2-.6 18 0 28 7.4 28 20.2 0 10-7.5 16.8-18.9 19.6 0 0 2.6 1.5 5.2 4.6 3.2 3.7 11.9 16.3 15.5 20.6 3.5 4.3 8.1 9.1 12.9 9.3v1.7c-2.3.5-6.1.9-8.9.9-6.1 0-11-2.1-15.7-7.9-4.1-5-11.2-15.6-15.3-20.8-2.6-3.3-5.8-5.9-11.6-6.9-1.1-.2-2.2-.3-3.5-.5v25.8h-.2zm237.1-30.6c2.4.3 5.9.5 9.4.5 14.1 0 18.5-8.1 18.5-15.2 0-10.2-6.8-16.4-19.1-16.4-2.6 0-6.2.3-8.8.8v30.3zm0 30.6c0 3.9 2.5 6 7 6.9v2.1c-2.5 0-10.7-.3-13-.3-2 0-10 .3-12.5.3v-2.1c4.2-.8 6.6-2.9 6.6-6.9V42.8c0-3.5-1.9-5.8-6.6-6.6v-2.1c2.5 0 10.4.3 12.7.3 3.7 0 14.1-.6 18.2-.6 18 0 28 7.4 28 20.2 0 10-7.5 16.8-18.9 19.6 0 0 2.6 1.5 5.2 4.6 3.2 3.7 12 16.3 15.5 20.6 3.6 4.3 8.1 9.1 12.9 9.3v1.7c-2.3.5-6 .9-8.9.9-6.1 0-11-2.1-15.8-7.9-4.1-5-11.2-15.6-15.3-20.8-2.6-3.3-5.8-5.9-11.5-6.9-1.2-.2-2.2-.3-3.5-.5v25.8h-.1zM382.1 79.2l-12-27.9L359 79.2h23.1zm-47.9 28.1c4.8-1.1 7.4-4.2 9.1-8.3l24.1-57.9c1-2.4 2.2-7.3 2.2-7.3h6.7s1.2 4.9 2.2 7.3L404 99.2c1.8 4 3.8 6.9 8.8 8.2v1.9c-2.8 0-11.3-.2-13.3-.2-1.9 0-10.1.2-12.9.2v-1.9c3.7-.9 4.9-2.8 4.9-4.6 0-1.5-.6-3.3-1.4-5.1l-5.5-12.5h-28l-5 12.5c-.6 1.4-1.2 3.4-1.2 4.8 0 2.1 1.1 3.8 5.3 4.9v1.9c-2.5 0-9.1-.2-10.6-.2l-10.9.2v-2zm286.5-28.1-12-27.9-11.1 27.9h23.1zm-47.9 28.1c4.8-1.1 7.4-4.2 9.1-8.3L606 41.1c1-2.4 2.2-7.3 2.2-7.3h6.7s1.2 4.9 2.2 7.3l25.5 58.1c1.8 4 3.8 6.9 8.8 8.2v1.9c-2.7 0-11.3-.2-13.3-.2-1.9 0-10.1.2-12.9.2v-1.9c3.7-.9 4.9-2.8 4.9-4.6 0-1.5-.6-3.3-1.4-5.1l-5.5-12.5h-28l-5 12.5c-.6 1.4-1.2 3.4-1.2 4.8 0 2.1 1.1 3.8 5.3 4.9v1.9c-2.5 0-9.1-.2-10.6-.2l-10.9.2v-2z" style="fill:#1d1d1b"></path><path d="M20.7 10.7h167.9v63.4c0 29.1-10.8 90.7-83.3 136.5 0 0-78.6-32.8-84.6-136.5V10.7z" style="fill:#a62131"></path><path d="M20.4 10.5h168.1V74c0 29.1-10.8 90.7-83.4 136.6 0 0-78.6-32.8-84.7-136.6V10.5z" style="fill:none;stroke:#000;stroke-width:.23"></path><path class="st3" d="M147.8 92.2s-1.5 4.8-8.2-.1l-22.9-.6s-3 1-3.4-2.2c0 0 .5-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.5-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19 0 0 .5-1.6-2.6-1.9 0 0-4.5.9-5-2 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.3-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.3c0 0 4.2.3 4.8-.1 0 0 .9-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.6 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.5 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1.2-.3-.7-.4-1 .1m-43.9 22.7s1-.4 1.2-1.1c0 0 2.9-.3 3.3-.7l9 .7s8.8.7 12.6-2.1c0 0 1.4 0 2.3.4 0 0 1.9.4 2.4 1.2l5.2-.1s3 1 2.7 2.9v2.4s-.5 4.9.8 5.2h4s3.3 1.5 2.9 3.4c0 0 .1 2.7-2.7 3.3 0 0-2.9.8-3.8.7 0 0-1 1.2-1.1 2.7l-.4 17.8s-.3 1.9 2.6 1.6c0 0 4.2-.4 5.4 1.9 0 0 1.1 3.9-1.1 4.6l-1.6.8-3.5.6s-1.5.1-1.6 2.7v4.8s.1 3.3-2.5 3.3l-3.8.3s-.9.8-2.9.3c0 0-23.1.4-23.9.1l-2.7.6s-3 2.7-5.4.7l-1.9-1.5s-1.4.3-1.9.1c-.5-.1-25.6.3-25.6.3s-5.3.6-5.7-1.2c0 0-.7-6.9-.4-7.8 0 0 0-2-1.2-2.2 0 0-5.3-.3-5.4-.8 0 0-2.9-2-2-4.1 0 0 1-2.7 3.8-3 0 0 4.4.7 4.4-.5 0 0 .3-.8.3-2.2 0 0 .3-16.2.1-17.3 0 0 .4-2-1.4-2 0 0-1.9.5-3.1-.3 0 0-3.3.1-3.9-2.5 0 0-.5-1.6.3-3 0 0 1.5-3 5.9-2 0 0 2.5.7 2.7-1.1l-.1-6.5s-.6-1.8 2.9-2.7c0 0 4.4 1.2 5.7-1 0 0 1.5-3 2.7-1 0 0 2.6 2.5 9.7 2.2 0 0 12.2-1 12.5-.4 0 0 3 .4 3.8 1.4l.4.1zm-37-22.7s-1.5 4.8-8.2-.1l-22.9-.5s-3 1-3.4-2.2c0 0 .5-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.6-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19.1 0 0 .6-1.6-2.6-1.9 0 0-4.5.9-5-2 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.4-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.4c0 0 4.2.3 4.8-.1 0 0 .9-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.7 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.4 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1 0-.2-.9-.3-1.2.2"></path><path d="M147.8 92.2s-1.5 4.8-8.2-.1l-22.9-.6s-3 1-3.4-2.2c0 0 .5-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.5-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19 0 0 .5-1.6-2.6-1.9 0 0-4.5.9-5-2 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.3-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.3c0 0 4.2.3 4.8-.1 0 0 .9-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.6 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.5 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1.2-.3-.7-.4-1 .1"></path><path class="st4" d="M147.8 92.2s-1.5 4.8-8.2-.1l-22.9-.6s-3 1-3.4-2.2c0 0 .6-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.5-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19 0 0 .6-1.6-2.6-1.9 0 0-4.5.9-5-2 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.3-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.3c0 0 4.2.3 4.8-.1 0 0 .9-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.6 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.5 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1.2-.3-.7-.4-1 .1z"></path><path class="st3" d="M122.1 36.1s.3 2.7.3 3.4-.4 39.3-.4 39.3.4 3-2.2 4.5c0 0-.7.3-.8-1.6l.3-40.8c-.1 0 .2-5 2.8-4.8"></path><path class="st5" d="M122.1 36.1s.3 2.7.3 3.4-.4 39.3-.4 39.3.4 3-2.2 4.5c0 0-.7.3-.8-1.6l.3-40.8c-.1 0 .2-5 2.8-4.8z"></path><path class="st3" d="M117.6 83.8s.3-38.4.1-39c-.1-.7.1.6.1.6s-.4-6.7 1.4-9.4c0 0-.6-2.3-3.7.6l-.1 5.6s0 3.1-2.9 3c0 0-3.3-.1-4.8 1.4 0 0-.8 3 2.3 2.5 0 0 4.9 0 5.2 2l-.2 20.3s1.1 3.4-2.7 3.4c0 0-4.6-.3-4.5 2.6 0 0 1 2.4 4.9 1.6 0 0 2.9.5 2.7 3.3v5.2s-.3 2.1 2.5 2.2h21.2s1.1.1 1.1-1.2c0 0-.1-1.1-1.8-1.1l-17.6-.4s-2.9 1-3.2-3.2"></path><path class="st3" d="M121.7 83.7s-.5 1.9 1.5 1.5h14l3.8.8s3.9-1 0-3.1c0 0-12.5.1-13.6-.1 0 0-2.6-.7-3.1-.8 0-.1-1.4-.6-2.6 1.7"></path><path class="st5" d="M121.7 83.7s-.5 1.9 1.5 1.5h14l3.8.8s3.9-1 0-3.1c0 0-12.5.1-13.6-.1 0 0-2.6-.7-3.1-.8 0-.1-1.4-.6-2.6 1.7z"></path><path class="st3" d="M145 85.6s-2.6 0-2.5 3.4c0 0 .3 2.6 3 2.6 0 0 2.4-.4 2.2-3.1 0 0-1.5-3.4-2.7-2.9m1.9-1.4s1.2-1.4 2-1.4c0 0 13.6 1.2 15.9-.4 0 0 2.1-.4 2.6 0 0 0 2 1.6 2 2.6 0 0-.1 1.2-2.6.5 0 0-15.4.1-16.2.1.1.2-4.2 1.1-3.7-1.4"></path><path class="st5" d="M146.9 84.2s1.2-1.4 2-1.4c0 0 13.6 1.2 15.9-.4 0 0 2.1-.4 2.6 0 0 0 2 1.6 2 2.6 0 0-.1 1.2-2.6.5 0 0-15.4.1-16.2.1.1.2-4.2 1.1-3.7-1.4z"></path><path class="st3" d="M148.6 88.7s0-1.1 1.9-1.1l19-.1s1.8 1 1.8-2.3c0 0 .7-14.8.8-31.2 0-4.9.2-10 .1-14.7 0 0-.4-2-1-2.5 0 0 0-2.2 2.3-1.4 0 0 2 .1 1.5 3.3l.1 4.7s-.4 2.1 3.3 1.5c0 0 3.7.6 3.8 1.9 0 0 1.1 2.7-3.3 2.5h-3.1s-1.4.8-.9 2.5l-.4 19.7s-.3 3.4 3 3.3c0 0 3.4-.1 4.1.9 0 0 2.3 3.4-2.7 3.1 0 0-3.1-.1-3.8.4 0 0-.5.7-1 7.1 0 0 .3 3.8-2.6 3.4 0 0-18.9.3-20.7-.1 0 0-1.9.6-2.2-.9m-24.5-53.8-.4 43.7s-.1 2 3.3 2.4c0 0 3.9.4 4.9 0 0 0 2.6-.1 9.3.1 0 0 1.6 1.6 1.8-.4 0 0 1.1-35.4.5-42.7 0 0-.1-3.1-2.5-3.3 0 0-10.3.8-11.3 0l-4.1-1.1c0 .1-1.6.2-1.5 1.3"></path><path class="st3" d="M145.6 37.1s-.4 20.5-.3 21.2l-.3 17.3-.6 6s.8 1.2 1.5.4c0 0 4.4-1.1 7.1-.7 0 0 8.7.8 10.5-.6 0 0 1.4.7 1.8-1.5 0 0 .5-38.1.3-38.8 0 0 .5-5.5.3-6 0 0-.1-1.2-2.4-.1 0 0 .3 1.1-12.6.7-.1 0-5-.1-5.3 2.1m21.7-.5-.1 43s1.1 3.8 2.9 3.4c0 0 .3-.8.4-2.2l.6-41c-.2 0-1.5-6.5-3.8-3.2"></path><path class="st5" d="m167.3 36.5-.1 43s1.1 3.8 2.9 3.4c0 0 .3-.8.4-2.2l.5-41c-.1.1-1.4-6.4-3.7-3.2z"></path><path d="M150.6 49.4c-.4-1.1 2.3-1.2 2.3-1.2l1.4.3c1-.1 1.9.3 1.9.3.9 1.5-.7 2.3-.7 2.3l-.5 1.8c-.1 1.5 0 14.4 0 14.4.3.7 1.4 1.9 1.4 1.9 0 1.2-1.5 1-1.5 1l-3.9.1c-1.2-.1-.8-1.5-.8-1.5 1.4-.7 1.8-3.1 1.8-3.1V52.1l-1.4-2.7zm-18.3 1.9c.1 3 .1 7.1.1 7.1 2.6 2 4.1-.8 4.1-.8 3.4-3.3 0-6.3-.5-7.1-.6-.8-3.3-.1-3.3-.1l-.4.9zm-.1 10.8-.3 4.2 1.2 2c.8 1.1-.5 1.9-.5 1.9l-4.4-.4c-.8-.4 0-1.8 0-1.8 1.1-.7 1.2-2.5 1.2-2.5l-.1-7.9.1-5.7c0-.7-1.1-1.9-1.1-1.9-.9-.1-.7-1.4-.7-1.4.3-.7 2.6-.3 2.6-.3h5.7c5.4 1.8 4.6 5.7 4.6 5.7.4 3.9-3.8 5.6-3.8 5.6l-.3 2.2 4 6.2c.7.5.5 1.1.5 1.1-.5 1.5-2.7 0-2.7 0l-4.9-7.6c-.3-.4-1.1.6-1.1.6m-28.3 52.8s1-.4 1.2-1.1c0 0 2.9-.3 3.3-.7l9 .7s8.8.7 12.6-2.1c0 0 1.4 0 2.3.4 0 0 1.9.4 2.4 1.2l5.2-.1s3 1 2.7 2.9v2.4s-.5 4.9.8 5.2h4s3.3 1.5 2.9 3.4c0 0 .1 2.7-2.7 3.3 0 0-2.9.8-3.8.7 0 0-1 1.2-1.1 2.7l-.4 17.8s-.3 1.9 2.6 1.6c0 0 4.2-.4 5.4 1.9 0 0 1.1 3.9-1.1 4.6l-1.6.8-3.5.6s-1.5.1-1.6 2.7v4.8s.1 3.3-2.5 3.3l-3.8.3s-.9.8-2.9.3c0 0-23.1.4-23.9.1l-2.7.6s-3 2.7-5.4.7l-1.9-1.5s-1.4.3-1.9.1c-.5-.1-25.6.3-25.6.3s-5.3.6-5.7-1.2c0 0-.7-6.9-.4-7.8 0 0 0-2-1.2-2.2 0 0-5.3-.3-5.4-.8 0 0-2.9-2-2-4.1 0 0 1-2.7 3.8-3 0 0 4.4.7 4.4-.5 0 0 .3-.8.3-2.2 0 0 .3-16.2.1-17.3 0 0 .4-2-1.4-2 0 0-1.9.5-3.1-.3 0 0-3.3.1-3.9-2.5 0 0-.5-1.6.3-3 0 0 1.5-3 5.9-2 0 0 2.5.7 2.7-1.1l-.1-6.5s-.6-1.8 2.9-2.7c0 0 4.4 1.2 5.7-1 0 0 1.5-3 2.7-1 0 0 2.6 2.5 9.7 2.2 0 0 12.2-1 12.5-.4 0 0 3 .4 3.8 1.4l.4.1z"></path><path class="st4" d="M103.9 114.8s.9-.4 1.2-1.1c0 0 2.9-.3 3.3-.7l9 .7s8.8.7 12.7-2.1c0 0 1.4 0 2.3.4 0 0 1.9.4 2.4 1.2l5.2-.1s3 1 2.7 2.9v2.4s-.5 4.9.8 5.2h4s3.3 1.5 2.9 3.4c0 0 .1 2.7-2.7 3.3 0 0-2.9.8-3.8.7 0 0-1 1.2-1.1 2.7l-.4 17.8s-.3 1.9 2.6 1.6c0 0 4.2-.4 5.4 1.9 0 0 1.1 3.9-1.1 4.6l-1.6.8-3.7.6s-1.5.1-1.6 2.7v4.8s.1 3.3-2.5 3.3l-3.8.3s-1 .8-2.9.3c0 0-23.1.4-23.9.1l-2.7.6s-3 2.7-5.4.7l-1.9-1.5s-1.4.3-1.9.1c-.5-.1-25.6.3-25.6.3s-5.3.5-5.7-1.2c0 0-.7-6.9-.4-7.8 0 0 0-2-1.2-2.2 0 0-5.3-.3-5.4-.8 0 0-2.9-2-2-4.1 0 0 1-2.7 3.8-3 0 0 4.3.7 4.3-.5 0 0 .3-.8.3-2.2 0 0 .3-16.2.1-17.3 0 0 .4-2-1.4-2 0 0-1.9.5-3.1-.3 0 0-3.3.1-3.9-2.5 0 0-.6-1.6.3-3 0 0 1.5-3 5.9-2 0 0 2.5.7 2.7-1.1l-.1-6.5s-.5-1.8 2.9-2.7c0 0 4.4 1.2 5.7-.9 0 0 1.5-3 2.7-1 0 0 2.6 2.5 9.7 2.2 0 0 12.3-1 12.5-.4 0 0 3 .4 3.8 1.4h.6z"></path><path class="st3" d="M107.4 168.4s1.6-.8 2.7-.5 17-.1 17-.1 6.9.5 8-.4l1.6-.5s.7-.8.7-1.9c0-1.1.1-4.8.1-4.8l.4-1.4s-.1-1.4 0-37.7c0 0 .5-2.8-1.2-4.6 0 0 0-1.1.5-.9.6.1.3.1.3.1s2.4-.7 2.8.3c0 0 1.1 3.3.1 5.2 0 0-.3 4 1.8 4 0 0 .8.8 2.4.4 0 0 3.5.4 3.5 1.5 0 0 0 2.5-2.5 2l-3.3.3s-2.2.4-1.9 3-.1 21.1-.1 21.1 0 2.2 3.4 2c0 0 4.2-.1 4.2 1.1 0 0 1.1 2.7-1.9 2.9 0 0-5.7-.1-5.3 1.5 0 0-.4 3.8-.7 4.5 0 0 .6 3.1-.1 3.7 0 0-.4 1.8-4.1 1.5 0 0-23.4-.1-27.1.1.3-.2-1.8-.5-1.3-2.4m-4.2-2.6s2.3.4 2.3 2.6.1 3.4-1.2 3.7c-1.4.3-2.8-.1-3.2-1.9-.5-1.8.2-4 2.1-4.4"></path><path class="st3" d="M105.9 164.7s2.4-1.6 3.4-1.4c0 0 12.4.8 16.2.1 3.8-.7 1.5-.1 1.5-.1s3.3 0 4.2-1.2c0 0 1-.3 1.5.7l2 2.2s.2 1.3-2.2 1.2c0 0-22.2 0-22.9.1.1.1-4.1.7-3.7-1.6"></path><path class="st5" d="M105.9 164.7s2.4-1.6 3.4-1.4c0 0 12.4.8 16.2.1 3.8-.7 1.5-.1 1.5-.1s3.3 0 4.2-1.2c0 0 1-.3 1.5.7l2 2.2s.1 1.4-2.2 1.2c0 0-22.2 0-22.9.1.1.1-4.1.7-3.7-1.6z"></path><path class="st3" d="M73.7 116.4s1 0 .8 1.6l-.3 41.1s.4 2.7-1.9 4.5c0 0-1.2 1.5-1.2-.8l.6-41.9c-.1 0 .1-3.1 2-4.5"></path><path class="st5" d="M73.7 116.4s1 0 .8 1.6l-.3 41.1s.4 2.7-1.9 4.5c0 0-1.2 1.5-1.2-.8l.6-41.9c-.1 0 .1-3.1 2-4.5z"></path><path class="st3" d="M71.5 116.7s-1.6 3-1.5 3.7l-.1 9.1s-.1 5.8 0 6.5-.1 17.8-.1 17.8-.1 3.3 0 4.4v7.3s-.3 3.3 2.2 2.4c0 0 23.9-.5 25.4-.1 0 0 2.2.4 1.4 1.5 0 0 0 1-1.5.7 0 0-11.3 1.1-21.5.7 0 0-6.8.4-7.2-.4 0 0-1.1-.1-1.1-4.1 0 0 0-3.7-.3-4.3 0 0 .3-1.9-3.1-2.3 0 0-4.8.8-4.9-2 0 0 .7-2 5.3-1.8 0 0 3.4.5 2.7-2.2l.4-20.1s.4-3.6-1.9-3.7c0 0-2.2-.1-2.9.1 0 0-3.9 0-3.7-2.3 0 0 1.4-2.5 5.3-1.9 0 0 3 .5 3.1-2.9 0 0-.1-6.9 1.5-7.2 0 0 3.5.1 2.5 1.1"></path><path class="st3" d="M75.6 161.7s1.9 2 5 1.9l18.5.3s2.1.1 1.9 1.2c0 0-.3 1.6-2.7 1.1l-22.3-.3-2.5.1s-1.6-.3 1-3.4l1.1-.9z"></path><path class="st5" d="M75.6 161.7s1.9 2 5 1.9l18.5.3s2 .1 1.9 1.2c0 0-.3 1.6-2.7 1.1L76 166l-2.5.1s-1.6-.3 1-3.4l1.1-1z"></path><path class="st3" d="M132.7 159.9s1.6 3.1 2.5 3.6c0 0 1.5 1.8 1.1-1.4l.3-39.2s-.4-1.4 0-3.5l-1.6-3s-1.8-1.4-1.8.8l-.3 41.8-.2.9z"></path><path class="st5" d="M132.7 160s1.6 3.1 2.5 3.6c0 0 1.5 1.8 1.1-1.4l.3-39.2s-.4-1.4 0-3.5l-1.6-3s-1.8-1.4-1.8.8l-.3 41.8-.2.9z"></path><path class="st3" d="M102.5 141.3s.1 3.3-.3 6.4c-.2 1.7.6 9.7-.1 13.7 0 0-.1 1.4-3 .1 0 0-17.4.4-18-.1 0 0-4.8.1-5.2-2.3 0 0 .7-18.6.3-19.3 0 0-.3-10.2 0-11.1 0 0 .4-9.4.1-10.5 0 0 .1-2.2-.3-3.3 0 0 0-1.4 2.3-.5 0 0 .8 1.5 5.3.8 0 0 3.3.5 6.4.1 0 0 6.9-.5 8.7 0 0 0 2.7-.5 3.5 2.5l-.1 8.4.8 2.7 1-.7.4-2 .1-9s.5-1.6 2.7-1.9c0 0 3.4-.4 4-.1.5.3 7.9.7 9.9.3 2-.4 2.4 1.4 8.6-1.8 0 0 1.6.1 1.6 1.2 0 0-.6 11.6 0 13.7 0 0 0 21.2-.4 22.7l.3 6.8s.3 3-4.5 3.3c0 0 3 .4-15.8-.1 0 0-3-.1-6.3 1.4 0 0-.7-.1-.7-1.9 0 0 .5-6.4 0-12.7l-.3-6.9-1 .1z"></path><path d="M90.3 131.7s.4 10.1 0 11.4l-.1 2.2s.5 1.5 1.4 2c0 0 .7 1.9-1 1.8 0 0-3.4-.3-4.2-.1 0 0-1.9 0 0-2.4 0 0 .7-1 .5-3.4 0 0 0-11.4.3-12.1 0 0 .3-1.5-.7-1.9l-1.6.1-2 1.9s-.4 2-1.4-.1V130l1.2-3.3.8-.1s1.2.8 2.3 1.1h6.4l2.3-1.1s.9.1.7 1.4c0 0 .4 3.1-1.4 3.5 0 0-1.1-.4-1.6-1.9 0-.1-1.8-1.1-1.9 2.1m30-3.7s-5.2-.4-5.3 5.2c0 0-.6 3.8 5.2 6.3 0 0 4.2 2 3.9 4.6 0 0-.5 4.6-5 2.3 0 0-2.2-1.1-2-2.3 0 0-.7-1.5-1.5-1.2 0 0-1 .3-.5 4.8 0 0 0 1.8 2 0l1.1.3s3.7 3 6.8-.7c0 0 3.4-3.1 1-6.9 0 0-1.8-2.7-4.9-4.1 0 0-3-1.4-3.7-3.7 0 0 1-4.1 4.6-1.9 0 0 1.4 1.6 2.2 1.9 0 0 1.1 0 .8-2.5l-.3-2s-.3-1.5-2.2 0c0-.1-1.8.6-2.2-.1m-18.7 6.1c-.1.7-1.1 4.5-1.1 4.5l.8 1.4s2.2.1 3 .1 1-.7 1-1.4-1.5-4.8-1.5-4.8l-.7-.8-1.5 1zm2.1-9c.9.7 1.1 2 1.1 2 2.7 7.2 5 17.7 5 17.7.7 1.3 1.9 2.4 1.9 2.4 2.3.8 0 1.5 0 1.5-1 .4-4.1-.1-4.1-.1-1.6-.3-1-.8-1-.8.7-.7.1-3.4.1-3.4-.1-1.1-1.4-2.9-1.4-2.9-.5-.3-1.5.9-1.5.9s-1.5.5-1.6 0c-.1-.6-1.1-1.1-1.1-1.1-1.6 0-2.3 4.1-2.3 4.1.9 3.4 0 2.9 0 2.9-.8 1.1-3.4.3-3.4.3-1.2-.3-.4-1.2-.4-1.2 1.5-.3 1.9-1.9 1.9-1.9.7-.9 5-18.6 5-18.6.6-.8.9-2.4 1.8-1.8M66.9 92.2s-1.5 4.8-8.2-.1l-22.9-.5s-3 1-3.4-2.2c0 0 .5-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.6-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19.1 0 0 .6-1.6-2.6-1.9 0 0-4.5.9-5-2 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.4-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.4c0 0 4.2.3 4.8-.1 0 0 .9-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.7 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.4 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1 0-.2-.9-.3-1.2.2"></path><path class="st4" d="M66.9 92.2s-1.5 4.8-8.2-.1l-22.9-.5s-3 1-3.4-2.2c0 0 .6-6.3.3-6.8 0 0 .1-1.8-2.9-1.8 0 0-4.6.6-4.8-3.5 0 0-.8-4.4 5.6-4.2 0 0 2 .9 2.2-1.6 0 0-.3-17.7-.1-19.1 0 0 .6-1.6-2.6-1.9 0 0-4.5.9-5-2.1 0 0-.3-4.5 2.7-4.8 0 0 3.7.3 4.4-.7 0 0 .8.5.7-2.7l.1-5.6s.4-1.5 2.7-1.4c0 0 4.2.3 4.8-.1 0 0 1-1.2 1.2-1.8 0 0 2.2-.1 2.6.4 0 0 3.3 2.2 15.7 1.4 0 0 1.9 0 2.3.5 0 0 2.3 1.8 3.7-.1 0 0 2-.4 8.2-.3 0 0 6.7 1.8 11.3-2l1.5.3s1.9 3.1 3.3 2.2c0 0 4.1-.4 4.8.4 0 0 1.4 2 .7 7.4 0 0-.8 2.3 3.1 2.3 0 0 4.5-.8 3.9 3.4 0 0 1.2 4.8-6.3 4.1 0 0-1 1.4-.8 2.3 0 0-.1 15.5-.3 17.3 0 0-.1 2.4 3.1 2.3 0 0 4.4-.3 4.5 3.7 0 0 .5 5.2-5.7 4.2 0 0-1.9.3-2.2 1.5l-.3 6.8s.1 3.4-3.7 2.6c-.5 0-21.2.4-23-.1 0-.1-.9-.2-1.2.3z"></path><path class="st3" d="M41.2 36.2s.3 2.7.3 3.4-.4 39.3-.4 39.3.4 3-2.2 4.5c0 0-.7.3-.8-1.6l.3-40.8c-.1-.1.2-5.1 2.8-4.8"></path><path class="st5" d="M41.2 36.1s.3 2.7.3 3.4-.5 39.4-.5 39.4.4 3-2.2 4.5c0 0-.7.3-.8-1.6l.3-40.8c0-.1.3-5.1 2.9-4.9z"></path><path class="st3" d="M36.7 83.8s.3-38.4.1-39c-.1-.7.1.6.1.6s-.4-6.7 1.4-9.4c0 0-.5-2.3-3.7.5l-.1 5.5s0 3.1-2.9 3c0 0-3.3-.1-4.8 1.4 0 0-.8 3 2.3 2.5 0 0 4.9 0 5.2 2L34 71.4s1.1 3.4-2.7 3.4c0 0-4.6-.3-4.5 2.6 0 0 1 2.5 4.9 1.6 0 0 2.9.5 2.7 3.3v5.2s-.3 2 2.4 2.2H58s1.1.1 1.1-1.2c0 0-.1-1.1-1.8-1.1L39.8 87s-2.9 1-3.1-3.2"></path><path class="st3" d="M40.4 84s-.5 1.9 1.5 1.5h14l3.8.8s4-.9 0-3.1c0 0-12.5.1-13.6-.1 0 0-2.6-.7-3.1-.8-.1 0-1.4-.6-2.6 1.7"></path><path class="st5" d="M40.4 84s-.5 1.9 1.5 1.5h14l3.8.8s4-.9 0-3.1c0 0-12.5.1-13.6-.1 0 0-2.6-.7-3.1-.8-.1 0-1.4-.6-2.6 1.7z"></path><path class="st3" d="M63.6 85.9s-2.6 0-2.5 3.4c0 0 .3 2.6 3 2.6 0 0 2.5-.4 2.2-3.1 0 0-1.5-3.4-2.7-2.9m1.9-1.3s1.2-1.4 2-1.4c0 0 13.6 1.2 15.9-.4 0 0 2-.4 2.6 0 0 0 2 1.6 2 2.6 0 0-.1 1.2-2.6.6 0 0-15.4.1-16.2.1.1 0-4.1.9-3.7-1.5"></path><path class="st5" d="M65.5 84.6s1.2-1.4 2-1.4c0 0 13.6 1.2 15.9-.4 0 0 2.1-.4 2.6 0 0 0 2 1.6 2 2.6 0 0-.1 1.2-2.6.6 0 0-15.4.1-16.2.1.1 0-4.1.9-3.7-1.5z"></path><path class="st3" d="M67.7 88.7s0-1.1 1.9-1.1l19.1-.1s1.8.9 1.8-2.3c0 0 .7-14.8.8-31.2 0-4.9.2-10 .1-14.7 0 0-.4-2-.9-2.5 0 0 0-2.2 2.3-1.4 0 0 2 .1 1.5 3.3l.1 4.8s-.4 2 3.3 1.5c0 0 3.7.5 3.8 1.9 0 0 1.1 2.7-3.2 2.5h-3.1s-1.4.8-1 2.5l-.4 19.7s-.3 3.4 3 3.3c0 0 3.4-.1 4.1.9 0 0 2.3 3.4-2.7 3.1 0 0-3.1-.1-3.8.4 0 0-.5.7-.9 7.1 0 0 .3 3.8-2.6 3.4 0 0-18.9.3-20.7-.1-.3-.1-2.2.5-2.5-1M43.2 34.9l-.4 43.7s-.1 2 3.3 2.4c0 0 4 .4 4.9 0 0 0 2.6-.1 9.2.1 0 0 1.6 1.6 1.8-.4 0 0 1.1-35.4.5-42.7 0 0-.1-3.1-2.5-3.3 0 0-10.3.8-11.3 0l-4.1-1.1c.1.1-1.5.2-1.4 1.3"></path><path class="st3" d="m86.3 36.5-.1 43s1.1 3.8 2.9 3.4c0 0 .3-.8.4-2.2l.6-41c-.1.1-1.4-6.4-3.8-3.2"></path><path class="st5" d="m86.3 36.5-.1 43s1.1 3.8 2.9 3.4c0 0 .3-.8.4-2.2l.6-41c-.1.1-1.4-6.4-3.8-3.2"></path><path class="st5" d="m86.3 36.5-.1 43s1.1 3.8 2.9 3.4c0 0 .3-.8.4-2.2l.6-41c-.1.1-1.4-6.4-3.8-3.2z"></path><path class="st3" d="M64.7 36.9s-.4 20.5-.3 21.2l-.3 17.3-.6 6s.8 1.2 1.5.4c0 0 4.4-1.1 7.1-.7 0 0 8.7.8 10.5-.5 0 0 1.4.7 1.8-1.5 0 0 .5-38.1.3-38.8 0 0 .5-5.4.3-6 0 0-.1-1.2-2.4-.2 0 0 .3 1.1-12.7.7 0 .1-4.9-.1-5.2 2.1"></path><path d="m60.5 49.5-1.2 2.6s-3 13.2-4.6 15.9c0 0-.3 2.7-2.6.5 0 0-5.3-16.7-5.6-17.3L44.7 50s-1.1-1.5.8-1.6l5 .1s1.6 1.4-.5 2.9c0 0 .3 2.2 3.1 10.5 0 0 .8 3.1 1.8.8l2.7-9.7-.8-2.9s-.8-1.4.4-1.5c.1.1 2.9-.2 3.3.9m7.9 2.2-.1 14.4-.4 1.1s-1.4 1.8-.4 1.9c0 0-1.2.8 8.7 1 0 0 3.8.7 2.9-4.2 0 0-.4-1.8-1.8.8 0 0-.4 2.2-5.3 1.5 0 0-1.1-.9-1.1-2v-6s-.1-1.5 2.2-1.3c0 0 2.6 1.6 3 .9 0 0 .9-2.2 0-3.5 0 0-1.2-.1-1.4.4 0 0-.8 1.6-3.1.7 0 0-1.1-2.2-.4-3.9 0 0-.3-3.4 2-3.1 0 0 2.7.5 3 1.4 0 0 .7 1.6 1.4-.3l-.8-2.5s-.4-1-1.1-.7c0 0-4.6.7-5.2.1l-3.7.4s-.5 1.4 1.1 2c.1.1.5.3.5.9"></path></svg></div><div id="container_svg-1859938347_3781"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1668 376" style="enable-background:new 0 0 1668 376" xml:space="preserve" id="svg-1859938347_3781"><path d="M14 13h166.5v166.5H14V13z" style="fill:#f25022"></path><path d="M197.5 13H364v166.5H197.5V13z" style="fill:#80ba01"></path><path d="M1515.4 76.4c15.4-10 35.2-10.9 52.5-6.2.1 10.3 0 20.6.1 30.9-8.2-3.6-17.9-6-26.5-2.4-6.9 2.7-10.9 9.8-12 16.8-1.3 8.9-.4 18-.7 27h52.7c.1-11.4-.1-22.8.2-34.2 11.9-3.4 23.8-7.2 35.6-10.8.1 15-.1 30 .1 45.1 11.9-.2 23.8 0 35.6-.1v29.2c-11.9-.5-23.8-.1-35.8-.2.1 16.8 0 33.6 0 50.4.2 9.3-.5 18.7.6 27.9.7 5.5 2.6 11.5 7.8 14.4 8.6 4.8 19.5 2.7 27.4-2.6v29.5c-10.3 4.5-21.8 5.8-32.9 5-10.6-.8-21.5-4.6-28.4-13-8-9.5-10-22.3-10.2-34.4-.1-25.8 0-51.6 0-77.4h-52.7v121.5h-36c0-40.5-.1-81 0-121.4-8.3-.2-16.7 0-25.1-.1 0-9.6.1-19.2 0-28.9 8.3-.2 16.6-.1 25-.1.6-12.8-1.3-25.9 2.5-38.4 3.2-11 10.4-21.1 20.2-27.5zm-770.9 2.1c7.3-1.1 15.1 1.4 19.9 7.1 5.6 6.2 6.7 16.1 2.5 23.4-4.6 8.2-15 12-24 10-9.3-1.7-17.1-10.4-16.7-20.1-.3-10.1 8.3-19.2 18.3-20.4zm-276 4.5h52.4c16.3 41.4 32.7 82.7 49 124.1 4.2 10.4 8.1 20.8 12.4 31.1 21-51.7 42.2-103.3 63.1-155.1 16.8-.3 33.6-.1 50.4-.1v210c-12.1 0-24.2.1-36.3-.1.1-51.6 0-103.3.1-155 0-2.3-.1-4.6-.2-7-.7 1.1-1.3 2.2-1.9 3.4-20.8 52.9-42 105.6-62.7 158.5-8.6.3-17.2 0-25.9.2-21.5-52.9-42.7-105.9-64-158.8-.6-1.1-1.2-2.2-1.8-3.2-.4 22.6-.1 45.3-.2 67.9v94h-34.3c-.1-69.9-.1-139.9-.1-209.9zm375.1 59.3c17.4-5 36.1-4.8 53.5.2 3.6 1 7 2.4 10.3 4.3-.2 11.5 0 23-.1 34.4-11.6-8.9-26.3-14.5-41.1-12.6-11.7 1.1-22.9 7.1-29.9 16.6-9 11.7-11.1 27.3-9.5 41.7 1.2 11.2 5.8 22.5 14.5 29.9 9.1 8.1 21.8 10.9 33.7 10 11.7-1.4 22.8-6.3 32.3-13.3.1 10.9 0 21.7.1 32.6-14.7 8.8-32.4 11-49.2 10.2-17.2-.9-34.4-7.4-46.9-19.6-13.6-13.1-20.9-31.7-21.8-50.4-.9-19.4 3.1-39.7 14.2-55.9 9.3-13.7 23.9-23.5 39.9-28.1zm326.9 35.3c-6.2-13.9-17.5-25.5-31.4-31.8-16-7.3-34.3-8.3-51.6-6.1-18.2 2.3-35.8 10.8-47.3 25.3-10.5 12.9-15.6 29.5-16.6 46-1.5 19.4 1.8 40 13.2 56.2 9.7 14 25 23.6 41.6 27.1 13.8 2.9 28.3 2.9 42.2 0 16.2-3.3 31.3-12.3 41.3-25.5 9.8-12.5 14.7-28.1 15.8-43.8 1.1-16-.5-32.6-7.2-47.4zm-34.7 69.1c-3.4 7.7-9.7 14.3-17.7 17.4-9.3 3.6-19.8 3.8-29.4 1.3-9.8-2.6-18.1-9.6-22.6-18.6-5.8-11.6-6.7-25-5.5-37.7 1.1-11.6 5.1-23.4 13.9-31.4 6.4-6 15.1-8.9 23.7-9.3 10.8-.7 22.3 2.3 30.1 10.2 8.3 8.2 11.5 20 12.4 31.4.8 12.3.3 25.2-4.9 36.7zm101.1-106.6c16.6-2.9 34.1-.8 49.8 5.2v33.1c-11-7.6-24.5-11.6-37.8-11.2-6.7.3-14.1 3.1-17.2 9.5-2.4 6.3-.8 14.6 5.2 18.4 10.2 6.8 22.4 9.7 33 15.9 8.3 4.7 16.4 10.8 20.6 19.6 7.9 16.5 4.6 38.4-9.4 50.7-13.3 12.4-32.5 15.9-50.1 15.2-12.5-.8-25.1-3.3-36.6-8.4.1-11.6-.1-23.2.1-34.8 9.7 7 20.8 12.1 32.7 14.1 8.2 1.3 17.2 1.4 24.9-2.3 7.3-3.6 8.7-14.1 4.3-20.5-4.1-4.9-10.1-7.6-15.7-10.3-10.6-4.8-21.8-8.8-31.1-15.9-6.6-5.1-11.5-12.3-13.5-20.4-2.9-12-2-25.5 5-35.9 7.9-12.2 21.7-19.5 35.8-22zm222 41.9c-5.5-14.9-16.6-27.9-30.9-35-16.4-8.3-35.5-9.5-53.4-7.3-14.5 1.9-28.7 7.4-39.8 17.1-13.5 11.6-21.4 28.7-23.8 46.2-2.2 17.5-1.5 35.9 5.7 52.3 6.8 16.1 20.3 29.1 36.6 35.5 16 6.3 33.8 7 50.7 4.3 17.2-2.8 33.6-11.9 44.3-25.9 11.4-14.4 16.3-33.1 16.3-51.3.2-12.1-1.3-24.5-5.7-35.9zm-32.5 51c-1.4 7.9-4.1 15.8-9.3 22.1-5.2 6.3-13 10.1-21 11.3-8.6 1.3-17.7.7-25.8-2.8-8.7-3.7-15.4-11.2-19-19.9-4.3-10.3-5.1-21.8-4.3-32.8.8-10.7 3.8-21.7 10.9-30 6.4-7.8 16.3-12 26.3-12.6 10.2-.7 21 1.6 28.8 8.5 7.2 6 11.2 14.9 13 24 2.2 10.6 2.2 21.6.4 32.2zm-438.6-88.2c9.9-6 22.6-6.5 33.4-2.8-.1 12 0 24 0 36-7.1-4.6-16-6.6-24.3-5.7-10.2 1.2-18.3 9-22.5 18-4.6 9.5-5.4 20.3-5.1 30.7v72h-35.5V142.6c11.8-.2 23.7-.1 35.5 0-.1 8.6 0 17.2 0 25.8 3.8-9.4 9.5-18.5 18.5-23.6zm-258.3-2.3c11.9 0 23.9-.2 35.8.1-.1 50.1 0 100.3-.1 150.4h-35.7V142.5z" style="fill:#777"></path><path d="M14 196.5h166.5V363H14V196.5z" style="fill:#02a4ef"></path><path d="M197.5 196.5H364V363H197.5V196.5z" style="fill:#ffb902"></path></svg></div><div id="container_svg1197562992_17469"><svg version="1.1" id="svg1197562992_17469" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 616 164" style="enable-background:new 0 0 616 164" xml:space="preserve"><style>.st2{fill:#011f5b}</style><path d="M133.2 8.6H9v62c-.1 26.9 14.9 46.5 27.4 58.1 13.8 12.8 29 20.1 34.1 21.3l.6.1.6-.1c5.1-1.1 20.3-8.5 34.1-21.3 12.6-11.6 27.5-31.2 27.4-58.1v-62z" style="fill:#fff"></path><path d="M125.6 34.6c.2.1.3.3.5.5.1.2.2.5.2.7 0 .3-.1.5-.2.7-.1.2-.3.3-.5.5-.3.2-.6.4-.6.8V40H98.8v-2.2c0-.3-.3-.6-.6-.8-.2-.1-.3-.3-.5-.5-.1-.2-.2-.5-.2-.7 0-.3.1-.5.2-.7s.3-.3.5-.5c.3-.2.6-.4.6-.8v-7.3c0-.3-.3-.6-.6-.8-.2-.1-.3-.3-.5-.5-.1-.2-.2-.5-.2-.7 0-.3.1-.5.2-.7s.3-.3.5-.5c.3-.2.6-.4.6-.8v-2.3h.3c1.5 0 2.8 0 3.8-.4.3-.1.8-.4 1.1-.6.8-.5 1.7-1 3.1-1 1.1 0 2.1.4 3 .8.6.3 1.3.5 1.7.5h.2c.4 0 1-.2 1.7-.5.9-.3 1.9-.8 3-.8 1.3 0 2.3.6 3.1 1 .3.2.7.5 1.1.6.9.4 2.3.4 3.8.4h.3v2.2c0 .3.3.6.6.8l.5.5c.1.2.2.5.2.7 0 .3-.1.5-.2.7-.1.2-.3.3-.5.5-.3.2-.6.4-.6.8v7.2c0 .5.3.7.6 1zm-32 3.7c-2 3.2-5.9 4-6 4-.8.1-1.5.2-2.4.2-4 0-7.8-1.7-11.6-3.8-.4-.2-.9-.5-1.4-.8-2.4-1.3-5.8-3.2-8.7-3.2-.8 0-1.6.1-2.4.6l-.1.1v.1c0 .3-.1.8-.2 1-.5 1.3-1.9 2.5-2.7 3.1-1.7 1.2-3.8 2.4-6.3 2.4-.4 0-.8-.1-1-.1-.5-.1-.8-.4-1.1-.8-.1-.2-.3-.4-.3-.6-.1-.2-.1-.3-.1-.6v-.1l-.1-.1c-1-.3-1.6-1-1.6-1.7 0-.6.4-1.1.8-1.6.7-.8 1.3-1.5.3-2.4-.1-.1-.2-.1-.3-.2-.1-.1-.3-.2-.3-.3-.8-.9-.3-2.7-.1-3.8v-.1c.3-1.2.8-2.4 1.5-3.8.9-1.8 2-3.9 3.5-5.1 0 0 .1-.1.1 0 .1 0 .1 0 .1.1s.2.3.4.7l.3.4.1-.5c.4-1.3 1.1-2.2 2.1-2.5s2 .1 2.5.5l.2.1.1-.2c.3-.5.9-1.1 1.9-1.2s1.9.7 2.4 1.1l.1.1.1-.1c.5-.4 1.7-1.3 2.9-1.1 1.2.1 1.6 1.2 1.7 1.6l.1.3.3-.1c.6-.3 1.6-.6 2.6 0 .6.4.7 1.1.7 1.6v.3l.3-.1c.8-.1 2.2-.2 3 .6.6.7.7 1.5.6 2.2l-.1.3.3-.1c.7-.1 1.5 0 2 .8.3.5.2 1.3.1 1.6v.4l.2.1c.4.1 1 .3 1.3.9.1.4.1 1.1-.1 1.9l-.1.2.2.1c.3.1 1 .5 1.3 1.1s.3 1.5-.1 2.4l-.1.1.1.1c.8.8 1.7 1.5 2.6 2.1 1.7 1.3 4.4 1.9 6.3.4.7-.6 1-1.6.8-2.6-.2-.8-.8-1.5-1.6-1.7-1.4-.3-2.5 1-3.4 2.4 0 .1-.1.1-.1.1l-.1-.1c-.8-1.9-.8-3.6-.1-4.9.6-1 1.9-1.8 3.3-2h.2v-.2c0-2.4.6-4 1.7-5.1.7-.6 1.9-1.3 4-1 .1 0 .1 0 .1.1v.1c-1.5 3.8-1.2 5.1-.3 8.2.1.3.1.6.2.8.9 2.8.7 5.3-.6 7.3zm-49.9-3.7c.2.1.3.3.5.5.1.2.2.5.2.7 0 .3-.1.5-.2.7-.1.2-.3.3-.5.5-.3.2-.6.4-.6.8V40h-26v-2.2c0-.3-.3-.6-.6-.8-.2-.1-.3-.3-.5-.5-.1-.2-.2-.5-.2-.7 0-.3.1-.5.2-.7.1-.2.3-.3.5-.5.3-.2.6-.4.6-.8v-7.3c0-.3-.3-.6-.6-.8-.2-.1-.3-.3-.5-.5-.1-.2-.2-.5-.2-.7 0-.3.1-.5.2-.7.1-.2.3-.3.5-.5.3-.2.6-.4.6-.8v-2.3h.3c1.5 0 2.8 0 3.8-.4.3-.1.8-.4 1.1-.6.8-.5 1.7-1 3.1-1 1.1 0 2.1.4 3 .8.6.3 1.3.5 1.7.5h.2c.4 0 1-.2 1.7-.5.9-.3 1.9-.8 3-.8 1.3 0 2.3.6 3.1 1 .3.2.7.5 1.1.6.9.4 2.3.4 3.8.4h.3v2.2c0 .3.3.6.6.8l.5.5c.1.2.2.5.2.7 0 .3-.1.5-.2.7-.1.2-.3.3-.5.5-.3.2-.6.4-.6.8v7.2c-.1.5.2.7.4 1zM12.1 11.9s.1 36 0 35.8h118V11.9h-118z" style="fill:#900"></path><path class="st2" d="M52.7 33c.4 0 .8.1 1.2.4.1.1.3.2.3.3.3.3.3.7-.1.9-.3.2-.6.5-.8.6-.5.3-1.3.9-1.9.5-.3-.2.3-.6 0-1.6 0-.8.7-1.1 1.3-1.1z"></path><path class="st2" d="M52.9 21.4c-1.3 1.3-2.3 3.1-3.1 4.7-.6 1.3-1.1 2.5-1.5 3.7v.1c-.2.8-.7 2.6-.1 3.3.1.1.2.1.3.3.1.1.2.1.3.3 1.3 1.3.4 2.3-.3 3.1-.3.5-.8.9-.8 1.3 0 .9 1.1 1.3 1.5 1.3.1.1.3.1.3.3 0 .3.1.5.1.7.1.2.1.3.3.5.2.3.6.5.9.6s.6.1.9.1c2.4 0 4.4-1.2 6-2.4 1-.7 2.2-1.9 2.5-2.8.1-.3.1-.5.1-.8l-1.7-.3h-.1c-.2-.1-.3-.2-.3-.3-.1-.3.3-.6.8-1 .2-.1.3-.3.6-.3.9-.6 1.6-1.2 2-1.5-.5-.6-.8-1.1-1.2-1.6-.3-.4-.5-.7-.7-.9-.8-.9-2.3-1.5-3.5-.7-.5.3-.9.7-1.4 1-.3.3-.8.6-1.2 1-.1.1-.3.1-.5-.1-.1-.1-.1-.3 0-.5l.2-.2c.6-.5 1.8-1.7 1.8-2.6.4-2-1.5-5.2-2.2-6.3zm10.6 13c2.9 0 6.3 1.8 8.9 3.3.5.3 1 .6 1.4.8 4.4 2.3 8.9 4.3 13.6 3.5 0 0 3.8-.8 5.7-3.8 1.2-1.9 1.4-4.2.6-6.9-.1-.3-.1-.6-.2-.8-.8-2.8-1.3-4.5.1-8.1-1.6-.1-2.6.4-3.1.9-1 1-1.5 2.6-1.5 5 0 .2-.1.3-.3.4-1.5.1-2.7.8-3.3 1.8-.6 1-.6 2.2-.1 3.8.7-1 1.9-2.3 3.5-1.9 1 .2 1.7 1 1.9 2 .3 1.2-.1 2.4-1 3.1-2.1 1.7-5 1-6.9-.4-2.2-1.6-4.4-3.7-6.7-6.3-.4-.5-.8-.9-1.2-1.4-1.5-1.7-3-3.5-4.8-4.9-2.8-2.2-6.2-2.5-8.5-2.4-1.6.1-4.7.5-6.8 1.8.6 1.3 1.3 2.9 1.3 4.1 0 .2-.1.4-.1.6 1.5-.9 3.3-.3 4.4.8.2.3.5.6.8 1 .4.6.8 1.3 1.4 1.8.1.1.1.2.1.3 0 .1-.1.2-.1.3 0 0-1 .7-2.4 1.7-.2.1-.3.3-.6.4l-.1.1 1.2.2c1-.7 1.9-.8 2.8-.8zm21.7 8.4c-4.1 0-7.8-1.7-11.7-3.8-.4-.2-.9-.5-1.4-.8-3.1-1.7-7.8-4.2-10.8-2.6 0 .4-.1.8-.2 1.1-.5 1.3-2 2.6-2.8 3.1-1.7 1.3-3.8 2.5-6.4 2.5-.4 0-.8-.1-1.1-.2-.5-.1-.9-.5-1.3-.9-.1-.2-.3-.4-.4-.7-.1-.2-.1-.4-.1-.6-1.1-.2-1.8-1-1.8-1.9 0-.7.5-1.3.9-1.7.8-.9 1-1.3.3-2-.1-.1-.1-.1-.3-.2s-.3-.2-.4-.3c-.9-1-.4-2.8-.1-4v-.1c.3-1.2.8-2.4 1.5-3.8.9-1.9 2-3.9 3.6-5.2.1-.1.2-.1.3-.1.1 0 .2.1.3.1.1.1.7 1 1.3 2.4 2.8-1.7 6.7-1.8 7.2-1.9 2.4-.1 6 .3 9 2.6 1.9 1.5 3.4 3.2 4.9 4.9.4.4.8.9 1.2 1.4 2.3 2.6 4.4 4.6 6.7 6.2 1.7 1.2 4.2 1.9 6 .4.6-.5.9-1.5.7-2.4-.2-.8-.7-1.3-1.4-1.5-1.3-.3-2.3 1-3.1 2.2-.1.1-.2.2-.3.1-.1 0-.3-.1-.3-.2-.8-2-.9-3.8-.1-5.1.7-1.1 1.9-1.9 3.5-2.2 0-2.4.6-4.2 1.7-5.2.7-.6 2-1.4 4.2-1 .1 0 .2.1.3.2.1.1.1.2 0 .3-1.5 3.7-1.1 5-.3 8.1.1.3.1.6.2.8.8 3 .6 5.5-.8 7.6-2.1 3.3-6 4.1-6.3 4.2-.9.1-1.6.2-2.4.2z"></path><path class="st2" d="M50.4 40.2c-.3 0-.7 0-1.1-.1-.2 0-.3-.2-.3-.4s.2-.3.4-.3c1.7.2 4.2 0 6.4-2.3.1-.1.3-.1.6 0 .1.1.1.3 0 .6-1.8 1.6-3.8 2.5-6 2.5zm30.1-5.6c-.1 0-.1 0 0 0-.3-.1-.4-.3-.3-.5.4-.9.4-1.7.1-2.2-.4-.8-1.3-1-1.3-1-.1 0-.2-.1-.2-.2-.1-.1-.1-.2 0-.3.1-.3.4-1.4.2-1.9-.1-.4-1-.6-1.3-.6-.1 0-.2-.1-.3-.1s-.1-.2 0-.3c.1-.3.3-1.1.1-1.5-.6-.8-2-.4-2-.4-.1.1-.3 0-.3-.1s-.1-.2-.1-.3c0 0 .4-1.3-.4-2.2-1-1-3-.4-3.1-.4-.1.1-.3 0-.3-.1s-.1-.2-.1-.3c0-.1.3-1.1-.3-1.6-1.2-.8-2.5.2-2.5.2-.1.1-.2.1-.3.1-.1-.1-.2-.1-.2-.3 0-.1-.2-1.5-1.5-1.7-1.4-.2-2.8 1.2-2.8 1.3-.1.1-.1.1-.3.1-.1 0-.2-.1-.3-.1 0 0-1-1.3-2.2-1.2-1.2.1-1.6 1.3-1.6 1.3-.1.1-.1.2-.3.2-.1 0-.2 0-.3-.1 0 0-1.2-1.1-2.4-.7-1.5.6-1.9 2.4-2 3-.1.2-.2.3-.4.3-.2-.1-.3-.2-.3-.4.1-.7.6-2.8 2.5-3.5 1.1-.4 2.2.1 2.7.6.3-.5.9-1.3 2-1.3 1.2-.1 2.1.8 2.5 1.2.6-.5 1.8-1.3 3.1-1.2 1.1.1 1.7 1 1.9 1.7.6-.3 1.7-.7 2.8 0 .8.5.8 1.3.8 1.9.8-.1 2.3-.2 3.2.7.8.8.8 1.7.7 2.3.6-.1 1.7-.1 2.3.8.3.6.3 1.3.1 1.8.5.1 1.2.4 1.5 1 .2.6 0 1.5-.1 2 .4.1 1 .6 1.4 1.3.4.8.3 1.7-.1 2.8-.1-.2-.2-.1-.3-.1z"></path><path class="st2" d="M78.7 39.4c-.2 0-.3-.2-.3-.4.1-1.3-.6-1.6-1.3-1.8-.2-.1-.3-.1-.5-.2-.3-.2-.4-.7-.4-1.1 0-.5 0-1-.4-1.5-.6-.7-1.9-.3-1.9-.3-.1.1-.3 0-.3-.1-.1-.1-.1-.2-.1-.3 0 0 .2-1.5-.6-2.2-.7-.6-2-.4-2.4-.3-.1 0-.2 0-.3-.1-.1-.1-.1-.2-.1-.3.1-.9-.3-1.6-1-2.1-.6-.3-1.3-.4-1.8-.1-1.5.8-2 .8-3.6-.3-.5-.3-1-.5-1.5-.3-.6.1-1.1.7-1.5 1.5-.1.1-.3.3-.5.1-.1-.1-.3-.3-.1-.5.5-1 1.2-1.7 2-1.9.7-.1 1.4 0 2 .4 1.3.9 1.6.9 2.8.2.8-.4 1.7-.3 2.5.1.8.5 1.3 1.3 1.3 2.3.6-.1 1.8-.1 2.6.6.8.6.9 1.7.9 2.2.6-.1 1.6-.1 2.2.6.6.6.6 1.4.6 1.9 0 .2 0 .5.1.6.1 0 .2.1.3.1.7.2 2 .6 1.8 2.6-.2.5-.3.6-.5.6zM44 35.8c0 .1-.1.3-.1.4-.1.1-.2.3-.4.3-.3.3-.8.6-.8 1.2v1.6H17.6v-1.6c0-.6-.4-.9-.8-1.2-.1-.1-.3-.2-.4-.3-.1-.1-.1-.3-.1-.4 0-.1.1-.3.1-.4.1-.1.2-.3.4-.3.3-.3.8-.6.8-1.2v-7.3c0-.6-.4-.9-.8-1.2-.1-.1-.3-.2-.4-.3-.1-.1-.1-.3-.1-.4 0-.1.1-.3.1-.4.1-.1.2-.3.4-.3.3-.3.8-.6.8-1.2V21h.1c.6 0 1.3 0 1.9-.1v16.5h9.1c.1.6.8 1.1 1.5 1.1s1.3-.5 1.5-1.1h9.1V20.8c.6.1 1.3.1 1.9.1h.1v1.7c0 .6.4.9.8 1.2.1.1.3.2.4.3.1.1.1.3.1.4 0 .1-.1.3-.1.4-.1.1-.2.3-.4.3-.3.3-.8.6-.8 1.2v7.2c0 .6.4.9.8 1.2.1.1.3.2.4.3v.7zM21.5 20.4c.4-.2.8-.4 1.2-.7.8-.5 1.5-1 2.7-1 1 0 1.9.4 2.8.7.6.2 1.1.4 1.6.5v14.4h-.1c-.7.1-1.7.3-2.8.8-.3.1-.8.2-1.1.3-1.1.3-2.2.3-3.3.3-.7 0-1.4 0-2 .1V20.5c.3.2.7.1 1-.1zm10.6-.9c.8-.3 1.8-.7 2.8-.7 1.2 0 1.9.5 2.7 1 .3.2.8.5 1.2.7.3.1.7.3 1.1.3v15.3c-.6-.1-1.3-.1-2-.1-1.1 0-2.2 0-3.3-.3-.3-.1-.8-.2-1.1-.3-1.1-.3-2.2-.7-2.8-.8h-.1V20c.5-.1 1-.3 1.5-.5zm11.8 14.9c-.3-.2-.5-.3-.5-.6v-7.2c0-.2.1-.3.5-.6.2-.1.4-.3.6-.6.1-.3.3-.6.3-.8s-.1-.6-.3-.8c-.1-.2-.3-.4-.6-.6s-.5-.3-.5-.6V20h-.6c-1.3 0-2.8 0-3.7-.4-.3-.1-.7-.3-1-.6-.8-.5-1.7-1.1-3.2-1.1-1.1 0-2.2.4-3.1.8-.6.3-1.2.5-1.6.5H30c-.4 0-1-.2-1.6-.5-.9-.3-1.9-.8-3.1-.8-1.4 0-2.4.6-3.2 1.1-.3.2-.7.4-1 .6-.9.4-2.4.4-3.7.4H17v2.6c0 .2-.1.3-.5.6-.2.1-.4.3-.6.6s-.3.6-.3.8c0 .3.1.6.3.8.1.2.3.4.6.6s.5.3.5.6v7.2c0 .2-.1.3-.5.6-.2.1-.4.3-.6.6-.1.3-.3.6-.3.8 0 .3.1.6.3.8.1.2.3.4.6.6s.5.3.5.6v2.4h26.6v-2.4c0-.2.1-.3.5-.6.2-.1.4-.3.6-.6s.3-.6.3-.8c0-.3-.1-.6-.3-.8-.4-.3-.6-.5-.8-.6zm81.9 1.4c0 .1-.1.3-.1.4-.1.1-.2.3-.4.3-.3.3-.8.6-.8 1.2v1.6h-25v-1.6c0-.6-.4-.9-.8-1.2-.1-.1-.3-.2-.4-.3-.1-.1-.1-.3-.1-.4 0-.1.1-.3.1-.4.1-.1.2-.3.4-.3.3-.3.8-.6.8-1.2v-7.3c0-.6-.4-.9-.8-1.2-.1-.1-.3-.2-.4-.3-.1-.1-.1-.3-.1-.4 0-.1.1-.3.1-.4.1-.1.2-.3.4-.3.3-.3.8-.6.8-1.2V21h.1c.6 0 1.3 0 1.9-.1v16.5h9.1c.1.6.8 1.1 1.5 1.1s1.3-.5 1.5-1.1h9.1V20.8c.6.1 1.3.1 1.9.1h.1v1.7c0 .6.4.9.8 1.2.1.1.3.2.4.3.1.1.1.3.1.4 0 .1-.1.3-.1.4-.1.1-.2.3-.4.3-.3.3-.8.6-.8 1.2v7.2c0 .6.4.9.8 1.2.1.1.3.2.4.3-.2.3-.1.5-.1.7zm-22.5-15.4c.4-.2.8-.4 1.2-.7.8-.5 1.5-1 2.7-1 1 0 1.9.4 2.8.7.6.2 1.1.4 1.6.5v14.4h-.1c-.7.1-1.7.3-2.8.8-.3.1-.8.2-1.1.3-1.1.3-2.2.3-3.3.3-.7 0-1.4 0-2 .1V20.5c.3.2.6.1 1-.1zm10.7-.9c.8-.3 1.8-.7 2.8-.7 1.2 0 1.9.5 2.7 1 .3.2.8.5 1.2.7.3.1.7.3 1.1.3v15.3c-.6-.1-1.3-.1-2-.1-1.1 0-2.2 0-3.3-.3-.3-.1-.8-.2-1.1-.3-1.1-.3-2.2-.7-2.8-.8h-.1V20c.4-.1.9-.3 1.5-.5zm11.7 14.9c-.3-.2-.5-.3-.5-.6v-7.2c0-.2.1-.3.5-.6.2-.1.4-.3.6-.6.1-.3.3-.6.3-.8s-.1-.6-.3-.8c-.1-.2-.3-.4-.6-.6s-.5-.3-.5-.6V20h-.6c-1.3 0-2.8 0-3.7-.4-.3-.1-.7-.3-1-.6-.8-.5-1.7-1.1-3.2-1.1-1.1 0-2.2.4-3.1.8-.6.3-1.2.5-1.6.5h-.1c-.4 0-1-.2-1.6-.5-.9-.3-1.9-.8-3.1-.8-1.4 0-2.4.6-3.2 1.1-.3.2-.7.4-1 .6-.9.4-2.4.4-3.7.4h-.6v2.6c0 .2-.1.3-.5.6-.2.1-.4.3-.6.6-.1.3-.3.6-.3.8 0 .3.1.6.3.8.1.2.3.4.6.6.3.2.5.3.5.6v7.2c0 .2-.1.3-.5.6-.2.1-.4.3-.6.6-.1.3-.3.6-.3.8 0 .3.1.6.3.8.1.2.3.4.6.6.3.2.5.3.5.6v2.4h26.6v-2.4c0-.2.1-.3.5-.6.2-.1.4-.3.6-.6.1-.3.3-.6.3-.8 0-.3-.1-.6-.3-.8-.3-.3-.6-.5-.7-.6z"></path><path class="st2" d="M122.6 99.8 71 50.1 19.5 99.8c-4-8.4-6.7-18.2-6.6-29.2v-22h116.2v22c.1 11-2.6 20.8-6.5 29.2zm-16.5 5.1c-4 4-10.5 4-14.4 0-4-4-4-10.5 0-14.4 4-4 10.5-4 14.4 0 4 4 4 10.5 0 14.4zM60.8 72.3c0-5.6 4.6-10.2 10.2-10.2s10.2 4.6 10.2 10.2S76.6 82.5 71 82.5c-5.6.1-10.2-4.5-10.2-10.2zm42.3 53.4c-13.8 12.8-28.1 19.2-31.9 20.2-3.8-1-18.1-7.4-31.9-20.2-.3-.3-.7-.6-1-1l33-35 33 35c-.5.4-.9.7-1.2 1zM36 104.9c-4-4-4-10.5 0-14.4 4-4 10.5-4 14.4 0 4 4 4 10.5 0 14.4-3.9 4.1-10.4 4.1-14.4 0zm93.2-92.3v34.2H13V12.6h116.2zm1.6-1.6H11.3v59.6c-.1 26.1 14.5 45 26.7 56.4 13.3 12.4 28.3 19.7 33 20.7h.2c4.7-1 19.7-8.3 33-20.7 12.2-11.3 26.7-30.3 26.7-56.4-.1-.8-.1-59.6-.1-59.6zm478.1 105.4H146.3v-1.8H609l-.1 1.8zm-456.5 22.2c0 4.7 1.3 8.2 6.2 8.2 4.5 0 6.9-3.1 6.9-8.3v-9.2c0-.7-.1-1.3-.2-1.8-.1-1.2-1.3-1.7-2.8-1.8v-.9h7.8v.8c-1.6.1-2.7.6-2.8 1.8-.1.6-.2 1.1-.2 1.8v9.4c0 7.2-4.5 9.8-8.8 9.8-7.1 0-9.2-3.6-9.2-9.6v-9.9c0-3.1.1-2.9-2.8-3.3v-.9h9v.8c-3 .3-2.8.2-2.8 3.3l-.3 9.8zm36.6 9.3c-4.1-4.8-8.3-9.9-12.5-14.8v11.3c0 .6.1 1 .1 1.4.1.9 1 1.3 2.7 1.4v.7H173v-.7c1.3-.1 2.1-.5 2.2-1.4.1-.4.1-.8.1-1.4v-12.1c-.7-.9-1.5-1.7-3-1.7v-.7h4.7c3.8 4.7 7.8 9.4 11.6 14h.1v-10.6c0-.6-.1-1-.1-1.4-.1-.9-1-1.3-2.7-1.4v-.7h6.3v.7c-1.2.1-2.1.5-2.2 1.4-.1.4-.1.8-.1 1.4v14.4h-.9v.2zm13.2-3.2c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h6.9v.7c-2.3.3-2.2.1-2.2 2.6v11.5zm13.2-14.1c-1.4 0-2.4.4-1.6 2.8 1 3.1 2.5 7.4 3.8 11.3 1.7-3.9 3.2-7.8 4.8-11.8.6-1.6 0-2.4-1.7-2.4v-.7h5.6v.7c-.8 0-1.6.3-2.2 1.5-2.2 5.3-4.3 10.5-6.5 15.7h-1.3c-1.5-4.4-3.3-9.4-4.9-13.8-1-2.7-1.7-3.5-3-3.5v-.7h6.9l.1.9zm14.7 16.6c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h11.5v4.1h-.7c-.3-1.9-.8-3.3-2.6-3.3h-3.5v7.3h2.2c1.6 0 2.3-.6 2.4-2.2h.7v5.5h-.7c-.1-1.5-.6-2.4-2.4-2.4h-2.2v5.8c0 2.2 1 2.4 3.3 2.4 3.7 0 3.5-1 4.7-3.8h.7l-.8 4.6h-12.6v-.6zm17.3-17.2h7.4c2.6 0 5.2 1.4 5.2 4.4 0 2.6-1.6 4.4-3.6 5.2l2.5 3.8c1.3 1.9 2.7 3.7 3.6 4.2v.3h-2.6c-1.5 0-2.4-2.8-5.4-7.9H252v4.7c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6l.1-.6zm4.6 9.2h2.2c2.2 0 3.3-1.7 3.3-4.6 0-2.8-1.5-3.8-3.4-3.8h-2v8.4h-.1zm23.2-5.4c-.2-1.8-1.2-3.2-3.2-3.2-1.5 0-2.8.8-2.8 2.6s1.8 2.8 4.4 4.8c2.7 2 3.6 3.3 3.6 5.5 0 3.1-3.1 4.7-6.2 4.7-1.7 0-3.1-.3-4.4-.8-.1-.1-.2-.1-.2-.3v-3.7h.7c.3 2.8 2.2 4 4.1 4 1.8 0 3.3-1.4 3.3-3.3 0-1.7-.9-3.1-4.7-5.6-1.4-.9-3.1-1.9-3.1-4.7 0-2.3 2.6-4.1 5.5-4.1 1.1 0 2.6.1 3.6.5.1.1.2.1.2.3v3.3h-.8zm11.2 10.9c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h6.9v.7c-2.3.3-2.2.1-2.2 2.6v11.5zm14.9 0c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7c2.3-.3 2.2-.1 2.2-2.6v-13.9h-2.4c-2.2 0-2.7 1.4-3.6 3.1h-.6l.7-3.9h14.2l.7 3.9h-.6c-.8-1.7-1.4-3.1-3.6-3.1h-2.3v13.9zm13.9 2.5c2.3-.3 2.2-.1 2.2-2.6v-4l-4-7.8c-.8-1.7-1.5-2.3-2.7-2.3v-.7h6.5v.7c-1.9 0-1.7 1-1.1 2.3l3.1 6.5 3.5-6.5c.9-1.5.1-2.3-1.5-2.3v-.7h5.3v.7c-.7 0-1.4.3-2.1 1.5l-4.3 7.8c-.3.5-.3 1.3-.3 1.9v2.8c0 2.4-.1 2.2 2.2 2.6v.7h-7v-.6h.2zm23.1-5.4c0-4.4 3.8-10.8 9.3-10.8 4.4 0 5.3 3 5.3 6 0 3.7-2.9 11.4-9.2 11.4-4.3-.1-5.4-3.5-5.4-6.6zm11.6-6.5c0-3.5-2.1-3.6-2.6-3.6-3.6 0-6 6.5-6 10.8 0 1.7.3 4.9 2.8 4.9 3.8-.1 5.8-6.3 5.8-12.1zm19.7-3.3-.8.7h-4.4c-1.6 7.9-4.3 18.4-6.2 23.5h-1.5l4.7-23.5h-3.1l.3-1.4h3.1c1.7-7.1 7.6-7.8 9-7.8.8 0 2.4.1 2.8.6l-.4 2.2-.8.4c-.5-.9-1.5-1.9-2.9-1.9-2 0-3.5 1.4-4.3 4.9l-.3 1.6h4.9l-.1.7zm20.3 11.8c0 3.1-.1 2.9 2.8 3.3v.8h-9v-.9c3-.3 2.8-.2 2.8-3.3v-14.9c0-3.1.1-2.9-2.8-3.3v-.9h8.7c5.5 0 8.3 2.2 8.3 6.2 0 4.8-4.3 7.4-9.4 7.1v-.7c4.2.1 6-2.4 6-6 0-3.8-1.8-5.4-5.5-5.4h-2l.1 18zm15.4 3.4c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h11.5v4.1h-.7c-.3-1.9-.8-3.3-2.6-3.3H410v7.3h2.2c1.6 0 2.3-.6 2.4-2.2h.7v5.5h-.7c-.1-1.5-.6-2.4-2.4-2.4H410v5.8c0 2.2 1 2.4 3.3 2.4 3.7 0 3.5-1 4.7-3.8h.7l-.8 4.6h-12.6v-.6zm33.8.7c-4.1-4.8-8.3-9.9-12.5-14.8v11.3c0 .6.1 1 .1 1.4.1.9 1 1.3 2.7 1.4v.7h-6.3v-.7c1.3-.1 2.1-.5 2.2-1.4.1-.4.1-.8.1-1.4v-12.1c-.7-.9-1.5-1.7-3-1.7v-.7h4.7c3.8 4.7 7.8 9.4 11.6 14h.1v-10.6c0-.6-.1-1-.1-1.4-.1-.9-1-1.3-2.7-1.4v-.7h6.3v.7c-1.2.1-2.1.5-2.2 1.4-.1.4-.1.8-.1 1.4v14.4h-.8v.2h-.1zm22.9 0c-4.1-4.8-8.3-9.9-12.5-14.8v11.3c0 .6.1 1 .1 1.4.1.9 1 1.3 2.7 1.4v.7H446v-.7c1.3-.1 2.1-.5 2.2-1.4.1-.4.1-.8.1-1.4v-12.1c-.7-.9-1.5-1.7-3-1.7v-.7h4.7c3.8 4.7 7.8 9.4 11.6 14h.1v-10.6c0-.6-.1-1-.1-1.4-.1-.9-1-1.3-2.7-1.4v-.7h6.3v.7c-1.2.1-2.1.5-2.2 1.4-.1.4-.1.8-.1 1.4v14.4h-.9v.2zm16.7-14.1c-.2-1.8-1.2-3.2-3.2-3.2-1.5 0-2.8.8-2.8 2.6s1.8 2.8 4.4 4.8c2.7 2 3.6 3.3 3.6 5.5 0 3.1-3.1 4.7-6.2 4.7-1.7 0-3.1-.3-4.4-.8-.1-.1-.2-.1-.2-.3v-3.7h.7c.3 2.8 2.2 4 4.1 4 1.8 0 3.3-1.4 3.3-3.3 0-1.7-.9-3.1-4.7-5.6-1.4-.9-3.1-1.9-3.1-4.7 0-2.3 2.6-4.1 5.5-4.1 1.1 0 2.6.1 3.6.5.1.1.2.1.2.3v3.3h-.8zm10.3 13.4c2.3-.3 2.2-.1 2.2-2.6v-4l-4-7.8c-.8-1.7-1.5-2.3-2.7-2.3v-.7h6.5v.7c-1.9 0-1.8 1-1.1 2.3l3.1 6.5 3.5-6.5c.9-1.5.1-2.3-1.5-2.3v-.7h5.3v.7c-.7 0-1.4.3-2.1 1.5l-4.3 7.8c-.3.5-.3 1.3-.3 1.9v2.8c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7h.1v.1zm15.6 0c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h6.9v.7c-2.3.3-2.2.1-2.2 2.6V145c0 1.8 1 1.9 3.3 1.9 3.7 0 3.5-1 4.7-3.8h.6l-.8 4.6h-12.6l.1-.5zm22.3-16.6c-1.4 0-2.4.4-1.6 2.8 1 3.1 2.5 7.4 3.8 11.3 1.7-3.9 3.2-7.8 4.8-11.8.6-1.6 0-2.4-1.7-2.4v-.7h5.6v.7c-.8 0-1.6.3-2.2 1.5-2.2 5.3-4.3 10.5-6.5 15.7h-1.3c-1.6-4.4-3.3-9.4-4.9-13.8-1-2.7-1.7-3.5-3-3.5v-.7h6.9v.9h.1zm15 11.4-1.2 3.4c-.3 1 .1 1.9 1.3 1.9v.7h-4.7v-.7c.7 0 1.5-.4 1.9-1.3 1.1-2.2 4.1-10.6 6.3-16.3h1.5c1.7 5.1 4.4 13.1 5.2 15.3.6 1.5 1 2.2 2.1 2.2v.7h-6v-.7c1.6 0 1.7-.8 1.4-1.9l-1.1-3.3h-6.7zm6.2-1-2.7-8.3h-.1l-3.1 8.3h5.9zm25.8 6.9c-4.1-4.8-8.3-9.9-12.5-14.8v11.3c0 .6.1 1 .1 1.4.1.9 1 1.3 2.7 1.4v.7H558v-.7c1.3-.1 2.1-.5 2.2-1.4.1-.4.1-.8.1-1.4v-12.1c-.7-.9-1.5-1.7-3-1.7v-.7h4.7c3.8 4.7 7.8 9.4 11.6 14h.1v-10.6c0-.6-.1-1-.1-1.4-.1-.9-1-1.3-2.7-1.4v-.7h6.3v.7c-1.2.1-2.1.5-2.2 1.4-.1.4-.1.8-.1 1.4v14.4h-.8v.2h-.2zm12.1-3.2c0 2.4-.1 2.2 2.2 2.6v.7h-6.9v-.7c2.3-.3 2.2-.1 2.2-2.6v-11.5c0-2.4.1-2.2-2.2-2.6v-.7h6.9v.7c-2.3.3-2.2.1-2.2 2.6v11.5zm10.7-2.7-1.2 3.4c-.3 1 .1 1.9 1.3 1.9v.7h-4.7v-.7c.7 0 1.5-.4 1.9-1.3 1.1-2.2 4.1-10.6 6.3-16.3h1.5c1.7 5.1 4.4 13.1 5.2 15.3.6 1.5 1 2.2 2.1 2.2v.7h-6v-.7c1.6 0 1.7-.8 1.3-1.9l-1.1-3.3h-6.6zm6.2-1-2.7-8.3h-.1L597 141h5.9zM374 93.1V73.9c-6.3 2.7-22.3 6.6-22.3 15 0 5.6 4.2 10.1 10 10.1 5.2 0 8.3-2.9 12.3-5.9zm.1-22.5V55.9c0-5.4-3.5-6.8-8.6-6.8-5 0-12.2 3.3-12.2 6.3 0 .8.3 1.8.3 3.1 0 5.1-1.3 8.6-7.2 8.6-2.5 0-4.2-2.4-4.2-4.6 0-3 2.1-5.8 5-8.3 6.3-5.1 16.2-9.3 24.4-9.3 8.5 0 13 5 13 13.1v36.2c0 2.3 0 6.3 3.2 6.3 2.2 0 3.5-2.8 3.9-4.6h2.2c-.7 6.5-3.5 10.3-10.1 10.3-5.1 0-9.4-4.6-9.6-9.3-6.5 4.7-10.9 9.3-19.5 9.3-6.4 0-12.3-5-12.3-11.5.1-14.8 20.5-19.4 31.7-24.1zm120.3 4.9c0 10.4 3.9 27.6 17.4 27.6 13.1 0 17.7-13.7 17.7-24.3 0-11.5-1.7-31.1-17.6-31.1-13.4-.1-17.5 17.7-17.5 27.8zm-11.7-.6c0-16.3 11.7-30.3 29-30.3 18.1 0 29.8 13.6 29.8 30.8 0 17.6-11.4 30.6-29.7 30.6-18.6 0-29.1-13.9-29.1-31.1zM146.2 14.2V11h31.5v3.2c-5.8.1-8.1.1-8.1 3.6 0 .1.1 1 .6 2.4l19 64.5L216 8h2.2l23.3 75.8s18.4-55.4 18.4-63.2c0-6.2-3.9-5.8-8.9-6.3V11h19.3c8.5 0 17.2-2.9 22.1-5.8v49c6-5.3 11-9.6 19.7-9.6 6.4 0 12.3 2.8 15.5 8.3 2.9 4.7 3.5 9.2 3.5 14.4v27.2c0 7 1.8 7 7.9 7.4v2.6h-26.3v-2.6c5.7-.4 7.5-.4 7.5-7.4V67.3c0-4.3 0-6.2-1.9-10.3-2.2-3.9-6.2-7.7-11.2-6.9-7.6 1.3-10.6 4.8-14.5 8.6v35.7c0 7 1.8 7 7.6 7.4v2.6h-26.6v-2.6c6.3-.4 8.1-.4 8.1-7.4V15c.1-.1-2.2 0-5 0-10.6.7-10.9 8.8-14.3 18.4L238.5 106H236l-23.6-72.8-27.3 72.9h-2.4l-25.1-80c-1.5-4.2-2.2-7.7-4.4-10-1.4-1.1-1.5-1.9-7-1.9zm423 90.3V102c-6-.4-7.8-.4-7.8-7.4v-32c0-6.3 8.3-11.9 14.2-11.9 9 0 13.8 5.7 13.8 14.2v29.7c0 7-1.8 7-8.1 7.4v2.6h27.4V102c-4.7-.4-8.5-.4-8.5-7.4v-32c0-10.1-10.2-17.8-20.2-17.8-8.8 0-13.1 4-18.8 9.7v-9.7c-6 2.2-12.3 5.3-18.8 6.2v2.2c5.8.6 7.8 2.4 7.8 7.6v34c0 7-1.8 7-8.1 7.4v2.6h27.1v-.3zm-144.6 0V102c-7.1-.4-8.9-.4-8.9-7.4V58.8c0-2.2 3.6-5.6 7.5-5.6 3.6-.1 4.2 4.2 8.8 4.2 5.6 0 6.9-3 6.9-7 0-4.2-4.5-6.6-9.6-6.6-6 0-10.6 5.1-13.8 9.7v-9.2c-6.3 2.8-13.1 5.6-19.3 6.5V53c6.2.6 8.6 2.1 8.6 7.4v34.2c0 7-1.8 7-7.8 7.4v2.6h27.6v-.1zM482.3 48l2-7.4h-23.8v-12h-1.4c-5.6 6.6-10.9 12.2-17.4 17.9v1.6h7.6v40.7c0 9.7 4.4 17.7 14.8 17.7 9 0 15.8-6.5 19.5-10.9l-1.7-2.3c-3.3 3.7-7.6 5.8-11.5 5.8-8 0-10-4.3-10-11.7V48h21.9z"></path></svg></div><div id="container_svg-1115871797_6019"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 934 204" style="enable-background:new 0 0 934 204" xml:space="preserve" id="svg-1115871797_6019"><path d="M129.4 160.1 78.8 90.3v50.8H99v50.2H5.5v-50.2h19V63h-19V12.8H79l50.7 70.5 50.8-70.5H254V63h-19v78.1h19v50.2h-93.6v-50.2h20.2V90.3l-51.2 69.8z" style="fill:#ffcb05"></path><path d="M629.6 15.2c-9.8 0-18.3 6-18.3 16.7 0 11.1 8.9 14.8 13.6 16.5 2.3.9 4.7 1.6 7 2.5 2.8 1.1 9 3.7 9 10.2 0 6.2-5.4 8.8-11.4 8.8-8.6 0-13.9-5-17.1-14.3l-2.9.3.8 11.3 4.8 3.5c2.3 1.5 7.1 4.5 14.9 4.5 12.1 0 20.2-8.2 20.2-18.1 0-6.9-3.7-11.9-8.3-14.6-2.3-1.4-5-2.3-8.3-3.3-6.4-2.1-13.2-4.4-13.2-11.2 0-4.5 3.6-7.5 9.7-7.5 4.5 0 8.4 1.7 11.2 5.2 2.3 3 3 4.7 4 8.5l2.8-.6-1.9-17-3.5 2.5c-4.9-3.1-9.3-3.9-13.1-3.9zm223.2.4c-18.4-.1-28.9 14-28.9 29.7 0 18.3 13 30.3 29.7 30.3 15.2 0 28.7-10.4 28.7-30.2 0-17.4-12.1-29.7-29.5-29.8zm-112.4.8v3.2c5.5.3 5.6.5 9.7 6.7l15.2 23.6v10c-.1 6.3-.1 7.7-1.4 9-1.1 1.1-2.8 1.3-7.3 1.8v3.1h28.2v-3.1c-8.5-.5-8.6-1.3-8.6-11.7V47l8.9-14.9c6.6-10.7 8.3-12.4 14-12.6v-3.2h-22.4v3.2c4 .2 6.3.3 6.3 3.1 0 1.3-.8 3-2.3 5.8l-8.1 14.3-8.8-14.3c-1.5-2.4-2.5-4.2-2.5-5.8 0-2.8 2.7-2.9 8.7-3.1v-3.2l-29.6.1zm-184.8 0v3.1c4.7.4 6.2 1 6.7 4.1.2 1.9.3 4.3.3 11.2v20.7c0 2.4 0 5-.1 7.4-.1 6.2-.7 7.1-6.9 7.6v3.2h23.9v-3.2c-6.9-.1-6.9-1.9-7-15.1V48c5.4.1 6.6-.4 10 6.8l3.2 6.7c4.5 9.6 9 13.4 19.8 13.4 1.3 0 2.5-.1 3.7-.2v-1.9c-4.6-.6-6.6-2.2-10.2-8.9l-10.1-17.6c7.9-2.4 11.3-9.6 11.3-15.1 0-7.9-4.5-14.7-18.6-14.7l-26-.1zm-50.3 0v3.1c4.1.4 5.9.7 6.5 3.3.4 1.8.5 8.1.5 10.4v20.1c0 1.5-.1 8.4-.2 9.8-.2 6.3-.9 6.8-6.8 7.4v3.2h43.6c0-4.1.7-11 1.2-14.8h-2.8c-2 10.2-7.7 9.7-16 9.7-7.6 0-9.2-2-9.2-5.8v-16h6.2c7.1 0 9.1.6 9.1 7h2.8V34.9h-2.8c-.6 5.1-1.6 6.8-9 6.8h-6.2V21.5h7.1c9.7 0 13.2.7 14.5 8.5h2.6l-.3-13.6h-40.8zm178.3 0-.5 16.1h3c.5-10.2 4.8-10 11.3-10h7.1v42.2c0 4.5-1.5 6.5-6.7 6.6v2.4h23.7v-2.4c-5.3-.1-6.7-2-6.7-6.6V22.5h7.1c6.5 0 10.8-.2 11.3 10h3l-.5-16.1h-52.1zm-400.6.1v3.1c4.3.2 6.1 1 6.6 4.6.2 1.5.2 4.2.2 10.4v13c0 9.3.1 17.9 8.6 23.6 5.4 3.6 11.8 4 15 4 4 0 19-.8 22.9-16.2 1-4.1 1.2-8.4 1.4-12.8.1-2.4.7-18.3.8-19.7.3-5.7 1.5-6.9 6.8-6.8v-3.1h-21.8v3.1c5.1.4 7.8.6 8.1 5.8.1 1.9.1 8.7.1 10.8 0 6.6-.1 16.7-1.7 22-1.9 6.7-6.4 11-14.6 11-2.5 0-7.5-.5-10.4-4.8-3.7-5.4-3.8-11.4-3.8-18.3V32.5c.1-12.2.1-12.4 7.7-13v-3.1l-25.9.1zm66.6 0v3.1c3.8.3 5.1.9 8.3 3.8-.1 5.9-.3 39.9-1.1 43.7-.6 3.2-2.4 3.3-7.1 3.6v3.1H373v-3.1c-8.8-.9-9.4-1.2-9.4-10.5V30.5l22.6 27.9c2.6 3.2 5.2 6.4 7.8 9.7l5.1 6.4h3.4l.8-44.2c.1-9.3.6-10.6 6.9-10.7v-3.1h-22.6v3.1c9.1.3 9.3.8 9.4 9.9v25.6l-16-19.8c-8.4-10.5-9.5-12-14.3-18.8h-17.1zm66.8 0v3.1c6.7 1 6.7 1.3 6.8 13.2v30.5c-.1 6-1.2 6.6-6.8 7.4v3.1h23.8v-3.1c-4.5-.3-6.1-1.1-6.5-4.6-.2-1.7-.2-8.1-.2-10.1V32.8c0-12.2 0-12.6 6.7-13.2v-3.1h-23.8zm29.2 0v3.1c5.8.9 6.2 2.2 10.1 13.1L471.4 75h3.1l17.4-41.6c4.7-11.3 5.4-12.9 10.9-13.7v-3.1h-20v3.1c3.6.1 7.6.2 7.6 4.4 0 1.5-1 4.1-1.7 6.1L477 60.3l-10-29.1c-1.5-4.8-2-6.2-2-7.8 0-3.3 3-3.5 7.6-3.7v-3.1l-27-.1zm208.2 0v3.1c6.7 1 6.7 1.3 6.8 13.2v30.5c-.1 6-1.2 6.6-6.8 7.4v3.1h23.8v-3.1c-4.5-.3-6.1-1.1-6.5-4.6-.2-1.7-.2-8.1-.2-10.1V32.8c0-12.2 0-12.6 6.7-13.2v-3.1h-23.8zm231.3.4V20c4.1.4 5.9.7 6.5 3.3.4 1.8.5 8.1.5 10.4v20.1c0 1.5-.1 8.4-.2 9.8-.2 6.2-.9 6.8-6.8 7.4v3.2h23.7V71c-5.9-.6-6.6-1.1-6.8-7.4V47.3h6.3c6.4 0 9.8.6 10.2 7h2.8v-19h-2.8c-.3 6.2-2 6.8-9 6.8H902V22.3c2-.3 3.2-.3 6.8-.3 11.2 0 14.8-.1 16.3 8.9h2.8l-.9-14h-41.9zm-32.2 4c4.8-.1 8.4 2.8 10.3 4.7 4.2 4.5 6.3 10.8 6.5 18.2.2 7.5-.6 13.2-4.7 19.4-3.5 5.4-8.1 7-11.3 7.1-3.2 0-7.8-1.5-11.5-6.7-4.2-6.1-5.2-11.8-5.2-19.2 0-7.4 1.9-13.8 5.9-18.4 1.7-2.1 5.3-5.1 10-5.1zm-276.8.6c10.5 0 13.9 5.7 13.5 12.4-.2 4.6-5.2 9.7-11.7 9.7-1.6 0-3.8-.3-5.4-.6V21.8c1.2-.2 2.1-.3 3.6-.3zm226 76.1c-4.3 3.2-7.1 4.7-17.2 8l1.3 2.1-23.3 64.3c-3.3 9.1-6.3 10.9-15.3 11.3v4.7h35.7v-4.7c-5.3-.2-11.8-.6-11.8-6.8 0-1.8.5-3.6 1.1-5.4l2.7-7.5h30.5l2.6 8.8c.5 1.6 1 3.4 1 5.2 0 4.8-4.5 5.3-10.4 5.6v4.7h73.3v-4.7c-13.3-1.4-14.2-1.8-14.2-15.8v-44.9l34.2 42.2c3.9 4.8 7.8 9.7 11.8 14.6l7.7 9.7h5.2l1.2-66.9c.1-14.1 1-16.1 10.4-16.2v-4.7h-34.1v4.7c13.7.5 14.1 1.2 14.2 15v38.8l-24.2-29.9c-12.8-15.8-14.4-18.1-21.6-28.5h-26v4.7c5.8.5 7.7 1.3 12.5 5.8-.1 9-.5 60.4-1.7 66.1-.9 4.4-3.4 4.9-9.1 5.3-8.5-1.1-10.1-3.8-13.4-13.9l-19.1-58.9c-2-6.1-2.2-6.9-4-12.8zm-93.9 1.5c-23.5 0-42.3 18-42.3 45.8 0 22.7 14.2 45.3 44.1 45.3 17 0 26.6-6 33.3-10.2v-6.5c.2-16.4.2-18.6 8.1-19.1v-4.7h-34.9v4.7c9.6 1 11.1 1.2 11.5 12.3.1 3.8-.2 5.9-1.8 8.2-.4.5-5.4 7.2-15 7.2-16.9 0-26.7-17-26.7-36.9 0-24.9 12.6-37.7 27.7-37.7 8.7 0 14.2 3.6 17.1 6.4 6.5 6.5 9.2 12.5 10.9 17.6l3.5-.6-.7-27.1-4.5 5.2c-7-4.4-15.5-9.9-30.3-9.9zm-222.3.1c-29.3 0-43.6 23.1-43.6 46.5 0 25.3 16.9 44.5 43 44.5 23.9 0 33.1-15.3 36.9-21.6l-5.2-3.9c-4.5 6.5-10.2 15-25.4 15-20.1 0-30.2-17.9-30.2-36.9 0-22.5 12.4-35.2 28-35.2 9.1 0 15.5 5.6 18.2 8.5 5.6 6.1 7.8 10.2 9.3 15.2l3.8-.7-.5-26.3-5.9 5.4c-4.8-3.9-13-10.5-28.4-10.5zm-199.5 2.1v4.7c7.7.1 9.4.7 9.4 9.3 0 3.1 0 7.2-.1 10.1l-1.2 36.3c-.1 4.8-.5 12-.7 14.4-.5 5.9-3.2 6.9-10.2 7.2v4.7h30.1v-4.7c-9.2-.7-10.2-2.3-10.2-10.8l.6-50.9 27.8 67.5h7.6l30.3-66.6V166c-.1 16.1-.1 16.6-11 17.3v4.7h37.9v-4.7c-6.9-.6-9.3-1.8-9.8-8.2-.4-4.4-.5-11.8-.5-17.7v-35.7c-.1-13.5 0-14.6 10.3-15.2v-5.2h-26.4l-29.7 65.8-27-65.8h-27.2zm118.8 0v4.7c10.2 1.5 10.2 2 10.3 20v46.1c-.1 9.1-1.8 10.1-10.3 11.2v4.7h36v-4.7c-6.7-.5-9.2-1.7-9.8-7-.2-2.6-.4-12.3-.4-15.3v-35c0-18.5 0-19 10.2-20v-4.7h-36zm123.5 0v4.7c6.7.6 9.2 1.3 9.7 6.5.4 2.7.4 8.3.4 14v36.3c0 3.1 0 6.1-.1 9.2 0 8.3-.1 10.4-9.9 11.3v4.7h37.1v-4.7c-11.2-1.2-11.4-1.2-11.5-19.3v-15.9c7.5-.4 13.3-.6 20.8-.6 7.1 0 12.8.4 19.7.6V164c0 6.3 0 9.6-.2 12-.7 6-3.1 6.3-11.8 7.2v4.7h37.6v-4.7c-6.4-.6-9-1.5-9.6-6-.4-2.6-.5-13.5-.5-16.9v-36.6c0-16.4 0-16.6 10.1-17.8v-4.7h-37.6v4.7c11.8 1.1 11.9 1.7 12 17.5v14.5c-7.4.4-13.2.6-20.6.6-7.2 0-12.5-.2-19.9-.6v-12c0-5.2.1-11.5.7-14.4.9-4.8 3.6-5 10.8-5.8V101l-37.2.3zm98.7 0v4.7c10.2 1.5 10.2 2 10.3 20v46.1c-.1 9.1-1.8 10.1-10.3 11.2v4.7h36v-4.7c-6.7-.5-9.2-1.7-9.8-7-.3-2.6-.4-12.3-.4-15.3v-35c0-18.5 0-19 10.2-20v-4.7h-36zm163.8 17.1 12.2 35.9h-24.9l12.7-35.9z" style="fill:#00274c"></path></svg></div><div id="container_svg1375198502_1037"><svg width="300" height="144" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg1375198502_1037"><g clip-path="url(#svg1375198502_1037_a)"><path d="M300 0H0v143.5h300V0Z" fill="#0076B5"></path><path d="M47.95 85.55c-.3-2.3-.15-5.6-.15-7.25l3.4-39.1h12.55L60.3 78.65c-.75 8.55 2.55 14.65 10.9 14.65 8.55 0 13.05-6.2 13.8-13.75l3.55-40.3h12.55l-3.45 39.8c-1.4 15.35-11.35 26.8-27.5 26.8-12.55-.05-20.65-8.3-22.2-20.3Zm56.1-13.25c1.65-18.75 14.05-34.35 35.5-34.35 5.65 0 11.05 1.3 16.5 4.85l-1.45 15.5c-4.95-6.65-10.85-7.7-16.25-7.7-13.35 0-20.7 9.45-21.75 21.45-1 11.65 4.9 21.15 17.8 21.15 5.65 0 11.65-1.65 17.9-8.05l-1.4 15.95c-6.1 3.35-11.8 4.9-17.5 4.9-21.2-.05-30.9-16.1-29.35-33.7Zm62-33.1h12.55l-4.55 53.1h17.9c1.25 0 5.55-.05 6.7-.15-.05.65-1.15 12.25-1.15 12.25h-37.15l5.7-65.2Zm65.25 24.35c.4-.95.8-2.1.95-2.75h.2c.05.8.2 1.8.5 2.75.7 2.95 5.55 19.5 5.55 19.5h-15.8c-.05 0 8.45-19.05 8.6-19.5Zm3.2-27.35h-.85l-34.3 68.2h13.3l4.8-10.35h24.15l3 10.35h13.35L234.5 36.2Z" fill="#fff"></path></g><defs><clipPath id="svg1375198502_1037_a"><path fill="#fff" d="M0 0h300v143.5H0z"></path></clipPath></defs></svg></div><div id="container_svg2378593587"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 19 12" id="svg2378593587"><path d="M 1.226 1.839 L 9.5 10.697 L 17.774 1.839" fill="transparent" stroke-width="2.26" stroke="var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168)) /* {&quot;name&quot;:&quot;Medium Gray&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div id="container_svg739324438"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" id="svg739324438"><path d="M 23 5.008 C 22.191 5.375 21.322 5.624 20.408 5.736 C 21.351 5.158 22.056 4.248 22.392 3.175 C 21.507 3.715 20.537 4.094 19.526 4.298 C 18.847 3.554 17.946 3.061 16.965 2.895 C 15.984 2.729 14.977 2.9 14.1 3.381 C 13.223 3.862 12.526 4.627 12.116 5.556 C 11.707 6.485 11.608 7.526 11.835 8.519 C 10.041 8.427 8.285 7.948 6.682 7.115 C 5.079 6.282 3.665 5.113 2.532 3.684 C 2.144 4.369 1.921 5.164 1.921 6.01 C 1.921 6.772 2.104 7.522 2.454 8.194 C 2.804 8.866 3.311 9.439 3.929 9.862 C 3.212 9.838 2.511 9.64 1.885 9.283 L 1.885 9.342 C 1.885 10.411 2.245 11.446 2.905 12.273 C 3.565 13.1 4.484 13.668 5.505 13.879 C 4.841 14.064 4.143 14.091 3.467 13.959 C 3.755 14.878 4.317 15.682 5.073 16.258 C 5.829 16.834 6.742 17.153 7.684 17.171 C 6.085 18.457 4.11 19.155 2.077 19.152 C 1.717 19.152 1.358 19.131 1 19.088 C 3.063 20.448 5.465 21.169 7.918 21.167 C 16.221 21.167 20.761 14.117 20.761 8.002 C 20.761 7.804 20.756 7.603 20.747 7.405 C 21.63 6.75 22.392 5.939 22.998 5.011 L 23 5.008 Z" fill="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */"></path></svg></div><div id="container_svg-1471486003_194"><svg width="15" height="15" fill="none" xmlns="http://www.w3.org/2000/svg" id="svg-1471486003_194"><path d="M10.834 5.833 7.501 9.167 4.167 5.833" stroke="#202124" stroke-linecap="round" stroke-linejoin="round"></path></svg></div></div><iframe id="_hjSafeContext_83897043" title="_hjSafeContext" tabindex="-1" aria-hidden="true" src="about:blank" style="display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;"></iframe><iframe id="intercom-frame" style="position: absolute !important; opacity: 0 !important; width: 1px !important; height: 1px !important; top: 0 !important; left: 0 !important; border: none !important; display: block !important; z-index: -1 !important; pointer-events: none;" aria-hidden="true" tabindex="-1" title="Intercom"></iframe><div class="intercom-lightweight-app"><style id="intercom-lightweight-app-style" type="text/css">
  @keyframes intercom-lightweight-app-launcher {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes intercom-lightweight-app-gradient {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes intercom-lightweight-app-messenger {
    0% {
      opacity: 0;
      transform: scale(0);
    }
    40% {
      opacity: 1;
    }
    100% {
      transform: scale(1);
    }
  }

  .intercom-lightweight-app {
    position: fixed;
    z-index: 2147483001;
    width: 0;
    height: 0;
    font-family: intercom-font, "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
  }

  .intercom-lightweight-app-gradient {
    position: fixed;
    z-index: 2147483002;
    width: 500px;
    height: 500px;
    bottom: 0;
    right: 0;
    pointer-events: none;
    background: radial-gradient(
      ellipse at bottom right,
      rgba(29, 39, 54, 0.16) 0%,
      rgba(29, 39, 54, 0) 72%);
    animation: intercom-lightweight-app-gradient 200ms ease-out;
  }

  .intercom-lightweight-app-launcher {
    position: fixed;
    z-index: 2147483003;
    padding: 0 !important;
    margin: 0 !important;
    border: none;
    bottom: 20px;
    right: 20px;
    max-width: 48px;
    width: 48px;
    max-height: 48px;
    height: 48px;
    border-radius: 50%;
    background: undefined;
    cursor: pointer;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06), 0 2px 32px 0 rgba(0, 0, 0, 0.16);
    transition: transform 167ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    box-sizing: content-box;
  }


  .intercom-lightweight-app-launcher:hover {
    transition: transform 250ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    transform: scale(1.1)
  }

  .intercom-lightweight-app-launcher:active {
    transform: scale(0.85);
    transition: transform 134ms cubic-bezier(0.45, 0, 0.2, 1);
  }


  .intercom-lightweight-app-launcher:focus {
    outline: none;

    
  }

  .intercom-lightweight-app-launcher-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    transition: transform 100ms linear, opacity 80ms linear;
  }

  .intercom-lightweight-app-launcher-icon-open {
    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-open svg {
    width: 24px;
    height: 24px;
  }

  .intercom-lightweight-app-launcher-icon-open svg path {
    fill: undefined;
  }

  .intercom-lightweight-app-launcher-icon-self-serve {
    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-self-serve svg {
    height: 44px;
  }

  .intercom-lightweight-app-launcher-icon-self-serve svg path {
    fill: undefined;
  }

  .intercom-lightweight-app-launcher-custom-icon-open {
    max-height: 24px;
    max-width: 24px;

    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-minimize {
    
        opacity: 0;
        transform: rotate(-60deg) scale(0);
      
  }

  .intercom-lightweight-app-launcher-icon-minimize svg path {
    fill: undefined;
  }

  .intercom-lightweight-app-messenger {
    position: fixed;
    z-index: 2147483003;
    overflow: hidden;
    background-color: #ffffff;
    animation: intercom-lightweight-app-messenger 250ms cubic-bezier(0, 1, 1, 1);
    transform-origin: bottom right;

    
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      

    border-radius: 16px;
  }

  .intercom-lightweight-app-messenger-header {
    height: 64px;
    border-bottom: none;
    background: #ffffff;
  }

  .intercom-lightweight-app-messenger-footer{
    position:absolute;
    bottom:0;
    width: 100%;
    height: 80px;
    background: #ffffff;
    font-size: 14px;
    line-height: 21px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.05);
  }

  @media print {
    .intercom-lightweight-app {
      display: none;
    }
  }
</style></div></body><div class="shoop-de-slider"></div></html>