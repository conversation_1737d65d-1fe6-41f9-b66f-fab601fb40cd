# Uizard AI Design Prompt for ProTutor

## **Project Brief**
**Project Type:** Web Application (Responsive)  
**Target:** Premium tutoring platform connecting students with elite IB & AP tutors

## **Core Vision**
```
Create a modern, trustworthy tutoring platform that feels premium yet approachable. The design should inspire confidence in parents while being intuitive for tech-savvy students (ages 16-18). Think of platforms like Masterclass or Coursera but more focused on personal connections and academic excellence.
```

## **Design Goals**
- **Feel:** Professional but not intimidating, premium but accessible
- **Audience:** Balance parent confidence with student appeal  
- **Mood:** Academic excellence meets modern technology
- **Inspiration:** Educational platforms that feel premium and trustworthy

## **Creative Direction** (Feel free to interpret creatively!)

### **Visual Personality**
- Clean and sophisticated
- Trustworthy and credible
- Modern but timeless
- International and inclusive

### **Style Preferences** (Guidelines, not rules!)
- Professional color palette that conveys trust
- Clean typography that's highly readable
- Generous whitespace for focus
- Modern, subtle visual elements
- Consistent iconography style

## **Core Screens to Design** (Let your creativity guide the specifics!)

Create a cohesive multi-screen design with these key pages:

### **1. Landing Page**
- Compelling hero that communicates trust and academic excellence
- Clear value proposition for both students and parents
- Social proof (testimonials, success stories, or university logos)
- Simple explanation of how the platform works
- Strong calls-to-action for getting started

### **2. Tutor Discovery/Browse**
- Easy way to search and filter tutors
- Clear tutor profiles at-a-glance (photo, subjects, ratings, price)
- Filters for subject, availability, experience level
- Professional but approachable tutor presentation

### **3. Tutor Profile Page**
- Professional tutor presentation with credentials
- Clear booking process
- Reviews and ratings
- Schedule/availability display
- Trust signals (verifications, experience, qualifications)

### **4. Student Dashboard**
- Personal learning hub
- Upcoming sessions and progress
- Easy access to book more sessions
- Connection to favorite tutors

### **5. Booking Flow**
- Simple, clear booking process
- Calendar selection
- Session details confirmation
- Payment integration

## **Key Features to Include**
- Professional tutor profiles with credentials
- Easy search and filtering
- Clear pricing and availability
- Review/rating system
- Secure booking and payment
- Mobile-responsive design

## **Let Your AI Creativity Shine!**
Feel free to interpret these requirements creatively. We want a design that feels fresh, modern, and trustworthy while serving both students and parents effectively.

## **How to Use This Prompt**

### **Getting Started**
1. Copy the core vision and creative direction into Uizard
2. Let the AI generate its interpretation first
3. Then provide feedback to guide it toward your vision

### **Iteration Examples**
Use conversational follow-ups to refine the design:
- "Make it feel more premium and trustworthy"
- "The colors feel too playful - can you make them more professional?"
- "I love the layout but can we try a different color scheme?"
- "Can you make the tutor profiles more prominent?"
- "This feels too corporate - can you make it more approachable?"

### **Tips for Best Results**
- Start broad, then get specific
- Focus on feeling and mood first, details later
- Let the AI surprise you with creative solutions
- Use the conversational features to iterate quickly
- Ask for alternatives when something doesn't feel right

## **Why This Approach Works Better**

✅ **Gives Uizard creative freedom** to interpret your vision  
✅ **Focuses on goals** rather than exact specifications  
✅ **Allows for surprising and innovative solutions**  
✅ **Creates more unique and personalized designs**  
✅ **Easier to iterate** when you're not locked into specifics  

---

**Remember:** Great design comes from balancing clear direction with creative freedom. Let Uizard's AI surprise you!