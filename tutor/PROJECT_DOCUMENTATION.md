# ProTutor Platform Development Documentation

## Project Overview

ProTutor is a premium AI-powered tutoring platform targeting IB/AP students from prestigious international schools. The platform connects students with verified tutors and features a tiered pricing system with platform commission.

## Core Features Implemented

### 1. Authentication System
- **Simple signup flow**: Email, password, full name only
- **Post-login role selection**: Beautiful choice page between Student/Tutor
- **Persistent role enforcement**: Users redirected to role selection until completed
- **Auto-login after signup**: Seamless flow to role selection page

### 2. Role-Based Access Control
- **Student role**: Immediate access after role selection
- **Tutor role**: Pending approval status with restricted access
- **Admin role**: Full platform access (structure in place)

### 3. Tutor Approval System
- **Automatic pending status** for new tutors
- **Manual admin approval** required before full access
- **Dashboard alerts** showing approval status
- **Restricted functionality** until approved

### 4. Platform-Controlled Pricing
- **Standard tier**: $40-50/hour for basic tutors
- **Verified tier**: $60-70/hour for qualified tutors
- **Automatic rate assignment** based on tier and subject
- **18% platform commission** (configurable)

### 5. Automatic Verification System
Tutors automatically upgraded to "Verified" tier when meeting criteria:
- University education (degree keywords detected)
- 2+ years of teaching/tutoring experience
- At least one certification
- Complete profile (bio, subjects, education)

### 6. Tutor Profile System
- **Comprehensive profile form** at `/tutor/profile`
- **Subject specialization** (IB/AP categories)
- **Education background** and certifications
- **Experience tracking** and language support
- **Bio and qualification display**

### 7. Professional UI/UX
- **Landing page** with ProTutor branding
- **Role-based dashboards** with status indicators
- **Responsive design** with Tailwind CSS
- **ShadCN UI components** for consistency

## Technical Architecture

### Tech Stack
- **Frontend**: Next.js 15 with App Router, TypeScript
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Database**: PostgreSQL with Row Level Security (RLS)
- **UI**: Tailwind CSS + ShadCN UI components
- **Authentication**: Supabase Auth with OAuth support

### Database Schema

#### Core Tables
1. **profiles** - User profile information
   - Basic info: id, email, full_name, role
   - Tutor fields: tutor_tier, hourly_rate, bio, subjects, education
   - Approval: approval_status, approval_notes, approved_at
   - Experience: experience_years, certifications, languages

2. **pricing_rules** - Platform pricing configuration
   - Tier-based rates: tier, subject_category, min/max rates
   - Commission settings: platform_commission_percent

#### Enums
- `user_role`: student, tutor, admin
- `approval_status`: pending, approved, rejected
- `tutor_tier`: standard, verified
- `subject_category`: IB/AP subjects + other

### Key Database Functions

#### Automatic Profile Creation
```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
    NULL  -- Role set later via role selection page
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Automatic Tier Assignment
```sql
CREATE OR REPLACE FUNCTION public.set_tutor_rate()
RETURNS TRIGGER AS $$
DECLARE
  min_rate INTEGER;
  max_rate INTEGER;
BEGIN
  -- Auto-upgrade tier based on qualifications
  IF NEW.role = 'tutor' THEN
    IF public.check_verification_eligibility(NEW.id) THEN
      NEW.tutor_tier := 'verified';
    ELSE 
      NEW.tutor_tier := 'standard';
    END IF;
  END IF;

  -- Set rate based on tier and primary subject
  IF NEW.role = 'tutor' AND NEW.subjects IS NOT NULL THEN
    SELECT min_rate_cents, max_rate_cents INTO min_rate, max_rate
    FROM public.pricing_rules 
    WHERE tier = NEW.tutor_tier AND subject_category = NEW.subjects[1];
    
    IF NEW.hourly_rate IS NULL AND min_rate IS NOT NULL THEN
      NEW.hourly_rate := (min_rate + max_rate) / 2;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### File Structure
```
src/
├── app/
│   ├── auth/page.tsx              # Authentication form
│   ├── choose-role/page.tsx       # Post-login role selection
│   ├── dashboard/page.tsx         # Role-based dashboard
│   ├── tutor/profile/page.tsx     # Tutor profile completion
│   └── page.tsx                   # Landing page
├── components/
│   ├── auth/auth-form.tsx         # Signup/login form
│   └── ui/                        # ShadCN UI components
└── lib/
    ├── supabase/                  # Supabase client setup
    └── utils.ts                   # Utility functions
```

## User Flows

### Student Flow
1. Sign up with email/password/name
2. Auto-login → role selection page
3. Choose "Student" → immediate dashboard access
4. Browse tutors, book sessions, manage profile

### Tutor Flow
1. Sign up with email/password/name
2. Auto-login → role selection page
3. Choose "Tutor" → pending approval status
4. Complete profile at `/tutor/profile`
5. Auto-upgrade to verified tier if qualified
6. Await admin approval for full access

### Admin Flow
1. Standard login process
2. Dashboard with admin capabilities
3. Approve/reject tutor applications
4. Manage platform settings

## Pricing Strategy

### Rate Structure
- **Verified tutors**: $60-70/hour (university education + experience)
- **Standard tutors**: $40-50/hour (basic qualifications)
- **Platform commission**: 18% on all transactions

### Trust-Building Features
- **Platform-set rates**: No price negotiation → prevents offline deals
- **Clear verification criteria**: Transparent quality tiers
- **No earnings visibility**: Students don't see tutor earnings info
- **Approval process**: Builds trust in tutor quality

## Deployment Considerations

### Environment Variables Required
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key  # For admin operations
```

### Database Migrations Applied
1. `simple_migration.sql` - Initial schema with profiles and RLS
2. `tutor_approval_migration.sql` - Approval system
3. `pricing_system_migration.sql` - Pricing tiers and rules
4. `verification_criteria_migration.sql` - Auto verification
5. `fix_role_selection.sql` - Role selection flow
6. `simple_signup_migration.sql` - Updated user creation

## Next Development Priorities

### Immediate Features
1. **Payment system integration** (Stripe)
2. **Session booking system** with calendar
3. **Real-time messaging** between tutors/students
4. **Admin panel** for tutor approval workflow

### Future Enhancements
1. **AI-powered tutor matching** (Google Gemini)
2. **Performance analytics** for tutors
3. **Student progress tracking**
4. **Mobile app development**
5. **Video call integration**

## Key Business Decisions Made

### Pricing Model
- Platform-controlled rates (no negotiation)
- Flat 18% commission initially
- Two-tier system (Standard/Verified)
- Automatic tier upgrades based on qualifications

### Trust & Retention Strategy
- No earnings visibility to students
- Transparent verification criteria
- Approval process for tutors
- No upfront payment (decided against 50% upfront)

### User Experience
- Simple signup without role selection
- Post-login role choice for better conversion
- Auto-redirect until role completed
- Professional, academic branding

## Technical Debt & Improvements

### Current Limitations
1. No payment processing yet
2. Basic admin interface needed
3. Manual tutor approval process
4. Limited search/filtering capabilities

### Recommended Improvements
1. Add comprehensive error handling
2. Implement proper logging system
3. Add unit/integration tests
4. Optimize database queries with proper indexing
5. Add rate limiting and security headers

## Development Team Notes

### Code Quality Standards
- TypeScript strict mode enabled
- ESLint and Prettier configured
- Component-based architecture
- Responsive design principles
- Accessibility considerations (WCAG guidelines)

### Security Considerations
- Row Level Security (RLS) policies implemented
- Secure authentication flow
- Input validation on all forms
- SQL injection prevention with parameterized queries
- HTTPS enforcement in production

---

*This documentation captures the complete development conversation and implementation details for the ProTutor platform foundation. All core authentication, role management, pricing, and verification systems are functional and ready for further development.*

**Last Updated**: January 2025  
**Status**: Core foundation complete, ready for feature expansion  
**Commit**: 721d072 - "Implement complete ProTutor platform foundation"