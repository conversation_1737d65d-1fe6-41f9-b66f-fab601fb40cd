'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
  approval_status: 'pending' | 'approved' | 'rejected'
}

export default function TutorsPage() {
  const [tutors, setTutors] = useState<Tutor[]>([])
  const [filteredTutors, setFilteredTutors] = useState<Tutor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [subjectFilter, setSubjectFilter] = useState('all')
  const [tierFilter, setTierFilter] = useState('all')
  const [priceFilter, setPriceFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)

  const supabase = createClient()

  const categories = [
    "All categories", "Art and design", "College mentorship", "Interview preparation",
    "College tutoring", "Technology", "Writing"
  ]
  const subcategories = ["Mathematics"]
  const specialties = ["SAT Prep", "AP Calculus", "AP Algebra"]
  const availability = ["Today", "Tomorrow", "Next 3 days", "Next 7 days", "Custom"]
  const durations = ["60 minutes", "30 minutes", "15 minutes"]
  const prices = ["$0 - $100", "$100 and above"]

  useEffect(() => {
    fetchTutors()
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  useEffect(() => {
    filterTutors()
  }, [tutors, searchTerm, subjectFilter, tierFilter, priceFilter])

  const fetchTutors = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .order('created_at', { ascending: false })

      if (error) throw error

      setTutors(data || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching tutors:', error)
      setError('Failed to load tutors. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filterTutors = () => {
    let filtered = tutors

    // Search by name or bio
    if (searchTerm) {
      filtered = filtered.filter(tutor =>
        tutor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (tutor.bio && tutor.bio.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by subject
    if (subjectFilter !== 'all') {
      filtered = filtered.filter(tutor =>
        tutor.subjects && tutor.subjects.includes(subjectFilter)
      )
    }

    // Filter by tier
    if (tierFilter !== 'all') {
      filtered = filtered.filter(tutor => tutor.tutor_tier === tierFilter)
    }

    // Filter by price
    if (priceFilter !== 'all') {
      filtered = filtered.filter(tutor => {
        if (!tutor.hourly_rate) return false
        const rate = tutor.hourly_rate / 100 // Convert from cents
        if (priceFilter === 'low') return rate < 100
        if (priceFilter === 'high') return rate >= 100
        return true
      })
    }

    setFilteredTutors(filtered)
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const formatSubjects = (subjects: string[] | null) => {
    if (!subjects || subjects.length === 0) return []
    return subjects.map(subject => subject.replace('_', ' ').toUpperCase())
  }

  const getTierBadgeColor = (tier: string) => {
    return tier === 'verified'
      ? 'bg-green-100 text-green-800'
      : 'bg-blue-100 text-blue-800'
  }

  const getAvailableSubjects = () => {
    const allSubjects = new Set<string>()
    tutors.forEach(tutor => {
      if (tutor.subjects) {
        tutor.subjects.forEach(subject => allSubjects.add(subject))
      }
    })
    return Array.from(allSubjects).sort()
  }

  const generateMockRating = (id: string) => {
    // Generate consistent mock rating based on ID
    const hash = id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    const rating = 4.0 + (Math.abs(hash) % 100) / 100
    const reviews = 10 + (Math.abs(hash) % 200)
    return `${rating.toFixed(1)} (${reviews} reviews)`
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/profile" className="text-gray-500 hover:text-gray-700">
            Profile
          </Link>
          <Link href="/tutors" className="text-gray-500 hover:text-gray-700">
            Search
          </Link>
          {user ? (
            <>
              <Link href="/dashboard" className="text-gray-500 hover:text-gray-700">
                Dashboard
              </Link>
              <button
                onClick={async () => {
                  await supabase.auth.signOut()
                  window.location.reload()
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                Logout
              </button>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {user.email?.charAt(0).toUpperCase()}
              </div>
            </>
          ) : (
            <>
              <Link href="/auth" className="text-gray-500 hover:text-gray-700">
                Sign In
              </Link>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </>
          )}
        </div>
      </header>

      <div className="flex p-4">
        {/* Filter Sidebar */}
        <div className="w-64 bg-white rounded-xl shadow-lg p-6 mr-6 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Filter by</h2>

          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Category</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {categories.map(cat => (
                <li key={cat}>
                  <label className="inline-flex items-center">
                    <input type="radio" name="category" className="form-radio text-blue-600 rounded-full" />
                    <span className="ml-2">{cat}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Subcategory Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Subcategory</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {subcategories.map(subcat => (
                <li key={subcat}>
                  <label className="inline-flex items-center">
                    <input type="radio" name="subcategory" className="form-radio text-blue-600 rounded-full" />
                    <span className="ml-2">{subcat}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Specialties Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Specialties</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <div className="flex flex-wrap gap-2">
              {specialties.map(spec => (
                <span key={spec} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full flex items-center">
                  {spec}
                  <button className="ml-1 text-blue-800 hover:text-blue-900">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
              <input
                type="text"
                placeholder="Search specialties"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Availability Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Availability</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {availability.map(avail => (
                <li key={avail}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{avail}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Duration Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Duration</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {durations.map(duration => (
                <li key={duration}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{duration}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Price Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Price</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {prices.map(price => (
                <li key={price}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{price}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 bg-white rounded-xl shadow-lg p-6">
          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-6">
            {['Explore', 'Prior Tutors', 'Favorites (12)'].map(tab => (
              <button
                key={tab}
                className={`py-2 px-4 -mb-px text-lg font-medium ${tab === 'Explore' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600 hover:text-gray-800'}`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Search Bar */}
          <div className="relative mb-6">
            <input
              type="text"
              placeholder="Search tutors by name or expertise..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-3 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap gap-4 mb-6">
            <select
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Subjects</option>
              {getAvailableSubjects().map(subject => (
                <option key={subject} value={subject}>
                  {subject.replace('_', ' ').toUpperCase()}
                </option>
              ))}
            </select>

            <select
              value={tierFilter}
              onChange={(e) => setTierFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Levels</option>
              <option value="standard">Standard</option>
              <option value="verified">Verified</option>
            </select>

            <select
              value={priceFilter}
              onChange={(e) => setPriceFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Prices</option>
              <option value="low">Under $100/hr</option>
              <option value="high">$100+/hr</option>
            </select>
          </div>

          {/* Results Summary */}
          <p className="text-gray-500 text-sm mb-6">
            Showing {filteredTutors.length} of {tutors.length} tutors
          </p>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading tutors...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={fetchTutors}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && filteredTutors.length === 0 && tutors.length > 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-2">No tutors found</h3>
              <p className="text-gray-500">Try adjusting your search criteria or filters</p>
            </div>
          )}

          {/* No Tutors Available */}
          {!loading && !error && tutors.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👨‍🏫</div>
              <h3 className="text-xl font-semibold mb-2">No approved tutors available</h3>
              <p className="text-gray-500">Tutors are currently being reviewed. Please check back soon!</p>
            </div>
          )}

          {/* Tutor Listings */}
          {!loading && !error && filteredTutors.length > 0 && (
            <div className="space-y-6">
              {filteredTutors.map(tutor => (
                <div key={tutor.id} className="bg-gray-50 p-6 rounded-xl shadow-sm flex items-start space-x-4">
                  <Image
                    src={`https://placehold.co/80x80/E0E0E0/808080?text=${tutor.full_name.charAt(0)}`}
                    alt={tutor.full_name}
                    width={80}
                    height={80}
                    className="w-20 h-20 rounded-full object-cover shadow-md"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-xl font-bold text-gray-900">{tutor.full_name}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getTierBadgeColor(tutor.tutor_tier)}`}>
                          {tutor.tutor_tier}
                        </span>
                      </div>
                      <Link href={`/profile?tutor=${tutor.id}`}>
                        <button className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-semibold hover:bg-blue-200 transition duration-200">
                          View Profile
                        </button>
                      </Link>
                    </div>
                    <p className="text-gray-700 mb-1">{formatRate(tutor.hourly_rate)}</p>
                    <div className="flex items-center text-gray-600 text-sm mb-2">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span>{generateMockRating(tutor.id)}</span>
                      {tutor.experience_years && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{tutor.experience_years} years experience</span>
                        </>
                      )}
                    </div>
                    <p className="text-gray-700 leading-relaxed text-sm mb-3">
                      {tutor.bio || 'No bio available'}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {formatSubjects(tutor.subjects).slice(0, 5).map(subject => (
                        <span key={subject} className="bg-gray-200 text-gray-700 text-xs px-3 py-1 rounded-full">
                          {subject}
                        </span>
                      ))}
                      {tutor.subjects && tutor.subjects.length > 5 && (
                        <span className="text-xs text-gray-500">+{tutor.subjects.length - 5} more</span>
                      )}
                      {tutor.languages && tutor.languages.length > 0 && (
                        <span className="bg-green-100 text-green-700 text-xs px-3 py-1 rounded-full">
                          {tutor.languages.join(', ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
