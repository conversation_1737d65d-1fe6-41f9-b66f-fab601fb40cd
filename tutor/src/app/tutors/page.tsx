'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'

export default function TutorsPage() {
  const categories = [
    "All categories", "Art and design", "College mentorship", "Interview preparation",
    "College tutoring", "Technology", "Writing"
  ]
  const subcategories = ["Mathematics"]
  const specialties = ["SAT Prep", "AP Calculus", "AP Algebra"]
  const availability = ["Today", "Tomorrow", "Next 3 days", "Next 7 days", "Custom"]
  const durations = ["60 minutes", "30 minutes", "15 minutes"]
  const prices = ["$0 - $100", "$100 and above"]

  const tutors = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Expert highschool and AP Calculus tutor",
      response: "97% Response Time",
      rating: "4.9 (56 reviews)",
      description: "The only thing I'm more passionate about than math, statistics, and data science is helping others learn. I recently completed three consecutive semesters as a teaching assistant for a masters level course in...",
      tags: ["Calculus I", "Calculus II", "Geometry", "R Programming", "Advanced regression"]
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Enthusiastic Math and Physics tutor and professor",
      response: "95% Response Time",
      rating: "4.8 (223 reviews)",
      description: "Hey there! I am a PhD in Applied Mathematics with a passion for teaching and tutoring. I've spent the last part of a decade honing my craft, first as a tutor and teaching assistant at Penn State University and later...",
      tags: ["Algebra", "Calculus I", "Calculus II", "AP Statistics", "R", "Python", "Year"]
    },
    {
      id: 3,
      name: "Chelsea V.",
      title: "M.S. in Engineering focused on SAT / ACT Math",
      response: "98% Response Time",
      rating: "4.8 (232 reviews)",
      description: "I've helped hundreds of students starting with Pre-Algebra through AP Calculus. My goal is to help any student understand the materials, and prepare for Math, GRE, SAT, ACT, PSAT, SSAT, ISEE, HSPT and oth...",
      tags: ["SAT Prep", "ACT Math", "AP Algebra", "GRE", "Linear Algebra", "Logarithms"]
    },
    {
      id: 4,
      name: "Dr. Emma T.",
      title: "Math PhD Advisor - University of Michigan",
      response: "99% Response Time",
      rating: "4.6 (23 reviews)",
      description: "I've been tutoring and teaching AP Calculus and all levels of math for more than 20 years. I teach math full-time and have taught at both the high school and college level. I am also a math content creator for...",
      tags: ["AP Calculus", "Linear Algebra", "Differential Equations", "Probability", "Statistics"]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/profile" className="text-gray-500 hover:text-gray-700">
            Profile
          </Link>
          <Link href="/tutors" className="text-gray-500 hover:text-gray-700">
            Search
          </Link>
          <button className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </button>
          <button className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </button>
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
        </div>
      </header>

      <div className="flex p-4">
        {/* Filter Sidebar */}
        <div className="w-64 bg-white rounded-xl shadow-lg p-6 mr-6 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Filter by</h2>

          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Category</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {categories.map(cat => (
                <li key={cat}>
                  <label className="inline-flex items-center">
                    <input type="radio" name="category" className="form-radio text-blue-600 rounded-full" />
                    <span className="ml-2">{cat}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Subcategory Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Subcategory</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {subcategories.map(subcat => (
                <li key={subcat}>
                  <label className="inline-flex items-center">
                    <input type="radio" name="subcategory" className="form-radio text-blue-600 rounded-full" />
                    <span className="ml-2">{subcat}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Specialties Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Specialties</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <div className="flex flex-wrap gap-2">
              {specialties.map(spec => (
                <span key={spec} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full flex items-center">
                  {spec}
                  <button className="ml-1 text-blue-800 hover:text-blue-900">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
              <input
                type="text"
                placeholder="Search specialties"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Availability Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Availability</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {availability.map(avail => (
                <li key={avail}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{avail}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Duration Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Duration</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {durations.map(duration => (
                <li key={duration}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{duration}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          {/* Price Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">Price</h3>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <ul className="space-y-2 text-gray-700">
              {prices.map(price => (
                <li key={price}>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                    <span className="ml-2">{price}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 bg-white rounded-xl shadow-lg p-6">
          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-6">
            {['Explore', 'Prior Tutors', 'Favorites (12)'].map(tab => (
              <button
                key={tab}
                className={`py-2 px-4 -mb-px text-lg font-medium ${tab === 'Explore' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600 hover:text-gray-800'}`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Search Bar */}
          <div className="relative mb-6">
            <input
              type="text"
              placeholder="college level math"
              className="w-full p-3 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <p className="text-gray-500 text-sm mb-6">Advanced search</p>

          {/* Tutor Listings */}
          <div className="space-y-6">
            {tutors.map(tutor => (
              <div key={tutor.id} className="bg-gray-50 p-6 rounded-xl shadow-sm flex items-start space-x-4">
                <Image
                  src={`https://placehold.co/80x80/E0E0E0/808080?text=${tutor.name.split(' ')[0]}`}
                  alt={tutor.name}
                  width={80}
                  height={80}
                  className="w-20 h-20 rounded-full object-cover shadow-md"
                />
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{tutor.name}</h3>
                    <Link href={`/profile?tutor=${tutor.id}`}>
                      <button className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-semibold hover:bg-blue-200 transition duration-200">
                        View Profile
                      </button>
                    </Link>
                  </div>
                  <p className="text-gray-700 mb-1">{tutor.title}</p>
                  <div className="flex items-center text-gray-600 text-sm mb-2">
                    <span className="mr-2">{tutor.response}</span>
                    <span className="text-yellow-500 mr-1">★</span>
                    <span>{tutor.rating}</span>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-sm mb-3">
                    {tutor.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {tutor.tags.map(tag => (
                      <span key={tag} className="bg-gray-200 text-gray-700 text-xs px-3 py-1 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
