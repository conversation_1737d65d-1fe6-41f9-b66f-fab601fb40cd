'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import Link from 'next/link'
import type { User } from '@supabase/supabase-js'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
  approval_status: 'pending' | 'approved' | 'rejected'
}

export default function TutorsPage() {
  const [tutors, setTutors] = useState<Tutor[]>([])
  const [filteredTutors, setFilteredTutors] = useState<Tutor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [subjectFilter, setSubjectFilter] = useState('all')
  const [tierFilter, setTierFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)

  const supabase = createClient()

  useEffect(() => {
    fetchTutors()
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  useEffect(() => {
    filterTutors()
  }, [tutors, searchTerm, subjectFilter, tierFilter])

  const fetchTutors = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .order('created_at', { ascending: false })

      if (error) throw error

      setTutors(data || [])
      setError(null) // Clear any previous errors
    } catch (error) {
      console.error('Error fetching tutors:', error)
      setError('Failed to load tutors. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filterTutors = () => {
    let filtered = tutors

    // Search by name or bio
    if (searchTerm) {
      filtered = filtered.filter(tutor =>
        tutor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (tutor.bio && tutor.bio.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by subject
    if (subjectFilter !== 'all') {
      filtered = filtered.filter(tutor =>
        tutor.subjects && tutor.subjects.includes(subjectFilter)
      )
    }

    // Filter by tier
    if (tierFilter !== 'all') {
      filtered = filtered.filter(tutor => tutor.tutor_tier === tierFilter)
    }

    setFilteredTutors(filtered)
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const formatSubjects = (subjects: string[] | null) => {
    if (!subjects || subjects.length === 0) return 'No subjects listed'
    return subjects
      .map(subject => subject.replace('_', ' ').toUpperCase())
      .join(', ')
  }

  const getTierBadgeColor = (tier: string) => {
    return tier === 'verified' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-blue-100 text-blue-800'
  }

  const getAvailableSubjects = () => {
    const allSubjects = new Set<string>()
    tutors.forEach(tutor => {
      if (tutor.subjects) {
        tutor.subjects.forEach(subject => allSubjects.add(subject))
      }
    })
    return Array.from(allSubjects).sort()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading tutors...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchTutors}>Try Again</Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold">ProTutor</h1>
          </Link>
          <nav className="space-x-4">
            {user ? (
              <>
                <span className="text-sm text-muted-foreground hidden md:block">
                  Welcome, {user.email}
                </span>
                <Link href="/dashboard">
                  <Button variant="outline">Dashboard</Button>
                </Link>
                <Button 
                  variant="ghost" 
                  onClick={async () => {
                    await supabase.auth.signOut()
                    window.location.reload()
                  }}
                >
                  Logout
                </Button>
              </>
            ) : (
              <Link href="/auth">
                <Button>Sign In</Button>
              </Link>
            )}
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Find Your Perfect Tutor</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Browse our verified tutors specializing in IB and AP courses. 
            All tutors are personally vetted for quality and expertise.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-background rounded-lg p-6 shadow-sm mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search Tutors</label>
              <Input
                placeholder="Search by name or expertise..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Subject Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Select value={subjectFilter} onValueChange={setSubjectFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {getAvailableSubjects().map(subject => (
                    <SelectItem key={subject} value={subject}>
                      {subject.replace('_', ' ').toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Tier Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Tutor Level</label>
              <Select value={tierFilter} onValueChange={setTierFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-muted-foreground">
            Showing {filteredTutors.length} of {tutors.length} tutors
          </p>
        </div>

        {/* Tutors Grid */}
        {filteredTutors.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2">
              {tutors.length === 0 ? 'No approved tutors available' : 'No tutors found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {tutors.length === 0 
                ? 'Tutors are currently being reviewed. Please check back soon!'
                : 'Try adjusting your search criteria or filters'
              }
            </p>
            {tutors.length > 0 && (
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('')
                  setSubjectFilter('all')
                  setTierFilter('all')
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTutors.map((tutor) => (
              <div
                key={tutor.id}
                className="bg-background rounded-lg p-6 shadow-sm border border-border hover:shadow-md transition-shadow"
              >
                {/* Tutor Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-semibold">{tutor.full_name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTierBadgeColor(tutor.tutor_tier)}`}>
                        {tutor.tutor_tier === 'verified' ? '✓ Verified' : 'Standard'}
                      </span>
                      {tutor.experience_years && (
                        <span className="text-sm text-muted-foreground">
                          {tutor.experience_years}+ years exp.
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-primary">
                      {formatRate(tutor.hourly_rate)}
                    </div>
                  </div>
                </div>

                {/* Subjects */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Subjects</p>
                  <p className="text-sm">{formatSubjects(tutor.subjects)}</p>
                </div>

                {/* Bio */}
                {tutor.bio && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {tutor.bio}
                    </p>
                  </div>
                )}

                {/* Languages */}
                {tutor.languages && tutor.languages.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm">
                      <span className="font-medium">Languages:</span>{' '}
                      {tutor.languages.join(', ')}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2">
                  <Link href={`/tutors/${tutor.id}`} className="flex-1">
                    <Button variant="outline" className="w-full">
                      View Profile
                    </Button>
                  </Link>
                  <Link href={`/tutors/${tutor.id}/book`} className="flex-1">
                    <Button className="w-full">
                      Book Session
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Call to Action */}
        {tutors.length > 0 && (
          <div className="text-center mt-12 py-8 bg-background rounded-lg">
            <h3 className="text-2xl font-semibold mb-2">Can&apos;t find the right tutor?</h3>
            <p className="text-muted-foreground mb-4">
              We&apos;re constantly adding new qualified tutors to our platform
            </p>
            <div className="space-x-4">
              <Link href="/contact">
                <Button variant="outline">Request a Subject</Button>
              </Link>
              <Link href="/tutors/apply">
                <Button>Become a Tutor</Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}