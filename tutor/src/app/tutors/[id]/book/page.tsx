'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
  approval_status: 'pending' | 'approved' | 'rejected'
}

interface AvailabilitySlot {
  slot_id: string
  start_time: string
  end_time: string
  local_start_time: string
  local_end_time: string
  duration_minutes: number
}

interface CalendarDay {
  date: Date
  dateString: string
  dayName: string
  isToday: boolean
  isPast: boolean
  slots: AvailabilitySlot[]
}

export default function BookTutorPage() {
  const params = useParams()
  const router = useRouter()
  const [tutor, setTutor] = useState<Tutor | null>(null)
  const [calendarWeeks, setCalendarWeeks] = useState<CalendarDay[][]>([])
  const [selectedSlot, setSelectedSlot] = useState<AvailabilitySlot | null>(null)
  const [loading, setLoading] = useState(true)
  const [loadingSlots, setLoadingSlots] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userTimezone, setUserTimezone] = useState<string>('UTC')
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(new Date())

  const supabase = createClient()

  useEffect(() => {
    setUserTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
    
    // Set current week to start of this week (Sunday)
    const today = new Date()
    const startOfWeek = new Date(today)
    startOfWeek.setDate(today.getDate() - today.getDay())
    startOfWeek.setHours(0, 0, 0, 0)
    setCurrentWeekStart(startOfWeek)
    
    if (params?.id) {
      fetchTutorAndAvailability(params.id as string, startOfWeek)
    }
  }, [params?.id])

  const fetchTutorAndAvailability = async (tutorId: string, weekStart: Date) => {
    try {
      setLoading(true)
      
      // Fetch tutor profile
      const { data: tutorData, error: tutorError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', tutorId)
        .eq('role', 'tutor')
        .eq('approval_status', 'approved')
        .single()

      if (tutorError) {
        if (tutorError.code === 'PGRST116') {
          setError('Tutor not found or not approved')
        } else {
          throw tutorError
        }
        return
      }

      setTutor(tutorData)
      await fetchWeekAvailability(tutorId, weekStart)
      
    } catch (error) {
      console.error('Error fetching tutor:', error)
      setError('Failed to load tutor profile. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const fetchWeekAvailability = async (tutorId: string, weekStart: Date) => {
    try {
      setLoadingSlots(true)
      
      const weeks: CalendarDay[][] = []
      const today = new Date()
      
      // Generate 4 weeks starting from weekStart
      for (let week = 0; week < 4; week++) {
        const weekDays: CalendarDay[] = []
        
        for (let day = 0; day < 7; day++) {
          const currentDate = new Date(weekStart)
          currentDate.setDate(weekStart.getDate() + (week * 7) + day)
          
          const dateString = currentDate.toISOString().split('T')[0]
          const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'short' })
          const isToday = currentDate.toDateString() === today.toDateString()
          const isPast = currentDate < today && !isToday
          
          // Fetch availability for this day
          const { data: slotsData, error: slotsError } = await supabase
            .rpc('get_tutor_availability', {
              target_tutor_id: tutorId,
              target_date: dateString,
              user_timezone: userTimezone
            })

          const slots: AvailabilitySlot[] = slotsError ? [] : (slotsData || [])

          weekDays.push({
            date: currentDate,
            dateString,
            dayName,
            isToday,
            isPast,
            slots
          })
        }
        
        weeks.push(weekDays)
      }
      
      setCalendarWeeks(weeks)
      
    } catch (error) {
      console.error('Error fetching availability:', error)
    } finally {
      setLoadingSlots(false)
    }
  }

  const handlePreviousWeek = () => {
    const newWeekStart = new Date(currentWeekStart)
    newWeekStart.setDate(currentWeekStart.getDate() - 7)
    
    // Don't go before today
    const today = new Date()
    const startOfThisWeek = new Date(today)
    startOfThisWeek.setDate(today.getDate() - today.getDay())
    startOfThisWeek.setHours(0, 0, 0, 0)
    
    if (newWeekStart >= startOfThisWeek) {
      setCurrentWeekStart(newWeekStart)
      if (tutor) {
        fetchWeekAvailability(tutor.id, newWeekStart)
      }
    }
  }

  const handleNextWeek = () => {
    const newWeekStart = new Date(currentWeekStart)
    newWeekStart.setDate(currentWeekStart.getDate() + 7)
    setCurrentWeekStart(newWeekStart)
    if (tutor) {
      fetchWeekAvailability(tutor.id, newWeekStart)
    }
  }

  const handleSlotSelect = (slot: AvailabilitySlot) => {
    setSelectedSlot(slot)
  }

  const handleBookSession = () => {
    if (selectedSlot && tutor) {
      // Navigate to confirmation page with slot info
      const bookingData = {
        tutorId: tutor.id,
        slotId: selectedSlot.slot_id,
        startTime: selectedSlot.start_time,
        endTime: selectedSlot.end_time,
        localStartTime: selectedSlot.local_start_time,
        localEndTime: selectedSlot.local_end_time
      }
      
      const queryParams = new URLSearchParams({
        slot: JSON.stringify(bookingData)
      })
      
      router.push(`/tutors/${tutor.id}/book/confirm?${queryParams}`)
    }
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const formatTimeSlot = (slot: AvailabilitySlot) => {
    return `${slot.local_start_time} - ${slot.local_end_time}`
  }

  const isSlotSelected = (slot: AvailabilitySlot) => {
    return selectedSlot?.slot_id === slot.slot_id
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading booking calendar...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !tutor) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h2 className="text-2xl font-semibold mb-2">Unable to Load Booking</h2>
            <p className="text-muted-foreground mb-6">
              {error || 'This tutor may not be available for booking.'}
            </p>
            <div className="space-x-4">
              <Link href="/tutors">
                <Button>Browse Other Tutors</Button>
              </Link>
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold">ProTutor</h1>
          </Link>
          <nav className="space-x-4">
            <Link href="/tutors">
              <Button variant="outline">Browse Tutors</Button>
            </Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="text-sm text-muted-foreground">
            <Link href="/tutors" className="hover:text-foreground">Tutors</Link>
            <span className="mx-2">→</span>
            <Link href={`/tutors/${tutor.id}`} className="hover:text-foreground">{tutor.full_name}</Link>
            <span className="mx-2">→</span>
            <span>Book Session</span>
          </nav>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Tutor Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-background rounded-lg p-6 shadow-sm border sticky top-4">
              <div className="text-center mb-4">
                <h2 className="text-xl font-semibold mb-1">{tutor.full_name}</h2>
                <div className="text-lg font-bold text-primary mb-2">
                  {formatRate(tutor.hourly_rate)}
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  tutor.tutor_tier === 'verified' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {tutor.tutor_tier === 'verified' ? '✓ Verified' : 'Standard'}
                </span>
              </div>

              {selectedSlot && (
                <div className="border-t pt-4">
                  <h3 className="font-medium mb-2">Selected Time</h3>
                  <div className="p-3 bg-primary/5 rounded-lg border border-primary/20 mb-4">
                    <div className="font-medium text-primary">
                      {new Date(selectedSlot.start_time).toLocaleDateString('en-US', { 
                        weekday: 'long',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatTimeSlot(selectedSlot)}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {userTimezone}
                    </div>
                  </div>
                  <Button onClick={handleBookSession} className="w-full">
                    Continue to Book
                  </Button>
                </div>
              )}

              <div className="border-t pt-4 mt-4">
                <Link href={`/tutors/${tutor.id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    View Full Profile
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Calendar */}
          <div className="lg:col-span-3">
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold">Select a Time</h1>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handlePreviousWeek}
                    disabled={loadingSlots}
                  >
                    ← Previous
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleNextWeek}
                    disabled={loadingSlots}
                  >
                    Next →
                  </Button>
                </div>
              </div>

              {loadingSlots ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-2 text-muted-foreground text-sm">Loading availability...</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-8">
                  {calendarWeeks.map((week, weekIndex) => (
                    <div key={weekIndex}>
                      <div className="text-sm font-medium text-muted-foreground mb-3">
                        Week of {week[0].date.toLocaleDateString('en-US', { 
                          month: 'long',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                      
                      <div className="grid grid-cols-7 gap-4">
                        {week.map((day, dayIndex) => (
                          <div key={dayIndex} className="min-h-[200px]">
                            <div className="text-center mb-3">
                              <div className="text-sm font-medium">{day.dayName}</div>
                              <div className={`text-lg ${day.isToday ? 'text-primary font-bold' : ''} ${day.isPast ? 'text-muted-foreground' : ''}`}>
                                {day.date.getDate()}
                              </div>
                            </div>
                            
                            <div className="space-y-1">
                              {day.isPast ? (
                                <div className="text-xs text-muted-foreground text-center py-2">
                                  Past
                                </div>
                              ) : day.slots.length === 0 ? (
                                <div className="text-xs text-muted-foreground text-center py-2">
                                  No availability
                                </div>
                              ) : (
                                day.slots.map((slot) => (
                                  <button
                                    key={slot.slot_id}
                                    onClick={() => handleSlotSelect(slot)}
                                    className={`w-full text-xs p-2 rounded border transition-colors ${
                                      isSlotSelected(slot)
                                        ? 'bg-primary text-primary-foreground border-primary'
                                        : 'bg-background hover:bg-primary/5 hover:border-primary/30 border-border'
                                    }`}
                                  >
                                    {slot.local_start_time}
                                  </button>
                                ))
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {!loadingSlots && calendarWeeks.length > 0 && (
                <div className="mt-8 text-center">
                  <p className="text-sm text-muted-foreground mb-4">
                    All times shown in your local timezone ({userTimezone})
                  </p>
                  <div className="text-xs text-muted-foreground">
                    Need a different time? <Link href="/contact" className="text-primary hover:underline">Contact us</Link> to request custom availability.
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}