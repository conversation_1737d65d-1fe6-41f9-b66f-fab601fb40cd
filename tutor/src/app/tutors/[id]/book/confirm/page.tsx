'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'

interface Tutor {
  id: string
  full_name: string
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
}

interface BookingData {
  tutorId: string
  slotId: string
  startTime: string
  endTime: string
  localStartTime: string
  localEndTime: string
}

export default function BookingConfirmPage() {
  // const params = useParams()
  // const tutorId = params.id as string
  const router = useRouter()
  const searchParams = useSearchParams()
  const [tutor, setTutor] = useState<Tutor | null>(null)
  const [bookingData, setBookingData] = useState<BookingData | null>(null)
  const [sessionDuration, setSessionDuration] = useState(60) // Default 1 hour
  const [prepNotes, setPrepNotes] = useState('')
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userTimezone, setUserTimezone] = useState<string>('UTC')

  const supabase = createClient()

  useEffect(() => {
    setUserTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
    
    // Parse booking data from query params
    const slotParam = searchParams.get('slot')
    if (slotParam) {
      try {
        const parsedData = JSON.parse(slotParam) as BookingData
        setBookingData(parsedData)
        fetchTutorData(parsedData.tutorId)
      } catch (error) {
        console.error('Error parsing booking data:', error)
        setError('Invalid booking data')
        setLoading(false)
      }
    } else {
      setError('No booking data provided')
      setLoading(false)
    }
  }, [searchParams])

  const fetchTutorData = async (tutorId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, tutor_tier, hourly_rate')
        .eq('id', tutorId)
        .eq('role', 'tutor')
        .eq('approval_status', 'approved')
        .single()

      if (error) throw error
      setTutor(data)
    } catch (error) {
      console.error('Error fetching tutor:', error)
      setError('Failed to load tutor information')
    } finally {
      setLoading(false)
    }
  }

  const calculateSessionCost = () => {
    if (!tutor?.hourly_rate) return 0
    const hourlyRateInDollars = tutor.hourly_rate / 100
    const sessionHours = sessionDuration / 60
    return Math.round(hourlyRateInDollars * sessionHours * 100) / 100
  }

  // const getCommissionAmount = () => {
  //   const sessionCost = calculateSessionCost()
  //   return Math.round(sessionCost * 0.18 * 100) / 100 // 18% commission
  // }

  // const getTutorEarnings = () => {
  //   const sessionCost = calculateSessionCost()
  //   const commission = getCommissionAmount()
  //   return Math.round((sessionCost - commission) * 100) / 100
  // }

  const handleSubmitBooking = async () => {
    if (!bookingData || !tutor) return

    try {
      setSubmitting(true)
      setError(null)

      // Check if user is authenticated
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        // Redirect to auth with return URL
        const returnUrl = encodeURIComponent(window.location.href)
        router.push(`/auth?redirect=${returnUrl}`)
        return
      }

      // Create the booking using the database function
      const { data: bookingId, error: bookingError } = await supabase
        .rpc('create_booking', {
          target_student_id: user.id,
          target_availability_id: bookingData.slotId,
          session_duration: sessionDuration,
          prep_notes: prepNotes || null
        })

      if (bookingError) {
        console.error('Booking error:', bookingError)
        
        if (bookingError.message.includes('already booked')) {
          setError('This time slot has been taken by another student. Please select a different time.')
        } else if (bookingError.message.includes('not available')) {
          setError('This time slot is no longer available. Please select a different time.')
        } else {
          setError('Failed to create booking. Please try again.')
        }
        return
      }

      // Success! Redirect to booking success page
      router.push(`/bookings/${bookingId}/success`)

    } catch (error) {
      console.error('Error creating booking:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = () => {
    if (!bookingData) return ''
    
    const date = new Date(bookingData.startTime)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTimeRange = () => {
    if (!bookingData) return ''
    return `${bookingData.localStartTime} - ${bookingData.localEndTime}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading booking details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !tutor || !bookingData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h2 className="text-2xl font-semibold mb-2">Booking Error</h2>
            <p className="text-muted-foreground mb-6">
              {error || 'Unable to load booking information.'}
            </p>
            <div className="space-x-4">
              <Link href="/tutors">
                <Button>Browse Tutors</Button>
              </Link>
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold">ProTutor</h1>
          </Link>
          <nav className="space-x-4">
            <Link href="/tutors">
              <Button variant="outline">Browse Tutors</Button>
            </Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="text-sm text-muted-foreground">
            <Link href="/tutors" className="hover:text-foreground">Tutors</Link>
            <span className="mx-2">→</span>
            <Link href={`/tutors/${tutor.id}`} className="hover:text-foreground">{tutor.full_name}</Link>
            <span className="mx-2">→</span>
            <Link href={`/tutors/${tutor.id}/book`} className="hover:text-foreground">Book Session</Link>
            <span className="mx-2">→</span>
            <span>Confirm Booking</span>
          </nav>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-background rounded-lg p-8 shadow-sm border">
            <h1 className="text-3xl font-bold mb-8 text-center">Confirm Your Booking</h1>

            {/* Booking Summary */}
            <div className="bg-primary/5 rounded-lg p-6 border border-primary/20 mb-8">
              <h2 className="text-xl font-semibold mb-4">Session Details</h2>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Tutor</span>
                  <span className="font-medium">{tutor.full_name}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Date</span>
                  <span className="font-medium">{formatDateTime()}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Time</span>
                  <span className="font-medium">{formatTimeRange()}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Timezone</span>
                  <span className="font-medium">{userTimezone}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Duration</span>
                  <span className="font-medium">{sessionDuration} minutes</span>
                </div>
                
                <div className="border-t pt-3 mt-3">
                  <div className="flex justify-between text-lg">
                    <span className="font-semibold">Total Cost</span>
                    <span className="font-bold text-primary">${calculateSessionCost().toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Session Options */}
            <div className="space-y-6 mb-8">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Session Duration
                </label>
                <select
                  value={sessionDuration}
                  onChange={(e) => setSessionDuration(Number(e.target.value))}
                  className="w-full p-3 border border-border rounded-md bg-background"
                >
                  <option value={30}>30 minutes - ${tutor.hourly_rate ? ((tutor.hourly_rate / 100) * 0.5).toFixed(2) : '0.00'}</option>
                  <option value={60}>1 hour - ${tutor.hourly_rate ? (tutor.hourly_rate / 100).toFixed(2) : '0.00'}</option>
                  <option value={90}>1.5 hours - ${tutor.hourly_rate ? ((tutor.hourly_rate / 100) * 1.5).toFixed(2) : '0.00'}</option>
                  <option value={120}>2 hours - ${tutor.hourly_rate ? ((tutor.hourly_rate / 100) * 2).toFixed(2) : '0.00'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Preparation Notes for Tutor (Optional)
                </label>
                <Textarea
                  value={prepNotes}
                  onChange={(e) => setPrepNotes(e.target.value)}
                  placeholder="Let your tutor know what you'd like to focus on, any specific topics you're struggling with, or materials you'd like to review..."
                  className="min-h-[100px]"
                  maxLength={500}
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {prepNotes.length}/500 characters
                </div>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-6">
                {error}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex-1"
                disabled={submitting}
              >
                Back to Calendar
              </Button>
              <Button
                onClick={handleSubmitBooking}
                className="flex-1"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Booking...
                  </>
                ) : (
                  'Confirm Booking'
                )}
              </Button>
            </div>

            {/* Terms */}
            <div className="mt-8 pt-6 border-t text-center">
              <p className="text-xs text-muted-foreground">
                By confirming this booking, you agree to our{' '}
                <Link href="/terms" className="text-primary hover:underline">Terms of Service</Link>
                {' '}and{' '}
                <Link href="/cancellation" className="text-primary hover:underline">Cancellation Policy</Link>.
                Sessions can be cancelled up to 24 hours before the start time for a full refund.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}