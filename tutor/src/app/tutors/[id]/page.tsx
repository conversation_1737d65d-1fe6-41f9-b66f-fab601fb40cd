'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
  education: string | null
  certifications: string[] | null
  approval_status: 'pending' | 'approved' | 'rejected'
  created_at: string
}

interface NextAvailableSlot {
  slot_id: string
  start_time: string
  end_time: string
  local_start_time: string
  local_end_time: string
}

export default function TutorProfilePage() {
  const params = useParams()
  const router = useRouter()
  const [tutor, setTutor] = useState<Tutor | null>(null)
  const [nextAvailable, setNextAvailable] = useState<NextAvailableSlot | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userTimezone, setUserTimezone] = useState<string>('UTC')

  const supabase = createClient()

  useEffect(() => {
    // Get user's timezone
    setUserTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
    
    if (params?.id) {
      fetchTutorData(params.id as string)
    }
  }, [params?.id])

  const fetchTutorData = async (tutorId: string) => {
    try {
      setLoading(true)
      
      // Fetch tutor profile
      const { data: tutorData, error: tutorError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', tutorId)
        .eq('role', 'tutor')
        .eq('approval_status', 'approved')
        .single()

      if (tutorError) {
        if (tutorError.code === 'PGRST116') {
          setError('Tutor not found or not approved')
        } else {
          throw tutorError
        }
        return
      }

      setTutor(tutorData)

      // Fetch next available slot
      await fetchNextAvailable(tutorId)
      
    } catch (error) {
      console.error('Error fetching tutor:', error)
      setError('Failed to load tutor profile. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const fetchNextAvailable = async (tutorId: string) => {
    try {
      // Get next 7 days of availability
      const today = new Date()
      const nextWeek = new Date(today)
      nextWeek.setDate(today.getDate() + 7)

      for (let i = 0; i < 7; i++) {
        const checkDate = new Date(today)
        checkDate.setDate(today.getDate() + i)
        
        const { data, error } = await supabase
          .rpc('get_tutor_availability', {
            target_tutor_id: tutorId,
            target_date: checkDate.toISOString().split('T')[0],
            user_timezone: userTimezone
          })

        if (error) {
          console.error('Error fetching availability:', error)
          continue
        }

        if (data && data.length > 0) {
          setNextAvailable(data[0])
          break
        }
      }
    } catch (error) {
      console.error('Error fetching next available slot:', error)
    }
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const formatSubjects = (subjects: string[] | null) => {
    if (!subjects || subjects.length === 0) return 'No subjects listed'
    return subjects
      .map(subject => subject.replace('_', ' ').toUpperCase())
      .join(', ')
  }

  const getTierBadgeColor = (tier: string) => {
    return tier === 'verified' 
      ? 'bg-green-100 text-green-800 border-green-200' 
      : 'bg-blue-100 text-blue-800 border-blue-200'
  }

  const formatNextAvailable = (slot: NextAvailableSlot) => {
    const date = new Date(slot.start_time)
    const isToday = date.toDateString() === new Date().toDateString()
    const isTomorrow = date.toDateString() === new Date(Date.now() + 86400000).toDateString()
    
    let dayText = date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })
    if (isToday) dayText = 'Today'
    else if (isTomorrow) dayText = 'Tomorrow'
    
    return `${dayText} at ${slot.local_start_time}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading tutor profile...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !tutor) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h2 className="text-2xl font-semibold mb-2">Tutor Not Found</h2>
            <p className="text-muted-foreground mb-6">
              {error || 'This tutor may not be available or approved yet.'}
            </p>
            <div className="space-x-4">
              <Link href="/tutors">
                <Button>Browse Other Tutors</Button>
              </Link>
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold">ProTutor</h1>
          </Link>
          <nav className="space-x-4">
            <Link href="/tutors">
              <Button variant="outline">Browse Tutors</Button>
            </Link>
            <Link href="/dashboard">
              <Button variant="outline">Dashboard</Button>
            </Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="text-sm text-muted-foreground">
            <Link href="/tutors" className="hover:text-foreground">Tutors</Link>
            <span className="mx-2">→</span>
            <span>{tutor.full_name}</span>
          </nav>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Profile Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Header Card */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold">{tutor.full_name}</h1>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getTierBadgeColor(tutor.tutor_tier)}`}>
                      {tutor.tutor_tier === 'verified' ? '✓ Verified Tutor' : 'Standard Tutor'}
                    </span>
                  </div>
                  
                  {tutor.experience_years && (
                    <p className="text-muted-foreground mb-2">
                      {tutor.experience_years}+ years of tutoring experience
                    </p>
                  )}
                  
                  <div className="text-2xl font-bold text-primary">
                    {formatRate(tutor.hourly_rate)}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex flex-col gap-2 sm:w-40">
                  <Link href={`/tutors/${tutor.id}/book`}>
                    <Button size="lg" className="w-full">
                      Book Session
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" className="w-full">
                    Message Tutor
                  </Button>
                </div>
              </div>
            </div>

            {/* About Section */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <h2 className="text-xl font-semibold mb-4">About {tutor.full_name}</h2>
              {tutor.bio ? (
                <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                  {tutor.bio}
                </p>
              ) : (
                <p className="text-muted-foreground italic">
                  This tutor hasn&apos;t added a bio yet.
                </p>
              )}
            </div>

            {/* Subjects & Expertise */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <h2 className="text-xl font-semibold mb-4">Subjects & Expertise</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Teaching Subjects</h3>
                  <p className="text-muted-foreground">{formatSubjects(tutor.subjects)}</p>
                </div>
                
                {tutor.education && (
                  <div>
                    <h3 className="font-medium mb-2">Education</h3>
                    <p className="text-muted-foreground">{tutor.education}</p>
                  </div>
                )}
                
                {tutor.certifications && tutor.certifications.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Certifications</h3>
                    <ul className="text-muted-foreground">
                      {tutor.certifications.map((cert, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-primary rounded-full"></span>
                          {cert}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {tutor.languages && tutor.languages.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Languages</h3>
                    <p className="text-muted-foreground">{tutor.languages.join(', ')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Next Available */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <h3 className="font-semibold mb-4">Next Available</h3>
              {nextAvailable ? (
                <div className="space-y-4">
                  <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
                    <div className="text-lg font-medium text-primary">
                      {formatNextAvailable(nextAvailable)}
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {userTimezone}
                    </div>
                  </div>
                  <Link href={`/tutors/${tutor.id}/book`}>
                    <Button className="w-full">
                      Book This Time
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground mb-4">
                    No availability in the next 7 days
                  </p>
                  <Link href={`/tutors/${tutor.id}/book`}>
                    <Button variant="outline" className="w-full">
                      View Full Calendar
                    </Button>
                  </Link>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <h3 className="font-semibold mb-4">Quick Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Response Time</span>
                  <span className="font-medium">Usually within 1 hour</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Experience</span>
                  <span className="font-medium">
                    {tutor.experience_years ? `${tutor.experience_years}+ years` : 'Not specified'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Member Since</span>
                  <span className="font-medium">
                    {new Date(tutor.created_at).toLocaleDateString('en-US', { 
                      month: 'short', 
                      year: 'numeric' 
                    })}
                  </span>
                </div>
              </div>
            </div>

            {/* Trust & Safety */}
            <div className="bg-background rounded-lg p-6 shadow-sm border">
              <h3 className="font-semibold mb-4">Trust & Safety</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span className="text-sm">Identity Verified</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span className="text-sm">Background Checked</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span className="text-sm">Qualifications Verified</span>
                </div>
                {tutor.tutor_tier === 'verified' && (
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm">Premium Verified Tutor</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}