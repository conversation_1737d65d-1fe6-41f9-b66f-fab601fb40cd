'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

// Animation component for scroll-triggered animations
const AnimatedSection = ({ children, delay = 0, className = "" }: { 
  children: React.ReactNode, 
  delay?: number,
  className?: string 
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'} ${className}`}>
      {children}
    </div>
  )
}

export default function Homepage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-gray-900">
                Popless
              </Link>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                Features
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                Pricing
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
                About
              </Link>
              <Link href="/auth" className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left side - Hero content */}
            <AnimatedSection>
              <div className="space-y-8">
                <div className="space-y-4">
                  <h1 
                    className="font-bold text-gray-900 leading-tight"
                    style={{
                      fontSize: 'clamp(2.5rem, 6vw, 4rem)',
                      lineHeight: 'clamp(1.1, 1.2, 1.3)'
                    }}
                  >
                    The all-in-one tutor platform for private tutors and group classes
                  </h1>
                  <p 
                    className="text-gray-600 leading-relaxed"
                    style={{
                      fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
                      lineHeight: 'clamp(1.5, 1.6, 1.7)'
                    }}
                  >
                    Popless is the best tutoring platform to manage and grow your tutoring business. 
                    Power your teaching and students from an all-in-one dashboard.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/auth">
                    <button 
                      className="bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200 w-full sm:w-auto"
                      style={{
                        fontSize: 'clamp(0.875rem, 2vw, 1rem)',
                        padding: 'clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 4vw, 2rem)'
                      }}
                    >
                      Start for free
                    </button>
                  </Link>
                  <button 
                    className="border-2 border-gray-300 text-gray-700 rounded-lg font-medium hover:border-gray-400 transition-colors duration-200 w-full sm:w-auto"
                    style={{
                      fontSize: 'clamp(0.875rem, 2vw, 1rem)',
                      padding: 'clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 4vw, 2rem)'
                    }}
                  >
                    Watch demo
                  </button>
                </div>

                {/* Trust indicators */}
                <div className="pt-8">
                  <p className="text-sm text-gray-500 mb-4">Trusted by 10,000+ tutors worldwide</p>
                  <div className="flex items-center space-x-6 opacity-60">
                    <div className="text-gray-400 font-semibold">★★★★★</div>
                    <div className="text-sm text-gray-500">4.9/5 rating</div>
                  </div>
                </div>
              </div>
            </AnimatedSection>

            {/* Right side - Hero image/dashboard preview */}
            <AnimatedSection delay={200}>
              <div className="relative">
                <div 
                  className="relative overflow-hidden rounded-2xl shadow-2xl bg-white border"
                  style={{
                    height: 'clamp(400px, 60vh, 700px)',
                    width: '100%'
                  }}
                >
                  {/* Dashboard mockup */}
                  <div 
                    className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center"
                    style={{
                      width: '110%',
                      height: '110%',
                      left: '-5%',
                      top: '-5%'
                    }}
                  >
                    <div className="text-center">
                      <div className="text-6xl mb-4">📊</div>
                      <p className="text-xl font-semibold text-gray-700">Tutor Dashboard</p>
                      <p className="text-gray-500">Complete platform preview</p>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h2 
                className="font-bold text-gray-900 mb-4"
                style={{
                  fontSize: 'clamp(2rem, 5vw, 3rem)',
                  lineHeight: 'clamp(1.2, 1.3, 1.4)'
                }}
              >
                Everything you need to run your tutoring business
              </h2>
              <p 
                className="text-gray-600 max-w-3xl mx-auto"
                style={{
                  fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
                  lineHeight: 'clamp(1.5, 1.6, 1.7)'
                }}
              >
                From scheduling to payments, student management to analytics - 
                we've got everything covered in one powerful platform.
              </p>
            </div>
          </AnimatedSection>

          {/* Feature grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📅",
                title: "Smart Scheduling",
                description: "Automated booking system with calendar integration and availability management."
              },
              {
                icon: "💳",
                title: "Secure Payments",
                description: "Built-in payment processing with automatic invoicing and financial tracking."
              },
              {
                icon: "👥",
                title: "Student Management",
                description: "Comprehensive student profiles with progress tracking and communication tools."
              },
              {
                icon: "📊",
                title: "Analytics Dashboard",
                description: "Detailed insights into your business performance and student engagement."
              },
              {
                icon: "🎥",
                title: "Virtual Classrooms",
                description: "Integrated video conferencing with screen sharing and recording capabilities."
              },
              {
                icon: "📚",
                title: "Resource Library",
                description: "Organize and share teaching materials with your students seamlessly."
              }
            ].map((feature, index) => (
              <AnimatedSection key={index} delay={index * 100}>
                <div className="p-6 rounded-xl border border-gray-100 hover:border-gray-200 transition-colors duration-200">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 
              className="font-bold text-white mb-6"
              style={{
                fontSize: 'clamp(2rem, 5vw, 3rem)',
                lineHeight: 'clamp(1.2, 1.3, 1.4)'
              }}
            >
              Ready to transform your tutoring business?
            </h2>
            <p 
              className="text-gray-300 mb-8"
              style={{
                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
                lineHeight: 'clamp(1.5, 1.6, 1.7)'
              }}
            >
              Join thousands of tutors who have already made the switch to Popless.
            </p>
            <Link href="/auth">
              <button 
                className="bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
                style={{
                  fontSize: 'clamp(0.875rem, 2vw, 1rem)',
                  padding: 'clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 4vw, 2rem)'
                }}
              >
                Get started for free
              </button>
            </Link>
          </AnimatedSection>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 mb-4">Popless</div>
            <p className="text-gray-600 mb-6">The all-in-one tutor platform</p>
            <div className="flex justify-center space-x-6">
              <Link href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                Privacy
              </Link>
              <Link href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                Terms
              </Link>
              <Link href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
