'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

// Animation component for scroll-triggered animations
const AnimatedSection = ({ children, delay = 0, className = "" }: { 
  children: React.ReactNode, 
  delay?: number,
  className?: string 
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'} ${className}`}>
      {children}
    </div>
  )
}

export default function PoplessClone() {
  return (
    <div className="min-h-screen bg-white font-inter">
      {/* Navigation - Exact Popless structure */}
      <nav className="fixed top-0 w-full bg-white z-50 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo section - matching Popless */}
            <div className="flex items-center space-x-3">
              {/* Hamburger menu icon */}
              <div className="bg-gray-200 rounded-lg p-2">
                <div className="space-y-1">
                  <div className="w-4 h-0.5 bg-gray-800 rounded-full"></div>
                  <div className="w-4 h-0.5 bg-gray-800 rounded-full"></div>
                </div>
              </div>
              
              {/* Popless logo */}
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-black rounded flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 32 32" className="text-white">
                    <path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="currentColor"/>
                    <path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="currentColor"/>
                  </svg>
                </div>
                <span className="text-lg font-extrabold text-gray-900 tracking-tight">Popless</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section - Black background like Popless */}
      <section className="pt-24 pb-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left side - Hero content matching Popless exactly */}
            <AnimatedSection>
              <div className="space-y-8">
                <div className="space-y-6">
                  {/* Eyebrow text */}
                  <p className="text-xl font-medium text-gray-300 leading-relaxed">
                    Tutor on a trusted platform.
                  </p>
                  
                  {/* Main headline */}
                  <h1 className="text-6xl font-bold text-white leading-tight tracking-tight">
                    Powerful tutoring and classes.
                  </h1>
                  
                  {/* Description */}
                  <p className="text-xl text-white leading-relaxed">
                    Set your own rate, we'll connect you with you with students, you earn money on your schedule.
                  </p>
                </div>
                
                {/* CTA Button */}
                <div className="pt-4">
                  <Link href="/auth">
                    <button className="bg-white text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-100 transition-colors duration-200 shadow-lg">
                      Get started today
                    </button>
                  </Link>
                </div>
              </div>
            </AnimatedSection>

            {/* Right side - Dashboard screenshots like Popless */}
            <AnimatedSection delay={200}>
              <div className="relative">
                {/* Main dashboard container */}
                <div className="relative h-96 lg:h-[500px]">
                  {/* Messages screenshot */}
                  <div className="absolute top-0 right-0 w-48 h-32 rounded-lg overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl mb-2">💬</div>
                        <p className="text-xs font-medium text-gray-700">Messages</p>
                      </div>
                    </div>
                  </div>

                  {/* Profile screenshot */}
                  <div className="absolute top-16 left-0 w-56 h-40 rounded-lg overflow-hidden shadow-xl">
                    <div className="bg-gradient-to-br from-green-50 to-green-100 h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-3xl mb-2">👨‍🏫</div>
                        <p className="text-sm font-medium text-gray-700">Profile</p>
                      </div>
                    </div>
                  </div>

                  {/* Earnings dashboard */}
                  <div className="absolute bottom-0 right-8 w-64 h-48 rounded-lg overflow-hidden shadow-xl">
                    <div className="bg-white h-full p-4">
                      <div className="text-center mb-4">
                        <div className="text-2xl mb-2">💰</div>
                        <p className="text-sm font-medium text-gray-700">Total Earnings</p>
                      </div>
                      
                      {/* Transaction items */}
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                            <div>
                              <p className="font-medium">Andrew Hawkins</p>
                              <p className="text-gray-500">Jul 23, 2022</p>
                            </div>
                          </div>
                          <span className="text-green-600 font-medium">+ $80.00</span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">P</span>
                            </div>
                            <div>
                              <p className="font-medium">Popless payout</p>
                              <p className="text-gray-500">Jul 21, 2022</p>
                            </div>
                          </div>
                          <span className="text-red-600 font-medium">- $90.00</span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                            <div>
                              <p className="font-medium">Samantha Jane</p>
                              <p className="text-gray-500">Jul 18, 2022</p>
                            </div>
                          </div>
                          <span className="text-green-600 font-medium">+ $70.00</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Date selection */}
                  <div className="absolute top-32 right-16 w-40 h-24 rounded-lg overflow-hidden shadow-md">
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-xl mb-1">📅</div>
                        <p className="text-xs font-medium text-gray-700">Date Selection</p>
                      </div>
                    </div>
                  </div>

                  {/* Checkout meets */}
                  <div className="absolute bottom-16 left-8 w-44 h-28 rounded-lg overflow-hidden shadow-lg">
                    <div className="bg-gradient-to-br from-orange-50 to-orange-100 h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-xl mb-1">🎯</div>
                        <p className="text-xs font-medium text-gray-700">Checkout Meets</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Second section - White background */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
              Power your tutoring and students from an all-in-one dashboard.
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Spend more time teaching and less time managing admin.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                  Get started today
                </button>
              </Link>
              <Link href="/demo">
                <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                  Request a demo
                </button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Features section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-12">
            {/* Feature 1 - Scheduling */}
            <AnimatedSection delay={100}>
              <div className="text-center">
                <div className="mb-6">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg h-48 flex items-center justify-center mb-4">
                      <span className="text-4xl">📅</span>
                    </div>
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Set your price and schedule</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Choose when you tutor and how much you want to charge.
                </p>
              </div>
            </AnimatedSection>

            {/* Feature 2 - Payments */}
            <AnimatedSection delay={200}>
              <div className="text-center">
                <div className="mb-6">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <div className="bg-white rounded-lg border h-48 p-4">
                      <div className="text-center mb-4">
                        <div className="text-3xl mb-2">💰</div>
                        <p className="text-sm font-medium text-gray-700">Earnings</p>
                      </div>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span>Jul 23, 2022</span>
                          <span className="text-green-600">+ $80.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Jul 21, 2022</span>
                          <span className="text-red-600">- $90.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Jul 18, 2022</span>
                          <span className="text-green-600">+ $70.00</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Automated payments and payouts</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Accept payments in 45 currencies.<br />
                  Get paid straight after your meeting.
                </p>
              </div>
            </AnimatedSection>

            {/* Feature 3 - Messaging */}
            <AnimatedSection delay={300}>
              <div className="text-center">
                <div className="mb-6">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg h-48 flex items-center justify-center">
                      <span className="text-4xl">💬</span>
                    </div>
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Private and group messaging</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Securely send messages,<br />
                  photos, videos, and documents.
                </p>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Additional sections matching Popless structure */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
              Integrated whiteboard, messaging, and document sharing.
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Interactive online features makes learning engaging. Bring tricky concepts to life with interactive exercises, drawing diagrams, and annotating homework and practice questions.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                  Sign up for free
                </button>
              </Link>
              <Link href="/features">
                <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                  View all features
                </button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Group classes section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
              Expand your tutoring income<br />with group classes.
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Tutor your students in a group setting. Perfect for<br />
              group tutoring before finals and teaching small classes.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/group-classes">
                <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                  Learn more about classes
                </button>
              </Link>
              <Link href="/auth">
                <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                  Become a tutor
                </button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Final section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
              Take better notes, view your students in one place, and stay on track.
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Featuring powerful note taking and contact management tools to help you stay organized and on top of your tutoring schedule.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                  Sign up for free
                </button>
              </Link>
              <Link href="/demo">
                <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                  Request a demo
                </button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
