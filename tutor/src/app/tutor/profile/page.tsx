'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useRouter } from 'next/navigation'

interface Profile {
  id: string
  full_name: string
  email: string
  role: string
  tutor_tier: 'standard' | 'verified'
  bio?: string
  subjects?: string[]
  education?: string
  experience_years?: number
  certifications?: string[]
  languages?: string[]
}

const SUBJECT_OPTIONS = [
  { value: 'ib_math', label: 'IB Mathematics' },
  { value: 'ib_sciences', label: 'IB Sciences (Biology, Chemistry, Physics)' },
  { value: 'ib_languages', label: 'IB Languages' },
  { value: 'ib_humanities', label: 'IB Humanities (History, Geography, etc.)' },
  { value: 'ap_math', label: 'AP Mathematics' },
  { value: 'ap_sciences', label: 'AP Sciences' },
  { value: 'ap_languages', label: 'AP Languages' },
  { value: 'ap_social_studies', label: 'AP Social Studies' },
  { value: 'other', label: 'Other' }
]

export default function TutorProfilePage() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  
  // Form fields
  const [bio, setBio] = useState('')
  const [subjects, setSubjects] = useState<string[]>([])
  const [education, setEducation] = useState('')
  const [experienceYears, setExperienceYears] = useState<number>(0)
  const [certifications, setCertifications] = useState<string[]>([''])
  const [languages, setLanguages] = useState<string[]>(['English'])

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    async function loadProfile() {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/auth')
        return
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (!profile || profile.role !== 'tutor') {
        router.push('/dashboard')
        return
      }

      setProfile(profile)
      
      // Populate form with existing data
      setBio(profile.bio || '')
      setSubjects(profile.subjects || [])
      setEducation(profile.education || '')
      setExperienceYears(profile.experience_years || 0)
      setCertifications(profile.certifications || [''])
      setLanguages(profile.languages || ['English'])
      
      setLoading(false)
    }

    loadProfile()
  }, [supabase, router])

  const handleSubjectToggle = (subject: string) => {
    setSubjects(prev => 
      prev.includes(subject) 
        ? prev.filter(s => s !== subject)
        : [...prev, subject]
    )
  }

  const handleCertificationChange = (index: number, value: string) => {
    const newCertifications = [...certifications]
    newCertifications[index] = value
    setCertifications(newCertifications)
  }

  const addCertification = () => {
    setCertifications([...certifications, ''])
  }

  const removeCertification = (index: number) => {
    setCertifications(certifications.filter((_, i) => i !== index))
  }

  const handleLanguageChange = (index: number, value: string) => {
    const newLanguages = [...languages]
    newLanguages[index] = value
    setLanguages(newLanguages)
  }

  const addLanguage = () => {
    setLanguages([...languages, ''])
  }

  const removeLanguage = (index: number) => {
    if (languages.length > 1) {
      setLanguages(languages.filter((_, i) => i !== index))
    }
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setMessage('')

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          bio: bio.trim(),
          subjects: subjects.filter(s => s),
          education: education.trim(),
          experience_years: experienceYears,
          certifications: certifications.filter(c => c.trim()),
          languages: languages.filter(l => l.trim())
        })
        .eq('id', profile?.id)

      if (error) throw error

      setMessage('Profile saved successfully!')
      setTimeout(() => router.push('/dashboard'), 2000)
    } catch (error) {
      setMessage(error instanceof Error ? error.message : 'Failed to save profile')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold cursor-pointer hover:text-primary">ProTutor</h1>
          </Link>
          <nav className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline">Dashboard</Button>
            </Link>
          </nav>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card rounded-lg p-6 border">
            <div className="mb-6">
              <h1 className="text-2xl font-bold mb-2">Complete Your Tutor Profile</h1>
              <p className="text-muted-foreground">
                Fill out your profile to help students find you and start accepting bookings.
              </p>
              {profile?.tutor_tier && (
                <div className="mt-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    profile.tutor_tier === 'verified' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {profile.tutor_tier === 'verified' ? '✓ Verified Tutor' : 'Standard Tutor'}
                  </span>
                </div>
              )}
            </div>

            <form onSubmit={handleSave} className="space-y-6">
              {/* Bio */}
              <div className="space-y-2">
                <Label htmlFor="bio">About You</Label>
                <Textarea
                  id="bio"
                  placeholder="Tell students about your teaching experience, approach, and what makes you a great tutor..."
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  rows={4}
                  required
                />
              </div>

              {/* Subjects */}
              <div className="space-y-2">
                <Label>Subjects You Teach</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {SUBJECT_OPTIONS.map((subject) => (
                    <label key={subject.value} className="flex items-center space-x-2 p-2 border rounded hover:bg-secondary/50 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={subjects.includes(subject.value)}
                        onChange={() => handleSubjectToggle(subject.value)}
                        className="rounded"
                      />
                      <span className="text-sm">{subject.label}</span>
                    </label>
                  ))}
                </div>
                {subjects.length === 0 && (
                  <p className="text-sm text-red-600">Please select at least one subject</p>
                )}
              </div>

              {/* Education */}
              <div className="space-y-2">
                <Label htmlFor="education">Education Background</Label>
                <Input
                  id="education"
                  placeholder="e.g. Bachelor's in Mathematics from University of Oxford"
                  value={education}
                  onChange={(e) => setEducation(e.target.value)}
                  required
                />
              </div>

              {/* Experience */}
              <div className="space-y-2">
                <Label htmlFor="experience">Years of Teaching/Tutoring Experience</Label>
                <Input
                  id="experience"
                  type="number"
                  min="0"
                  max="50"
                  value={experienceYears}
                  onChange={(e) => setExperienceYears(parseInt(e.target.value) || 0)}
                  required
                />
              </div>

              {/* Certifications */}
              <div className="space-y-2">
                <Label>Certifications & Qualifications</Label>
                {certifications.map((cert, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="e.g. IB Teacher Training Certificate"
                      value={cert}
                      onChange={(e) => handleCertificationChange(index, e.target.value)}
                    />
                    {certifications.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCertification(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={addCertification}>
                  Add Certification
                </Button>
              </div>

              {/* Languages */}
              <div className="space-y-2">
                <Label>Languages You Speak</Label>
                {languages.map((lang, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="e.g. English"
                      value={lang}
                      onChange={(e) => handleLanguageChange(index, e.target.value)}
                      required={index === 0}
                    />
                    {languages.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeLanguage(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={addLanguage}>
                  Add Language
                </Button>
              </div>

              {message && (
                <div className={`text-center text-sm ${
                  message.includes('successfully') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {message}
                </div>
              )}

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard')}
                  disabled={saving}
                >
                  Back to Dashboard
                </Button>
                <Button 
                  type="submit" 
                  disabled={saving || subjects.length === 0}
                  className="flex-1"
                >
                  {saving ? 'Saving...' : 'Save Profile'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}