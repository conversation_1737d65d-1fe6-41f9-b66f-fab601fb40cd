'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'

interface TutorData {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
}

export default function TutorProfilePage() {
  const params = useParams()
  const tutorId = params.id as string
  const [tutorData, setTutorData] = useState<TutorData | null>(null)
  const [loading, setLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const supabase = createClient()

  const openModal = () => setIsModalOpen(true)
  const closeModal = () => setIsModalOpen(false)

  useEffect(() => {
    if (tutorId) {
      fetchTutorData(tutorId)
    }
  }, [tutorId])

  const fetchTutorData = async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      setTutorData(data)
    } catch (error) {
      console.error('Error fetching tutor:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return '$55'
    return `$${(rateInCents / 100).toFixed(0)}`
  }

  const formatSubjects = (subjects: string[] | null) => {
    if (!subjects || subjects.length === 0) return []
    return subjects.map(subject => subject.replace('_', ' ').toUpperCase())
  }

  const generatePixelAvatar = (name: string, id: string) => {
    // Generate consistent pixel avatar based on name and ID
    const hash = (name + id).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)

    // Use different pixel art styles
    const styles = ['pixel-art', 'pixel-art-neutral', 'avataaars', 'bottts', 'personas']
    const selectedStyle = styles[Math.abs(hash) % styles.length]

    // Generate seed for consistent avatars
    const seed = Math.abs(hash).toString()

    return `https://api.dicebear.com/7.x/${selectedStyle}/svg?seed=${seed}&size=120&backgroundColor=b6e3f4,c0aede,d1d4f9,ffd5dc,ffdfbf`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!tutorData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Tutor not found</h1>
          <p className="text-gray-600">The tutor you&apos;re looking for doesn&apos;t exist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 font-inter">
      <div className="bg-white shadow-xl rounded-2xl p-6 md:p-8 w-full max-w-6xl flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:space-x-8">
        {/* Left Column - Profile Info */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
            <span>Education</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span>Maths</span>
          </div>

          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6 mb-8">
            <Image
              src={generatePixelAvatar(tutorData.full_name, tutorData.id)}
              alt={tutorData.full_name}
              width={120}
              height={120}
              className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover shadow-md bg-white p-1"
            />
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-1">{tutorData.full_name}</h1>
              <div className="flex items-center space-x-2 text-gray-600 mb-2">
                <span className="text-lg">Online Tutor</span>
                {tutorData.tutor_tier === 'verified' && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Verified
                  </span>
                )}
              </div>
              <div className="flex items-center text-yellow-500 mb-2">
                <span className="text-xl font-bold mr-1">4.74</span>
                <span className="text-gray-500 text-sm">(238 reviews)</span>
              </div>
              <p className="text-gray-700 leading-relaxed max-w-lg">
                {tutorData.bio || 'Experienced tutor ready to help you achieve your academic goals.'}
                <button className="text-blue-600 hover:underline ml-1">Show more</button>
              </p>
              <div className="flex space-x-4 mt-4">
                <button className="flex items-center text-gray-600 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  Share
                </button>
                <button className="flex items-center text-gray-600 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                  </svg>
                  Save
                </button>
                <button onClick={openModal} className="flex items-center text-gray-600 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  Message
                </button>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Specialties</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {formatSubjects(tutorData.subjects).length > 0 ? (
                formatSubjects(tutorData.subjects).map((subject, index) => (
                  <div key={index}>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">{subject}</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Expert level instruction and guidance</li>
                    </ul>
                  </div>
                ))
              ) : (
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">General Tutoring</h3>
                  <ul className="list-disc list-inside text-gray-700 space-y-1">
                    <li>Personalized instruction across multiple subjects</li>
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-xl shadow-inner">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Meet your tutor, {tutorData.full_name.split(' ')[0]}</h2>
            <p className="text-gray-700 mb-2">
              <span className="font-semibold">Host on ProTutor</span> since 2021
            </p>
            <ul className="text-gray-700 space-y-1">
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                238 reviews
              </li>
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Identity verified
              </li>
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                4 hr response time
              </li>
            </ul>
            <p className="text-gray-700 mt-4">
              Languages spoken: <span className="font-semibold">
                {tutorData.languages && tutorData.languages.length > 0 
                  ? tutorData.languages.join(', ') 
                  : 'English'
                }
              </span>
            </p>
          </div>
        </div>

        {/* Right Column - Schedule & Message */}
        <div className="lg:w-96 p-6 bg-white border border-gray-200 rounded-xl shadow-lg flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <span className="text-lg font-semibold text-gray-800">Session {formatRate(tutorData.hourly_rate)} / 60 minutes</span>
            <button className="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>

          <div className="text-gray-600 mb-4">
            <p>Sunday, May 1 · 8:15 AM - 9:15 AM (PST)</p>
          </div>

          {/* Schedule Grid */}
          <div className="grid grid-cols-7 gap-1 text-center text-sm mb-6">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="font-medium text-gray-700">{day}</div>
            ))}
            {[...Array(3)].map((_, i) => <div key={`empty-${i}`} className="p-1"></div>)}
            {[1, 2, 3, 4, 5, 6, 7].map((date) => (
              <div key={date} className={`p-1 rounded-md ${date === 1 ? 'bg-blue-500 text-white' : 'text-gray-700'}`}>
                {date}
              </div>
            ))}
          </div>

          {/* Time Slots */}
          <div className="space-y-3 mb-6">
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>7:00 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>7:15 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>7:30 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>7:45 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>8:00 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
            <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
              <span>8:15 AM</span>
              <span className="font-semibold text-gray-900">{formatRate(tutorData.hourly_rate)}</span>
            </div>
          </div>

          <button
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md mb-4"
          >
            Select and review
          </button>

          <p className="text-gray-500 text-xs text-center">
            Cancel up to 24 hours before the start time for a full refund.
          </p>
        </div>
      </div>

      {/* Message Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Send {tutorData.full_name.split(' ')[0]} a message</h2>
              <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 mb-4">
              Hey {tutorData.full_name.split(' ')[0]}, could you help me with a few equations before I tackle my exams? I&apos;m not super confident I know enough to get the result I&apos;m looking for, which is why I&apos;m reaching out to you.
            </p>
            <p className="text-gray-700 mb-6">
              I&apos;ll send through some example questions so you get a better idea of how you could help me. Thank you!
            </p>
            <button
              onClick={closeModal}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md"
            >
              Send message
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
