'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import VerificationRequirements from '@/components/VerificationRequirements'
import Link from 'next/link'
import type { User } from '@supabase/supabase-js'

type UserRole = 'student' | 'tutor' | 'admin'
type ApprovalStatus = 'pending' | 'approved' | 'rejected'

interface Profile {
  id: string
  email: string
  full_name: string
  roles: UserRole[]
  student_status: ApprovalStatus
  tutor_status: ApprovalStatus | null
  admin_status: ApprovalStatus | null
  tutor_tier?: 'standard' | 'verified'
  hourly_rate?: number
  bio?: string
  subjects?: string[]
  education?: string
  experience_years?: number
  certifications?: string[]
  languages?: string[]
  avatar_url?: string
  phone_number?: string
  date_of_birth?: string
  created_at: string
  updated_at: string
}

interface RoleStatus {
  role: UserRole
  status: ApprovalStatus | null
  can_access: boolean
}

export default function MultiRoleDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [roleStatuses, setRoleStatuses] = useState<RoleStatus[]>([])
  const [activeRole, setActiveRole] = useState<UserRole>('student')
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    loadUserData()
  }, [])

  const loadUserData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)

      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
        
        if (!profile || !profile.roles || profile.roles.length === 0) {
          // No roles yet, redirect to role management
          window.location.href = '/roles'
          return
        }
        
        setProfile(profile)
        
        // Set default active role to first accessible role
        const { data: statuses } = await supabase
          .rpc('get_user_role_status', { user_id: user.id })
        
        if (statuses) {
          setRoleStatuses(statuses)
          const firstAccessibleRole = statuses.find((s: any) => s.can_access)?.role || 'student'
          setActiveRole(firstAccessibleRole)
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getAccessibleRoles = () => {
    return roleStatuses.filter(status => status.can_access)
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    window.location.href = '/'
  }

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'student': return '📚'
      case 'tutor': return '👨‍🏫'
      case 'admin': return '⚙️'
      default: return '👤'
    }
  }

  const getRoleTitle = (role: UserRole) => {
    switch (role) {
      case 'student': return 'Student'
      case 'tutor': return 'Tutor'
      case 'admin': return 'Admin'
      default: return 'User'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Please sign in</h1>
          <Button onClick={() => window.location.href = '/auth'}>
            Go to Sign In
          </Button>
        </div>
      </div>
    )
  }

  const accessibleRoles = getAccessibleRoles()

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold cursor-pointer hover:text-primary">ProTutor Dashboard</h1>
          </Link>
          <div className="flex items-center space-x-4">
            <Link href="/roles">
              <Button variant="outline" size="sm">
                Manage Roles
              </Button>
            </Link>
            <span className="text-sm text-muted-foreground">
              Welcome, {profile?.full_name || user.email}
            </span>
            <Button variant="outline" onClick={handleSignOut}>
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          
          {/* Role Tabs */}
          <Tabs value={activeRole} onValueChange={(value) => setActiveRole(value as UserRole)}>
            <TabsList className="grid w-full max-w-md mb-8" style={{gridTemplateColumns: `repeat(${accessibleRoles.length}, 1fr)`}}>
              {accessibleRoles.map(roleStatus => (
                <TabsTrigger key={roleStatus.role} value={roleStatus.role} className="flex items-center gap-2">
                  <span>{getRoleIcon(roleStatus.role)}</span>
                  {getRoleTitle(roleStatus.role)}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Student Dashboard */}
            <TabsContent value="student" className="space-y-8">
              <div className="bg-card rounded-lg p-6 border">
                <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                  📚 Student Dashboard
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Find Your Perfect Tutor</h3>
                    <p className="text-muted-foreground mb-4">
                      Browse verified tutors specializing in IB and AP courses.
                    </p>
                    <Link href="/tutors">
                      <Button>Browse Tutors</Button>
                    </Link>
                  </div>
                  
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Upcoming Sessions</h3>
                    <p className="text-muted-foreground mb-4">
                      No sessions scheduled yet. Book your first session to get started!
                    </p>
                    <Button variant="outline">View Sessions</Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Tutor Dashboard */}
            <TabsContent value="tutor" className="space-y-8">
              <div className="bg-card rounded-lg p-6 border">
                <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                  👨‍🏫 Tutor Dashboard
                </h2>
                
                {/* Tutor Status Alert */}
                {profile && 'tutor' in profile.roles && profile.tutor_status !== 'approved' && (
                  <div className={`rounded-lg p-4 border mb-6 ${
                    profile.tutor_status === 'pending' 
                      ? 'bg-yellow-50 border-yellow-200 text-yellow-800' 
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    <h3 className="font-semibold mb-2">
                      {profile.tutor_status === 'pending' ? '⏳ Application Under Review' : '❌ Application Rejected'}
                    </h3>
                    <p className="text-sm">
                      {profile.tutor_status === 'pending' 
                        ? 'Your tutor application is being reviewed by our team. You\'ll be notified once approved.'
                        : 'Your tutor application was not approved. Please contact support for more information.'
                      }
                    </p>
                  </div>
                )}

                {/* Verification Requirements */}
                {profile && profile.tutor_status === 'approved' && user && (
                  <div className="mb-6">
                    <VerificationRequirements userId={user.id} />
                  </div>
                )}

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Your Tutor Profile</h3>
                    <p className="text-muted-foreground mb-4">
                      Complete your profile to start accepting students.
                    </p>
                    <Button 
                      disabled={profile?.tutor_status === 'rejected'}
                      onClick={() => window.location.href = '/tutor/profile'}
                    >
                      Complete Profile
                    </Button>
                  </div>
                  
                  <div className="bg-gradient-to-br from-teal-50 to-teal-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Manage Availability</h3>
                    <p className="text-muted-foreground mb-4">
                      Set your available time slots for students to book.
                    </p>
                    <Button 
                      variant="outline"
                      disabled={profile?.tutor_status !== 'approved'}
                    >
                      Set Availability
                    </Button>
                  </div>
                  
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">My Students</h3>
                    <p className="text-muted-foreground mb-4">
                      View and manage your current students.
                    </p>
                    <Button variant="outline">View Students</Button>
                  </div>
                  
                  <div className="bg-gradient-to-br from-pink-50 to-pink-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Earnings</h3>
                    <p className="text-muted-foreground mb-4">
                      Track your earnings and payment history.
                    </p>
                    <Button variant="outline">View Earnings</Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Admin Dashboard */}
            <TabsContent value="admin" className="space-y-8">
              <div className="bg-card rounded-lg p-6 border">
                <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                  ⚙️ Admin Dashboard
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Pending Approvals</h3>
                    <p className="text-muted-foreground mb-4">
                      Review and approve tutor applications.
                    </p>
                    <Link href="/admin">
                      <Button>Manage Approvals</Button>
                    </Link>
                  </div>
                  
                  <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">Platform Overview</h3>
                    <p className="text-muted-foreground mb-4">
                      Monitor platform activity and user engagement.
                    </p>
                    <Button variant="outline">View Analytics</Button>
                  </div>
                  
                  <div className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">User Management</h3>
                    <p className="text-muted-foreground mb-4">
                      Manage user accounts and role assignments.
                    </p>
                    <Button variant="outline">Manage Users</Button>
                  </div>
                  
                  <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-6 border">
                    <h3 className="text-xl font-semibold mb-4">System Settings</h3>
                    <p className="text-muted-foreground mb-4">
                      Configure platform settings and policies.
                    </p>
                    <Button variant="outline">Settings</Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Quick Stats */}
          <div className="mt-8 bg-card rounded-lg p-6 border">
            <h3 className="text-lg font-semibold mb-4">Account Overview</h3>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Active Roles</p>
                <p className="text-2xl font-bold">{accessibleRoles.length}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Member Since</p>
                <p className="text-lg font-semibold">
                  {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'N/A'}
                </p>
              </div>
              {profile?.tutor_tier && (
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Tutor Tier</p>
                  <p className="text-lg font-semibold">
                    {profile.tutor_tier === 'verified' ? '✓ Verified' : 'Standard'}
                  </p>
                </div>
              )}
              {profile?.hourly_rate && (
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Hourly Rate</p>
                  <p className="text-lg font-semibold">
                    ${(profile.hourly_rate / 100).toFixed(0)}/hour
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}