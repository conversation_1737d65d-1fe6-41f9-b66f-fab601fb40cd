'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface TutorProfile {
  id: string
  email: string
  full_name: string
  roles: string[]
  tutor_status: 'pending' | 'approved' | 'rejected'
  approval_notes?: string
  tutor_tier: 'standard' | 'verified'
  hourly_rate?: number
  bio?: string
  subjects?: string[]
  education?: string
  experience_years?: number
  certifications?: string[]
  languages?: string[]
  created_at: string
}

export default function AdminDashboard() {
  const [user, setUser] = useState<{ id: string; email: string; full_name?: string } | null>(null)
  const [pendingTutors, setPendingTutors] = useState<TutorProfile[]>([])
  const [allTutors, setAllTutors] = useState<TutorProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<string | null>(null)
  const [selectedTab, setSelectedTab] = useState<'pending' | 'all'>('pending')
  const [approvalNotes, setApprovalNotes] = useState<{[key: string]: string}>({})
  
  const supabase = createClient()
  const router = useRouter()

  const checkAdminAccess = useCallback(async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError) {
        console.error('Auth error:', userError)
        router.push('/auth')
        return
      }

      if (!user) {
        router.push('/auth')
        return
      }

      // Check if user has admin role (multi-role system)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        router.push('/dashboard')
        return
      }

      if (!profile) {
        router.push('/dashboard')
        return
      }

      // Check if user has admin role in the roles array
      if (!profile.roles || !profile.roles.includes('admin') || profile.admin_status !== 'approved') {
        router.push('/dashboard')
        return
      }
      setUser(profile)
      await fetchTutors()
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [router, supabase])

  useEffect(() => {
    checkAdminAccess()
  }, [checkAdminAccess])

  const fetchTutors = async () => {
    try {
      // Fetch pending tutors (multi-role system)
      const { data: pending } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'pending')
        .order('created_at', { ascending: false })

      setPendingTutors(pending || [])

      // Fetch all tutors (multi-role system)
      const { data: all } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .order('created_at', { ascending: false })

      setAllTutors(all || [])
    } catch (error) {
      console.error('Error fetching tutors:', error)
    }
  }

  const handleApproval = async (tutorId: string, action: 'approve' | 'reject') => {
    try {
      setProcessing(tutorId)
      
      const updateData = {
        tutor_status: action === 'approve' ? 'approved' : 'rejected',
        approval_notes: approvalNotes[tutorId] || null,
        approved_at: action === 'approve' ? new Date().toISOString() : null
      }
      
      
      const { data, error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', tutorId)
        .select()

      if (error) {
        console.error('Supabase error:', error)
        throw error
      }


      // Refresh the lists
      await fetchTutors()
      
      // Clear the notes
      setApprovalNotes(prev => {
        const newNotes = { ...prev }
        delete newNotes[tutorId]
        return newNotes
      })


    } catch (error) {
      console.error('Error updating approval:', error)
      alert(`Error ${action}ing tutor: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setProcessing(null)
    }
  }

  const handleVerification = async (tutorId: string, verified: boolean) => {
    try {
      setProcessing(tutorId)
      
      const { data, error } = await supabase
        .rpc('set_tutor_verification', {
          tutor_id: tutorId,
          verified: verified
        })

      if (error) {
        console.error('Verification error:', error)
        throw error
      }

      
      // Refresh the lists
      await fetchTutors()
      

    } catch (error) {
      console.error('Error updating verification:', error)
      alert(`Error ${verified ? 'verifying' : 'unverifying'} tutor: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setProcessing(null)
    }
  }

  const formatSubjects = (subjects: string[] | null | undefined) => {
    if (!subjects || subjects.length === 0) return 'No subjects listed'
    return subjects
      .map(subject => subject.replace('_', ' ').toUpperCase())
      .join(', ')
  }

  const formatRate = (rateInCents: number | null | undefined) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      approved: 'bg-green-100 text-green-800 border-green-200',
      rejected: 'bg-red-100 text-red-800 border-red-200'
    }
    return styles[status as keyof typeof styles] || styles.pending
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">ProTutor Admin</h1>
            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
              ADMIN
            </span>
          </div>
          <nav className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline">Dashboard</Button>
            </Link>
            <span className="text-sm text-muted-foreground">
              Welcome, {user?.full_name || user?.email}
            </span>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-6 mb-8">
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Pending Approvals</h3>
            <p className="text-3xl font-bold text-yellow-600">{pendingTutors.length}</p>
          </div>
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Approved Tutors</h3>
            <p className="text-3xl font-bold text-green-600">
              {allTutors.filter(t => t.tutor_status === 'approved').length}
            </p>
          </div>
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Verified Tutors</h3>
            <p className="text-3xl font-bold text-blue-600">
              {allTutors.filter(t => t.tutor_status === 'approved' && t.tutor_tier === 'verified').length}
            </p>
          </div>
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Standard Tutors</h3>
            <p className="text-3xl font-bold text-gray-600">
              {allTutors.filter(t => t.tutor_status === 'approved' && t.tutor_tier === 'standard').length}
            </p>
          </div>
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Rejected</h3>
            <p className="text-3xl font-bold text-red-600">
              {allTutors.filter(t => t.tutor_status === 'rejected').length}
            </p>
          </div>
          <div className="bg-background rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-muted-foreground">Total Tutors</h3>
            <p className="text-3xl font-bold">{allTutors.length}</p>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6">
          <button
            onClick={() => setSelectedTab('pending')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedTab === 'pending'
                ? 'bg-primary text-primary-foreground'
                : 'bg-background text-muted-foreground hover:text-foreground'
            }`}
          >
            Pending Approvals ({pendingTutors.length})
          </button>
          <button
            onClick={() => setSelectedTab('all')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedTab === 'all'
                ? 'bg-primary text-primary-foreground'
                : 'bg-background text-muted-foreground hover:text-foreground'
            }`}
          >
            All Tutors ({allTutors.length})
          </button>
        </div>

        {/* Tutors List */}
        <div className="space-y-6">
          {(selectedTab === 'pending' ? pendingTutors : allTutors).map((tutor) => (
            <div key={tutor.id} className="bg-background rounded-lg p-6 border">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                {/* Tutor Info */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-xl font-semibold">{tutor.full_name}</h3>
                      <p className="text-muted-foreground">{tutor.email}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusBadge(tutor.tutor_status)}`}>
                          {tutor.tutor_status.charAt(0).toUpperCase() + tutor.tutor_status.slice(1)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Applied {new Date(tutor.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary">
                        {formatRate(tutor.hourly_rate)}
                      </div>
                      <div className="flex items-center gap-2 justify-end">
                        <span className={`text-sm px-2 py-1 rounded-full ${
                          tutor.tutor_tier === 'verified' 
                            ? 'bg-green-100 text-green-800 border border-green-200' 
                            : 'bg-gray-100 text-gray-600 border border-gray-200'
                        }`}>
                          {tutor.tutor_tier === 'verified' ? '✓ Verified' : 'Standard'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-1">Subjects</h4>
                      <p className="text-sm text-muted-foreground">{formatSubjects(tutor.subjects)}</p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-1">Experience</h4>
                      <p className="text-sm text-muted-foreground">
                        {tutor.experience_years ? `${tutor.experience_years} years` : 'Not specified'}
                      </p>
                    </div>
                    {tutor.education && (
                      <div>
                        <h4 className="font-medium mb-1">Education</h4>
                        <p className="text-sm text-muted-foreground">{tutor.education}</p>
                      </div>
                    )}
                    {tutor.languages && tutor.languages.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-1">Languages</h4>
                        <p className="text-sm text-muted-foreground">{tutor.languages.join(', ')}</p>
                      </div>
                    )}
                  </div>

                  {tutor.bio && (
                    <div>
                      <h4 className="font-medium mb-1">Bio</h4>
                      <p className="text-sm text-muted-foreground">{tutor.bio}</p>
                    </div>
                  )}

                  {tutor.certifications && tutor.certifications.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-1">Certifications</h4>
                      <ul className="text-sm text-muted-foreground">
                        {tutor.certifications.map((cert, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full"></span>
                            {cert}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {tutor.approval_notes && (
                    <div className="bg-secondary/20 p-3 rounded">
                      <h4 className="font-medium mb-1">Admin Notes</h4>
                      <p className="text-sm text-muted-foreground">{tutor.approval_notes}</p>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="lg:w-80 space-y-4">
                  {/* Approval Actions (for pending tutors) */}
                  {tutor.tutor_status === 'pending' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium mb-2">Approval Notes (Optional)</label>
                        <Textarea
                          placeholder="Add notes about this approval decision..."
                          value={approvalNotes[tutor.id] || ''}
                          onChange={(e) => setApprovalNotes(prev => ({
                            ...prev,
                            [tutor.id]: e.target.value
                          }))}
                          className="min-h-[80px]"
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleApproval(tutor.id, 'approve')}
                          disabled={processing === tutor.id}
                          className="flex-1 bg-green-600 hover:bg-green-700"
                        >
                          {processing === tutor.id ? 'Processing...' : 'Approve'}
                        </Button>
                        <Button
                          onClick={() => handleApproval(tutor.id, 'reject')}
                          disabled={processing === tutor.id}
                          variant="destructive"
                          className="flex-1"
                        >
                          {processing === tutor.id ? 'Processing...' : 'Reject'}
                        </Button>
                      </div>
                    </>
                  )}

                  {/* Verification Actions (for approved tutors) */}
                  {tutor.tutor_status === 'approved' && (
                    <div className="space-y-3">
                      <div className="border-t pt-4">
                        <h4 className="font-medium mb-2">Verification Status</h4>
                        <p className="text-sm text-muted-foreground mb-3">
                          {tutor.tutor_tier === 'verified' 
                            ? 'This tutor is verified and can charge premium rates.'
                            : 'Standard tier tutor. Verify to unlock premium features.'
                          }
                        </p>
                        <div className="flex gap-2">
                          {tutor.tutor_tier === 'verified' ? (
                            <Button
                              onClick={() => handleVerification(tutor.id, false)}
                              disabled={processing === tutor.id}
                              variant="outline"
                              className="flex-1 border-orange-200 text-orange-700 hover:bg-orange-50"
                            >
                              {processing === tutor.id ? 'Processing...' : 'Remove Verification'}
                            </Button>
                          ) : (
                            <Button
                              onClick={() => handleVerification(tutor.id, true)}
                              disabled={processing === tutor.id}
                              className="flex-1 bg-blue-600 hover:bg-blue-700"
                            >
                              {processing === tutor.id ? 'Processing...' : 'Verify Tutor'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}

          {(selectedTab === 'pending' ? pendingTutors : allTutors).length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">
                {selectedTab === 'pending' ? '✅' : '👥'}
              </div>
              <h3 className="text-xl font-semibold mb-2">
                {selectedTab === 'pending' ? 'No Pending Approvals' : 'No Tutors Yet'}
              </h3>
              <p className="text-muted-foreground">
                {selectedTab === 'pending' 
                  ? 'All tutor applications have been reviewed.'
                  : 'No tutors have registered yet.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}