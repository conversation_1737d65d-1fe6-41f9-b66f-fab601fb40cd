'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'

interface BookingDetails {
  id: string
  tutor_name: string
  tutor_id: string
  start_time: string
  end_time: string
  session_duration: number
  prep_notes: string | null
  status: string
  created_at: string
}

export default function BookingSuccessPage() {
  const params = useParams()
  const router = useRouter()
  const [booking, setBooking] = useState<BookingDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userTimezone, setUserTimezone] = useState<string>('UTC')

  const supabase = createClient()

  useEffect(() => {
    setUserTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
    
    if (params?.id) {
      fetchBookingDetails(params.id as string)
    }
  }, [params?.id])

  const fetchBookingDetails = async (bookingId: string) => {
    try {
      setLoading(true)

      // Check if user is authenticated
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        router.push('/auth')
        return
      }

      // Fetch booking details with tutor information
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          start_time,
          end_time,
          session_duration,
          prep_notes,
          status,
          created_at,
          tutor:tutor_id (
            id,
            full_name
          )
        `)
        .eq('id', bookingId)
        .eq('student_id', user.id)
        .single()

      if (error) {
        console.error('Error fetching booking:', error)
        setError('Booking not found or you do not have permission to view it.')
        return
      }

      // Transform the data to match our interface
      const tutor = Array.isArray(data.tutor) ? data.tutor[0] : data.tutor
      const bookingDetails: BookingDetails = {
        id: data.id,
        tutor_name: tutor?.full_name || 'Unknown Tutor',
        tutor_id: tutor?.id || '',
        start_time: data.start_time,
        end_time: data.end_time,
        session_duration: data.session_duration,
        prep_notes: data.prep_notes,
        status: data.status,
        created_at: data.created_at
      }

      setBooking(bookingDetails)

    } catch (error) {
      console.error('Error:', error)
      setError('Failed to load booking details.')
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime)
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} minutes`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`
    }
    return `${hours}h ${remainingMinutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading booking details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h2 className="text-2xl font-semibold mb-2">Booking Not Found</h2>
            <p className="text-muted-foreground mb-6">
              {error || 'This booking may not exist or you may not have permission to view it.'}
            </p>
            <div className="space-x-4">
              <Link href="/dashboard">
                <Button>Go to Dashboard</Button>
              </Link>
              <Link href="/tutors">
                <Button variant="outline">Browse Tutors</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const startDateTime = formatDateTime(booking.start_time)
  const endTime = new Date(booking.end_time).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold">ProTutor</h1>
          </Link>
          <nav className="space-x-4">
            <Link href="/dashboard">
              <Button variant="outline">Dashboard</Button>
            </Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Success Message */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">🎉</div>
            <h1 className="text-3xl font-bold text-green-600 mb-2">Booking Confirmed!</h1>
            <p className="text-muted-foreground">
              Your tutoring session has been successfully booked.
            </p>
          </div>

          {/* Booking Details Card */}
          <div className="bg-background rounded-lg p-8 shadow-sm border mb-8">
            <h2 className="text-xl font-semibold mb-6">Session Details</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Booking ID</span>
                <span className="font-mono text-sm">{booking.id}</span>
              </div>
              
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Tutor</span>
                <span className="font-medium">{booking.tutor_name}</span>
              </div>
              
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Date</span>
                <span className="font-medium">{startDateTime.date}</span>
              </div>
              
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Time</span>
                <span className="font-medium">{startDateTime.time} - {endTime}</span>
              </div>
              
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Duration</span>
                <span className="font-medium">{formatDuration(booking.session_duration)}</span>
              </div>
              
              <div className="flex justify-between py-2 border-b border-border">
                <span className="text-muted-foreground">Timezone</span>
                <span className="font-medium">{userTimezone}</span>
              </div>
              
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">Status</span>
                <span className="font-medium text-green-600 capitalize">{booking.status}</span>
              </div>
            </div>

            {booking.prep_notes && (
              <div className="mt-6 pt-6 border-t border-border">
                <h3 className="font-medium mb-2">Preparation Notes</h3>
                <p className="text-muted-foreground text-sm bg-secondary/20 p-3 rounded">
                  {booking.prep_notes}
                </p>
              </div>
            )}
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-blue-900 mb-3">What happens next?</h3>
            <ul className="space-y-2 text-blue-800 text-sm">
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>You&apos;ll receive a confirmation email with session details and join instructions</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Your tutor will reach out 24 hours before the session to confirm and prepare</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>You can manage your booking from your dashboard or contact support if needed</span>
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Link href="/dashboard" className="flex-1">
              <Button className="w-full">
                View All Bookings
              </Button>
            </Link>
            <Link href={`/tutors/${booking.tutor_id}`} className="flex-1">
              <Button variant="outline" className="w-full">
                View Tutor Profile
              </Button>
            </Link>
            <Link href="/tutors" className="flex-1">
              <Button variant="outline" className="w-full">
                Book Another Session
              </Button>
            </Link>
          </div>

          {/* Support Info */}
          <div className="text-center mt-8 pt-6 border-t border-border">
            <p className="text-sm text-muted-foreground">
              Need help or have questions?{' '}
              <Link href="/contact" className="text-primary hover:underline">
                Contact our support team
              </Link>
              {' '}or email us at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}