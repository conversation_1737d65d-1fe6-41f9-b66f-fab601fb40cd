'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  experience_years: number | null
  languages: string[] | null
  approval_status: 'pending' | 'approved' | 'rejected'
}

export default function TutorsPage() {
  const [tutors, setTutors] = useState<Tutor[]>([])
  const [filteredTutors, setFilteredTutors] = useState<Tutor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [subjectFilter, setSubjectFilter] = useState('all')
  const [tierFilter, setTierFilter] = useState('all')
  const [priceFilter, setPriceFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)

  // Filter section states
  const [selectedCategory, setSelectedCategory] = useState('All categories')
  const [selectedSubcategory, setSelectedSubcategory] = useState('')
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>(['SAT Prep', 'AP Calculus', 'AP Algebra'])
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([])
  const [selectedDurations, setSelectedDurations] = useState<string[]>([])
  const [selectedPrices, setSelectedPrices] = useState<string[]>([])
  const [specialtyInput, setSpecialtyInput] = useState('')

  // Collapsible sections state
  const [collapsedSections, setCollapsedSections] = useState<{[key: string]: boolean}>({
    category: false,
    subcategory: false,
    specialties: false,
    availability: false,
    duration: false,
    price: false
  })

  const supabase = createClient()

  const categories = [
    "All categories", "Art and design", "College mentorship", "Interview preparation",
    "College tutoring", "Technology", "Writing"
  ]
  const subcategories = ["Mathematics"]
  const availabilityOptions = ["Today", "Tomorrow", "Next 3 days", "Next 7 days", "Custom"]
  const durations = ["60 minutes", "30 minutes", "15 minutes"]
  const prices = ["$0 - $100", "$100 and above"]

  useEffect(() => {
    fetchTutors()
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  useEffect(() => {
    filterTutors()
  }, [tutors, searchTerm, subjectFilter, tierFilter, priceFilter])

  const fetchTutors = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .order('created_at', { ascending: false })

      if (error) throw error

      setTutors(data || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching tutors:', error)
      setError('Failed to load tutors. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filterTutors = () => {
    let filtered = tutors

    // Search by name or bio
    if (searchTerm) {
      filtered = filtered.filter(tutor =>
        tutor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (tutor.bio && tutor.bio.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by subject
    if (subjectFilter !== 'all') {
      filtered = filtered.filter(tutor =>
        tutor.subjects && tutor.subjects.includes(subjectFilter)
      )
    }

    // Filter by tier
    if (tierFilter !== 'all') {
      filtered = filtered.filter(tutor => tutor.tutor_tier === tierFilter)
    }

    // Filter by price
    if (priceFilter !== 'all') {
      filtered = filtered.filter(tutor => {
        if (!tutor.hourly_rate) return false
        const rate = tutor.hourly_rate / 100 // Convert from cents
        if (priceFilter === 'low') return rate < 100
        if (priceFilter === 'high') return rate >= 100
        return true
      })
    }

    setFilteredTutors(filtered)
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}/hour`
  }

  const formatSubjects = (subjects: string[] | null) => {
    if (!subjects || subjects.length === 0) return []
    return subjects.map(subject => subject.replace('_', ' ').toUpperCase())
  }



  const getAvailableSubjects = () => {
    const allSubjects = new Set<string>()
    tutors.forEach(tutor => {
      if (tutor.subjects) {
        tutor.subjects.forEach(subject => allSubjects.add(subject))
      }
    })
    return Array.from(allSubjects).sort()
  }

  const generateMockRating = (id: string) => {
    // Generate consistent mock rating based on ID
    const hash = id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    const rating = 4.0 + (Math.abs(hash) % 100) / 100
    const reviews = 10 + (Math.abs(hash) % 200)
    return `${rating.toFixed(1)} (${reviews} reviews)`
  }

  const generatePixelAvatar = (name: string, id: string) => {
    // Generate consistent colorful avatar based on name
    const colors = ['FF6B6B', '4ECDC4', '45B7D1', '96CEB4', 'FFEAA7', 'DDA0DD', 'FFB6C1', '87CEEB']
    const hash = (name + id).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    const color = colors[Math.abs(hash) % colors.length]

    // Use UI Avatars - very reliable service
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=80&background=${color}&color=fff&bold=true`
  }

  // Filter helper functions
  const toggleSection = (section: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const removeSpecialty = (specialty: string) => {
    setSelectedSpecialties(prev => prev.filter(s => s !== specialty))
  }

  const addSpecialty = () => {
    if (specialtyInput.trim() && !selectedSpecialties.includes(specialtyInput.trim())) {
      setSelectedSpecialties(prev => [...prev, specialtyInput.trim()])
      setSpecialtyInput('')
    }
  }

  const handleSpecialtyKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addSpecialty()
    }
  }

  const toggleAvailability = (option: string) => {
    setSelectedAvailability(prev =>
      prev.includes(option)
        ? prev.filter(a => a !== option)
        : [...prev, option]
    )
  }

  const toggleDuration = (duration: string) => {
    setSelectedDurations(prev =>
      prev.includes(duration)
        ? prev.filter(d => d !== duration)
        : [...prev, duration]
    )
  }

  const togglePrice = (price: string) => {
    setSelectedPrices(prev =>
      prev.includes(price)
        ? prev.filter(p => p !== price)
        : [...prev, price]
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/profile" className="text-gray-500 hover:text-gray-700">
            Profile
          </Link>
          <Link href="/search" className="text-gray-500 hover:text-gray-700">
            Search
          </Link>
          {user ? (
            <>
              <Link href="/dashboard" className="text-gray-500 hover:text-gray-700">
                Dashboard
              </Link>
              <button
                onClick={async () => {
                  await supabase.auth.signOut()
                  window.location.reload()
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                Logout
              </button>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {user.email?.charAt(0).toUpperCase()}
              </div>
            </>
          ) : (
            <>
              <Link href="/auth" className="text-gray-500 hover:text-gray-700">
                Sign In
              </Link>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </>
          )}
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex gap-8">
          {/* Filter Sidebar */}
          <div className="w-80 bg-white rounded-lg shadow-sm p-6 flex-shrink-0 h-fit sticky top-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Filter by</h2>

          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('category')}>
              <h3 className="text-lg font-medium text-gray-800">Category</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.category ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.category && (
              <ul className="space-y-2 text-gray-700">
                {categories.map(cat => (
                  <li key={cat}>
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="category"
                        checked={selectedCategory === cat}
                        onChange={() => setSelectedCategory(cat)}
                        className="form-radio text-blue-600 rounded-full"
                      />
                      <span className="ml-2">{cat}</span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Subcategory Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('subcategory')}>
              <h3 className="text-lg font-medium text-gray-800">Subcategory</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.subcategory ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.subcategory && (
              <ul className="space-y-2 text-gray-700">
                {subcategories.map(subcat => (
                  <li key={subcat}>
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="subcategory"
                        checked={selectedSubcategory === subcat}
                        onChange={() => setSelectedSubcategory(selectedSubcategory === subcat ? '' : subcat)}
                        className="form-radio text-blue-600 rounded-full"
                      />
                      <span className="ml-2">{subcat}</span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Specialties Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('specialties')}>
              <h3 className="text-lg font-medium text-gray-800">Specialties</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.specialties ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.specialties && (
              <div className="flex flex-wrap gap-2">
                {selectedSpecialties.map(spec => (
                  <span key={spec} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full flex items-center">
                    {spec}
                    <button
                      onClick={() => removeSpecialty(spec)}
                      className="ml-1 text-blue-800 hover:text-blue-900 transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                ))}
                <input
                  type="text"
                  placeholder="Add specialty"
                  value={specialtyInput}
                  onChange={(e) => setSpecialtyInput(e.target.value)}
                  onKeyPress={handleSpecialtyKeyPress}
                  onBlur={addSpecialty}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                />
              </div>
            )}
          </div>

          {/* Availability Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('availability')}>
              <h3 className="text-lg font-medium text-gray-800">Availability</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.availability ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.availability && (
              <ul className="space-y-2 text-gray-700">
                {availabilityOptions.map(avail => (
                  <li key={avail}>
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedAvailability.includes(avail)}
                        onChange={() => toggleAvailability(avail)}
                        className="form-checkbox text-blue-600 rounded"
                      />
                      <span className="ml-2">{avail}</span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Duration Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('duration')}>
              <h3 className="text-lg font-medium text-gray-800">Duration</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.duration ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.duration && (
              <ul className="space-y-2 text-gray-700">
                {durations.map(duration => (
                  <li key={duration}>
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedDurations.includes(duration)}
                        onChange={() => toggleDuration(duration)}
                        className="form-checkbox text-blue-600 rounded"
                      />
                      <span className="ml-2">{duration}</span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Price Filter */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2 cursor-pointer" onClick={() => toggleSection('price')}>
              <h3 className="text-lg font-medium text-gray-800">Price</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform ${collapsedSections.price ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {!collapsedSections.price && (
              <ul className="space-y-2 text-gray-700">
                {prices.map(price => (
                  <li key={price}>
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedPrices.includes(price)}
                        onChange={() => togglePrice(price)}
                        className="form-checkbox text-blue-600 rounded"
                      />
                      <span className="ml-2">{price}</span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

          {/* Main Content Area */}
          <div className="flex-1 min-w-0">
            {/* Search Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Find Your Perfect Tutor</h1>

              {/* Search Bar */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search by subject, tutor name, or expertise..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-4 pl-12 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500"
                />
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Quick Filters */}
              <div className="flex flex-wrap gap-3 mb-4">
                <select
                  value={subjectFilter}
                  onChange={(e) => setSubjectFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value="all">All Subjects</option>
                  {getAvailableSubjects().map(subject => (
                    <option key={subject} value={subject}>
                      {subject.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>

                <select
                  value={tierFilter}
                  onChange={(e) => setTierFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value="all">All Levels</option>
                  <option value="standard">Standard</option>
                  <option value="verified">Verified</option>
                </select>

                <select
                  value={priceFilter}
                  onChange={(e) => setPriceFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value="all">All Prices</option>
                  <option value="low">Under $100/hr</option>
                  <option value="high">$100+/hr</option>
                </select>
              </div>

              {/* Results Summary */}
              <p className="text-gray-600 text-sm">
                Showing {filteredTutors.length} of {tutors.length} tutors
              </p>
            </div>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading tutors...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={fetchTutors}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && filteredTutors.length === 0 && tutors.length > 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-2">No tutors found</h3>
              <p className="text-gray-500">Try adjusting your search criteria or filters</p>
            </div>
          )}

          {/* No Tutors Available */}
          {!loading && !error && tutors.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👨‍🏫</div>
              <h3 className="text-xl font-semibold mb-2">No approved tutors available</h3>
              <p className="text-gray-500">Tutors are currently being reviewed. Please check back soon!</p>
            </div>
          )}

          {/* Tutor Listings */}
          {!loading && !error && filteredTutors.length > 0 && (
            <div className="space-y-4">
              {filteredTutors.map(tutor => (
                <div key={tutor.id} className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-start space-x-4">
                    <div className="relative">
                      <Image
                        src={generatePixelAvatar(tutor.full_name, tutor.id)}
                        alt={tutor.full_name}
                        width={64}
                        height={64}
                        className="w-16 h-16 rounded-full object-cover flex-shrink-0"
                        onError={(e) => {
                          console.log('Image failed to load:', generatePixelAvatar(tutor.full_name, tutor.id))
                          // Fallback to placeholder
                          e.currentTarget.src = `https://placehold.co/64x64/E0E0E0/808080?text=${tutor.full_name.charAt(0)}`
                        }}
                      />
                      {/* Online status indicator */}
                      <div className={`absolute bottom-0 right-0 w-4 h-4 rounded-full border-2 border-white ${
                        tutor.id.charCodeAt(0) % 3 === 0 ? 'bg-green-500' : 'bg-gray-400'
                      }`}></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="text-base font-semibold text-gray-900 mb-1.5">{tutor.full_name}</h3>

                          {/* First line: Price, Response Time, Reviews */}
                          <div className="flex items-center space-x-4 text-sm mb-1.5">
                            <div className="flex items-center space-x-1">
                              <span className="font-semibold text-gray-900">{formatRate(tutor.hourly_rate)}</span>
                              <span className="text-gray-500">/ 60 minutes</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <span className="text-gray-600">95% Response Time</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                              <span className="text-gray-900 font-medium">{generateMockRating(tutor.id)}</span>
                            </div>
                          </div>

                          {/* Second line: Description */}
                          <p className="text-sm text-gray-600 leading-relaxed mb-3">
                            {tutor.bio ? tutor.bio.substring(0, 140) + (tutor.bio.length > 140 ? '...' : '') : 'Experienced tutor ready to help you achieve your academic goals.'}
                          </p>
                        </div>

                        <button className="ml-4 p-2 text-gray-400 hover:text-red-500 transition-colors duration-200 flex-shrink-0">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {formatSubjects(tutor.subjects).slice(0, 4).map(subject => (
                          <span key={subject} className="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full font-medium border">
                            {subject}
                          </span>
                        ))}
                        {tutor.subjects && tutor.subjects.length > 4 && (
                          <span className="text-xs text-gray-500 px-2.5 py-1">+{tutor.subjects.length - 4} more</span>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          {tutor.experience_years && (
                            <span>{tutor.experience_years} years experience</span>
                          )}
                          {tutor.languages && tutor.languages.length > 0 && (
                            <span>Speaks {tutor.languages.join(', ')}</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-3">
                          <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                            Message
                          </button>
                          <Link href={`/tutor/${tutor.id}`}>
                            <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                              View Profile
                            </button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>
      </div>
    </div>
  )
}
