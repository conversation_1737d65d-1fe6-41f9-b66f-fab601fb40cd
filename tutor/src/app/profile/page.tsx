'use client'

import { useState } from 'react'
import Image from 'next/image'

export default function ProfilePage() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openModal = () => setIsModalOpen(true)
  const closeModal = () => setIsModalOpen(false)

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 font-inter">
      <div className="bg-white shadow-xl rounded-2xl p-6 md:p-8 w-full max-w-6xl flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:space-x-8">
        {/* Left Column - Profile Info */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
            <span>Education</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span>Maths</span>
          </div>

          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6 mb-8">
            <Image
              src="https://placehold.co/120x120/E0E0E0/808080?text=Profile"
              alt="Jane Cooper"
              width={120}
              height={120}
              className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover shadow-md"
            />
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-1">Jane Cooper</h1>
              <div className="flex items-center space-x-2 text-gray-600 mb-2">
                <span className="text-lg">Online Tutor</span>
                <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Verified
                </span>
              </div>
              <div className="flex items-center text-yellow-500 mb-2">
                <span className="text-xl font-bold mr-1">4.74</span>
                <span className="text-gray-500 text-sm">(238 reviews)</span>
              </div>
              <p className="text-gray-700 leading-relaxed max-w-lg">
                Jane has an M.S. in math and is completing her Ph.D. in aerospace engineering from Stanford University. She can help with upper-level high school and college level math, as well as algebra, and precalculus...
                <button className="text-blue-600 hover:underline ml-1">Show more</button>
              </p>
              <div className="flex space-x-4 mt-4">
                <button className="flex items-center text-gray-600 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  Share
                </button>
                <button className="flex items-center text-gray-600 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                  </svg>
                  Save
                </button>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Specialties</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Maths</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Calculus, Linear algebra, Binomial probability, Rational functions, Finite math, Trigonometry, Polynomials</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">English</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>ACT English, Comprehension, Story telling, Proof reading, Public speaking, Shakespeare</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Finance and markets</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Derivative, Valuation structures</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Statistics</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Advanced regression, Hypothesis testing, Analysis of variance, Confidence intervals, Geometric random variables</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Computer Science</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Python, R, Django, SQL, Postgres, Data manipulation, Machine learning</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Test preparation</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>SAT, MCAT, CFA, GRE</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Economics</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Macroeconomics, Balance of payments</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-xl shadow-inner">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Meet your tutor, Jane</h2>
            <p className="text-gray-700 mb-2">
              <span className="font-semibold">Host on Popless</span> since 2022
            </p>
            <ul className="text-gray-700 space-y-1">
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                238 reviews
              </li>
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Identity verified
              </li>
              <li className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                4 hr response time
              </li>
            </ul>
            <p className="text-gray-700 mt-4">
              Languages spoken: <span className="font-semibold">English, Spanish, German, and French</span>
            </p>
          </div>
        </div>

        {/* Right Column - Schedule & Message */}
        <div className="lg:w-96 p-6 bg-white border border-gray-200 rounded-xl shadow-lg flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <span className="text-lg font-semibold text-gray-800">Session $55 / 60 minutes</span>
            <button className="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>

          <div className="text-gray-600 mb-4">
            <p>Sunday, May 1 · 8:15 AM - 9:15 AM (PST)</p>
          </div>

          {/* Dummy Schedule */}
          <div className="grid grid-cols-7 gap-1 text-center text-sm mb-6">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className="font-medium text-gray-700">{day}</div>
            ))}
            {[...Array(30)].map((_, i) => (
              <div key={i} className={`p-1 rounded-md ${i % 5 === 0 ? 'bg-blue-500 text-white' : 'text-gray-700'}`}>
                {i + 1}
              </div>
            ))}
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between text-gray-700">
              <span>7:45 AM</span>
              <div className="w-1/2 h-6 bg-blue-100 rounded-md"></div>
            </div>
            <div className="flex items-center justify-between text-gray-700">
              <span>8:30 AM</span>
              <div className="w-1/2 h-6 bg-blue-100 rounded-md"></div>
            </div>
            <div className="flex items-center justify-between text-gray-700">
              <span>9:15 AM</span>
              <div className="w-1/2 h-6 bg-blue-100 rounded-md"></div>
            </div>
            <div className="flex items-center justify-between text-gray-700">
              <span>10:00 AM</span>
              <div className="w-1/2 h-6 bg-blue-100 rounded-md"></div>
            </div>
            <div className="flex items-center justify-between text-gray-700">
              <span>10:45 AM</span>
              <div className="w-1/2 h-6 bg-blue-100 rounded-md"></div>
            </div>
          </div>

          <button
            onClick={openModal}
            className="mt-8 w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md"
          >
            Send Jane a message
          </button>
        </div>
      </div>

      {/* Message Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Send Jane a message</h2>
              <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 mb-4">
              Hey Jane, could you help me with a few equations before I tackle my exams? I&apos;m not super confident I know enough to get the result I&apos;m looking for, which is why I&apos;m reaching out to you.
            </p>
            <p className="text-gray-700 mb-6">
              I&apos;ll send through some example questions so you get a better idea of how you could help me. Thank you!
            </p>
            <button
              onClick={closeModal} // In a real app, this would send the message
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md"
            >
              Send message
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
