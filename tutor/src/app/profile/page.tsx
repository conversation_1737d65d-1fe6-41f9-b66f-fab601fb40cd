'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function ProfilePage() {
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 to-purple-200">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-black rounded-sm flex items-center justify-center">
              <span className="text-white text-xs font-bold">P</span>
            </div>
            <span className="font-semibold text-gray-900">Popless</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="p-2 text-gray-600 hover:text-gray-900">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </button>
            <button className="p-2 text-gray-600 hover:text-gray-900">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
              </svg>
            </button>
            <div className="w-8 h-8 bg-orange-400 rounded-full"></div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Profile Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* Profile Image and Basic Info */}
              <div className="flex flex-col items-start mb-6">
                <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg mb-4 overflow-hidden flex items-center justify-center">
                  <div className="w-28 h-28 bg-white rounded-lg flex items-center justify-center">
                    <span className="text-4xl">👩‍🏫</span>
                  </div>
                </div>
                
                <div className="text-left w-full">
                  <div className="flex items-center mb-2">
                    <span className="text-sm text-gray-500 mr-2">Education</span>
                    <span className="text-sm text-gray-400">&gt;</span>
                    <span className="text-sm text-gray-500 ml-2">Maths</span>
                  </div>
                  
                  <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                    Jane Cooper <span className="text-blue-500">✓</span>
                  </h1>
                  
                  <p className="text-gray-600 text-sm mb-3">
                    Maths Tutor | High School and College
                  </p>
                  
                  <div className="flex items-center mb-4">
                    <div className="flex items-center">
                      <span className="text-red-500 mr-1">★</span>
                      <span className="font-semibold">4.74</span>
                      <span className="text-gray-500 text-sm ml-1">(238 reviews)</span>
                    </div>
                    <div className="flex items-center ml-4 space-x-2">
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Jane has an M.S. in math and is currently pursuing her Ph.D. in 
                    aerospace engineering from Stanford University. She can 
                    help students with math and science concepts, as well as 
                    well as algebra, and precalculus...
                  </p>
                  
                  <button className="text-blue-600 text-sm mt-2 hover:underline">
                    Show more
                  </button>
                </div>
              </div>

              {/* Specialties Section */}
              <div className="border-t pt-6">
                <h3 className="font-semibold text-gray-900 mb-4">Specialties</h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Maths</h4>
                    <p className="text-sm text-gray-600">
                      Calculus, Linear algebra, Binomial probability, Rational functions, Finite 
                      math, Trigonometry, Polynomials
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">English</h4>
                    <p className="text-sm text-gray-600">
                      Reading, Comprehension, Story telling, Proof reading, Public speaking, 
                      Shakespeare
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Finance and markets</h4>
                    <p className="text-sm text-gray-600">
                      Derivatives, Valuation structures
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Statistics</h4>
                    <p className="text-sm text-gray-600">
                      Advanced regression, Hypothesis testing, Analysis of variance, 
                      Confidence intervals, Descriptive statistics, random variables
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Computer Science</h4>
                    <p className="text-sm text-gray-600">
                      Python, R, Django, SQL, Postgres, Data manipulation, Machine learning
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Test preparation</h4>
                    <p className="text-sm text-gray-600">
                      SAT, MCAT, CPA, GRE
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Economics</h4>
                    <p className="text-sm text-gray-600">
                      Macroeconomics, Balance of payments
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Meet your tutor section */}
            <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                <div>
                  <h3 className="font-semibold text-gray-900">Meet your tutor, Jane</h3>
                  <p className="text-sm text-gray-500">New on Popless since 2022</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center">
                  <span className="text-red-500 mr-1">★</span>
                  <span className="font-medium">238 reviews</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-1">✓</span>
                  <span>Identity verified</span>
                </div>
                <div className="flex items-center">
                  <span className="text-blue-500 mr-1">⚡</span>
                  <span>4 hr response time</span>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mt-3">
                Languages spoken: English, Spanish, German, and French.
              </p>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  🎯 <strong>Perfect for students</strong> who struggle with math concepts or 
                  need help with homework.
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Calendar and Booking */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* Session Info */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600 mb-1">
                    <span>📚 Share</span>
                    <span>💾 Save</span>
                  </div>
                  <h2 className="text-lg font-semibold">Session $55 / 60 minutes</h2>
                  <p className="text-sm text-gray-600">Sunday, May 1 • 8:15 AM - 9:15 AM (PST)</p>
                </div>
              </div>

              {/* Calendar */}
              <div className="mb-6">
                <div className="grid grid-cols-7 gap-1 text-center text-sm mb-4">
                  <div className="font-medium text-gray-600 p-2">Sun</div>
                  <div className="font-medium text-gray-600 p-2">Mon</div>
                  <div className="font-medium text-gray-600 p-2">Tue</div>
                  <div className="font-medium text-gray-600 p-2">Wed</div>
                  <div className="font-medium text-gray-600 p-2">Thu</div>
                  <div className="font-medium text-gray-600 p-2">Fri</div>
                  <div className="font-medium text-gray-600 p-2">Sat</div>

                  <div className="p-2 bg-blue-100 text-blue-800 rounded font-medium">1</div>
                  <div className="p-2 text-gray-400">2</div>
                  <div className="p-2 text-gray-400">3</div>
                  <div className="p-2 text-gray-400">4</div>
                  <div className="p-2 text-gray-400">5</div>
                  <div className="p-2 text-gray-400">6</div>
                  <div className="p-2 text-gray-400">7</div>

                  <div className="p-2 text-gray-400">8</div>
                  <div className="p-2 text-gray-400">9</div>
                  <div className="p-2 text-gray-400">10</div>
                  <div className="p-2 text-gray-400">11</div>
                  <div className="p-2 text-gray-400">12</div>
                  <div className="p-2 text-gray-400">13</div>
                  <div className="p-2 text-gray-400">14</div>
                </div>
              </div>

              {/* Time Slots */}
              <div className="mb-6">
                <div className="grid grid-cols-4 gap-2">
                  <button className="p-2 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50">7:00 AM</button>
                  <button className="p-2 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50">7:45 AM</button>
                  <button className="p-2 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50">8:30 AM</button>
                  <button className="p-2 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50">9:15 AM</button>
                </div>
              </div>

              {/* Message Button */}
              <Button 
                onClick={() => setShowMessageModal(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
              >
                Send Jane a message
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Message Modal */}
      {showMessageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Send Jane a message</h3>
              <button 
                onClick={() => setShowMessageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Hey Jane, could you help me with a few equations before I tackle my exams? I'm not super confident I know enough to get the result I'm looking for, which is why I'm reaching out to you.

I'll send through some example questions so you get a better idea of how you could help me. Thank you!"
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            
            <div className="flex justify-end mt-4">
              <Button 
                onClick={() => {
                  // Handle message send
                  setShowMessageModal(false)
                  setMessage('')
                }}
                className="bg-gray-800 hover:bg-gray-900 text-white px-6 py-2"
              >
                Send message
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
