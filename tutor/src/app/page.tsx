'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    
    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold">ProTutor</h1>
          <nav className="space-x-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-10 w-20 bg-gray-200 rounded"></div>
              </div>
            ) : user ? (
              <>
                <span className="text-sm text-muted-foreground hidden md:block">
                  Welcome, {user.email}
                </span>
                <Link href="/tutors">
                  <Button variant="outline">Browse Tutors</Button>
                </Link>
                <Link href="/dashboard">
                  <Button variant="outline">Dashboard</Button>
                </Link>
                <Button 
                  variant="ghost" 
                  onClick={async () => {
                    await supabase.auth.signOut()
                    window.location.reload()
                  }}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Link href="/auth">
                  <Button variant="outline">Sign In</Button>
                </Link>
                <Link href="/auth">
                  <Button>Get Started</Button>
                </Link>
              </>
            )}
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-20">
        <div className="text-center space-y-8">
          {user ? (
            <>
              <h2 className="text-5xl font-bold tracking-tight">
                Welcome back to <span className="text-primary">ProTutor</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Ready to continue your learning journey? Browse our expert tutors or manage your sessions.
              </p>
              
              <div className="space-y-4">
                <div className="flex gap-4 justify-center">
                  <Link href="/tutors">
                    <Button size="lg" className="text-lg px-8 py-6">
                      Find Tutors
                    </Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button size="lg" variant="outline" className="text-lg px-8 py-6">
                      My Dashboard
                    </Button>
                  </Link>
                </div>
              </div>
            </>
          ) : (
            <>
              <h2 className="text-5xl font-bold tracking-tight">
                Find Your Perfect <span className="text-primary">IB & AP Tutor</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Connect with verified, elite tutors who specialize in IB Diploma and AP courses. 
                Get personalized guidance from experts who understand your academic goals.
              </p>
              
              <div className="space-y-4">
                <Link href="/auth">
                  <Button size="lg" className="text-lg px-8 py-6">
                    Start Learning Today
                  </Button>
                </Link>
                <p className="text-sm text-muted-foreground">
                  Join students from top international schools worldwide
                </p>
              </div>
            </>
          )}
        </div>

        {/* Features */}
        <div className="mt-20 grid md:grid-cols-3 gap-8">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
              <span className="text-primary font-bold">✓</span>
            </div>
            <h3 className="text-xl font-semibold">Verified Tutors</h3>
            <p className="text-muted-foreground">
              Every tutor is personally verified with proven IB/AP expertise and university admissions success.
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
              <span className="text-primary font-bold">⚡</span>
            </div>
            <h3 className="text-xl font-semibold">Instant Booking</h3>
            <p className="text-muted-foreground">
              Book sessions instantly with transparent pricing. No phone calls or waiting for confirmations.
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
              <span className="text-primary font-bold">🎯</span>
            </div>
            <h3 className="text-xl font-semibold">Specialized Expertise</h3>
            <p className="text-muted-foreground">
              Tutors who understand IB assessment criteria and AP exam strategies for maximum score improvement.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}