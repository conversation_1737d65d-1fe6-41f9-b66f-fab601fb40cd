'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    
    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <span className="text-xl font-semibold text-gray-900">ProTutor</span>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/tutors" className="text-gray-700 hover:text-gray-900 text-sm font-medium">
                Find Tutors
              </Link>
              <Link href="#how-it-works" className="text-gray-700 hover:text-gray-900 text-sm font-medium">
                How It Works
              </Link>
              <Link href="#pricing" className="text-gray-700 hover:text-gray-900 text-sm font-medium">
                Pricing
              </Link>
              <Link href="#success-stories" className="text-gray-700 hover:text-gray-900 text-sm font-medium">
                Success Stories
              </Link>
            </nav>

            <div className="flex items-center space-x-3">
              {loading ? (
                <div className="animate-pulse">
                  <div className="h-9 w-20 bg-gray-200 rounded"></div>
                </div>
              ) : user ? (
                <>
                  <span className="text-sm text-gray-600 hidden lg:block">
                    Welcome, {user.email}
                  </span>
                  <Link href="/tutors">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                      Browse Tutors
                    </button>
                  </Link>
                  <Link href="/dashboard">
                    <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                      Dashboard
                    </button>
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/auth">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                      Sign In
                    </button>
                  </Link>
                  <Link href="/auth">
                    <button className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                      Get Started Free
                    </button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="py-20">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <div className="mb-12">
            <div className="w-28 h-28 bg-gray-300 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <div className="w-16 h-16 bg-gray-400 rounded-xl flex items-center justify-center">
                <svg className="w-10 h-10 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
          </div>

          {user ? (
            <>
              <h1 className="text-5xl font-bold text-gray-900 leading-tight mb-8">
                Welcome back to <span className="text-blue-600">ProTutor</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-12">
                Ready to continue your learning journey? Browse our expert tutors or manage your sessions.
              </p>
              
              <div className="flex gap-4 justify-center">
                <Link href="/tutors">
                  <button className="px-8 py-4 text-lg font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                    Find Tutors
                  </button>
                </Link>
                <Link href="/dashboard">
                  <button className="px-8 py-4 text-lg font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">
                    My Dashboard
                  </button>
                </Link>
              </div>
            </>
          ) : (
            <>
              <h1 className="text-5xl font-bold text-gray-900 leading-tight mb-16">
                Enhance your learning with<br />
                top-tier tutors
              </h1>
              
              <div>
                <Link href="/tutors">
                  <button className="px-12 py-4 text-lg font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 shadow-sm">
                    Find a tutor
                  </button>
                </Link>
              </div>
            </>
          )}
        </div>
      </main>

      {/* Simple Features */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-6">
          <div className="grid md:grid-cols-3 gap-16">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                <svg className="w-12 h-12 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Top Rated Tutors</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Connect with qualified IBDP tutors.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                <svg className="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Flexible Sessions</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Schedule lessons at your convenience.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                <svg className="w-12 h-12 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V7C19 8.1 18.1 9 17 9H16V19C16 20.1 15.1 21 14 21H10C8.9 21 8 20.1 8 19V9H7C5.9 9 5 8.1 5 7V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V7H17V6H7ZM10 9V19H14V9H10Z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Success Stories</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Hear from our satisfied students.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Simple Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <span className="text-xl font-bold">ProTutor</span>
            </div>
            <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
              Connecting students with expert IB and AP tutors worldwide. Achieve your academic goals with personalized, one-on-one instruction.
            </p>
            <div className="text-sm text-gray-500">
              © 2024 ProTutor. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
