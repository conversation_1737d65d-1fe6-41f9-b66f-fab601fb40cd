'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          {loading ? (
            <div className="animate-pulse">
              <div className="h-10 w-20 bg-gray-200 rounded"></div>
            </div>
          ) : user ? (
            <>
              <span className="text-sm text-gray-600 hidden md:block">
                Welcome, {user.email}
              </span>
              <Link href="/search">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Search Tutors
                </button>
              </Link>
              <Link href="/profile">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Profile
                </button>
              </Link>
              <Link href="/dashboard">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Dashboard
                </button>
              </Link>
              <button
                onClick={async () => {
                  await supabase.auth.signOut()
                  window.location.reload()
                }}
                className="text-gray-500 hover:text-gray-700 px-4 py-2 text-sm font-medium"
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link href="/auth">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Sign In
                </button>
              </Link>
              <Link href="/auth">
                <button className="bg-gray-800 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-900 transition-colors duration-200">
                  Get Started
                </button>
              </Link>
            </>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <main className="bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center space-y-8">
            <h1 className="text-6xl font-bold tracking-tight text-gray-900">
              Find Your Perfect <span className="text-gray-800">Tutor</span>
            </h1>
            <h2 className="text-4xl font-bold tracking-tight text-gray-700">
              or <span className="text-gray-800">Become an Online Tutor</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Connect students with verified, elite tutors specializing in IB Diploma and AP courses.
              Whether you're seeking academic excellence or sharing your expertise, join our community.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/search">
                <button className="bg-gray-800 text-white px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-900 transition-colors duration-200 w-full sm:w-auto">
                  Find a Tutor
                </button>
              </Link>
              <span className="text-gray-400 text-lg">or</span>
              <Link href="/auth">
                <button className="bg-white border-2 border-gray-800 text-gray-800 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-50 transition-colors duration-200 w-full sm:w-auto">
                  Become a Tutor
                </button>
              </Link>
            </div>
          </div>

          {/* Creative Misaligned Screenshots */}
          <div className="mt-20 relative">
            <div className="grid grid-cols-12 gap-4 items-center">
              {/* Large main screenshot - slightly off-center */}
              <div className="col-span-12 md:col-span-7 md:col-start-2">
                <div className="transform rotate-1 shadow-2xl">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-gray-400 text-5xl mb-3">🔍</div>
                      <p className="text-gray-500 text-lg font-medium">Main Search Interface</p>
                      <p className="text-gray-400 text-sm">Browse and discover tutors</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Smaller screenshot - top right */}
              <div className="col-span-6 md:col-span-3 md:col-start-10 -mt-16">
                <div className="transform -rotate-2 shadow-xl">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-48 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-gray-400 text-3xl mb-2">👨‍🏫</div>
                      <p className="text-gray-500 text-sm font-medium">Tutor Profile</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Medium screenshot - bottom left */}
              <div className="col-span-6 md:col-span-4 md:col-start-1 -mt-8">
                <div className="transform rotate-2 shadow-lg">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-56 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-gray-400 text-4xl mb-2">📅</div>
                      <p className="text-gray-500 text-sm font-medium">Booking System</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Small screenshot - floating */}
              <div className="col-span-4 md:col-span-2 md:col-start-9 -mt-12">
                <div className="transform -rotate-3 shadow-md">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-32 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-gray-400 text-2xl mb-1">💬</div>
                      <p className="text-gray-500 text-xs font-medium">Messages</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Choice Section */}
        <div className="bg-gray-900 py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-8">
              Choose Your Journey
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="text-4xl mb-4">🎓</div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">For Students</h3>
                <p className="text-gray-600 mb-6">
                  Find expert tutors, book sessions, and excel in your IB & AP courses
                </p>
                <button className="bg-gray-800 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-900 transition-colors duration-200 w-full">
                  Explore as Student
                </button>
              </div>
              <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="text-4xl mb-4">👨‍🏫</div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">For Tutors</h3>
                <p className="text-gray-600 mb-6">
                  Share your expertise, build your reputation, and earn income teaching online
                </p>
                <button className="bg-gray-800 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-900 transition-colors duration-200 w-full">
                  Explore as Tutor
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tutor Journey Section 1 - Profile Creation */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">1</div>
                  <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">For Tutors</span>
                </div>
                <h2 className="text-4xl font-bold text-gray-900">
                  Create Your Professional Profile
                </h2>
                <p className="text-lg text-gray-600">
                  Showcase your expertise, credentials, and teaching experience. Build a compelling profile
                  that attracts students and demonstrates your qualifications in IB & AP subjects.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Upload credentials and certifications</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Set your hourly rates and availability</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Highlight your teaching specialties</span>
                  </li>
                </ul>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="transform rotate-1">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center shadow-lg">
                    <div className="text-center">
                      <div className="text-gray-400 text-4xl mb-3">👨‍🏫</div>
                      <p className="text-gray-500 font-medium">poplanding2.png</p>
                      <p className="text-gray-400 text-sm">Tutor profile creation interface</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Book Instantly Section */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="order-2 lg:order-1">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">📅</div>
                    <p className="text-gray-500 font-medium">Booking Interface Screenshot</p>
                    <p className="text-gray-400 text-sm">Easy scheduling and booking flow</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6 order-1 lg:order-2">
                <h2 className="text-4xl font-bold text-gray-900">
                  Book Sessions Instantly
                </h2>
                <p className="text-lg text-gray-600">
                  No phone calls, no waiting for confirmations. Book sessions instantly with transparent pricing
                  and flexible scheduling that fits your busy academic calendar.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">⚡</span>
                    </div>
                    <span className="text-gray-700">Instant booking confirmation</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">💰</span>
                    </div>
                    <span className="text-gray-700">Transparent, upfront pricing</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📅</span>
                    </div>
                    <span className="text-gray-700">Flexible scheduling options</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Track Progress Section */}
        <div className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <h2 className="text-4xl font-bold text-gray-900">
                  Track Your Learning Journey
                </h2>
                <p className="text-lg text-gray-600">
                  Stay organized with your personalized dashboard. Track sessions, manage assignments,
                  monitor progress, and communicate with tutors all in one place.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📊</span>
                    </div>
                    <span className="text-gray-700">Progress tracking and analytics</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📝</span>
                    </div>
                    <span className="text-gray-700">Assignment and homework management</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">💬</span>
                    </div>
                    <span className="text-gray-700">Direct messaging with tutors</span>
                  </li>
                </ul>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">📊</div>
                    <p className="text-gray-500 font-medium">Dashboard Screenshot</p>
                    <p className="text-gray-400 text-sm">Student progress tracking interface</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Feature Section */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="order-2 lg:order-1">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">🎯</div>
                    <p className="text-gray-500 font-medium">Additional Features Screenshot</p>
                    <p className="text-gray-400 text-sm">Advanced platform capabilities</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6 order-1 lg:order-2">
                <h2 className="text-4xl font-bold text-gray-900">
                  Everything You Need to Succeed
                </h2>
                <p className="text-lg text-gray-600">
                  Our platform provides all the tools and resources you need for academic success.
                  From study materials to progress reports, we've got you covered.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🎯</span>
                    </div>
                    <span className="text-gray-700">Personalized learning plans</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📚</span>
                    </div>
                    <span className="text-gray-700">Comprehensive study resources</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🏆</span>
                    </div>
                    <span className="text-gray-700">Achievement tracking and rewards</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Final CTA Section */}
        <div className="bg-gray-900 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Excel in Your IB & AP Courses?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of students who have improved their grades with our expert tutors.
            </p>
            <div className="space-y-4">
              <Link href="/auth">
                <button className="bg-white text-gray-900 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-100 transition-colors duration-200">
                  Start Learning Today
                </button>
              </Link>
              <p className="text-sm text-gray-400">
                No commitment required • Cancel anytime • 100% satisfaction guaranteed
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}