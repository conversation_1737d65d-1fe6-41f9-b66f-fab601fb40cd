'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

// Custom hook for scroll animations
function useScrollAnimation() {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return [ref, isVisible] as const
}

// Animated section component
function AnimatedSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode,
  className?: string,
  delay?: number
}) {
  const [ref, isVisible] = useScrollAnimation()

  return (
    <div
      ref={ref}
      className={`transition-all duration-700 ease-out ${
        isVisible
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 translate-y-8'
      } ${className}`}
      style={{
        transitionDelay: isVisible ? `${delay}ms` : '0ms'
      }}
    >
      {children}
    </div>
  )
}

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          {loading ? (
            <div className="animate-pulse">
              <div className="h-10 w-20 bg-gray-200 rounded"></div>
            </div>
          ) : user ? (
            <>
              <span className="text-sm text-gray-600 hidden md:block">
                Welcome, {user.email}
              </span>
              <Link href="/search">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Search Tutors
                </button>
              </Link>
              <Link href="/profile">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Profile
                </button>
              </Link>
              <Link href="/dashboard">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Dashboard
                </button>
              </Link>
              <button
                onClick={async () => {
                  await supabase.auth.signOut()
                  window.location.reload()
                }}
                className="text-gray-500 hover:text-gray-700 px-4 py-2 text-sm font-medium"
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link href="/auth">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Sign In
                </button>
              </Link>
              <Link href="/auth">
                <button className="bg-gray-800 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-900 transition-colors duration-200">
                  Get Started
                </button>
              </Link>
            </>
          )}
        </div>
      </header>

      {/* Hero Section - Based on mainpage.png */}
      <main className="bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
            {/* Left side - Text content */}
            <div className="space-y-8 max-w-md">
              <h2 className="text-2xl font-medium tracking-tight text-gray-600">
                Tutor on a trusted platform
              </h2>
              <h1 className="text-6xl font-bold tracking-tight text-gray-900 leading-tight">
                Powerful tutoring and classes.
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                Set your own rate, we'll connect you with students, you earn money on your schedule.
              </p>

              <div className="space-y-4">
                <Link href="/auth">
                  <button className="bg-white border-2 border-gray-800 text-gray-800 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-50 transition-colors duration-200">
                    Get started today
                  </button>
                </Link>
              </div>
            </div>

            {/* Right side - Large tutor profile image */}
            <AnimatedSection delay={300}>
              <div className="mt-10 lg:mt-0">
                <div className="transform rotate-1 shadow-2xl">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-96 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-gray-400 text-6xl mb-4">👨‍🏫</div>
                      <p className="text-gray-500 text-xl font-medium">Large Tutor Profile Screenshot</p>
                      <p className="text-gray-400 text-sm">Complete tutor interface and dashboard</p>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>

        {/* Section 1 - Based on mainpage.png structure */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="space-y-6">
                    <h2 className="text-4xl font-bold text-gray-900">
                      Create Your Professional Profile
                    </h2>
                    <p className="text-lg text-gray-600">
                      Showcase your expertise, credentials, and teaching experience. Build a compelling profile
                      that attracts students and demonstrates your qualifications.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Upload credentials and certifications</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Set your hourly rates and availability</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Highlight your teaching specialties</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="mt-10 lg:mt-0">
                    <div className="transform -rotate-1 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">📝</div>
                          <p className="text-gray-500 font-medium">poplanding2.png</p>
                          <p className="text-gray-400 text-sm">Profile creation interface</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Tutor Journey Section 1 - Profile Creation */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">1</div>
                  <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">For Tutors</span>
                </div>
                <h2 className="text-4xl font-bold text-gray-900">
                  Create Your Professional Profile
                </h2>
                <p className="text-lg text-gray-600">
                  Showcase your expertise, credentials, and teaching experience. Build a compelling profile
                  that attracts students and demonstrates your qualifications in IB & AP subjects.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Upload credentials and certifications</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Set your hourly rates and availability</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Highlight your teaching specialties</span>
                  </li>
                </ul>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="transform rotate-1">
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center shadow-lg">
                    <div className="text-center">
                      <div className="text-gray-400 text-4xl mb-3">👨‍🏫</div>
                      <p className="text-gray-500 font-medium">poplanding2.png</p>
                      <p className="text-gray-400 text-sm">Tutor profile creation interface</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 2 - Manage Students */}
        <AnimatedSection>
          <div className="bg-white py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="order-2 lg:order-1">
                    <div className="transform rotate-2 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">📊</div>
                          <p className="text-gray-500 font-medium">poplanding3.png</p>
                          <p className="text-gray-400 text-sm">Student management dashboard</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="space-y-6 order-1 lg:order-2">
                    <h2 className="text-4xl font-bold text-gray-900">
                      Manage Your Students
                    </h2>
                    <p className="text-lg text-gray-600">
                      Keep track of all your students, their progress, and upcoming sessions in one organized dashboard.
                      Monitor performance and provide personalized feedback.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📊</span>
                        </div>
                        <span className="text-gray-700">Track student progress and performance</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📝</span>
                        </div>
                        <span className="text-gray-700">Manage assignments and homework</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">💬</span>
                        </div>
                        <span className="text-gray-700">Communicate directly with students</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Section 3 - Earn & Schedule */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="space-y-6">
                    <h2 className="text-4xl font-bold text-gray-900">
                      Earn & Grow Your Business
                    </h2>
                    <p className="text-lg text-gray-600">
                      Set your own rates, manage your schedule, and grow your tutoring business.
                      Track earnings, receive payments securely, and build your reputation.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">💰</span>
                        </div>
                        <span className="text-gray-700">Set your own hourly rates</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📅</span>
                        </div>
                        <span className="text-gray-700">Flexible scheduling and availability</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">⭐</span>
                        </div>
                        <span className="text-gray-700">Build reputation through reviews</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="mt-10 lg:mt-0">
                    <div className="transform -rotate-2 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">💰</div>
                          <p className="text-gray-500 font-medium">poplanding4.png</p>
                          <p className="text-gray-400 text-sm">Earnings and scheduling interface</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Tutor Journey Section 3 - Earn & Grow */}
        <AnimatedSection>
          <div className="bg-white py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">3</div>
                      <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">For Tutors</span>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      Earn & Grow Your Business
                    </h2>
                    <p className="text-lg text-gray-600">
                      Set your own rates, manage your schedule, and grow your tutoring business.
                      Track earnings, receive payments securely, and build your reputation.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">💰</span>
                        </div>
                        <span className="text-gray-700">Set your own hourly rates</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📅</span>
                        </div>
                        <span className="text-gray-700">Flexible scheduling and availability</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">⭐</span>
                        </div>
                        <span className="text-gray-700">Build reputation through reviews</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="mt-10 lg:mt-0">
                    <div className="transform rotate-2">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center shadow-lg">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">💰</div>
                          <p className="text-gray-500 font-medium">poplanding4.png</p>
                          <p className="text-gray-400 text-sm">Earnings and scheduling interface</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Student Journey Placeholder Sections */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <div className="space-y-6">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">🎓</div>
                  <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">For Students</span>
                </div>
                <h2 className="text-4xl font-bold text-gray-900">
                  Student Journey Coming Soon
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  We're crafting an amazing experience for students. This section will showcase
                  how students can find tutors, book sessions, and track their academic progress.
                </p>
                <div className="grid md:grid-cols-3 gap-8 mt-12">
                  <AnimatedSection delay={200}>
                    <div className="bg-white p-6 rounded-lg shadow-lg">
                      <div className="text-4xl mb-4">🔍</div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Find Tutors</h3>
                      <p className="text-gray-600 text-sm">Browse and discover expert tutors</p>
                    </div>
                  </AnimatedSection>
                  <AnimatedSection delay={400}>
                    <div className="bg-white p-6 rounded-lg shadow-lg">
                      <div className="text-4xl mb-4">📅</div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Book Sessions</h3>
                      <p className="text-gray-600 text-sm">Schedule lessons instantly</p>
                    </div>
                  </AnimatedSection>
                  <AnimatedSection delay={600}>
                    <div className="bg-white p-6 rounded-lg shadow-lg">
                      <div className="text-4xl mb-4">📊</div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Track Progress</h3>
                      <p className="text-gray-600 text-sm">Monitor your academic growth</p>
                    </div>
                  </AnimatedSection>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Final CTA Section */}
        <AnimatedSection>
          <div className="bg-gray-900 py-20">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Start Your Journey?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Whether you're a student seeking academic excellence or an expert ready to teach,
                join our community today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <AnimatedSection delay={200}>
                  <Link href="/search">
                    <button className="bg-white text-gray-900 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-100 transition-colors duration-200 w-full sm:w-auto">
                      Find a Tutor
                    </button>
                  </Link>
                </AnimatedSection>
                <span className="text-gray-400 text-lg">or</span>
                <AnimatedSection delay={400}>
                  <Link href="/auth">
                    <button className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-md text-lg font-medium hover:bg-white hover:text-gray-900 transition-colors duration-200 w-full sm:w-auto">
                      Become a Tutor
                    </button>
                  </Link>
                </AnimatedSection>
              </div>
              <AnimatedSection delay={600}>
                <p className="text-sm text-gray-400 mt-6">
                  No commitment required • Cancel anytime • 100% satisfaction guaranteed
                </p>
              </AnimatedSection>
            </div>
          </div>
        </AnimatedSection>
      </main>
    </div>
  )
}