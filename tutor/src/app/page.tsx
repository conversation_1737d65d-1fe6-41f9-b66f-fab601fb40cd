'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  return (
    <div className="min-h-screen bg-white">
      {/* Custom Header */}
      <header className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-7 h-7 bg-black rounded-sm flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
              <span className="text-xl font-semibold text-gray-900">ProTutor</span>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/tutors" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Expert Tutors
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Personalised
              </Link>
              <Link href="#pricing" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Pricing
              </Link>
              <Link href="#testimonials" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Success Stories
              </Link>
            </nav>

            {/* Auth Buttons */}
            <div className="flex items-center space-x-3">
              {loading ? (
                <div className="animate-pulse">
                  <div className="h-9 w-20 bg-gray-200 rounded"></div>
                </div>
              ) : user ? (
                <>
                  <span className="text-sm text-gray-600 hidden lg:block">
                    Welcome, {user.email}
                  </span>
                  <Link href="/tutors">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                      Browse Tutors
                    </button>
                  </Link>
                  <Link href="/dashboard">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                      Dashboard
                    </button>
                  </Link>
                  <button
                    onClick={async () => {
                      await supabase.auth.signOut()
                      window.location.reload()
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link href="/auth">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                      Log In
                    </button>
                  </Link>
                  <Link href="/auth">
                    <button className="px-6 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800 transition-colors">
                      Try for free
                    </button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Custom Hero Section */}
      <main className="bg-gray-100 py-20">
        <div className="max-w-4xl mx-auto px-6 text-center">
          {/* Hero Icon */}
          <div className="flex justify-center mb-12">
            <div className="w-28 h-28 bg-gray-300 rounded-2xl flex items-center justify-center shadow-sm">
              <div className="w-16 h-16 bg-gray-400 rounded-xl flex items-center justify-center">
                {/* Custom person with presentation board icon */}
                <div className="relative">
                  <div className="w-8 h-8 bg-gray-600 rounded-full mb-1"></div>
                  <div className="w-10 h-6 bg-gray-600 rounded-t-lg"></div>
                  <div className="absolute -right-3 top-1 w-4 h-3 bg-gray-600 rounded-sm"></div>
                </div>
              </div>
            </div>
          </div>

          {user ? (
            <>
              <h1 className="text-5xl font-bold text-gray-900 leading-tight mb-8">
                Welcome back to <span className="text-blue-600">ProTutor</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-12">
                Ready to continue your learning journey? Browse our expert tutors or manage your sessions.
              </p>

              <div className="flex gap-4 justify-center">
                <Link href="/tutors">
                  <button className="px-8 py-4 text-lg font-medium text-white bg-black rounded-lg hover:bg-gray-800 transition-colors">
                    Find Tutors
                  </button>
                </Link>
                <Link href="/dashboard">
                  <button className="px-8 py-4 text-lg font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    My Dashboard
                  </button>
                </Link>
              </div>
            </>
          ) : (
            <>
              <h1 className="text-5xl font-bold text-gray-900 leading-tight mb-16">
                Enhance your learning with<br />
                top-tier tutors
              </h1>

              <div>
                <Link href="/tutors">
                  <button className="px-12 py-4 text-lg font-medium text-white bg-black rounded-lg hover:bg-gray-800 transition-colors shadow-sm">
                    Find a tutor
                  </button>
                </Link>
              </div>
            </>
          )}
        </div>
      </main>

      {/* Custom Features Section */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-5xl mx-auto px-6">
          <div className="grid md:grid-cols-3 gap-16">
            {/* Top Rated Tutors */}
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                {/* Custom star icon */}
                <svg className="w-12 h-12 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Top Rated Tutors</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Connect with qualified IBDP tutors.
              </p>
            </div>

            {/* Flexible Sessions */}
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                {/* Custom clock icon */}
                <svg className="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Flexible Sessions</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Schedule lessons at your convenience.
              </p>
            </div>

            {/* Success Stories */}
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                {/* Custom trophy icon */}
                <svg className="w-12 h-12 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V7C19 8.1 18.1 9 17 9H16V19C16 20.1 15.1 21 14 21H10C8.9 21 8 20.1 8 19V9H7C5.9 9 5 8.1 5 7V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V7H17V6H7ZM10 9V19H14V9H10Z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Success Stories</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Hear from our satisfied students.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Testimonials Section */}
      <section className="bg-white py-20" id="testimonials">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900">
              Join the ProTutor community and learn with confidence
            </h2>
          </div>

          {/* Custom Testimonials Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Testimonial 1 */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 text-sm mb-6 leading-relaxed">
                "I've improved my grades significantly."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                  <span className="text-gray-600 text-xs font-medium">ER</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Emily R.</p>
                  <p className="text-xs text-gray-500">High School Student</p>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 text-sm mb-6 leading-relaxed">
                "ProTutor made learning enjoyable!"
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                  <span className="text-gray-600 text-xs font-medium">DT</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">David T.</p>
                  <p className="text-xs text-gray-500">University Student</p>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 text-sm mb-6 leading-relaxed">
                "The tutors are incredibly supportive."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                  <span className="text-gray-600 text-xs font-medium">SK</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Sophie K.</p>
                  <p className="text-xs text-gray-500">IB Student</p>
                </div>
              </div>
            </div>

            {/* Testimonial 4 */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 text-sm mb-6 leading-relaxed">
                "I finally understand my subjects better!"
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                  <span className="text-gray-600 text-xs font-medium">MX</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Michael X.</p>
                  <p className="text-xs text-gray-500">AP Student</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Pricing Section */}
      <section className="bg-gray-100 py-20" id="pricing">
        <div className="max-w-5xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900">
              Choose your plan
            </h2>
          </div>

          {/* Custom Pricing Grid */}
          <div className="grid md:grid-cols-3 gap-8">
            {/* Premium Plan - $40 */}
            <div className="bg-white border border-gray-200 rounded-lg p-8 shadow-sm">
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Premium</h3>
                <div className="mb-6">
                  <span className="text-3xl font-bold text-gray-900">$40</span>
                  <span className="text-gray-600">/ month</span>
                </div>

                <div className="space-y-3 mb-8 text-left">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">5 sessions monthly</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Basic resources</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Connect with tutors</span>
                  </div>
                </div>

                <button className="w-full py-3 px-4 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors">
                  Select Basic
                </button>
              </div>
            </div>

            {/* Pro Plan - $60 */}
            <div className="bg-white border border-gray-200 rounded-lg p-8 shadow-sm relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-black text-white px-4 py-1 rounded-full text-sm font-medium">
                  UPGRADE PLAN
                </span>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Pro</h3>
                <div className="mb-6">
                  <span className="text-3xl font-bold text-gray-900">$60</span>
                  <span className="text-gray-600">/ month</span>
                </div>

                <div className="space-y-3 mb-8 text-left">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">10 sessions monthly</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Priority resources</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-red-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Connect with tutors</span>
                  </div>
                </div>

                <button className="w-full py-3 px-4 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors">
                  Select Pro
                </button>
              </div>
            </div>

            {/* Premium Plan - $80 */}
            <div className="bg-white border border-gray-200 rounded-lg p-8 shadow-sm">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Premium</h3>
                <div className="mb-6">
                  <span className="text-3xl font-bold text-gray-900">$80</span>
                  <span className="text-gray-600">/ month</span>
                </div>

                <div className="space-y-3 mb-8 text-left">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Unlimited sessions</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Premium resources</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">Connect with tutors</span>
                  </div>
                </div>

                <button className="w-full py-3 px-4 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors">
                  Select Basic
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid md:grid-cols-5 gap-8">
            {/* ProTutor Brand */}
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <div className="w-7 h-7 bg-black rounded-sm flex items-center justify-center mr-2">
                  <div className="w-4 h-4 bg-white rounded-sm"></div>
                </div>
                <h3 className="text-xl font-semibold">ProTutor</h3>
              </div>
            </div>

            {/* Learn More About Us */}
            <div>
              <h4 className="font-medium mb-4 text-gray-300">Learn more about us</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About ProTutor</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Media enquiries</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Subscription plans</a></li>
              </ul>
            </div>

            {/* Help Center */}
            <div>
              <h4 className="font-medium mb-4 text-gray-300">Help center</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Customer support</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Frequently asked questions</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Need help?</a></li>
              </ul>
            </div>

            {/* Join our Community */}
            <div>
              <h4 className="font-medium mb-4 text-gray-300">Join our community</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Facebook</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Discord Community</a></li>
              </ul>
            </div>

            {/* Download our App */}
            <div>
              <h4 className="font-medium mb-4 text-gray-300">Download our App</h4>
              <div className="space-y-3">
                <div className="bg-gray-800 rounded-lg p-3 flex items-center space-x-3 cursor-pointer hover:bg-gray-700 transition-colors">
                  <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                    <span className="text-xs">📱</span>
                  </div>
                  <div>
                    <p className="text-xs text-gray-400">Get it on</p>
                    <p className="text-sm font-medium">App Store</p>
                  </div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 flex items-center space-x-3 cursor-pointer hover:bg-gray-700 transition-colors">
                  <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                    <span className="text-xs">🤖</span>
                  </div>
                  <div>
                    <p className="text-xs text-gray-400">Get it on</p>
                    <p className="text-sm font-medium">Google Play</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-800 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              {/* Links */}
              <div className="flex flex-wrap gap-6 text-sm text-gray-400">
                <a href="#" className="hover:text-white transition-colors">Our Mission</a>
                <a href="#" className="hover:text-white transition-colors">Testimonials</a>
                <a href="#" className="hover:text-white transition-colors">Support Education</a>
                <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              </div>

              {/* Social Icons and Newsletter */}
              <div className="flex items-center space-x-4">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors">
                    <span className="text-xs font-bold">f</span>
                  </div>
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors">
                    <span className="text-xs">📷</span>
                  </div>
                </div>

                {/* Newsletter Subscription */}
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Subscribe Now"
                    className="bg-gray-800 text-white px-4 py-2 rounded-l-lg text-sm border border-gray-700 focus:outline-none focus:border-gray-600 w-40"
                  />
                  <button className="bg-gray-700 hover:bg-gray-600 rounded-l-none px-4 py-2 text-white transition-colors">
                    →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}