'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-4">
          {loading ? (
            <div className="animate-pulse">
              <div className="h-10 w-20 bg-gray-200 rounded"></div>
            </div>
          ) : user ? (
            <>
              <span className="text-sm text-gray-600 hidden md:block">
                Welcome, {user.email}
              </span>
              <Link href="/search">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Search Tutors
                </button>
              </Link>
              <Link href="/profile">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Profile
                </button>
              </Link>
              <Link href="/dashboard">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Dashboard
                </button>
              </Link>
              <button
                onClick={async () => {
                  await supabase.auth.signOut()
                  window.location.reload()
                }}
                className="text-gray-500 hover:text-gray-700 px-4 py-2 text-sm font-medium"
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link href="/auth">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                  Sign In
                </button>
              </Link>
              <Link href="/auth">
                <button className="bg-gray-800 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-900 transition-colors duration-200">
                  Get Started
                </button>
              </Link>
            </>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <main className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center space-y-8">
            {user ? (
              <>
                <h1 className="text-5xl font-bold tracking-tight text-gray-900">
                  Welcome back to <span className="text-gray-800">ProTutor</span>
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Ready to continue your learning journey? Browse our expert tutors or manage your sessions.
                </p>

                <div className="space-y-4">
                  <div className="flex gap-4 justify-center">
                    <Link href="/search">
                      <button className="bg-gray-800 text-white px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-900 transition-colors duration-200">
                        Find Tutors
                      </button>
                    </Link>
                    <Link href="/dashboard">
                      <button className="bg-white border border-gray-300 text-gray-700 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-50 transition-colors duration-200">
                        My Dashboard
                      </button>
                    </Link>
                  </div>
                </div>
              </>
            ) : (
              <>
                <h1 className="text-5xl font-bold tracking-tight text-gray-900">
                  Find Your Perfect <span className="text-gray-800">IB & AP Tutor</span>
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Connect with verified, elite tutors who specialize in IB Diploma and AP courses.
                  Get personalized guidance from experts who understand your academic goals.
                </p>

                <div className="space-y-4">
                  <Link href="/auth">
                    <button className="bg-gray-800 text-white px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-900 transition-colors duration-200">
                      Start Learning Today
                    </button>
                  </Link>
                  <p className="text-sm text-gray-500">
                    Join students from top international schools worldwide
                  </p>
                </div>
              </>
            )}
          </div>

          {/* Hero Image Placeholder */}
          <div className="mt-16">
            <div className="relative max-w-5xl mx-auto">
              <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-96 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-gray-400 text-6xl mb-4">📸</div>
                  <p className="text-gray-500 text-lg font-medium">Hero Screenshot Placeholder</p>
                  <p className="text-gray-400 text-sm">Main platform interface screenshot goes here</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Meet Your Tutors Section */}
        <div className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <h2 className="text-4xl font-bold text-gray-900">
                  Meet Your Expert Tutors
                </h2>
                <p className="text-lg text-gray-600">
                  Every tutor is personally verified with proven IB/AP expertise and university admissions success.
                  Browse detailed profiles, read reviews, and find the perfect match for your learning style.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Verified academic credentials</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Proven track record of student success</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-gray-700">Specialized in IB & AP programs</span>
                  </li>
                </ul>
                <Link href="/search">
                  <button className="bg-gray-800 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-900 transition-colors duration-200">
                    Browse Tutors
                  </button>
                </Link>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">👨‍🏫</div>
                    <p className="text-gray-500 font-medium">Tutor Profiles Screenshot</p>
                    <p className="text-gray-400 text-sm">Detailed tutor profiles interface</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Book Instantly Section */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="order-2 lg:order-1">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">📅</div>
                    <p className="text-gray-500 font-medium">Booking Interface Screenshot</p>
                    <p className="text-gray-400 text-sm">Easy scheduling and booking flow</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6 order-1 lg:order-2">
                <h2 className="text-4xl font-bold text-gray-900">
                  Book Sessions Instantly
                </h2>
                <p className="text-lg text-gray-600">
                  No phone calls, no waiting for confirmations. Book sessions instantly with transparent pricing
                  and flexible scheduling that fits your busy academic calendar.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">⚡</span>
                    </div>
                    <span className="text-gray-700">Instant booking confirmation</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">💰</span>
                    </div>
                    <span className="text-gray-700">Transparent, upfront pricing</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📅</span>
                    </div>
                    <span className="text-gray-700">Flexible scheduling options</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Track Progress Section */}
        <div className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <h2 className="text-4xl font-bold text-gray-900">
                  Track Your Learning Journey
                </h2>
                <p className="text-lg text-gray-600">
                  Stay organized with your personalized dashboard. Track sessions, manage assignments,
                  monitor progress, and communicate with tutors all in one place.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📊</span>
                    </div>
                    <span className="text-gray-700">Progress tracking and analytics</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📝</span>
                    </div>
                    <span className="text-gray-700">Assignment and homework management</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">💬</span>
                    </div>
                    <span className="text-gray-700">Direct messaging with tutors</span>
                  </li>
                </ul>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">📊</div>
                    <p className="text-gray-500 font-medium">Dashboard Screenshot</p>
                    <p className="text-gray-400 text-sm">Student progress tracking interface</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Feature Section */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
              <div className="order-2 lg:order-1">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-4xl mb-3">🎯</div>
                    <p className="text-gray-500 font-medium">Additional Features Screenshot</p>
                    <p className="text-gray-400 text-sm">Advanced platform capabilities</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6 order-1 lg:order-2">
                <h2 className="text-4xl font-bold text-gray-900">
                  Everything You Need to Succeed
                </h2>
                <p className="text-lg text-gray-600">
                  Our platform provides all the tools and resources you need for academic success.
                  From study materials to progress reports, we've got you covered.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🎯</span>
                    </div>
                    <span className="text-gray-700">Personalized learning plans</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">📚</span>
                    </div>
                    <span className="text-gray-700">Comprehensive study resources</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🏆</span>
                    </div>
                    <span className="text-gray-700">Achievement tracking and rewards</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Final CTA Section */}
        <div className="bg-gray-900 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Excel in Your IB & AP Courses?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of students who have improved their grades with our expert tutors.
            </p>
            <div className="space-y-4">
              <Link href="/auth">
                <button className="bg-white text-gray-900 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-100 transition-colors duration-200">
                  Start Learning Today
                </button>
              </Link>
              <p className="text-sm text-gray-400">
                No commitment required • Cancel anytime • 100% satisfaction guaranteed
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}