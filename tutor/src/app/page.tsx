'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-neutral-50)' }}>
      {/* Research-Based Header - Trust & Professionalism */}
      <header className="bg-white shadow-sm border-b" style={{ borderColor: 'var(--color-neutral-200)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo - Trust & Recognition */}
            <div className="flex items-center">
              <div className="flex items-center space-x-3">
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: 'var(--color-primary-600)' }}
                >
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <span className="text-heading-3" style={{ color: 'var(--color-primary-600)' }}>
                  ProTutor
                </span>
              </div>
            </div>

            {/* Navigation - Clear Hierarchy */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/tutors" className="text-body font-medium hover:text-primary-600 transition-colors">
                Find Tutors
              </Link>
              <Link href="#how-it-works" className="text-body font-medium hover:text-primary-600 transition-colors">
                How It Works
              </Link>
              <Link href="#pricing" className="text-body font-medium hover:text-primary-600 transition-colors">
                Pricing
              </Link>
              <Link href="#success-stories" className="text-body font-medium hover:text-primary-600 transition-colors">
                Success Stories
              </Link>
            </nav>

            {/* CTA Buttons - Clear Action Hierarchy */}
            <div className="flex items-center space-x-4">
              {loading ? (
                <div className="animate-pulse">
                  <div className="h-10 w-20 bg-gray-200 rounded"></div>
                </div>
              ) : user ? (
                <>
                  <span className="text-body-small hidden lg:block" style={{ color: 'var(--color-neutral-600)' }}>
                    Welcome, {user.email}
                  </span>
                  <Link href="/tutors" className="hidden sm:block">
                    <button className="btn-outline">Browse Tutors</button>
                  </Link>
                  <Link href="/dashboard" className="hidden sm:block">
                    <button className="btn-primary">Dashboard</button>
                  </Link>
                  <button
                    className="text-body-small hover:text-primary-600 transition-colors hidden sm:block"
                    onClick={async () => {
                      await supabase.auth.signOut()
                      window.location.reload()
                    }}
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link href="/auth" className="hidden sm:block">
                    <button className="btn-outline">Sign In</button>
                  </Link>
                  <Link href="/auth" className="hidden sm:block">
                    <button className="btn-accent">Get Started Free</button>
                  </Link>
                </>
              )}

              {/* Mobile Menu Button */}
              <button
                className="md:hidden p-2"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                aria-label="Toggle mobile menu"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {mobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden border-t" style={{ borderColor: 'var(--color-neutral-200)' }}>
              <div className="px-4 py-4 space-y-4">
                <Link href="/tutors" className="block text-body font-medium hover:text-primary-600 transition-colors">
                  Find Tutors
                </Link>
                <Link href="#how-it-works" className="block text-body font-medium hover:text-primary-600 transition-colors">
                  How It Works
                </Link>
                <Link href="#pricing" className="block text-body font-medium hover:text-primary-600 transition-colors">
                  Pricing
                </Link>
                <Link href="#success-stories" className="block text-body font-medium hover:text-primary-600 transition-colors">
                  Success Stories
                </Link>

                <div className="pt-4 border-t space-y-3" style={{ borderColor: 'var(--color-neutral-200)' }}>
                  {user ? (
                    <>
                      <Link href="/tutors">
                        <button className="btn-outline w-full">Browse Tutors</button>
                      </Link>
                      <Link href="/dashboard">
                        <button className="btn-primary w-full">Dashboard</button>
                      </Link>
                      <button
                        className="w-full text-center text-body-small hover:text-primary-600 transition-colors py-2"
                        onClick={async () => {
                          await supabase.auth.signOut()
                          window.location.reload()
                        }}
                      >
                        Logout
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/auth">
                        <button className="btn-outline w-full">Sign In</button>
                      </Link>
                      <Link href="/auth">
                        <button className="btn-accent w-full">Get Started Free</button>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
          </div>
        </div>
      </header>

      {/* Hero Section - Research-Based Trust & Value Proposition */}
      <main className="relative overflow-hidden">
        {/* Background Pattern for Visual Interest */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, var(--color-primary-200) 0%, transparent 50%),
                             radial-gradient(circle at 75% 75%, var(--color-secondary-200) 0%, transparent 50%)`
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28">
          <div className="text-center">
            {user ? (
              <>
                <h1 className="text-display-1 mb-6">
                  Welcome back to <span style={{ color: 'var(--color-primary-600)' }}>ProTutor</span>
                </h1>
                <p className="text-body-large max-w-3xl mx-auto mb-10" style={{ color: 'var(--color-neutral-600)' }}>
                  Ready to continue your learning journey? Browse our expert tutors or manage your sessions.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/tutors">
                    <button className="btn-primary text-lg px-8 py-4">
                      Find Tutors
                    </button>
                  </Link>
                  <Link href="/dashboard">
                    <button className="btn-outline text-lg px-8 py-4">
                      My Dashboard
                    </button>
                  </Link>
                </div>
              </>
            ) : (
              <>
                {/* Trust Badge */}
                <div className="inline-flex items-center px-4 py-2 rounded-full mb-6"
                     style={{ backgroundColor: 'var(--color-secondary-50)', color: 'var(--color-secondary-700)' }}>
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-body-small font-medium">Trusted by 10,000+ Students Worldwide</span>
                </div>

                {/* Main Value Proposition */}
                <h1 className="text-display-1 mb-6 leading-tight">
                  Achieve Your <span style={{ color: 'var(--color-primary-600)' }}>IB & AP</span> Goals<br />
                  with Expert Tutors
                </h1>

                <p className="text-body-large max-w-3xl mx-auto mb-10" style={{ color: 'var(--color-neutral-600)' }}>
                  Connect with verified, elite tutors who specialize in IB Diploma and AP courses.
                  Get personalized guidance from experts who understand your academic goals and help you succeed.
                </p>

                {/* Primary CTA */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                  <Link href="/auth">
                    <button className="btn-accent text-lg px-10 py-4 shadow-lg">
                      Start Learning Today - Free Trial
                    </button>
                  </Link>
                  <Link href="/tutors">
                    <button className="btn-outline text-lg px-8 py-4">
                      Browse Expert Tutors
                    </button>
                  </Link>
                </div>

                {/* Trust Indicators */}
                <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-body-small"
                     style={{ color: 'var(--color-neutral-500)' }}>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span>4.9/5 Average Rating</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" style={{ color: 'var(--color-secondary-500)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>500+ Verified Tutors</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" style={{ color: 'var(--color-primary-500)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>24/7 Support</span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </main>

      {/* How It Works Section - Clear Process Flow */}
      <section id="how-it-works" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-heading-1 mb-4">How ProTutor Works</h2>
            <p className="text-body-large max-w-2xl mx-auto" style={{ color: 'var(--color-neutral-600)' }}>
              Get started with expert tutoring in just three simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            {/* Step 1 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div
                  className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"
                  style={{ backgroundColor: 'var(--color-primary-100)' }}
                >
                  <svg className="w-10 h-10" style={{ color: 'var(--color-primary-600)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <div
                  className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                  style={{ backgroundColor: 'var(--color-accent-500)' }}
                >
                  1
                </div>
              </div>
              <h3 className="text-heading-3 mb-4">Find Your Perfect Tutor</h3>
              <p className="text-body" style={{ color: 'var(--color-neutral-600)' }}>
                Browse our verified tutors by subject, availability, and expertise. Read reviews and compare profiles to find your ideal match.
              </p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div
                  className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"
                  style={{ backgroundColor: 'var(--color-secondary-100)' }}
                >
                  <svg className="w-10 h-10" style={{ color: 'var(--color-secondary-600)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div
                  className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                  style={{ backgroundColor: 'var(--color-accent-500)' }}
                >
                  2
                </div>
              </div>
              <h3 className="text-heading-3 mb-4">Book Your Session</h3>
              <p className="text-body" style={{ color: 'var(--color-neutral-600)' }}>
                Schedule sessions that fit your timeline. Choose from one-time help or ongoing support with flexible pricing options.
              </p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div
                  className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"
                  style={{ backgroundColor: 'var(--color-accent-100)' }}
                >
                  <svg className="w-10 h-10" style={{ color: 'var(--color-accent-600)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div
                  className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                  style={{ backgroundColor: 'var(--color-accent-500)' }}
                >
                  3
                </div>
              </div>
              <h3 className="text-heading-3 mb-4">Achieve Your Goals</h3>
              <p className="text-body" style={{ color: 'var(--color-neutral-600)' }}>
                Get personalized instruction, track your progress, and see real improvements in your grades and understanding.
              </p>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <Link href="/auth">
              <button className="btn-primary text-lg px-8 py-4">
                Get Started Today
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Trust Indicators & Social Proof */}
      <section className="py-16" style={{ backgroundColor: 'var(--color-neutral-50)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-heading-2" style={{ color: 'var(--color-primary-600)' }}>10K+</div>
              <div className="text-body-small" style={{ color: 'var(--color-neutral-600)' }}>Students Helped</div>
            </div>
            <div>
              <div className="text-heading-2" style={{ color: 'var(--color-secondary-600)' }}>500+</div>
              <div className="text-body-small" style={{ color: 'var(--color-neutral-600)' }}>Expert Tutors</div>
            </div>
            <div>
              <div className="text-heading-2" style={{ color: 'var(--color-accent-600)' }}>4.9★</div>
              <div className="text-body-small" style={{ color: 'var(--color-neutral-600)' }}>Average Rating</div>
            </div>
            <div>
              <div className="text-heading-2" style={{ color: 'var(--color-primary-600)' }}>95%</div>
              <div className="text-body-small" style={{ color: 'var(--color-neutral-600)' }}>Success Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories & Testimonials */}
      <section id="success-stories" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-heading-1 mb-4">Student Success Stories</h2>
            <p className="text-body-large max-w-2xl mx-auto" style={{ color: 'var(--color-neutral-600)' }}>
              See how ProTutor has helped students achieve their academic goals
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="card">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-body mb-6" style={{ color: 'var(--color-neutral-700)' }}>
                "My IB Math HL score improved from a 4 to a 7 thanks to my ProTutor. The personalized approach and exam strategies were game-changing."
              </p>
              <div className="flex items-center">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
                  style={{ backgroundColor: 'var(--color-primary-100)', color: 'var(--color-primary-600)' }}
                >
                  <span className="font-semibold">SA</span>
                </div>
                <div>
                  <div className="text-body font-semibold" style={{ color: 'var(--color-neutral-800)' }}>Sarah A.</div>
                  <div className="text-body-small" style={{ color: 'var(--color-neutral-500)' }}>IB Diploma Student</div>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="card">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-body mb-6" style={{ color: 'var(--color-neutral-700)' }}>
                "Got into my dream university! My AP Chemistry tutor helped me understand complex concepts and ace the exam with a 5."
              </p>
              <div className="flex items-center">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
                  style={{ backgroundColor: 'var(--color-secondary-100)', color: 'var(--color-secondary-600)' }}
                >
                  <span className="font-semibold">MR</span>
                </div>
                <div>
                  <div className="text-body font-semibold" style={{ color: 'var(--color-neutral-800)' }}>Marcus R.</div>
                  <div className="text-body-small" style={{ color: 'var(--color-neutral-500)' }}>AP Student</div>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="card">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-body mb-6" style={{ color: 'var(--color-neutral-700)' }}>
                "The flexibility and quality of tutoring is unmatched. My tutor adapted to my learning style and schedule perfectly."
              </p>
              <div className="flex items-center">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
                  style={{ backgroundColor: 'var(--color-accent-100)', color: 'var(--color-accent-600)' }}
                >
                  <span className="font-semibold">EL</span>
                </div>
                <div>
                  <div className="text-body font-semibold" style={{ color: 'var(--color-neutral-800)' }}>Emma L.</div>
                  <div className="text-body-small" style={{ color: 'var(--color-neutral-500)' }}>IB Student</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Tutors Preview */}
      <section className="py-20" style={{ backgroundColor: 'var(--color-neutral-50)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-heading-1 mb-4">Meet Our Expert Tutors</h2>
            <p className="text-body-large max-w-2xl mx-auto" style={{ color: 'var(--color-neutral-600)' }}>
              All tutors are verified experts with proven track records in IB and AP success
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Tutor 1 */}
            <div className="card text-center">
              <div
                className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-xl font-bold"
                style={{ backgroundColor: 'var(--color-primary-600)' }}
              >
                DR
              </div>
              <h3 className="text-heading-3 mb-2">Dr. Rachel M.</h3>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-primary-600)' }}>IB Mathematics HL/SL</p>
              <div className="flex items-center justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="ml-2 text-body-small" style={{ color: 'var(--color-neutral-600)' }}>5.0 (127 reviews)</span>
              </div>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-neutral-600)' }}>
                PhD in Mathematics, 8+ years IB experience
              </p>
              <button className="btn-outline w-full">View Profile</button>
            </div>

            {/* Tutor 2 */}
            <div className="card text-center">
              <div
                className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-xl font-bold"
                style={{ backgroundColor: 'var(--color-secondary-600)' }}
              >
                JS
              </div>
              <h3 className="text-heading-3 mb-2">James S.</h3>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-secondary-600)' }}>AP Chemistry & Biology</p>
              <div className="flex items-center justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="ml-2 text-body-small" style={{ color: 'var(--color-neutral-600)' }}>4.9 (89 reviews)</span>
              </div>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-neutral-600)' }}>
                MSc Chemistry, Former AP exam grader
              </p>
              <button className="btn-outline w-full">View Profile</button>
            </div>

            {/* Tutor 3 */}
            <div className="card text-center">
              <div
                className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-xl font-bold"
                style={{ backgroundColor: 'var(--color-accent-600)' }}
              >
                AL
              </div>
              <h3 className="text-heading-3 mb-2">Anna L.</h3>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-accent-600)' }}>IB English A & TOK</p>
              <div className="flex items-center justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-4 h-4" style={{ color: 'var(--color-accent-500)' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="ml-2 text-body-small" style={{ color: 'var(--color-neutral-600)' }}>4.8 (156 reviews)</span>
              </div>
              <p className="text-body-small mb-4" style={{ color: 'var(--color-neutral-600)' }}>
                MA Literature, IB examiner & workshop leader
              </p>
              <button className="btn-outline w-full">View Profile</button>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/tutors">
              <button className="btn-primary text-lg px-8 py-4">
                Browse All Tutors
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-heading-1 mb-4">Flexible Pricing Plans</h2>
            <p className="text-body-large max-w-2xl mx-auto" style={{ color: 'var(--color-neutral-600)' }}>
              Choose the plan that fits your learning goals and budget
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Basic Plan */}
            <div className="card relative">
              <div className="text-center">
                <h3 className="text-heading-3 mb-2">Basic</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold" style={{ color: 'var(--color-primary-600)' }}>$40</span>
                  <span className="text-body" style={{ color: 'var(--color-neutral-600)' }}>/month</span>
                </div>

                <ul className="space-y-3 mb-8 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">4 sessions per month</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Access to all subjects</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Basic progress tracking</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Email support</span>
                  </li>
                </ul>

                <button className="btn-outline w-full">Choose Basic</button>
              </div>
            </div>

            {/* Pro Plan - Most Popular */}
            <div className="card relative" style={{ borderColor: 'var(--color-primary-600)', borderWidth: '2px' }}>
              <div
                className="absolute -top-4 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full text-white text-sm font-medium"
                style={{ backgroundColor: 'var(--color-primary-600)' }}
              >
                Most Popular
              </div>
              <div className="text-center">
                <h3 className="text-heading-3 mb-2">Pro</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold" style={{ color: 'var(--color-primary-600)' }}>$60</span>
                  <span className="text-body" style={{ color: 'var(--color-neutral-600)' }}>/month</span>
                </div>

                <ul className="space-y-3 mb-8 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">8 sessions per month</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Priority tutor matching</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Advanced progress analytics</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Priority support</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Study materials included</span>
                  </li>
                </ul>

                <button className="btn-primary w-full">Choose Pro</button>
              </div>
            </div>

            {/* Premium Plan */}
            <div className="card relative">
              <div className="text-center">
                <h3 className="text-heading-3 mb-2">Premium</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold" style={{ color: 'var(--color-primary-600)' }}>$80</span>
                  <span className="text-body" style={{ color: 'var(--color-neutral-600)' }}>/month</span>
                </div>

                <ul className="space-y-3 mb-8 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Unlimited sessions</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Top-tier tutor access</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">Personalized study plans</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">24/7 priority support</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 mr-3 flex-shrink-0" style={{ color: 'var(--color-secondary-600)' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-body">University admission guidance</span>
                  </li>
                </ul>

                <button className="btn-accent w-full">Choose Premium</button>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-body-small" style={{ color: 'var(--color-neutral-500)' }}>
              All plans include a 7-day free trial. Cancel anytime.
            </p>
          </div>
        </div>
      </section>

      {/* Comprehensive Footer */}
      <footer style={{ backgroundColor: 'var(--color-neutral-800)' }} className="text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: 'var(--color-primary-600)' }}
                >
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <span className="text-xl font-bold">ProTutor</span>
              </div>
              <p className="text-body-small mb-6" style={{ color: 'var(--color-neutral-300)' }}>
                Connecting students with expert IB and AP tutors worldwide. Achieve your academic goals with personalized, one-on-one instruction.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="hover:opacity-75 transition-opacity">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="hover:opacity-75 transition-opacity">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
                <a href="#" className="hover:opacity-75 transition-opacity">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                <li><Link href="/tutors" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Find Tutors</Link></li>
                <li><Link href="/subjects" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Subjects</Link></li>
                <li><Link href="/how-it-works" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>How It Works</Link></li>
                <li><Link href="/pricing" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Pricing</Link></li>
                <li><Link href="/success-stories" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Success Stories</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Support</h4>
              <ul className="space-y-3">
                <li><Link href="/help" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Help Center</Link></li>
                <li><Link href="/contact" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Contact Us</Link></li>
                <li><Link href="/faq" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>FAQ</Link></li>
                <li><Link href="/safety" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Safety & Trust</Link></li>
                <li><Link href="/tutor-application" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Become a Tutor</Link></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Company</h4>
              <ul className="space-y-3">
                <li><Link href="/about" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>About Us</Link></li>
                <li><Link href="/careers" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Careers</Link></li>
                <li><Link href="/press" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Press</Link></li>
                <li><Link href="/blog" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Blog</Link></li>
                <li><Link href="/partnerships" className="text-body-small hover:text-white transition-colors" style={{ color: 'var(--color-neutral-300)' }}>Partnerships</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t mt-12 pt-8" style={{ borderColor: 'var(--color-neutral-700)' }}>
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="flex flex-wrap gap-6 text-body-small" style={{ color: 'var(--color-neutral-400)' }}>
                <Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
                <Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
                <Link href="/cookies" className="hover:text-white transition-colors">Cookie Policy</Link>
                <Link href="/accessibility" className="hover:text-white transition-colors">Accessibility</Link>
              </div>
              <div className="text-body-small" style={{ color: 'var(--color-neutral-400)' }}>
                © 2024 ProTutor. All rights reserved.
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}