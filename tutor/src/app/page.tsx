'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

// Custom hook for scroll animations
function useScrollAnimation() {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return [ref, isVisible] as const
}

// Animated section component
function AnimatedSection({ children, className = '', delay = 0 }: {
  children: React.ReactNode,
  className?: string,
  delay?: number
}) {
  const [ref, isVisible] = useScrollAnimation()

  return (
    <div
      ref={ref}
      className={`transition-all duration-700 ease-out ${
        isVisible
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 translate-y-8'
      } ${className}`}
      style={{
        transitionDelay: isVisible ? `${delay}ms` : '0ms'
      }}
    >
      {children}
    </div>
  )
}

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span className="text-xl font-bold text-gray-800">ProTutor</span>
        </div>
        <div className="flex items-center space-x-6">
          {/* Navigation links - always visible */}
          <div className="hidden md:flex items-center space-x-6">
            <Link href="/search" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">
              Find Tutors
            </Link>
            <Link href="/tutoring" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">
              How it Works
            </Link>
            <Link href="/tutor/profile" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">
              Become a Tutor
            </Link>
          </div>

          {/* User section */}
          <div className="flex items-center space-x-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-10 w-20 bg-gray-200 rounded"></div>
              </div>
            ) : user ? (
              <>
                {/* Profile hover section */}
                <div className="relative group">
                  {/* Profile image */}
                  <div className="w-8 h-8 bg-gray-600 rounded-full cursor-pointer flex items-center justify-center hover:bg-gray-700 transition-colors">
                    <span className="text-sm font-medium text-white">
                      {user.email?.charAt(0).toUpperCase()}
                    </span>
                  </div>

                  {/* Hover bubble */}
                  <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 p-4 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-900">{user.email}</p>
                      <p className="text-xs text-gray-500">@{user.email?.split('@')[0] || 'user'}</p>
                      <div className="border-t border-gray-100 pt-2 mt-2">
                        <Link href="/profile" className="block text-sm text-gray-600 hover:text-gray-900 py-1 transition-colors">
                          View Profile
                        </Link>
                        <Link href="/dashboard" className="block text-sm text-gray-600 hover:text-gray-900 py-1 transition-colors">
                          Dashboard
                        </Link>
                        <button
                          onClick={async () => {
                            await supabase.auth.signOut()
                            window.location.reload()
                          }}
                          className="block text-sm text-gray-600 hover:text-gray-900 py-1 w-full text-left transition-colors"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                    {/* Arrow pointing up */}
                    <div className="absolute -top-1 right-4 w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <Link href="/auth">
                  <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                    Sign In
                  </button>
                </Link>
                <Link href="/auth">
                  <button className="bg-gray-800 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-900 transition-colors duration-200">
                    Get Started
                  </button>
                </Link>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Hero Section - Responsive like Popless */}
      <main className="bg-black overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center min-h-[60vh]">
            {/* Left side - Responsive text content */}
            <div className="space-y-5" style={{
              maxWidth: 'clamp(250px, 35vw, 350px)'
            }}>
              <h2 style={{
                fontSize: 'clamp(0.875rem, 2vw, 1rem)',
                lineHeight: 'clamp(1.2, 1.3, 1.4)'
              }} className="font-medium tracking-tight text-gray-400">
                Tutor on a trusted platform.
              </h2>
              <h1 style={{
                fontSize: 'clamp(2rem, 6vw, 4rem)',
                lineHeight: 'clamp(1.1, 1.2, 1.3)'
              }} className="font-bold tracking-tight text-white">
                Powerful tutoring and classes.
              </h1>
              <p style={{
                fontSize: 'clamp(0.75rem, 1.5vw, 0.875rem)',
                lineHeight: 'clamp(1.4, 1.5, 1.6)'
              }} className="text-gray-300">
                Set your own rate, we'll connect you with you with students, you earn money on your schedule.
              </p>

              <div className="space-y-4">
                <Link href="/auth">
                  <button style={{
                    fontSize: 'clamp(0.625rem, 1.2vw, 0.75rem)',
                    padding: 'clamp(0.375rem, 1.2vw, 0.5rem) clamp(0.75rem, 2.5vw, 1rem)'
                  }} className="bg-white border-2 border-white text-black rounded-md font-medium hover:bg-gray-100 transition-colors duration-200">
                    Get started today
                  </button>
                </Link>
              </div>
            </div>

            {/* Right side - Exact Popless homepage clone with local images */}
            <AnimatedSection delay={300}>
              <div className="mt-10 lg:mt-0 relative">
                {/* Container matching Popless dimensions */}
                <div className="relative w-full overflow-hidden" style={{ height: '520px' }}>

                  {/* Large tutor profile - Very right, exactly like Popless */}
                  <div className="absolute" style={{ right: '0px', top: '0px', width: '460px', height: '520px' }}>
                    <div className="rounded-2xl overflow-hidden shadow-2xl" style={{ width: '460px', height: '520px' }}>
                      <img
                        src="/popless_framer/framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png"
                        alt="Tutor Profile Interface"
                        className="w-full h-full object-cover"
                        style={{ width: '460px', height: '520px' }}
                      />
                    </div>
                  </div>

                  {/* First smaller image - Top left offset (Earnings dashboard) */}
                  <div className="absolute" style={{ right: '480px', top: '0px', width: '190px', height: '140px' }}>
                    <div className="rounded-xl overflow-hidden shadow-lg" style={{ width: '190px', height: '140px' }}>
                      <img
                        src="/popless_framer/framerusercontent.com/images/BM7YOwABbWREv6GVwox81dWBNn8.png"
                        alt="Total Earnings Dashboard"
                        className="w-full h-full object-cover"
                        style={{ width: '190px', height: '140px' }}
                      />
                    </div>
                  </div>

                  {/* Second smaller image - Middle left offset (Booking calendar) */}
                  <div className="absolute" style={{ right: '500px', top: '160px', width: '190px', height: '240px' }}>
                    <div className="rounded-xl overflow-hidden shadow-lg" style={{ width: '190px', height: '240px' }}>
                      <img
                        src="/popless_framer/framerusercontent.com/images/sGVAlIleLngGdt5ONlDKn4bbI.png"
                        alt="English Tutoring Booking Calendar"
                        className="w-full h-full object-cover"
                        style={{ width: '190px', height: '240px' }}
                      />
                    </div>
                  </div>

                  {/* Third smaller image - Bottom left offset (Review meeting) */}
                  <div className="absolute" style={{ right: '520px', top: '420px', width: '190px', height: '100px' }}>
                    <div className="rounded-xl overflow-hidden shadow-lg" style={{ width: '190px', height: '100px' }}>
                      <img
                        src="/popless_framer/framerusercontent.com/images/MqDX17oIrwlazxq1yev0XXhIU.png"
                        alt="Review Meeting Interface"
                        className="w-full h-full object-cover"
                        style={{ width: '190px', height: '100px' }}
                      />
                    </div>
                  </div>

                  {/* Messages interface - Far left */}
                  <div className="absolute" style={{ right: '740px', top: '80px', width: '160px', height: '200px' }}>
                    <div className="rounded-xl overflow-hidden shadow-md" style={{ width: '160px', height: '200px' }}>
                      <img
                        src="/popless_framer/framerusercontent.com/images/0XK2wgaHskIE0XvlI8000WaXusA.png"
                        alt="Messages Interface"
                        className="w-full h-full object-cover"
                        style={{ width: '160px', height: '200px' }}
                      />
                    </div>
                  </div>

                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>

        {/* White section - Exact Popless copy */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
                Power your tutoring and students from an all-in-one dashboard.
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Spend more time teaching and less time managing admin.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth">
                  <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                    Get started today
                  </button>
                </Link>
                <Link href="/demo">
                  <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                    Request a demo
                  </button>
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </section>

        {/* Gray section with three features - Exact Popless copy */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-3 gap-12">
              {/* Feature 1 - Scheduling */}
              <AnimatedSection delay={100}>
                <div className="text-center">
                  <div className="mb-6">
                    <div className="bg-white rounded-lg p-6 shadow-sm">
                      <div className="rounded-lg h-48 overflow-hidden">
                        <img
                          src="/popless_framer/framerusercontent.com/images/sGVAlIleLngGdt5ONlDKn4bbI.png"
                          alt="Set your price and schedule"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Set your price and schedule</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Choose when you tutor and how much you want to charge.
                  </p>
                </div>
              </AnimatedSection>

              {/* Feature 2 - Payments */}
              <AnimatedSection delay={200}>
                <div className="text-center">
                  <div className="mb-6">
                    <div className="bg-white rounded-lg p-6 shadow-sm">
                      <div className="rounded-lg h-48 overflow-hidden">
                        <img
                          src="/popless_framer/framerusercontent.com/images/BM7YOwABbWREv6GVwox81dWBNn8.png"
                          alt="Automated payments and payouts"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Automated payments and payouts</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Accept payments in 45 currencies.<br />
                    Get paid straight after your meeting.
                  </p>
                </div>
              </AnimatedSection>

              {/* Feature 3 - Messaging */}
              <AnimatedSection delay={300}>
                <div className="text-center">
                  <div className="mb-6">
                    <div className="bg-white rounded-lg p-6 shadow-sm">
                      <div className="rounded-lg h-48 overflow-hidden">
                        <img
                          src="/popless_framer/framerusercontent.com/images/dX34Z5nGLKcjM2iqt7tUjG6fDk.jpg"
                          alt="Private and group messaging"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Private and group messaging</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Securely send messages,<br />
                    photos, videos, and documents.
                  </p>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </section>

        {/* Additional sections matching Popless structure */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
                Integrated whiteboard, messaging, and document sharing.
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Interactive online features makes learning engaging. Bring tricky concepts to life with interactive exercises, drawing diagrams, and annotating homework and practice questions.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth">
                  <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                    Sign up for free
                  </button>
                </Link>
                <Link href="/features">
                  <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                    View all features
                  </button>
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </section>

        {/* Group classes section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
                Expand your tutoring income<br />with group classes.
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Tutor your students in a group setting. Perfect for<br />
                group tutoring before finals and teaching small classes.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/group-classes">
                  <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                    Learn more about classes
                  </button>
                </Link>
                <Link href="/auth">
                  <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                    Become a tutor
                  </button>
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </section>

        {/* Final section */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-tight">
                Take better notes, view your students in one place, and stay on track.
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Featuring powerful note taking and contact management tools to help you stay organized and on top of your tutoring schedule.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth">
                  <button className="bg-black text-white px-8 py-4 rounded-md text-base font-normal hover:bg-gray-800 transition-colors duration-200 shadow-lg">
                    Sign up for free
                  </button>
                </Link>
                <Link href="/demo">
                  <button className="border border-gray-900 text-gray-900 px-8 py-4 rounded-md text-base font-normal hover:bg-gray-50 transition-colors duration-200">
                    Request a demo
                  </button>
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </section>

      </main>
    </div>
  )
}