'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

type AuthMode = 'sign-in' | 'sign-up'

export function AuthForm() {
  const [mode, setMode] = useState<AuthMode>('sign-in')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const supabase = createClient()

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    try {
      if (mode === 'sign-up') {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
            },
          },
        })

        if (error) throw error

        if (data?.user && !data?.user?.email_confirmed_at) {
          setMessage('Check your email for the confirmation link!')
        } else {
          setMessage('Account created successfully!')
          // Auto-redirect to role management for new users
          setTimeout(() => {
            window.location.href = '/roles'
          }, 1500)
        }
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        })

        if (error) throw error
        setMessage('Signed in successfully!')
        
        // Redirect to role selection (dashboard will handle role check)
        window.location.href = '/dashboard'
      }
    } catch (error) {
      console.error('Auth error:', error)
      if (error instanceof Error) {
        let errorMessage = error.message
        
        // Handle specific database errors
        if (errorMessage.includes('Database error') || errorMessage.includes('trigger') || errorMessage.includes('profiles')) {
          errorMessage = 'There was an issue creating your account. Please try again or contact support if the problem persists.'
        }
        
        setMessage(errorMessage)
      } else {
        setMessage('An unexpected error occurred. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold tracking-tight">
          {mode === 'sign-in' ? 'Welcome back' : 'Create account'}
        </h1>
        <p className="text-muted-foreground">
          {mode === 'sign-in' 
            ? 'Sign in to your ProTutor account' 
            : 'Join ProTutor to find the perfect tutor'
          }
        </p>
      </div>

      <form onSubmit={handleAuth} className="space-y-4">
        {mode === 'sign-up' && (
          <div className="space-y-2">
            <Label htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              type="text"
              placeholder="Enter your full name"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              required
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            minLength={6}
          />
        </div>

        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? 'Loading...' : mode === 'sign-in' ? 'Sign In' : 'Sign Up'}
        </Button>
      </form>

      {message && (
        <div className={`text-center text-sm p-3 rounded-md ${
          message.includes('error') || message.includes('Error') || message.includes('Database') 
            ? 'bg-red-50 text-red-700 border border-red-200' 
            : 'bg-green-50 text-green-700 border border-green-200'
        }`}>
          {message}
        </div>
      )}

      <div className="text-center">
        <button
          type="button"
          onClick={() => setMode(mode === 'sign-in' ? 'sign-up' : 'sign-in')}
          className="text-sm text-muted-foreground hover:text-primary underline-offset-4 hover:underline"
        >
          {mode === 'sign-in' 
            ? "Don't have an account? Sign up" 
            : 'Already have an account? Sign in'
          }
        </button>
      </div>
    </div>
  )
}