-- Smart Migration: Check existing schema and only add what's missing
-- Created: 2025-07-18
-- Purpose: Fix admin approval by only adding missing components

-- Check what already exists
SELECT 'Checking existing schema...' as status;

-- Check if approval_status enum exists (it does!)
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_type WHERE typname = 'approval_status') 
    THEN 'approval_status enum EXISTS' 
    ELSE 'approval_status enum MISSING' 
  END as enum_status;

-- Check what columns exist in profiles table
SELECT 
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check what functions exist
SELECT 
  proname as function_name,
  proargnames as parameters
FROM pg_proc 
WHERE proname IN ('set_tutor_verification', 'get_user_role_status', 'add_user_role')
ORDER BY proname;

-- Only create approval_status enum if it doesn't exist (it exists, so skip)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'approval_status') THEN
    CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');
    RAISE NOTICE 'Created approval_status enum';
  ELSE
    RAISE NOTICE 'approval_status enum already exists - skipping';
  END IF;
END $$;

-- Add missing columns only if they don't exist
DO $$
BEGIN
  -- Check and add roles column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='roles') THEN
    ALTER TABLE public.profiles ADD COLUMN roles user_role[] DEFAULT ARRAY['student'];
    RAISE NOTICE 'Added roles column';
  ELSE
    RAISE NOTICE 'roles column already exists';
  END IF;

  -- Check and add student_status column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='student_status') THEN
    ALTER TABLE public.profiles ADD COLUMN student_status approval_status DEFAULT 'approved';
    RAISE NOTICE 'Added student_status column';
  ELSE
    RAISE NOTICE 'student_status column already exists';
  END IF;

  -- Check and add tutor_status column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='tutor_status') THEN
    ALTER TABLE public.profiles ADD COLUMN tutor_status approval_status;
    RAISE NOTICE 'Added tutor_status column';
  ELSE
    RAISE NOTICE 'tutor_status column already exists';
  END IF;

  -- Check and add admin_status column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='admin_status') THEN
    ALTER TABLE public.profiles ADD COLUMN admin_status approval_status;
    RAISE NOTICE 'Added admin_status column';
  ELSE
    RAISE NOTICE 'admin_status column already exists';
  END IF;

  -- Check and add approved_at column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='approved_at') THEN
    ALTER TABLE public.profiles ADD COLUMN approved_at TIMESTAMPTZ;
    RAISE NOTICE 'Added approved_at column';
  ELSE
    RAISE NOTICE 'approved_at column already exists';
  END IF;

  -- Check and add approval_notes column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='approval_notes') THEN
    ALTER TABLE public.profiles ADD COLUMN approval_notes TEXT;
    RAISE NOTICE 'Added approval_notes column';
  ELSE
    RAISE NOTICE 'approval_notes column already exists';
  END IF;
END $$;

-- Create set_tutor_verification function (always recreate to ensure it's correct)
CREATE OR REPLACE FUNCTION public.set_tutor_verification(tutor_id UUID, verified boolean)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        tutor_status = CASE 
            WHEN verified THEN 'approved'::approval_status
            ELSE 'pending'::approval_status
        END,
        updated_at = NOW()
    WHERE id = tutor_id
    AND 'tutor' = ANY(COALESCE(roles, '{}'));
END;
$$;

-- Create get_user_role_status function (always recreate to ensure it's correct)
CREATE OR REPLACE FUNCTION public.get_user_role_status(user_id UUID)
RETURNS TABLE(
    role user_role,
    status approval_status,
    can_access boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH user_profile AS (
        SELECT p.roles, p.student_status, p.tutor_status, p.admin_status
        FROM public.profiles p
        WHERE p.id = user_id
    )
    SELECT 
        r.role,
        CASE 
            WHEN r.role = 'student' THEN up.student_status
            WHEN r.role = 'tutor' THEN up.tutor_status
            WHEN r.role = 'admin' THEN up.admin_status
        END as status,
        CASE 
            WHEN r.role = 'student' THEN (up.student_status = 'approved')
            WHEN r.role = 'tutor' THEN (up.tutor_status = 'approved')
            WHEN r.role = 'admin' THEN (up.admin_status = 'approved')
            ELSE false
        END as can_access
    FROM (
        SELECT unnest(up.roles) as role
        FROM user_profile up
    ) r, user_profile up;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.set_tutor_verification(UUID, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_status(UUID) TO authenticated;

-- Final status check
SELECT 'Smart migration completed - only missing components were added' as status;

-- Show final schema state
SELECT 
  column_name,
  data_type
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
AND column_name IN ('roles', 'student_status', 'tutor_status', 'admin_status', 'approved_at', 'approval_notes')
ORDER BY column_name;