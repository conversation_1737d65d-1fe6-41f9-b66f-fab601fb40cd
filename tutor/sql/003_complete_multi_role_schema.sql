-- Migration: Complete Multi-Role Schema Update
-- Created: 2025-07-18
-- Purpose: Add missing fields and functions for admin approval system

-- Create approval_status enum
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');

-- Add missing fields to profiles table for multi-role system
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS roles user_role[] DEFAULT ARRAY['student'],
ADD COLUMN IF NOT EXISTS student_status approval_status DEFAULT 'approved',
ADD COLUMN IF NOT EXISTS tutor_status approval_status,
ADD COLUMN IF NOT EXISTS admin_status approval_status,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS approval_notes TEXT;

-- Update existing profiles to have default roles if empty
UPDATE public.profiles 
SET roles = ARRAY['student']::user_role[]
WHERE roles IS NULL OR array_length(roles, 1) IS NULL;

-- Update existing users based on their old role field (if it exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='role') THEN
    -- Migrate single role to multi-role array
    UPDATE public.profiles 
    SET 
      roles = ARRAY[role]::user_role[],
      student_status = CASE WHEN role = 'student' THEN 'approved'::approval_status ELSE student_status END,
      tutor_status = CASE WHEN role = 'tutor' THEN 'approved'::approval_status ELSE tutor_status END,
      admin_status = CASE WHEN role = 'admin' THEN 'approved'::approval_status ELSE admin_status END
    WHERE role IS NOT NULL;
    
    -- Drop the old single role column
    ALTER TABLE public.profiles DROP COLUMN role;
  END IF;
END $$;

-- Create set_tutor_verification function for admin verification workflow
CREATE OR REPLACE FUNCTION public.set_tutor_verification(tutor_id UUID, verified boolean)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        tutor_status = CASE 
            WHEN verified THEN 'approved'::approval_status
            ELSE 'pending'::approval_status
        END,
        updated_at = NOW()
    WHERE id = tutor_id
    AND 'tutor' = ANY(COALESCE(roles, '{}'));
END;
$$;

-- Create get_user_role_status function for role management
CREATE OR REPLACE FUNCTION public.get_user_role_status(user_id UUID)
RETURNS TABLE(
    role user_role,
    status approval_status,
    can_access boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH user_profile AS (
        SELECT p.roles, p.student_status, p.tutor_status, p.admin_status
        FROM public.profiles p
        WHERE p.id = user_id
    )
    SELECT 
        r.role,
        CASE 
            WHEN r.role = 'student' THEN up.student_status
            WHEN r.role = 'tutor' THEN up.tutor_status
            WHEN r.role = 'admin' THEN up.admin_status
        END as status,
        CASE 
            WHEN r.role = 'student' THEN (up.student_status = 'approved')
            WHEN r.role = 'tutor' THEN (up.tutor_status = 'approved')
            WHEN r.role = 'admin' THEN (up.admin_status = 'approved')
            ELSE false
        END as can_access
    FROM (
        SELECT unnest(up.roles) as role
        FROM user_profile up
    ) r, user_profile up;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.set_tutor_verification(UUID, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_status(UUID) TO authenticated;

-- Update RLS policies for multi-role system (when RLS is re-enabled)
-- Note: RLS is currently disabled for development

-- Verification message
SELECT 'Multi-role schema migration completed successfully' as status;