-- Migration: Fix add_user_role function
-- Created: 2025-07-18
-- Purpose: Create working add_user_role function for role assignment

-- Drop any existing variations to avoid conflicts
DROP FUNCTION IF EXISTS public.add_user_role(UUID, user_role) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(UUID, text) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(UUID, character varying) CASCADE;

-- Create the working function
CREATE FUNCTION public.add_user_role(user_id UUID, new_role text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Update profile with new role if user doesn't already have it
    UPDATE public.profiles 
    SET 
        roles = array_append(COALESCE(roles, '{}'), new_role::user_role),
        tutor_status = CASE 
            WHEN new_role = 'tutor' THEN 'pending'::approval_status
            ELSE tutor_status
        END,
        student_status = CASE 
            WHEN new_role = 'student' THEN 'approved'::approval_status
            ELSE student_status
        END,
        updated_at = NOW()
    WHERE id = user_id
    AND NOT (new_role::user_role = ANY(COALESCE(roles, '{}')));
END;
$$;

-- <PERSON> permissions
GRANT EXECUTE ON FUNCTION public.add_user_role(UUID, text) TO authenticated;