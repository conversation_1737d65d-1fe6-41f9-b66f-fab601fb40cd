-- Add Missing Functions Only
-- Since schema fields already exist, we just need the functions

-- Create set_tutor_verification function (this is what's missing!)
CREATE OR REPLACE FUNCTION public.set_tutor_verification(tutor_id UUID, verified boolean)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        tutor_status = CASE 
            WHEN verified THEN 'approved'::approval_status
            ELSE 'pending'::approval_status
        END,
        updated_at = NOW()
    WHERE id = tutor_id
    AND 'tutor' = ANY(COALESCE(roles, '{}'));
END;
$$;

-- Create get_user_role_status function 
CREATE OR REPLACE FUNCTION public.get_user_role_status(user_id UUID)
RETURNS TABLE(
    role user_role,
    status approval_status,
    can_access boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH user_profile AS (
        SELECT p.roles, p.student_status, p.tutor_status, p.admin_status
        FROM public.profiles p
        WHERE p.id = user_id
    )
    SELECT 
        r.role,
        CASE 
            WHEN r.role = 'student' THEN up.student_status
            WHEN r.role = 'tutor' THEN up.tutor_status
            WHEN r.role = 'admin' THEN up.admin_status
        END as status,
        CASE 
            WHEN r.role = 'student' THEN (up.student_status = 'approved')
            WHEN r.role = 'tutor' THEN (up.tutor_status = 'approved')
            WHEN r.role = 'admin' THEN (up.admin_status = 'approved')
            ELSE false
        END as can_access
    FROM (
        SELECT unnest(up.roles) as role
        FROM user_profile up
    ) r, user_profile up;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.set_tutor_verification(UUID, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_status(UUID) TO authenticated;

-- Test that functions were created
SELECT 
    proname as function_name,
    proargnames as parameters
FROM pg_proc 
WHERE proname IN ('set_tutor_verification', 'get_user_role_status', 'add_user_role')
ORDER BY proname;

SELECT 'Missing functions added successfully!' as status;