-- ProTutor Multi-Role System Initial Schema
-- Complete schema with multi-role support

-- =============================================================================
-- CREATE ENUMS
-- =============================================================================

-- User roles enum
DO $$ BEGIN
  CREATE TYPE user_role AS ENUM ('student', 'tutor', 'admin');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Approval status enum
DO $$ BEGIN
  CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Tutor tier enum
DO $$ BEGIN
  CREATE TYPE tutor_tier AS ENUM ('standard', 'verified');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Subject categories
DO $$ BEGIN
  CREATE TYPE subject_category AS ENUM ('ib_math', 'ib_sciences', 'ib_languages', 'ib_humanities', 'ap_math', 'ap_sciences', 'ap_languages', 'ap_social_studies', 'other');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- =============================================================================
-- CREATE TABLES
-- =============================================================================

-- Main profiles table with multi-role support
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  full_name TEXT NOT NULL,
  
  -- Multi-role system
  roles user_role[] NOT NULL DEFAULT ARRAY['student'],
  student_status approval_status NOT NULL DEFAULT 'approved',
  tutor_status approval_status DEFAULT NULL,
  admin_status approval_status DEFAULT NULL,
  
  -- Tutor specific fields
  tutor_tier tutor_tier DEFAULT 'standard',
  hourly_rate INTEGER, -- in cents
  bio TEXT,
  subjects subject_category[],
  education TEXT,
  experience_years INTEGER,
  certifications TEXT[],
  languages TEXT[] DEFAULT ARRAY['English'],
  
  -- Contact and profile info
  avatar_url TEXT,
  phone_number TEXT,
  date_of_birth DATE,
  
  -- Analytics fields
  timezone TEXT DEFAULT 'UTC',
  is_available BOOLEAN DEFAULT true,
  max_students_per_slot INTEGER DEFAULT 1,
  last_activity_at TIMESTAMPTZ DEFAULT NOW(),
  total_sessions_taught INTEGER DEFAULT 0,
  average_rating NUMERIC(3,2) DEFAULT NULL,
  total_earnings_cents INTEGER DEFAULT 0,
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ DEFAULT NULL
);

-- Pricing rules table
CREATE TABLE IF NOT EXISTS public.pricing_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tier tutor_tier NOT NULL,
  subject_category subject_category NOT NULL,
  min_rate_cents INTEGER NOT NULL,
  max_rate_cents INTEGER NOT NULL,
  platform_commission_percent INTEGER DEFAULT 18,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tier, subject_category)
);

-- Tutor availabilities table
CREATE TABLE IF NOT EXISTS public.tutor_availabilities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tutor_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  timezone TEXT DEFAULT 'UTC',
  is_recurring BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ DEFAULT NULL
);

-- Bookings table
CREATE TABLE IF NOT EXISTS public.bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  tutor_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  availability_id UUID REFERENCES public.tutor_availabilities(id),
  
  -- Session details
  scheduled_start TIMESTAMPTZ NOT NULL,
  scheduled_end TIMESTAMPTZ NOT NULL,
  timezone TEXT DEFAULT 'UTC',
  subject subject_category,
  session_notes TEXT,
  prep_notes TEXT,
  
  -- Pricing
  hourly_rate_cents INTEGER NOT NULL,
  total_cost_cents INTEGER NOT NULL,
  platform_fee_cents INTEGER NOT NULL,
  tutor_earnings_cents INTEGER NOT NULL,
  
  -- Status
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled')),
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ DEFAULT NULL
);

-- =============================================================================
-- INSERT DEFAULT DATA
-- =============================================================================

-- Insert default pricing rules
INSERT INTO public.pricing_rules (tier, subject_category, min_rate_cents, max_rate_cents) VALUES
-- Verified tier (60-70/hr)
('verified', 'ib_math', 6000, 7000),
('verified', 'ib_sciences', 6000, 7000), 
('verified', 'ib_languages', 6000, 7000),
('verified', 'ib_humanities', 6000, 7000),
('verified', 'ap_math', 6000, 7000),
('verified', 'ap_sciences', 6000, 7000),
('verified', 'ap_languages', 6000, 7000),
('verified', 'ap_social_studies', 6000, 7000),
('verified', 'other', 6000, 7000),
-- Standard tier (40-50/hr)  
('standard', 'ib_math', 4000, 5000),
('standard', 'ib_sciences', 4000, 5000),
('standard', 'ib_languages', 4000, 5000),
('standard', 'ib_humanities', 4000, 5000),
('standard', 'ap_math', 4000, 5000),
('standard', 'ap_sciences', 4000, 5000),
('standard', 'ap_languages', 4000, 5000),
('standard', 'ap_social_studies', 4000, 5000),
('standard', 'other', 4000, 5000)
ON CONFLICT (tier, subject_category) DO NOTHING;

-- =============================================================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pricing_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_availabilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- CREATE RLS POLICIES
-- =============================================================================

-- Profiles policies
DROP POLICY IF EXISTS "Users can read their own profile and public tutor profiles" ON public.profiles;
CREATE POLICY "Users can read their own profile and public tutor profiles" ON public.profiles
  FOR SELECT USING (
    id = auth.uid() OR 
    ('tutor' = ANY(roles))
  );

DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (id = auth.uid());

DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (id = auth.uid());

-- Pricing rules policies (read-only for users)
CREATE POLICY "Anyone can read pricing rules" ON public.pricing_rules
  FOR SELECT USING (true);

-- Tutor availabilities policies
CREATE POLICY "Tutors can manage their own availability" ON public.tutor_availabilities
  FOR ALL USING (tutor_id = auth.uid());

CREATE POLICY "Students can view tutor availability" ON public.tutor_availabilities
  FOR SELECT USING (true);

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON public.bookings
  FOR SELECT USING (student_id = auth.uid() OR tutor_id = auth.uid());

CREATE POLICY "Students can create bookings" ON public.bookings
  FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "Users can update their own bookings" ON public.bookings
  FOR UPDATE USING (student_id = auth.uid() OR tutor_id = auth.uid());

-- =============================================================================
-- CREATE FUNCTIONS
-- =============================================================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, roles, student_status)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
    ARRAY['student'],
    'approved'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add a role to user
CREATE OR REPLACE FUNCTION public.add_user_role(user_id UUID, new_role user_role)
RETURNS void AS $$
BEGIN
  -- Add role if not already present
  UPDATE public.profiles 
  SET 
    roles = CASE 
      WHEN new_role = ANY(roles) THEN roles 
      ELSE array_append(roles, new_role) 
    END,
    -- Set status based on role
    tutor_status = CASE 
      WHEN new_role = 'tutor' AND tutor_status IS NULL THEN 'pending'
      ELSE tutor_status
    END,
    admin_status = CASE 
      WHEN new_role = 'admin' AND admin_status IS NULL THEN 'approved'
      ELSE admin_status
    END,
    updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user role status
CREATE OR REPLACE FUNCTION public.get_user_role_status(user_id UUID)
RETURNS TABLE(role user_role, status approval_status, can_access boolean) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    unnest(p.roles) as role,
    CASE 
      WHEN unnest(p.roles) = 'student' THEN p.student_status
      WHEN unnest(p.roles) = 'tutor' THEN p.tutor_status
      WHEN unnest(p.roles) = 'admin' THEN p.admin_status
    END as status,
    CASE 
      WHEN unnest(p.roles) = 'student' THEN (p.student_status = 'approved')
      WHEN unnest(p.roles) = 'tutor' THEN (p.tutor_status = 'approved')
      WHEN unnest(p.roles) = 'admin' THEN (p.admin_status = 'approved')
    END as can_access
  FROM public.profiles p
  WHERE p.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check verification eligibility
CREATE OR REPLACE FUNCTION public.check_verification_eligibility(user_id UUID)
RETURNS boolean AS $$
DECLARE
  profile_record RECORD;
BEGIN
  SELECT * INTO profile_record FROM public.profiles WHERE id = user_id;
  
  IF profile_record.education IS NULL OR 
     profile_record.experience_years IS NULL OR 
     profile_record.experience_years < 2 OR
     profile_record.certifications IS NULL OR 
     array_length(profile_record.certifications, 1) = 0 OR
     profile_record.bio IS NULL OR
     profile_record.subjects IS NULL OR
     array_length(profile_record.subjects, 1) = 0 THEN
    RETURN false;
  END IF;
  
  -- Check for university education keywords
  IF profile_record.education ~* '.*(university|college|bachelor|master|phd|degree).*' THEN
    RETURN true;
  END IF;
  
  RETURN false;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically set tutor rate and tier
CREATE OR REPLACE FUNCTION public.set_tutor_rate()
RETURNS TRIGGER AS $$
DECLARE
  min_rate INTEGER;
  max_rate INTEGER;
BEGIN
  -- Auto-upgrade tier based on qualifications if tutor
  IF 'tutor' = ANY(NEW.roles) THEN
    IF public.check_verification_eligibility(NEW.id) THEN
      NEW.tutor_tier := 'verified';
    ELSE 
      NEW.tutor_tier := 'standard';
    END IF;
  END IF;

  -- Set rate based on tier and primary subject
  IF 'tutor' = ANY(NEW.roles) AND NEW.subjects IS NOT NULL AND array_length(NEW.subjects, 1) > 0 THEN
    SELECT min_rate_cents, max_rate_cents INTO min_rate, max_rate
    FROM public.pricing_rules 
    WHERE tier = NEW.tutor_tier AND subject_category = NEW.subjects[1];
    
    IF NEW.hourly_rate IS NULL AND min_rate IS NOT NULL THEN
      NEW.hourly_rate := (min_rate + max_rate) / 2;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- CREATE TRIGGERS
-- =============================================================================

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Trigger for automatic rate setting
DROP TRIGGER IF EXISTS set_tutor_rate_trigger ON public.profiles;
CREATE TRIGGER set_tutor_rate_trigger
  BEFORE INSERT OR UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.set_tutor_rate();

-- =============================================================================
-- CREATE INDEXES
-- =============================================================================

CREATE INDEX IF NOT EXISTS idx_profiles_roles ON profiles USING GIN(roles);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_tutor_tier ON profiles(tutor_tier);
CREATE INDEX IF NOT EXISTS idx_profiles_subjects ON profiles USING GIN(subjects);
CREATE INDEX IF NOT EXISTS idx_profiles_hourly_rate ON profiles(hourly_rate);
CREATE INDEX IF NOT EXISTS idx_tutor_availabilities_tutor_id ON tutor_availabilities(tutor_id);
CREATE INDEX IF NOT EXISTS idx_tutor_availabilities_day_time ON tutor_availabilities(day_of_week, start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_bookings_student_id ON bookings(student_id);
CREATE INDEX IF NOT EXISTS idx_bookings_tutor_id ON bookings(tutor_id);
CREATE INDEX IF NOT EXISTS idx_bookings_scheduled_start ON bookings(scheduled_start);