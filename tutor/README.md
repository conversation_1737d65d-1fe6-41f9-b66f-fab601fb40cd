# ProTutor - Online Tutoring Platform

A modern, full-featured tutoring platform built with Next.js 15 and Supabase, enabling seamless connections between students and verified tutors.

## 🚀 Features

### Core Functionality ✅
- **Multi-Role System**: Students, tutors, and administrators with distinct permissions
- **User Authentication**: Secure signup/login with email verification
- **Role Management**: Dynamic role assignment with approval workflows
- **Tutor Verification**: Admin approval process for tutor applications
- **Profile Management**: Comprehensive user profiles with role-specific features

### User Roles

#### 👨‍🎓 Students
- Browse and search verified tutors
- Book tutoring sessions
- Track learning progress
- Leave reviews and ratings

#### 👨‍🏫 Tutors
- Apply for tutor verification
- Create detailed teaching profiles
- Set availability and rates
- Manage student sessions

#### ⚙️ Administrators
- Approve/reject tutor applications
- Platform analytics and oversight
- User management capabilities
- System configuration

## 🛠 Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Deployment**: Vercel
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Authentication**: Supabase Auth with email/password

## 📁 Project Structure

```
src/
├── app/
│   ├── admin/          # Admin dashboard and approval system
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # User dashboard
│   ├── roles/          # Role management interface
│   └── tutors/         # Tutor browsing and booking
├── components/
│   ├── auth/           # Authentication components
│   └── ui/             # Reusable UI components
└── lib/
    └── supabase/       # Supabase client configuration

sql/
├── 001_initial_schema.sql              # Basic database schema
├── 002_fix_add_user_role_function.sql  # Role assignment functionality
├── 003_complete_multi_role_schema.sql  # Complete multi-role system
├── 004_check_and_fix_schema.sql        # Smart schema migration
└── 005_add_missing_functions.sql       # Additional database functions
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tutor
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create `.env.local` file:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Database Setup**
   Run the SQL scripts in order in your Supabase SQL editor:
   ```bash
   # Run these in Supabase SQL Editor
   sql/001_initial_schema.sql
   sql/002_fix_add_user_role_function.sql
   sql/003_complete_multi_role_schema.sql
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

Visit `http://localhost:3000` to see the application.

## 🗄 Database Schema

### Key Tables
- **profiles**: User profiles with multi-role support
- **Enums**: `user_role` (student, tutor, admin), `approval_status` (pending, approved, rejected)

### Key Functions
- `add_user_role(user_id, role)`: Assigns roles to users
- `handle_new_user()`: Automatic profile creation trigger
- `set_tutor_verification()`: Admin approval workflow
- `get_user_role_status()`: Role status queries

## 🔧 Development Notes

### Current Status (July 18, 2025)
- ✅ **Core Platform**: Fully functional with all major features working
- ✅ **User Management**: Signup, authentication, and role assignment
- ✅ **Multi-Role System**: Students, tutors, and admin roles with approval workflows
- ✅ **Database**: Clean schema with proper functions and triggers

### Recently Resolved
- ✅ Fixed persistent role assignment errors
- ✅ Resolved user signup database conflicts  
- ✅ Eliminated PostgreSQL function ambiguity issues
- ✅ Updated all database triggers for multi-role compatibility

### For Production Deployment
- [ ] Re-enable RLS policies on profiles table
- [ ] Implement booking and session management
- [ ] Add payment integration (Stripe)
- [ ] Create comprehensive test suite
- [ ] Add monitoring and error tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Links

- **Live Demo**: [Deployed on Vercel]
- **Documentation**: See `DEVELOPMENT_NOTES.md` for detailed technical information
- **Completed Tasks**: See `COMPLETED_TASKS.md` for recent development progress

---

**Built with ❤️ for seamless learning experiences**