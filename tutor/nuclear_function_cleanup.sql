-- Nuclear Function Cleanup - Remove ALL variations aggressively
-- The previous cleanup didn't catch all function signatures

-- Step 1: Find ALL add_user_role functions
SELECT 'Found these add_user_role functions:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types,
    pronargs as arg_count
FROM pg_proc 
WHERE proname = 'add_user_role';

-- Step 2: Drop ALL possible variations more aggressively
DROP FUNCTION IF EXISTS public.add_user_role CASCADE;
DROP FUNCTION IF EXISTS add_user_role CASCADE;

-- Check if any functions with similar names exist
SELECT 'Checking for any remaining functions...' as status;
SELECT proname, oidvectortypes(proargtypes) as argument_types
FROM pg_proc 
WHERE proname LIKE '%add_user_role%' OR proname LIKE '%user_role%';

-- Step 3: Create the ONE and ONLY function
CREATE FUNCTION public.add_user_role(user_id UUID, new_role text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        roles = array_append(COALESCE(roles, '{}'), new_role::user_role),
        tutor_status = CASE 
            WHEN new_role = 'tutor' THEN 'pending'::approval_status
            ELSE tutor_status
        END,
        student_status = CASE 
            WHEN new_role = 'student' THEN 'approved'::approval_status
            ELSE student_status
        END,
        updated_at = NOW()
    WHERE id = user_id
    AND NOT (new_role::user_role = ANY(COALESCE(roles, '{}')));
END;
$$;

-- Step 4: Grant permissions
GRANT EXECUTE ON FUNCTION public.add_user_role(UUID, text) TO authenticated;

-- Step 5: Final verification - should show only ONE function
SELECT 'Final check - should show only ONE function:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types,
    pronargs as arg_count
FROM pg_proc 
WHERE proname = 'add_user_role';

SELECT 'Nuclear cleanup completed!' as final_status;