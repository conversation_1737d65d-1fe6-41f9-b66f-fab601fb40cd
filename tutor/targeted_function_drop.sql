-- Targeted Function Drop - Drop each signature specifically
-- Error shows we need to specify argument lists

-- First, let's see exactly what we have
SELECT 'Current add_user_role functions:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types,
    pronargs as arg_count,
    proargnames as parameter_names
FROM pg_proc 
WHERE proname = 'add_user_role';

-- Drop each specific signature individually
DROP FUNCTION IF EXISTS public.add_user_role(uuid, text) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(uuid, user_role) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(uuid, character varying) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(uuid, text, uuid) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(uuid, user_role, uuid) CASCADE;

-- Check what's left
SELECT 'After targeted drops:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types
FROM pg_proc 
WHERE proname = 'add_user_role';

-- Create the clean function
CREATE FUNCTION public.add_user_role(user_id UUID, new_role text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        roles = array_append(COALESCE(roles, '{}'), new_role::user_role),
        tutor_status = CASE 
            WHEN new_role = 'tutor' THEN 'pending'::approval_status
            ELSE tutor_status
        END,
        student_status = CASE 
            WHEN new_role = 'student' THEN 'approved'::approval_status
            ELSE student_status
        END,
        updated_at = NOW()
    WHERE id = user_id
    AND NOT (new_role::user_role = ANY(COALESCE(roles, '{}')));
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.add_user_role(UUID, text) TO authenticated;

-- Final check
SELECT 'Should be clean now:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types
FROM pg_proc 
WHERE proname = 'add_user_role';