# Development Notes

## Current Development State (July 18, 2025)

### Database Configuration
- **RLS (Row Level Security)**: Currently DISABLED on profiles table for development
- **Reason**: RLS policies were blocking role assignment function calls
- **Status**: Temporary - should be re-enabled with proper policies for production

### Database Schema
- **Multi-role system**: Users can have multiple roles (student, tutor, admin)
- **Approval system**: Separate approval status for each role type
- **Enums**: `user_role` ('student', 'tutor', 'admin'), `approval_status` ('pending', 'approved', 'rejected')

### Recent Fixes (July 18, 2025)
1. **Role Assignment Bug**: Fixed duplicate function issues causing PGRST203 errors
   - **Solution**: `sql/002_fix_add_user_role_function.sql`
2. **Admin Approval System**: Fixed missing database schema for approval workflow
   - **Solution**: `sql/003_complete_multi_role_schema.sql`
3. **Comprehensive Cleanup**: Removed 21 debugging files and code residue
4. **Persistent Role Assignment Errors**: Fixed PostgreSQL function ambiguity conflicts
   - **Problem**: Multiple function signatures causing "could not choose best candidate" errors
   - **Solution**: `targeted_function_drop.sql` - removed duplicate functions
5. **User Signup Issues**: Fixed database trigger for multi-role schema
   - **Problem**: Trigger function using old single-role schema causing signup failures
   - **Solution**: `fix_signup_trigger.sql` - updated trigger for multi-role compatibility

### Database Functions
- `add_user_role(user_id UUID, new_role text)`: Adds role to user profile (FIXED - no duplicate signatures)
- `handle_new_user()`: Trigger function for automatic profile creation on signup (FIXED)
- `set_tutor_verification(tutor_id UUID, verified boolean)`: Admin verification workflow
- `get_user_role_status(user_id UUID)`: Returns user's role statuses and access permissions

### Working Features ✅
- **User Management**:
  - ✅ User signup and profile creation
  - ✅ Multi-role system (student/tutor/admin)
  - ✅ Role assignment with proper status tracking
- **Student Features**:
  - ✅ Browse tutors
  - ✅ Book sessions
- **Tutor Features**:
  - ✅ Apply for tutor role (pending approval)
  - ✅ Profile management
- **Admin Features**:
  - ✅ View pending tutor applications
  - ✅ Approve/reject tutor applications
  - ✅ Tutor verification system

### Known Issues 🐛
- **Missing Tables**: Bookings, sessions, payments tables not yet implemented

### Recently Resolved Issues ✅
- ✅ **Role Assignment Errors**: Fixed duplicate function conflicts (July 18, 2025)
- ✅ **User Signup Failures**: Fixed database trigger compatibility (July 18, 2025)
- ✅ **Function Ambiguity**: Resolved PostgreSQL "best candidate function" errors (July 18, 2025)

### TODO for Production
- [ ] Re-enable RLS on profiles table with proper policies
- [ ] Implement booking system (tables and UI)
- [ ] Add payment integration (Stripe)
- [ ] Add session management system
- [ ] Create comprehensive test suite

### File Structure
```
sql/
├── 001_initial_schema.sql               # Basic schema (single-role)
├── 002_fix_add_user_role_function.sql   # Role assignment fix
├── 003_complete_multi_role_schema.sql   # Complete multi-role schema
├── 004_check_and_fix_schema.sql         # Smart schema migration
└── 005_add_missing_functions.sql        # Missing function additions

Debugging & Fixes:
├── debug_role_assignment.sql            # Comprehensive debugging script
├── fix_signup_trigger.sql               # User signup trigger fix
├── fix_duplicate_functions.sql          # Function conflict resolution
├── nuclear_function_cleanup.sql         # Aggressive function cleanup
└── targeted_function_drop.sql           # Final function conflict fix

src/app/
├── roles/         # Role management page
├── admin/         # Admin approval system
├── dashboard/     # User dashboard
└── tutors/        # Tutor browsing and booking
```

### Environment
- **Database**: Supabase (PostgreSQL)
- **Frontend**: Next.js 15 + TypeScript
- **Deployment**: Vercel
- **Status**: Development environment, ALL core features fully functional
- **Last Updated**: July 18, 2025 - All major issues resolved