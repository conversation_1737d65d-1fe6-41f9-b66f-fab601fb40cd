# Development Setup - ProTutor

## Quick Start (No More Port Issues!)

We've created scripts to handle all the common localhost/port problems automatically.

### Option 1: Use the Smart Startup Script (Recommended)
```bash
npm run dev:start
```
This script will:
- ✅ Automatically kill any processes on ports 3000-3002
- ✅ Clean Next.js cache
- ✅ Start the server on port 3001 consistently
- ✅ Show you the exact URLs to use

### Option 2: Quick Port 3001 Start
```bash
npm run dev:3001
```
This will clean ports and start directly on 3001.

### Option 3: Just Clean Ports
```bash
npm run dev:clean
```
Use this if you just need to free up the ports.

### VS Code Integration
If you're using VS Code:
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Tasks: Run Task"
3. Select "Start Development Server"

## Development URLs

The app will always be available at:
- **Local**: http://localhost:3001
- **Admin Panel**: http://localhost:3001/admin
- **Dashboard**: http://localhost:3001/dashboard

## Troubleshooting

### Still having port issues?
```bash
# Nuclear option - kill everything on common ports
sudo lsof -ti:3000,3001,3002,8080,8081 | xargs sudo kill -9

# Then start normally
npm run dev:start
```

### Need to see what's using a port?
```bash
lsof -ti:3001  # Shows process IDs using port 3001
lsof -i:3001   # Shows detailed info about port 3001
```

### Clear everything and start fresh
```bash
rm -rf .next node_modules package-lock.json
npm install
npm run dev:start
```

## Pro Tips

1. **Always use `npm run dev:start`** - it handles everything automatically
2. **Bookmark http://localhost:3001** - this will be your consistent URL
3. **Use VS Code tasks** - they're configured to work perfectly
4. **If something's weird** - run `npm run dev:clean` first

No more port conflicts, no more "This site can't be reached" errors! 🎉