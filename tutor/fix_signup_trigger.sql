-- Fix Signup Trigger Function
-- The trigger is failing during user creation
-- Issue: Likely missing columns or wrong data types

-- First, let's see what's in the current trigger
SELECT 'Current trigger function:' as status;

SELECT 
    proname as function_name,
    prosrc as function_body
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- Drop and recreate the trigger function with proper error handling
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create updated trigger function that works with multi-role schema
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert with proper multi-role schema fields
    INSERT INTO public.profiles (
        id, 
        email, 
        full_name, 
        roles, 
        student_status
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
        ARRAY['student']::user_role[],  -- Default to student role
        'approved'::approval_status     -- Auto-approve students
    );
    
    RETURN NEW;
EXCEPTION WHEN others THEN
    -- Log the error but don't fail the auth process
    RAISE LOG 'Error in handle_new_user trigger: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Test that the function was created properly
SELECT 'Trigger function updated successfully!' as status;

-- Verify the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';