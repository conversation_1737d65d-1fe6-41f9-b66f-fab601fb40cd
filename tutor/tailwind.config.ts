import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: ["var(--font-inter)", "Inter", "sans-serif"],
      },
      colors: {
        // Popless color tokens extracted from their CSS
        primary: {
          50: "rgb(240, 249, 255)",
          100: "rgb(224, 242, 254)",
          200: "rgb(186, 230, 253)",
          300: "rgb(125, 211, 252)",
          400: "rgb(56, 189, 248)",
          500: "rgb(14, 165, 233)",
          600: "rgb(3, 104, 224)", // --token-7b040a80 (Popless primary blue)
          700: "rgb(29, 78, 216)",
          800: "rgb(30, 64, 175)",
          900: "rgb(30, 58, 138)",
        },
        gray: {
          50: "rgb(249, 250, 251)",
          100: "rgb(243, 244, 246)",
          200: "rgb(229, 231, 235)",
          300: "rgb(209, 213, 219)",
          400: "rgb(156, 163, 175)",
          500: "rgb(107, 114, 128)",
          600: "rgb(75, 85, 99)",
          700: "rgb(55, 65, 81)",
          800: "rgb(31, 41, 55)",
          900: "rgb(17, 24, 39)",
        },
        // Popless specific colors
        popless: {
          dark: "rgb(19, 22, 18)", // --token-15b486ba
          blue: "rgb(3, 104, 224)", // --token-7b040a80
          "blue-light": "rgb(44, 126, 234)", // --token-b2ddaf18
          "gray-light": "rgb(168, 168, 168)", // --token-840e2253
          "gray-lighter": "rgb(224, 225, 227)", // --token-cd156118
          "gray-lightest": "rgb(245, 245, 245)", // --token-ce5164cd
          white: "rgb(255, 255, 255)", // --token-36a54893
        },
      },
      fontSize: {
        // Responsive font sizes using clamp() like Popless
        'responsive-xs': 'clamp(0.75rem, 1.5vw, 0.875rem)',
        'responsive-sm': 'clamp(0.875rem, 2vw, 1rem)',
        'responsive-base': 'clamp(1rem, 2.5vw, 1.125rem)',
        'responsive-lg': 'clamp(1.125rem, 3vw, 1.25rem)',
        'responsive-xl': 'clamp(1.25rem, 3.5vw, 1.5rem)',
        'responsive-2xl': 'clamp(1.5rem, 4vw, 2rem)',
        'responsive-3xl': 'clamp(2rem, 5vw, 3rem)',
        'responsive-4xl': 'clamp(2.5rem, 6vw, 4rem)',
        'responsive-5xl': 'clamp(3rem, 7vw, 5rem)',
        'responsive-6xl': 'clamp(3.5rem, 8vw, 6rem)',
      },
      lineHeight: {
        'responsive-tight': 'clamp(1.1, 1.2, 1.3)',
        'responsive-normal': 'clamp(1.4, 1.5, 1.6)',
        'responsive-relaxed': 'clamp(1.5, 1.6, 1.7)',
      },
      spacing: {
        'responsive-xs': 'clamp(0.5rem, 1.5vw, 0.75rem)',
        'responsive-sm': 'clamp(0.75rem, 2vw, 1rem)',
        'responsive-md': 'clamp(1rem, 3vw, 1.5rem)',
        'responsive-lg': 'clamp(1.5rem, 4vw, 2rem)',
        'responsive-xl': 'clamp(2rem, 5vw, 3rem)',
      },
    },
  },
  plugins: [],
};

export default config;
