-- Fix Duplicate Functions Issue
-- Problem: Multiple versions of add_user_role function causing conflicts

-- Step 1: Drop ALL versions of the function to clean slate
DROP FUNCTION IF EXISTS public.add_user_role(UUID, text) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(UUID, user_role) CASCADE; 
DROP FUNCTION IF EXISTS public.add_user_role(UUID, text, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(UUID, character varying) CASCADE;
DROP FUNCTION IF EXISTS public.add_user_role(UUID, character varying, UUID) CASCADE;

-- Step 2: Create ONE clean version that matches the frontend call
CREATE OR REPLACE FUNCTION public.add_user_role(user_id UUID, new_role text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Simple role addition - update profile with new role if user doesn't already have it
    UPDATE public.profiles 
    SET 
        roles = array_append(COALESCE(roles, '{}'), new_role::user_role),
        tutor_status = CASE 
            WHEN new_role = 'tutor' THEN 'pending'::approval_status
            ELSE tutor_status
        END,
        student_status = CASE 
            WHEN new_role = 'student' THEN 'approved'::approval_status
            ELSE student_status
        END,
        updated_at = NOW()
    WHERE id = user_id
    AND NOT (new_role::user_role = ANY(COALESCE(roles, '{}')));
    
    -- Raise exception if no rows were updated (user not found or already has role)
    IF NOT FOUND THEN
        -- Check if user exists
        IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id) THEN
            RAISE EXCEPTION 'User profile not found';
        END IF;
        
        -- Check if user already has role
        IF EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id AND new_role::user_role = ANY(COALESCE(roles, '{}'))) THEN
            RAISE EXCEPTION 'User already has % role', new_role;
        END IF;
    END IF;
END;
$$;

-- Step 3: Grant permissions
GRANT EXECUTE ON FUNCTION public.add_user_role(UUID, text) TO authenticated;

-- Step 4: Verify only one function exists now
SELECT 
    proname as function_name,
    pronargs as arg_count,
    proargnames as parameters
FROM pg_proc 
WHERE proname = 'add_user_role'
ORDER BY proname;

SELECT 'Function conflicts resolved!' as status;