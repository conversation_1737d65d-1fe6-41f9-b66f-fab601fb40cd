-- Comprehensive Role Assignment Debug Script
-- Created: 2025-07-18
-- Purpose: Debug the persistent "Failed to request tutor role" error

-- Step 1: Check current database functions
SELECT 'Checking existing functions...' as step;

SELECT 
    proname as function_name,
    pronargs as arg_count,
    proargnames as parameters,
    prosrc as function_body
FROM pg_proc 
WHERE proname = 'add_user_role'
ORDER BY proname;

-- Step 2: Check if user_role enum values are correct
SELECT 'Checking user_role enum...' as step;

SELECT enumlabel as role_value
FROM pg_enum e
JOIN pg_type t ON e.enumtypid = t.oid
WHERE t.typname = 'user_role'
ORDER BY enumsortorder;

-- Step 3: Check approval_status enum values
SELECT 'Checking approval_status enum...' as step;

SELECT enumlabel as status_value
FROM pg_enum e
JOIN pg_type t ON e.enumtypid = t.oid
WHERE t.typname = 'approval_status'
ORDER BY enumsortorder;

-- Step 4: Test the add_user_role function with debug output
CREATE OR REPLACE FUNCTION public.add_user_role_debug(user_id UUID, new_role text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_roles user_role[];
    new_roles user_role[];
    role_enum user_role;
    result_msg text;
BEGIN
    -- Step 1: Validate role parameter
    BEGIN
        role_enum := new_role::user_role;
        result_msg := 'Step 1 OK: Role validation passed - ' || new_role;
    EXCEPTION WHEN others THEN
        RETURN 'ERROR Step 1: Invalid role ' || new_role || '. Valid roles: student, tutor, admin';
    END;

    -- Step 2: Check if user exists
    SELECT roles INTO current_roles 
    FROM public.profiles 
    WHERE id = user_id;
    
    IF NOT FOUND THEN
        RETURN 'ERROR Step 2: User profile not found for ID: ' || user_id;
    END IF;
    
    result_msg := result_msg || ' | Step 2 OK: User found with roles: ' || COALESCE(array_to_string(current_roles, ','), 'NULL');

    -- Step 3: Check if role already exists
    IF role_enum = ANY(COALESCE(current_roles, '{}')) THEN
        RETURN 'ERROR Step 3: User already has role ' || new_role;
    END IF;
    
    result_msg := result_msg || ' | Step 3 OK: Role not already assigned';

    -- Step 4: Add the new role
    new_roles := COALESCE(current_roles, '{}') || ARRAY[role_enum];
    result_msg := result_msg || ' | Step 4: New roles array: ' || array_to_string(new_roles, ',');

    -- Step 5: Update the user profile
    BEGIN
        UPDATE public.profiles 
        SET 
            roles = new_roles,
            tutor_status = CASE 
                WHEN role_enum = 'tutor' THEN 'pending'::approval_status
                ELSE tutor_status
            END,
            admin_status = CASE 
                WHEN role_enum = 'admin' THEN 'pending'::approval_status  
                ELSE admin_status
            END,
            updated_at = NOW()
        WHERE id = user_id;
        
        result_msg := result_msg || ' | Step 5 OK: Database update successful';
    EXCEPTION WHEN others THEN
        RETURN 'ERROR Step 5: Database update failed - ' || SQLERRM;
    END;

    RETURN 'SUCCESS: ' || result_msg;
END;
$$;

-- Grant permissions for the debug function
GRANT EXECUTE ON FUNCTION public.add_user_role_debug(UUID, text) TO authenticated;

-- Step 5: Check current profiles table structure
SELECT 'Checking profiles table structure...' as step;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 6: Show any existing profiles
SELECT 'Current profiles in database:' as step;

SELECT 
    id,
    email,
    full_name,
    roles,
    student_status,
    tutor_status,
    admin_status
FROM public.profiles
ORDER BY created_at DESC
LIMIT 5;

-- Step 7: Check RLS status
SELECT 'Checking RLS status...' as step;

SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' AND tablename = 'profiles';

SELECT 'Debug script completed - check the results above' as final_status;