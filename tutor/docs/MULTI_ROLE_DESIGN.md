# Multi-Role System Design

## Overview
Users can have multiple roles simultaneously instead of being locked into a single role.

## Database Changes

### Before (Single Role)
```sql
role user_role NOT NULL DEFAULT 'student'  -- enum: student, tutor, admin
```

### After (Multiple Roles)
```sql
roles user_role[] NOT NULL DEFAULT ARRAY['student']  -- array of roles
```

## Role Logic

### Default Behavior
- New users start with `['student']` by default
- Users can add `tutor` and/or `admin` roles later
- Admin role requires special permission (not self-assignable)

### Approval System
- **Student role**: Always approved (instant)
- **Tutor role**: Requires admin approval
- **Admin role**: Requires super admin assignment (manual)

### Role States
Each role can have its own approval status:
```sql
student_status: 'approved' (always)
tutor_status: 'pending' | 'approved' | 'rejected'  
admin_status: 'approved' (when assigned)
```

## UI/UX Changes

### Dashboard
- **Role Switcher**: Tab interface to switch between active roles
- **Student Tab**: Browse tutors, my sessions, etc.
- **Tutor Tab**: Manage availability, my students, earnings
- **Admin Tab**: Approve tutors, platform analytics

### Role Management Page
- Toggle switches for each role
- Status indicators for approval states
- Apply/Request buttons for new roles

## RLS Policy Updates

### Before
```sql
WHERE profiles.role = 'tutor'
```

### After  
```sql
WHERE 'tutor' = ANY(profiles.roles)
```

## Benefits

1. **Eliminates Role Conflicts**: No forced choosing
2. **Real-world Modeling**: Students who tutor, admin-tutors, etc.
3. **Flexible Permissions**: Fine-grained access control
4. **Better UX**: Users see relevant features for their roles
5. **Easier Onboarding**: Start as student, add roles as needed

## Implementation Plan

1. Update database schema with migration
2. Create role management interface
3. Update dashboard with role tabs
4. Modify RLS policies
5. Update all role checks throughout app
6. Test with existing users (preserve current single roles)