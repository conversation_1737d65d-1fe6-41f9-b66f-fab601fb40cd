(()=>{function E(e,t){for(var s=new X(31),i=0;i<31;++i)s[i]=t+=1<<e[i-1];for(var n=new J(s[30]),i=1;i<30;++i)for(var r=s[i];r<s[i+1];++r)n[r]=r-s[i]<<5|i;return{b:s,r:n}}var C={},_=function(e,t,s,i,n){t=new Worker(C[t]||(C[t]=URL.createObjectURL(new Blob([e+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return t.onmessage=function(e){var t,e=e.data,s=e.$e$;s?((t=new Error(s[0])).code=s[1],t.stack=s[2],n(t,null)):n(null,e)},t.postMessage(s,i),t},V=Uint8Array,X=Uint16Array,J=Int32Array,K=new V([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),G=new V([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),q=new V([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),e=E(K,2),t=e.b,Q=e.r;t[28]=258,Q[258]=28;for(var Y=E(G,0).r,N=new X(32768),s=0;s<32768;++s){var i=(43690&s)>>1|(21845&s)<<1;N[s]=((65280&(i=(61680&(i=(52428&i)>>2|(13107&i)<<2))>>4|(3855&i)<<4))>>8|(255&i)<<8)>>1}for(var F=function(e,t,s){for(var i=e.length,n=0,r=new X(t);n<i;++n)e[n]&&++r[e[n]-1];var a=new X(t);for(n=1;n<t;++n)a[n]=a[n-1]+r[n-1]<<1;if(s){for(var o=new X(1<<t),h=15-t,n=0;n<i;++n)if(e[n])for(var l=n<<4|e[n],c=t-e[n],d=a[e[n]-1]++<<c,u=d|(1<<c)-1;d<=u;++d)o[N[d]>>h]=l}else for(o=new X(i),n=0;n<i;++n)e[n]&&(o[n]=N[a[e[n]-1]++]>>15-e[n]);return o},j=new V(288),s=0;s<144;++s)j[s]=8;for(s=144;s<256;++s)j[s]=9;for(s=256;s<280;++s)j[s]=7;for(s=280;s<288;++s)j[s]=8;for(var W=new V(32),s=0;s<32;++s)W[s]=5;function O(){return[V,X,J,K,G,q,Q,Y,$,j,Z,W,N,oe,he,F,B,P,se,ie,ne,H,re,ae,ee,te,le,ue,ke,ye]}function R(){return[Se,we,be,de,ce]}var d,n,M,L,$=F(j,9,0),Z=F(W,5,0),ee=function(e){return(e+7)/8|0},te=function(e,t,s){return(null==s||s>e.length)&&(s=e.length),new V(e.subarray(t=null==t||t<0?0:t,s))},D=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],A=function(e,t,s){t=new Error(t||D[e]);if(t.code=e,Error.captureStackTrace&&Error.captureStackTrace(t,A),s)return t;throw t},B=function(e,t,s){var i=t/8|0;e[i]|=s<<=7&t,e[1+i]|=s>>8},P=function(e,t,s){var i=t/8|0;e[i]|=s<<=7&t,e[1+i]|=s>>8,e[2+i]|=s>>16},se=function(e,t){for(var s=[],i=0;i<e.length;++i)e[i]&&s.push({s:i,f:e[i]});var n=s.length,r=s.slice();if(!n)return{t:he,l:0};if(1==n)return(g=new V(s[0].s+1))[s[0].s]=1,{t:g,l:1};s.sort(function(e,t){return e.f-t.f}),s.push({s:-1,f:25001});var a=s[0],o=s[1],h=0,l=1,c=2;for(s[0]={s:-1,f:a.f+o.f,l:a,r:o};l!=n-1;)a=s[s[h].f<s[c].f?h++:c++],o=s[h!=l&&s[h].f<s[c].f?h++:c++],s[l++]={s:-1,f:a.f+o.f,l:a,r:o};for(var d=r[0].s,i=1;i<n;++i)r[i].s>d&&(d=r[i].s);var u=new X(d+1),p=ie(s[l-1],u,0);if(t<p){var i=0,f=0,g=p-t,m=1<<g;for(r.sort(function(e,t){return u[t.s]-u[e.s]||e.f-t.f});i<n;++i){var v=r[i].s;if(!(u[v]>t))break;f+=m-(1<<p-u[v]),u[v]=t}for(f>>=g;0<f;){var y=r[i].s;u[y]<t?f-=1<<t-u[y]++-1:++i}for(;0<=i&&f;--i){var b=r[i].s;u[b]==t&&(--u[b],++f)}p=t}return{t:new V(u),l:p}},ie=function(e,t,s){return-1==e.s?Math.max(ie(e.l,t,s+1),ie(e.r,t,s+1)):t[e.s]=s},ne=function(e){for(var t=e.length;t&&!e[--t];);for(var s=new X(++t),i=0,n=e[0],r=1,a=function(e){s[i++]=e},o=1;o<=t;++o)if(e[o]==n&&o!=t)++r;else{if(!n&&2<r){for(;138<r;r-=138)a(32754);2<r&&(a(10<r?r-11<<5|28690:r-3<<5|12305),r=0)}else if(3<r){for(a(n),--r;6<r;r-=6)a(8304);2<r&&(a(r-3<<5|8208),r=0)}for(;r--;)a(n);r=1,n=e[o]}return{c:s.subarray(0,i),n:t}},H=function(e,t){for(var s=0,i=0;i<t.length;++i)s+=e[i]*t[i];return s},re=function(e,t,s){var i=s.length,n=ee(t+2);e[n]=255&i,e[n+1]=i>>8,e[n+2]=255^e[n],e[n+3]=255^e[n+1];for(var r=0;r<i;++r)e[n+r+4]=s[r];return 8*(n+4+i)},ae=function(e,t,s,i,n,r,a,o,h,l,c){B(t,c++,s),++n[256];for(var s=se(n,15),d=s.t,s=s.l,u=se(r,15),p=u.t,u=u.l,f=ne(d),g=f.c,f=f.n,m=ne(p),v=m.c,m=m.n,y=new X(19),b=0;b<g.length;++b)++y[31&g[b]];for(b=0;b<v.length;++b)++y[31&v[b]];for(var S=se(y,7),w=S.t,S=S.l,k=19;4<k&&!w[q[k-1]];--k);var T=l+5<<3,I=H(n,j)+H(r,W)+a,n=H(n,d)+H(r,p)+a+14+3*k+H(y,w)+2*y[16]+3*y[17]+7*y[18];if(0<=h&&T<=I&&T<=n)return re(t,c,e.subarray(h,h+l));if(B(t,c,1+(n<I)),c+=2,n<I){var x=F(d,s,0),E=d,C=F(p,u,0),_=p,N=F(w,S,0);B(t,c,f-257),B(t,c+5,m-1),B(t,c+10,k-4),c+=14;for(b=0;b<k;++b)B(t,c+3*b,w[q[b]]);c+=3*k;for(var O=[g,v],R=0;R<2;++R)for(var M=O[R],b=0;b<M.length;++b){var D=31&M[b];B(t,c,N[D]),c+=w[D],15<D&&(B(t,c,M[b]>>5&127),c+=M[b]>>12)}}else x=$,E=j,C=Z,_=W;for(b=0;b<o;++b){var A,L=i[b];255<L?(P(t,c,x[(D=L>>18&31)+257]),c+=E[D+257],7<D&&(B(t,c,L>>23&31),c+=K[D]),P(t,c,C[A=31&L]),c+=_[A],3<A&&(P(t,c,L>>5&8191),c+=G[A])):(P(t,c,x[L]),c+=E[L])}return P(t,c,x[256]),c+E[256]},oe=new J([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),he=new V(0),le=function(t,e,s,i,n,r){var a=r.z||t.length,o=new V(i+a+5*(1+Math.ceil(a/7e3))+n),h=o.subarray(i,o.length-n),l=r.l,c=7&(r.r||0);if(e){c&&(h[0]=r.r>>3);for(var e=oe[e-1],F=e>>13,j=8191&e,d=(1<<s)-1,u=r.p||new X(32768),p=r.h||new X(1+d),f=Math.ceil(s/3),B=2*f,g=function(e){return(t[e]^t[e+1]<<f^t[e+2]<<B)&d},m=new J(25e3),v=new X(288),y=new X(32),b=0,S=0,w=r.i||0,k=0,T=r.w||0,I=0;w+2<a;++w){var x=g(w),E=32767&w,C=p[x];if(u[E]=C,p[x]=E,T<=w){var _=a-w;if((7e3<b||24576<k)&&(423<_||!l)){for(var c=ae(t,h,0,m,v,y,S,k,I,w-I,c),k=b=S=0,I=w,N=0;N<286;++N)v[N]=0;for(N=0;N<30;++N)y[N]=0}var O=2,R=0,P=j,M=E-C&32767;if(2<_&&x==g(w-M))for(var H=Math.min(F,_)-1,U=Math.min(32767,w),z=Math.min(258,_);M<=U&&--P&&E!=C;){if(t[w+O]==t[w+O-M]){for(var D=0;D<z&&t[w+D]==t[w+D-M];++D);if(O<D){if(R=M,H<(O=D))break;for(var q=Math.min(M,D-2),W=0,N=0;N<q;++N){var A=w-M+N&32767,$=A-u[A]&32767;W<$&&(W=$,C=A)}}}M+=(E=C)-(C=u[E])&32767}R?(m[k++]=268435456|Q[O]<<18|Y[R],x=31&Q[O],_=31&Y[R],S+=K[x]+G[_],++v[257+x],++y[_],T=w+O,++b):(m[k++]=t[w],++v[t[w]])}}for(w=Math.max(w,T);w<a;++w)m[k++]=t[w],++v[t[w]];c=ae(t,h,l,m,v,y,S,k,I,w-I,c),l||(r.r=7&c|h[c/8|0]<<3,c-=7,r.h=p,r.p=u,r.i=w,r.w=T)}else{for(w=r.w||0;w<a+l;w+=65535){var L=w+65535;a<=L&&(h[c/8|0]=l,L=a),c=re(h,c+1,t.subarray(w,L))}r.i=a}return te(o,0,i+ee(c)+n)},ce=(()=>{for(var e=new Int32Array(256),t=0;t<256;++t){for(var s=t,i=9;--i;)s=(1&s&&-306674912)^s>>>1;e[t]=s}return e})(),de=function(){var i=-1;return{p:function(e){for(var t=i,s=0;s<e.length;++s)t=ce[255&t^e[s]]^t>>>8;i=t},d:function(){return~i}}},ue=function(e,t,s,i,n){var r,a;return n||(n={l:1},t.dictionary&&(r=t.dictionary.subarray(-32768),(a=new V(r.length+e.length)).set(r),a.set(e,r.length),e=a,n.w=r.length)),le(e,null==t.level?6:t.level,null==t.mem?n.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):20:12+t.mem,s,i,n)},pe=function(e,t){var s,i={};for(s in e)i[s]=e[s];for(s in t)i[s]=t[s];return i},fe=function(e,t,s){for(var i=e(),e=e.toString(),n=e.slice(e.indexOf("[")+1,e.lastIndexOf("]")).replace(/\s+/g,"").split(","),r=0;r<i.length;++r){var a=i[r],o=n[r];if("function"==typeof a){t+=";"+o+"=";var h=a.toString();if(a.prototype)if(-1!=h.indexOf("[native code]")){var l=h.indexOf(" ",8)+1;t+=h.slice(l,h.indexOf("(",l))}else for(var c in t+=h,a.prototype)t+=";"+o+".prototype."+c+"="+a.prototype[c].toString();else t+=h}else s[o]=a}return t},ge=[],me=function(e){var t,s=[];for(t in e)e[t].buffer&&s.push((e[t]=new e[t].constructor(e[t])).buffer);return s},ve=function(e,t,s,i){if(!ge[s]){for(var n="",r={},a=e.length-1,o=0;o<a;++o)n=fe(e[o],n,r);ge[s]={c:fe(e[a],n,r),e:r}}var h=pe({},ge[s].e);return _(ge[s].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+t.toString()+"}",s,h,me(h),i)},ye=function(e){return postMessage(e,[e.buffer])},be=function(e,t,s){for(;s;++t)e[t]=s,s>>>=8},Se=function(e,t){var s=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&be(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),s){e[3]=8;for(var i=0;i<=s.length;++i)e[i+10]=s.charCodeAt(i)}},we=function(e){return 10+(e.filename?e.filename.length+1:0)};function ke(e,t){return ue(e,t||{},0,0)}function Te(e,t,s){var i,n;s||(s=t,t={}),"function"!=typeof s&&A(7),e=e,t=t,i=s,(n=ve([O,R,function(){return[Ie]}],function(e){return ye(Ie(e.data[0],e.data[1]))},2,function(e,t){n.terminate(),i(e,t)})).postMessage([e,t],t.consume?[e.buffer]:[])}function Ie(e,t){t=t||{};var s=de(),i=e.length,e=(s.p(e),ue(e,t,we(t),8)),n=e.length;return Se(e,t),be(e,n-8,s.d()),be(e,n-4,i),e}e="undefined"!=typeof TextDecoder&&new TextDecoder;try{e.decode(he,{stream:!0})}catch(li){}class xe{constructor(){this.lastTs=0,this.lastSuffix=1,this.backDict={},this.getKey=t=>{let s=!1;t="__"+t;if(!this.backDict[t]){s=!0;var i=Date.now()%1e11;let e=i;e===this.lastTs?(e=1e4*e+this.lastSuffix,this.lastSuffix+=1):this.lastSuffix=1,this.backDict[t]=e,this.lastTs=i}return[this.backDict[t],s]}}}class Ee{constructor(e){this.sendSetAttribute=(e,t,s)=>this.isDictDisabled?this.app.send([12,e,t,s]):(e=[35,e,this.applyDict(t),this.applyDict(s)],this.app.send(e)),this.app=e.app,this.isDictDisabled=e.isDictDisabled,this.dict=new xe}applyDict(e){var[t,s]=this.dict.getKey(e);return s&&this.app.send([34,t,e]),t}clear(){this.dict=new xe}}class Ce{constructor(e,t){this.app=e,this.startParams=t,this.conditions=[],this.hasStarted=!1,this.createConditionFromFilter=e=>{e=_e(e);if(e.type)return e},this.durationInt=null}setConditions(e){this.conditions=e}async fetchConditions(e,t){try{var s=(await(await fetch(this.app.options.ingestPoint+"/v1/web/conditions/"+e,{method:"GET",headers:{Authorization:"Bearer "+t}})).json()).conditions;let i=[];s.forEach(s=>{s.filters.forEach(e=>{let t;"fetch"===e.type?(t={type:"network_request",subConditions:[],name:s.name},e.filters.forEach(e=>{e=this.createConditionFromFilter(e);e&&t.subConditions.push(e)})):t=this.createConditionFromFilter(e),t&&("session_duration"===t.type&&this.processDuration(t.value[0],s.name),i.push({...t,name:s.name}))})}),this.conditions=i}catch(e){this.app.debug.error("Critical: cannot fetch start conditions")}}trigger(e){if(!this.hasStarted)try{this.hasStarted=!0,this.app.start(this.startParams,void 0,e)}catch(e){this.app.debug.error(e)}}processMessage(e){if(!this.hasStarted)switch(e[0]){case 78:this.jsExceptionEvent(e);break;case 27:this.customEvent(e);break;case 68:this.clickEvent(e);break;case 122:this.pageLocationEvent(e);break;case 83:this.networkRequest(e)}}processFlags(e){var t=this.conditions.filter(e=>"feature_flag"===e.type);t.length&&t.forEach(t=>{let s=r[t.operator];s&&e.find(e=>s(e.key,t.value))&&this.trigger(t.name)})}processDuration(t,s){this.durationInt=setInterval(()=>{var e=performance.now();t<e&&this.trigger(s)},1e3),this.app.attachStopCallback(()=>{this.durationInt&&clearInterval(this.durationInt)})}networkRequest(i){var e=this.conditions.filter(e=>"network_request"===e.type);e.length&&e.forEach(e=>{var t=e.subConditions.filter(e=>"isAny"!==e.operator);t.length?t.every(e=>{let t;switch(e.key){case"url":t=i[3];break;case"status":t=i[6];break;case"method":t=i[2];break;case"duration":t=i[8]}var s=r[e.operator];if(s&&s(t,e.value))return!0})&&this.trigger(e.name):0===t.length&&e.subConditions.length&&this.trigger(e.name)})}customEvent(s){var e=this.conditions.filter(e=>"custom_event"===e.type);e.length&&e.forEach(e=>{var t=r[e.operator];t&&(t(s[1],e.value)||t(s[2],e.value))&&this.trigger(e.name)})}clickEvent(s){var e=this.conditions.filter(e=>"click"===e.type);e.length&&e.forEach(e=>{var t=r[e.operator];t&&(t(s[3],e.value)||t(s[4],e.value))&&this.trigger(e.name)})}pageLocationEvent(s){var e=this.conditions.filter(e=>"visited_url"===e.type);e&&e.forEach(e=>{var t=r[e.operator];t&&t(s[1],e.value)&&this.trigger(e.name)})}jsExceptionEvent(e){let i=[e[1],e[2],e[3]];e=this.conditions.filter(e=>"exception"===e.type);e&&e.forEach(t=>{let s=r[t.operator];s&&i.some(e=>s(e,t.value))&&this.trigger(t.name)})}}let r={is:(t,e)=>e.some(e=>t.includes(e)),isAny:()=>!0,isNot:(t,e)=>!e.some(e=>t.includes(e)),contains:(t,e)=>e.some(e=>t.includes(e)),notContains:(t,e)=>!e.some(e=>t.includes(e)),startsWith:(t,e)=>e.some(e=>t.startsWith(e)),endsWith:(t,e)=>e.some(e=>t.endsWith(e)),greaterThan:(e,t)=>t<e,greaterOrEqual:(e,t)=>t<=e,lessOrEqual:(e,t)=>e<=t,lessThan:(e,t)=>e<t},_e=e=>{let t={on:"is",notOn:"isNot",">":"greaterThan","<":"lessThan","=":"is","<=":"lessOrEqual",">=":"greaterOrEqual"};var s=e=>{if(Object.keys(t).includes(e))return t[e]};let i={type:"",operator:"",value:e.value,key:""};switch(e.type){case"click":i={type:"click",operator:s(e.operator),value:e.value,key:""};break;case"location":i={type:"visited_url",operator:e.operator,value:e.value,key:""};break;case"custom":i={type:"custom_event",operator:e.operator,value:e.value,key:""};break;case"metadata":i={type:"featureFlag"===e.source?"feature_flag":e.type,operator:e.operator,value:e.value,key:""};break;case"error":i={type:"exception",operator:e.operator,value:e.value,key:""};break;case"duration":i={type:"session_duration",value:e.value,key:"",operator:"is"};break;case"fetchUrl":i={type:"network_request",key:"url",operator:e.operator,value:e.value};break;case"fetchStatusCode":i={type:"network_request",key:"status",operator:s(e.operator),value:e.value};break;case"fetchMethod":i={type:"network_request",key:"method",operator:s(e.operator),value:e.value};break;case"fetchDuration":i={type:"network_request",key:"duration",operator:s(e.operator),value:e.value}}return i};class Ne{constructor(e){this.app=e,this.flags=[],this.storageKey="__openreplay_flags";var e=this.app.sessionStorage.getItem(this.storageKey);e&&(e=e.split(";").filter(Boolean),this.flags=e.map(e=>JSON.parse(e)))}getFeatureFlag(t){return this.flags.find(e=>e.key===t)}isFlagEnabled(t){return-1!==this.flags.findIndex(e=>e.key===t)}onFlagsLoad(e){this.onFlagsCb=e}async reloadFlags(e){var t=this.app.sessionStorage.getItem(this.storageKey);let s={};t&&t.split(";").filter(Boolean).forEach(e=>{e=JSON.parse(e);s[e.key]={key:e.key,value:e.value}});var t=this.app.session.getInfo(),i=this.app.session.userInfo,t={projectID:t.projectID,userID:t.userID,metadata:t.metadata,referrer:document.referrer,os:i.userOS,device:i.userDevice,country:i.userCountry,state:i.userState,city:i.userCity,browser:i.userBrowser,persistFlags:s},i=e??this.app.session.getSessionToken(),e=await fetch(this.app.options.ingestPoint+"/v1/web/feature-flags",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer "+i},body:JSON.stringify(t)});if(200===e.status)return i=await e.json(),this.handleFlags(i.flags)}handleFlags(e){let t=[],s=(e.forEach(e=>{e.is_persist&&t.push(e)}),"");return this.diffPersist(t).forEach(e=>{s+=JSON.stringify(e)+";"}),this.app.sessionStorage.setItem(this.storageKey,s),this.flags=e,this.onFlagsCb?.(e)}clearPersistFlags(){this.app.sessionStorage.removeItem(this.storageKey)}diffPersist(e){var t=this.app.sessionStorage.getItem(this.storageKey);if(!t)return e;let s=t.split(";").filter(Boolean).map(e=>JSON.parse(e));return e.filter(t=>-1===s.findIndex(e=>e.key===t.key))}}let Oe={htmlmasked:"hidden",masked:"obscured"},h=!("undefined"==typeof window),Re=h&&navigator.userAgent.match(/firefox|fxios/i),T=h?Date.now()-performance.now():0;function Me(){T=Date.now()-performance.now()}let U=h&&performance.now?()=>Math.round(performance.now()+T):()=>Date.now(),De="repeat"in String.prototype?e=>"*".repeat(e.length):e=>e.replace(/./g,"*");function Ae(e){return e.trim().replace(/\s+/g," ")}function Le(e){return e.startsWith("https://")||e.startsWith("http://")}let Fe="https://docs.openreplay.com",je={};function I(e,t,s="/"){je[e]||(console.warn(`OpenReplay: ${e} is deprecated. ${t?`Please, use ${t} instead.`:""} Visit ${Fe}${s} for more information.`),je[e]=!0)}function Be(e){var t=e.getAttribute("data-openreplay-label");return null===t&&null!==(t=e.getAttribute("data-asayer-label"))&&I('"data-asayer-label" attribute','"data-openreplay-label" attribute',"/"),t}function a(e,t){var s="data-openreplay-"+t;return e.hasAttribute(s)&&(Oe[t]&&I(`"${s}" attribute`,`"${Oe[t]}" attribute`,"/en/sdk/sanitize-data"),1)}function Pe(e){try{e instanceof HTMLIFrameElement?e.contentDocument:e instanceof Window?e.document:e instanceof Document?e.defaultView:"nodeType"in e?e.nodeType:"addEventListener"in e&&e.addEventListener}catch(e){if(e instanceof DOMException&&"SecurityError"===e.name)return}return 1}function He(e){return e.toString(16).padStart(2,"0")}function Ue(){try{return window.self&&window.top&&window.self!==window.top}catch(e){return!0}}function ze(e){return window.Zone&&"__symbol__"in window.Zone?window.Zone.__symbol__(e):e}function qe(e,t){return new(t?MutationObserver:(t=ze("MutationObserver"),window[t]))(e)}function We(t,s,i,n,r){if(Pe(t)){let e="addEventListener";r||(e=ze("addEventListener"));try{t[e]?t[e](s,i,n):t.addEventListener(s,i,n)}catch(e){r=e.message;console.error(`Openreplay: ${r}; if this error is caused by an IframeObserver, ignore it`,s,t)}}}function $e(t,s,i,n,r){if(Pe(t)){let e="removeEventListener";r||(e=ze("removeEventListener"));try{t[e]?t[e](s,i,n):t.removeEventListener(s,i,n)}catch(e){r=e.message;console.error(`Openreplay: ${r}; if this error is caused by an IframeObserver, ignore it`,s,t)}}}let Ve=new class{constructor(){this.taskQueue=[],this.isRunning=!1}addTask(e){this.taskQueue.push(e),this.runTasks()}runTasks(){if(!this.isRunning&&0!==this.taskQueue.length){this.isRunning=!0;let t=()=>{var e;0===this.taskQueue.length?this.isRunning=!1:(e=this.taskQueue.shift(),Promise.resolve(e()).then(()=>{requestAnimationFrame(()=>t())}))};t()}}};function Xe(e){Ve.addTask(e)}function Je(e,t){return[13,e,t]}function Ke(e,t){return[30,e,t]}function Ge(e,t,s,i){return[57,e,t,s,i]}function Qe(e){return[58,e]}function Ye(e,t,s,i){return[60,e,t,s,i]}function Ze(e,t,s){return[61,e,t,s]}function et(e,t){return[70,e,t]}function tt(e,t,s){return[71,e,t,s]}function st(e,t,s,i){return[73,e,t,s,i]}function it(e,t){return[75,e,t]}function nt(e,t){return[76,e,t]}function rt(e,t,s,i){return[78,e,t,s,i]}function at(e,t,s,i,n,r,a,o,h){return[83,e,t,s,i,n,r,a,o,h]}function ot(e,t,s){return[113,e,t,s]}function ht(e){return[115,e]}function lt(e,t,s,i,n,r,a,o,h,l){return[116,e,t,s,i,n,r,a,o,h,l]}function ct(e){return[117,e]}function dt(e){return[118,e]}let ut=h&&"performance"in window&&"memory"in performance?performance:{memory:{}},pt=h?1024*(navigator.deviceMemory||0):0,ft=ut.memory.jsHeapSizeLimit||0,gt="__or__watched_tags__";class mt{constructor(e){this.intervals={},this.tags=[],this.sessionStorage=e.sessionStorage,this.errLog=e.errLog,this.onTag=e.onTag;e=JSON.parse(e.sessionStorage.getItem(gt)??"[]");this.setTags(e),this.observer=new IntersectionObserver(e=>{e.forEach(e=>{var t;e.isIntersecting&&e.target&&((t=e.target.__or_watcher_tagname)&&this.onTagRendered(t),this.observer.unobserve(e.target))})})}async fetchTags(e,t){return fetch(e+"/v1/web/tags",{method:"GET",headers:{Authorization:"Bearer "+t}}).then(e=>e.json()).then(({tags:e})=>{e&&e.length&&(this.setTags(e),e=JSON.stringify(e),this.sessionStorage.setItem(gt,e||""))}).catch(e=>this.errLog(e))}setTags(e){this.tags=e,this.intervals={},e.forEach(t=>{this.intervals[t.id]=setInterval(()=>{var e=document.querySelectorAll(t.selector);0<e.length&&((e=e[0]).__or_watcher_tagname=t.id,this.observer.observe(e))},500)})}onTagRendered(e){this.intervals[e]&&clearInterval(this.intervals[e]),this.onTag(e)}clear(){this.tags.forEach(e=>{clearInterval(this.intervals[e.id])}),this.tags=[],this.intervals={},this.observer.disconnect()}}let vt={position:"fixed",top:0,left:0,width:"100vw",height:"100vh",background:"rgba(0, 0, 0, 0.40)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:999999,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"'},yt={display:"flex",flexDirection:"column",gap:"2rem",alignItems:"center",padding:"1.5rem",borderRadius:"2px",border:"1px solid rgb(255 255 255 / var(--tw-bg-opacity, 1))",background:"#FFF",width:"22rem"},bt={display:"flex","flex-direction":"column",gap:"unset","align-items":"center",padding:"unset",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',"border-radius":"2px",border:"1px solid rgb(255 255 255 / var(--tw-bg-opacity, 1))",background:"rgba(255, 255, 255, 0.75)",width:"22rem"},St={fontFamily:"Verdana, sans-serif",fontSize:"1.25rem",fontStyle:"normal",fontWeight:"500",lineHeight:"1.75rem",color:"rgba(0, 0, 0, 0.85)"},wt={borderTop:"1px solid rgba(0, 0, 0, 0.06)",borderBottom:"1px solid rgba(0, 0, 0, 0.06)",padding:"1.25rem 0rem",color:"rgba(0, 0, 0, 0.85)",fontFamily:"Verdana, sans-serif",fontSize:"13px",fontStyle:"normal",fontWeight:"400",lineHeight:"auto",whiteSpace:"pre-wrap"},kt={display:"flex",padding:"0.4rem 0.9375rem",justifyContent:"center",alignItems:"center",gap:"0.625rem",borderRadius:"0.25rem",border:"1px solid #394EFF",background:"#394EFF",boxShadow:"0px 2px 0px 0px rgba(0, 0, 0, 0.04)",color:"#FFF",textAlign:"center",fontFamily:"Verdana, sans-serif",fontSize:"1rem",fontStyle:"normal",fontWeight:"500",lineHeight:"1.5rem",cursor:"pointer"},Tt={fontFamily:"Verdana, sans-serif",fontSize:"13px",fontWeight:"500",lineHeight:"auto",display:"flex",justifyContent:"space-between",width:"100%",cursor:"pointer"},v={display:"flex",flexDirection:"column",alignItems:"flex-start",gap:"0.625rem",fontSize:"13px",lineHeight:"auto"},It={padding:"0.5rem",gap:"0.5rem",fontFamily:"Verdana, sans-serif",fontSize:"16px",fontStyle:"normal",fontWeight:"500",lineHeight:"auto",color:"white",display:"flex",alignItems:"center",width:"100%",borderRadius:"2px",background:"rgba(0, 0, 0, 0.75)",boxSizing:"border-box"},y={boxSizing:"border-box",display:"block",width:"100%",borderBottom:"1px solid rgb(255 255 255 / var(--tw-bg-opacity, 1))",background:"#FFF",padding:"0.65rem",alignSelf:"stretch",color:"#000",fontFamily:"Verdana, sans-serif",fontStyle:"normal",fontWeight:"400"},xt={...y,display:"flex",flexDirection:"column",alignItems:"center",gap:"0.625rem"},Et={fontSize:"1.25rem",fontWeight:"500",cursor:"pointer",color:"#394EFF"},Ct={display:"flex",padding:"0.4rem 0.9375rem",justifyContent:"center",alignItems:"center",gap:"0.625rem",borderRadius:"0.25rem",border:"1px solid #394EFF",background:"#394EFF",boxShadow:"0px 2px 0px 0px rgba(0, 0, 0, 0.04)",color:"#FFF",textAlign:"center",fontFamily:"Verdana, sans-serif",fontSize:"1rem",fontStyle:"normal",fontWeight:"500",lineHeight:"1.5rem",width:"100%",boxSizing:"border-box",cursor:"pointer"},_t={marginTop:"1rem",marginBottom:"1rem",cursor:"pointer",display:"block",fontWeight:"500",fontSize:"13px!important",lineHeight:"auto"},Nt={display:"flex",justifyContent:"space-between",alignItems:"center",gap:"1rem",padding:"0.5rem",width:"100%",boxSizing:"border-box"},Ot={display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"6.25em",outline:"1px solid #394EFF",fontSize:"13px",height:"24px",width:"24px"},Rt={display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"6.25em",outline:"1px solid #D2DFFF",boxShadow:"0px 2px 0px 0px rgba(0, 0, 0, 0.04)",background:"#D2DFFF",fontSize:"13px",height:"24px",width:"24px"},Mt={borderRadius:"0.375rem",border:"1px solid rgba(0, 0, 0, 0.06)",background:"#F5F7FF",boxShadow:"0px 2px 0px 0px rgba(0, 0, 0, 0.04)",display:"flex",flexDirection:"column",padding:"0.625rem 0.9375rem",gap:"0.5rem",alignSelf:"stretch"},Dt={fontWeight:"bold"},At={fontSize:"13px",lineHeight:"auto"},Lt={marginRight:"0.5rem",cursor:"pointer",color:"#394EFF",textAlign:"center",fontFamily:"Verdana, sans-serif",fontSize:"13px",fontStyle:"normal",fontWeight:"500",lineHeight:"auto"},Ft={...Lt,display:"flex",padding:"0.25rem 0.9375rem",justifyContent:"center",alignItems:"center",gap:"0.5rem",borderRadius:"0.25rem",border:"1px solid #394EFF"},jt={display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",boxSizing:"border-box"},Bt={border:"4px solid rgba(255, 255, 255, 0.4)",width:"16px",height:"16px",borderRadius:"50%",borderLeftColor:"#fff",animation:"spin 0.5s linear infinite"},Pt={Standard:{width:1280,height:720}};class Ht{constructor(e){this.app=e,this.mediaRecorder=null,this.recordedChunks=[],this.stream=null,this.recStartTs=null}async startRecording(e,t,s,i){this.recStartTs=this.app.timestamp();try{this.stream=await navigator.mediaDevices.getUserMedia({video:!!i&&{...t,frameRate:{ideal:e}},audio:s}),this.mediaRecorder=new MediaRecorder(this.stream,{mimeType:"video/webm;codecs=vp9"}),this.recordedChunks=[],this.mediaRecorder.ondataavailable=e=>{0<e.data.size&&this.recordedChunks.push(e.data)},this.mediaRecorder.start()}catch(e){console.error(e)}}async stopRecording(){return new Promise(t=>{this.mediaRecorder&&(this.mediaRecorder.onstop=()=>{var e=new Blob(this.recordedChunks,{type:"video/webm"});t(e)},this.mediaRecorder.stop())})}async sendToAPI(){let t=await this.stopRecording();return fetch(this.app.options.ingestPoint+"/v1/web/uxt/upload-url",{headers:{Authorization:"Bearer "+this.app.session.getSessionToken()}}).then(e=>{if(e.ok)return e.json();throw new Error("Failed to get upload url")}).then(({url:e})=>fetch(e,{method:"PUT",headers:{"Content-Type":"video/webm"},body:t})).catch(console.error).finally(()=>{this.discard()})}async saveToFile(e="recorded-video.webm"){var t=await this.stopRecording(),t=URL.createObjectURL(t),s=document.createElement("a");s.style.display="none",s.href=t,s.download=e,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s)}discard(){this.mediaRecorder?.stop(),this.stream?.getTracks().forEach(e=>e.stop())}}function Ut(){(e=document.createElement("style")).type="text/css",e.innerText=`@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }`,document.head.appendChild(e);var e=document.createElement("div");return e.classList.add("spinner"),Object.assign(e.style,Bt),e}function b(e,t,s,i,n){e=document.createElement(e);return e.className=t,Object.assign(e.style,s),i&&(e.textContent=i),n&&(e.id=n),e}let zt="or_uxt_test_start",qt="or_uxt_task_index",Wt="or_uxt_session_id";class $t{constructor(e,t,s,i,n,r,a,o,h){this.ingestPoint=e,this.getTimestamp=t,this.token=s,this.testId=i,this.storageKey=n,this.setStorageKey=r,this.removeStorageKey=a,this.getStorageKey=o,this.getSessionId=h,this.durations={testStart:0,tasks:[]},this.getDurations=()=>this.durations,this.setDurations=e=>{this.durations.testStart=e.testStart,this.durations.tasks=e.tasks},this.signalTask=(t,e,s)=>{var i,n;return t?(n=this.durations.tasks.find(e=>e.taskId===t),i=this.getTimestamp(),n=n?i-n.started:0,fetch(this.ingestPoint+"/v1/web/uxt/signals/task",{method:"POST",headers:{Authorization:"Bearer "+this.token},body:JSON.stringify({testId:this.testId,taskId:t,status:e,duration:n,timestamp:i,taskAnswer:s})})):console.error("User Testing: No Task ID Given")},this.signalTest=e=>{var t=this.getTimestamp(),s=("begin"===e&&this.testId?(s=this.getSessionId(),this.setStorageKey(Wt,s),this.setStorageKey(this.storageKey,this.testId.toString()),this.setStorageKey(zt,t.toString())):(this.removeStorageKey(this.storageKey),this.removeStorageKey(qt),this.removeStorageKey(zt)),this.durations.testStart||t);return fetch(this.ingestPoint+"/v1/web/uxt/signals/test",{method:"POST",headers:{Authorization:"Bearer "+this.token},body:JSON.stringify({testId:this.testId,status:e,duration:t-s,timestamp:t})})};e=this.getStorageKey(zt);e&&(this.durations.testStart=parseInt(e,10))}}class Vt{constructor(e,t){this.app=e,this.storageKey=t,this.bg=b("div","bg",vt,void 0,"__or_ut_bg"),this.container=b("div","container",yt,void 0,"__or_ut_ct"),this.widgetGuidelinesVisible=!0,this.widgetTasksVisible=!1,this.widgetVisible=!0,this.isActive=!1,this.descriptionSection=null,this.taskSection=null,this.endSection=null,this.stopButton=null,this.stopButtonContainer=null,this.test=null,this.testId=null,this.signalManager=null,this.getTest=(t,s,i)=>{this.testId=t;var e=this.app.options.ingestPoint;return fetch(e+"/v1/web/uxt/test/"+t,{headers:{Authorization:"Bearer "+s}}).then(e=>e.json()).then(({test:e})=>{this.isActive=!0,this.test=e,this.signalManager=new $t(this.app.options.ingestPoint,()=>this.app.timestamp(),s,t,this.storageKey,(e,t)=>this.app.localStorage.setItem(e,t),e=>this.app.localStorage.removeItem(e),e=>this.app.localStorage.getItem(e),()=>this.app.getSessionID()),this.createGreeting(e.title,e.reqMic,e.reqCamera),i&&((e.reqMic||e.reqCamera)&&this.userRecorder.startRecording(30,Pt.Standard,e.reqMic,e.reqCamera),this.showWidget(e.description,e.tasks,!0),this.showTaskSection())}).then(()=>t).catch(e=>{console.log("OR: Error fetching test",e)})},this.hideTaskSection=()=>!1,this.showTaskSection=()=>!0,this.collapseWidget=()=>!1,this.removeGreeting=()=>!1,this.toggleDescriptionVisibility=()=>{},this.currentTaskIndex=0,this.userRecorder=new Ht(e);this.app.getSessionID()!==this.app.localStorage.getItem(Wt)&&(this.app.localStorage.removeItem(this.storageKey),this.app.localStorage.removeItem(Wt),this.app.localStorage.removeItem("or_uxt_test_id"),this.app.localStorage.removeItem(qt),this.app.localStorage.removeItem(zt));t=this.app.localStorage.getItem(qt);t&&(this.currentTaskIndex=parseInt(t,10))}getTestId(){return this.testId}createGreeting(e,t,s){let i=b("div","title",St,e),n=b("div","description",wt,`Welcome, you're here to help us improve, not to be judged. Your insights matter!

📹 We're recording this browser tab to learn from your experience.
🎤 Please enable mic and camera if asked, to give us a complete picture.`),r=b("div","button",kt,"Read guidelines to begin");this.removeGreeting=()=>((t||s)&&this.userRecorder.startRecording(30,Pt.Standard,t,s),this.container.removeChild(r),this.container.removeChild(n),this.container.removeChild(i),!1),r.onclick=()=>{this.removeGreeting();var e=this.signalManager?.getDurations();e&&this.signalManager&&(e.testStart=this.app.timestamp(),this.signalManager.setDurations(e)),this.signalManager?.signalTest("begin"),this.container.style.fontFamily='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',Object.assign(this.container.style,bt),this.showWidget(this.test?.guidelines||"",this.test?.tasks||[])},this.container.append(i,n,r),this.bg.appendChild(this.container),document.body.appendChild(this.bg)}showWidget(e,t,s){this.container.innerHTML="",Object.assign(this.bg.style,{position:"fixed",zIndex:99999999999999,right:"8px",left:"unset",width:"fit-content",top:"8px",height:"fit-content",background:"unset",display:"unset",alignItems:"unset",justifyContent:"unset"});var i=this.createTitleSection(),e=(this.container.style.fontFamily='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',Object.assign(this.container.style,bt),this.createDescriptionSection(e)),t=this.createTasksSection(t),n=b("div","stop_bn_or",_t,"Abort Session"),r=b("div","stop_ct_or",{fontSize:"13px!important"});r.style.fontSize="13px",r.append(n),this.container.append(i,e,t,r),this.taskSection=t,this.descriptionSection=e,this.stopButton=n,this.stopButtonContainer=r,n.onclick=()=>{this.userRecorder.discard(),this.signalManager?.signalTest("skipped"),document.body.removeChild(this.bg),window.close()},s?this.toggleDescriptionVisibility():this.hideTaskSection()}createTitleSection(){var e=b("div","title",It),t=(()=>{var t=document.createElement("div");t.className="grid";for(let e=0;e<16;e++){var s=document.createElement("div");Object.assign(s.style,{width:"2px",height:"2px",borderRadius:"10px",background:"white"}),s.className="cell",t.appendChild(s)}return Object.assign(t.style,{display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gridTemplateRows:"repeat(4, 1fr)",gap:"2px",cursor:"grab"}),t})(),s=b("div","title_text",{maxWidth:"19rem",overflow:"hidden",textOverflow:"ellipsis",width:"100%",fontSize:16,lineHeight:"auto",cursor:"pointer"},this.test?.title);a=document.createElement("div"),Object.assign(a.style,{width:"0",height:"0",borderLeft:"7px solid transparent",borderRight:"7px solid transparent",borderBottom:"7px solid white"}),(r=document.createElement("div")).appendChild(a),Object.assign(r.style,{display:"flex",alignItems:"center",justifyContent:"center",width:"16px",height:"16px",cursor:"pointer",marginLeft:"auto",transform:"rotate(180deg)"});let i=r,n=(e.append(t,s,i),e=>(this.widgetVisible=e,this.container.style.fontFamily='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',Object.assign(this.container.style,this.widgetVisible?bt:{border:"none",background:"none",padding:0}),this.taskSection&&Object.assign(this.taskSection.style,this.widgetVisible?y:{display:"none"}),this.descriptionSection&&Object.assign(this.descriptionSection.style,this.widgetVisible?y:{display:"none"}),this.endSection&&Object.assign(this.endSection.style,this.widgetVisible?y:{display:"none"}),this.stopButton&&Object.assign(this.stopButton.style,this.widgetVisible?_t:{display:"none"}),e));var o,r,a=()=>{Object.assign(i.style,{transform:this.widgetVisible?"rotate(0deg)":"rotate(180deg)"}),n(!this.widgetVisible)};return s.onclick=a,i.onclick=a,o=this.bg,(r=t).onmousedown=function(e){let n=o.getBoundingClientRect(),r=e.clientX-n.left,a=e.clientY-n.top;function t(e,t){let s=e-r,i=t-a;s<=5&&(s=5),i<=5&&(i=5),s>=window.innerWidth-n.width&&(s=window.innerWidth-n.width),i>=window.innerHeight-n.height&&(i=window.innerHeight-n.height),o.style.left=s+"px",o.style.top=i+"px"}function s(e){t(e.pageX,e.pageY)}o.style.position="fixed",o.style.zIndex=99999999999999,t(e.pageX,e.pageY),document.addEventListener("mousemove",s);let i=()=>{document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",i)};document.addEventListener("mouseup",i)},r.ondragstart=function(){return!1},this.collapseWidget=()=>n(!1),e}createDescriptionSection(e){var t=b("div","description_section_or",y),s=b("div","description_s_title_or",Tt),i=b("div","title",{fontSize:13,fontWeight:500,lineHeight:"auto"},"Introduction & Guidelines");let n=b("div","icon",Et,"-"),r=b("div","content",v);var a=b("div","text_description",{maxHeight:"250px",overflowY:"auto",whiteSpace:"pre-wrap",fontSize:13,color:"#454545",lineHeight:"auto"});a.innerHTML=e;let o=b("div","button_begin_or",Ct,"Begin Test"),h=(s.append(i,n),r.append(a,o),t.append(s,r),()=>{this.widgetGuidelinesVisible=!this.widgetGuidelinesVisible,n.textContent=this.widgetGuidelinesVisible?"-":"+",Object.assign(r.style,this.widgetGuidelinesVisible?v:{display:"none"})});return s.onclick=h,this.toggleDescriptionVisibility=()=>{this.widgetGuidelinesVisible=!1,n.textContent=this.widgetGuidelinesVisible?"-":"+",Object.assign(r.style,this.widgetGuidelinesVisible?v:{display:"none"}),r.removeChild(o)},o.onclick=()=>{var e,t;h(),this.test&&(t=(e=this.signalManager?.getDurations())?e.tasks.findIndex(e=>this.test&&e.taskId===this.test.tasks[0].task_id):null,e&&-1===t&&(e.tasks.push({taskId:this.test.tasks[0].task_id,started:this.app.timestamp()}),this.signalManager?.setDurations(e)),this.signalManager?.signalTask(this.test.tasks[0].task_id,"begin")),this.showTaskSection(),r.removeChild(o)},t}createTasksSection(t){this.container.style.fontFamily='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',Object.assign(this.container.style,bt);var e=b("div","task_section_or",y),s=b("div","description_t_title_or",Tt),i=b("div","title",{fontSize:"13px",fontWeight:"500",lineHeight:"auto"},"Tasks");let n=b("div","icon",Et,"-"),r=b("div","content",v),a=b("div","pagination",Nt);var o=b("div","taskCard",Mt);let h=b("div","taskText",Dt),l=b("div","taskDescription",At);var c=b("div","taskButtons",jt),d=b("div","taskText",Dt);d.textContent="Your answer";let u=b("textarea","taskDescription",{resize:"vertical"}),p=b("div","inputArea",Mt);p.append(d,u);var d=b("div","closePanelButton",Lt,"Collapse Panel"),f=b("div","nextButton",Ft,"Done, Next");s.append(i,n),o.append(h,l),c.append(d,f),r.append(a,o,p,c),e.append(s,r);let g=()=>{var e=t[this.currentTaskIndex];h.textContent=e.title,l.textContent=e.description,p.style.display=e.allow_typing?"flex":"none"};t.forEach((e,t)=>{var s=b("span","or_task_"+t,{outline:"1px solid #efefef",fontSize:"13px",height:"24px",width:"24px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"6.25em"},(t+1).toString());s.id="or_task_"+t,a.append(s)});this.hideTaskSection=()=>(n.textContent="+",Object.assign(r.style,{display:"none"}),this.widgetTasksVisible=!1),this.showTaskSection=()=>(n.textContent="-",Object.assign(r.style,v),this.widgetTasksVisible=!0);let m=()=>{var e=document.getElementById("or_task_"+this.currentTaskIndex);e&&Object.assign(e.style,Ot);for(let e=0;e<this.currentTaskIndex;e++){var t=document.getElementById("or_task_"+e);t&&Object.assign(t.style,Rt)}};return s.onclick=()=>{this.widgetTasksVisible=!this.widgetTasksVisible,n.textContent=this.widgetTasksVisible?"-":"+",Object.assign(r.style,this.widgetTasksVisible?v:{display:"none"})},d.onclick=this.collapseWidget,f.onclick=()=>{var e=t[this.currentTaskIndex].allow_typing?u.value:void 0;u.value="",this.signalManager?.signalTask(t[this.currentTaskIndex].task_id,"done",e),this.currentTaskIndex<t.length-1?(this.currentTaskIndex++,g(),(e=this.signalManager?.getDurations())&&-1===e.tasks.findIndex(e=>e.taskId===t[this.currentTaskIndex].task_id)&&(e.tasks.push({taskId:t[this.currentTaskIndex].task_id,started:this.app.timestamp()}),this.signalManager?.setDurations(e)),this.signalManager?.signalTask(t[this.currentTaskIndex].task_id,"begin"),m()):this.showEndSection(),this.app.localStorage.setItem("or_uxt_task_index",this.currentTaskIndex.toString())},setTimeout(()=>{var e=document.getElementById("or_task_0");e&&Object.assign(e.style,Ot),g(),m()},1),e}showEndSection(){let t=!0;this.signalManager?.signalTest("done");var e=b("div","end_section_or",xt),s=b("div","end_title_or",{fontSize:"1.25rem",fontWeight:"500"},"Thank you! 👍"),i=b("div","end_description_or",{},this.test?.conclusion??"Thank you for participating in our usability test. Your feedback has been captured and will be used to enhance our website. \n\nWe appreciate your time and valuable input.");let n=b("div","end_button_or",Ct,"Submitting Feedback"),r=Ut();n.appendChild(r),this.test?.reqMic||this.test?.reqCamera?this.userRecorder.sendToAPI().then(()=>{n.removeChild(r),n.textContent="End Session",t=!1}).catch(e=>{console.error(e),n.removeChild(r),n.textContent="End Session",t=!1}):(n.removeChild(r),n.textContent="End Session",t=!1),this.taskSection&&this.container.removeChild(this.taskSection),this.descriptionSection&&this.container.removeChild(this.descriptionSection),this.stopButton&&this.stopButtonContainer&&this.container.removeChild(this.stopButtonContainer),n.onclick=()=>{t||(window.close(),document.body.removeChild(this.bg))},e.append(s,i,n),this.endSection=e,this.container.append(e)}}function Xt(e){return e&&null!=e.nodeType}function Jt(e){return"http://www.w3.org/2000/svg"===e.namespaceURI||"svg"===e.localName}function u(e){return e.nodeType===Node.ELEMENT_NODE}function Kt(e){return e.nodeType===Node.TEXT_NODE}function Gt(e){return e.nodeType===Node.DOCUMENT_NODE}function Qt(e){return e.nodeType===Node.DOCUMENT_NODE||e.nodeType===Node.DOCUMENT_FRAGMENT_NODE}function x(e,t){return e.localName===t}class Yt{constructor(e,t){this.app=e,this.options=t,this.snapshots={},this.intervals=[],this.restartTracking=()=>{this.clear(),this.app.nodes.scanTree(this.captureCanvas)},this.captureCanvas=e=>{let t=this.app.nodes.getID(e);t&&x(e,"canvas")&&(this.app.sanitizer.isObscured(t)||this.app.sanitizer.isHidden(t)||!x(e,"canvas")||this.snapshots[t]||new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&(e.target?this.snapshots[t]&&this.snapshots[t].createdAt?this.snapshots[t].paused=!1:this.recordCanvas(e.target,t):this.snapshots[t]&&(this.snapshots[t].paused=!0))})}).observe(e))},this.recordCanvas=(s,a)=>{var e=this.app.timestamp(),e=(this.snapshots[a]={images:[],createdAt:e,paused:!1,dummy:document.createElement("canvas")},[119,a.toString(),e]);this.app.send(e);let i=e=>{var[e,t="medium",s,i=!1,n,r]=[e,this.options.quality,this.snapshots[a].dummy,this.options.fixedScaling,this.fileExt,e=>{if(e){if(!this.snapshots[a])return this.app.debug.warn("Canvas not present in snapshots after capture:",this.snapshots,a);this.snapshots[a].images.push({id:this.app.timestamp(),data:e}),9<this.snapshots[a].images.length&&(this.sendSnaps(this.snapshots[a].images,a,this.snapshots[a].createdAt),this.snapshots[a].images=[])}}];if(n="image/"+n,i){i=window.devicePixelRatio||1,i=(s.width=e.width/i,s.height=e.height/i,s.getContext("2d"));if(!i)return;i.clearRect(0,0,s.width,s.height),i.drawImage(e,0,0,s.width,s.height),s.toBlob(r,n,Zt[t])}else e.toBlob(r,n,Zt[t])},n=setInterval(()=>{var e=this.app.nodes.getID(s);let t=e?this.app.nodes.getNode(e):void 0;this.snapshots[a]?t&&x(t,"canvas")&&t===s?this.snapshots[a].paused||(this.options.useAnimationFrame?requestAnimationFrame(()=>{i(t)}):i(t)):(this.app.debug.log("Canvas element not in sync",t,s),clearInterval(n)):(this.app.debug.log("Canvas is not present in {snapshots}"),clearInterval(n))},this.interval);this.intervals.push(n)},this.fileExt=t.fileExt??"webp",this.interval=1e3/t.fps}startTracking(){setTimeout(()=>{this.app.nodes.scanTree(this.captureCanvas),this.app.nodes.attachNodeCallback(this.captureCanvas)},250)}sendSnaps(e,n,r){if(0!==Object.keys(this.snapshots).length){let i=new FormData,t=(e.forEach(e=>{var t,s=e.data;s&&(i.append("snapshot",s,r+`_${n}_${e.id}.`+this.fileExt),this.options.isDebug)&&(t=s,e=`${r}_${n}_${e.id}.`+this.fileExt,t=URL.createObjectURL(s),(s=document.createElement("a")).href=t,s.download=e,s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s))}),()=>{this.app.debug.log("Restarting tracker; token expired"),this.app.stop(!1),setTimeout(()=>{this.app.start({},!0)},250)});fetch(this.app.options.ingestPoint+"/v1/web/images",{method:"POST",headers:{Authorization:"Bearer "+(this.app.session.getSessionToken()??"")},body:i}).then(e=>401!==e.status||t()).catch(e=>{this.app.debug.error("error saving canvas",e)})}}clear(){this.intervals.forEach(e=>clearInterval(e)),this.snapshots={}}}let Zt={low:.35,medium:.55,high:.8},o={Verbose:5,Log:4,Warnings:3,Errors:2,Silent:0};class es{constructor(e=o.Silent){this.shouldLog=e=>this.level>=e,this.info=(...e)=>{this.shouldLog(o.Verbose)&&console.info(...e)},this.log=(...e)=>{this.shouldLog(o.Log)&&console.log(...e)},this.warn=(...e)=>{this.shouldLog(o.Warnings)&&console.warn(...e)},this.error=(...e)=>{this.shouldLog(o.Errors)&&console.error(...e)},this.level=e}}function ts(e,i,n){let r=e.entries();!function e(){var t=[];let s=r.next();for(;!s.done&&t.length<i;)t.push(s.value),s=r.next();0<t.length&&(t.forEach(([,e])=>{e&&n(e)}),setTimeout(e,50))}()}let ss={interval:3e4,batchSize:2500,enabled:!0};class is{constructor(e,t,s){this.nodes=e,this.unregisterNode=t,this.start=()=>{this.options.enabled&&(this.stop(),this.interval=setInterval(()=>{ts(this.nodes,this.options.batchSize,e=>{(e=>{try{var t,s,i;return e.isConnected?(s=(t=e.nodeType===Node.DOCUMENT_NODE)?e.defaultView:e.ownerDocument?.defaultView,i=t?e:e.ownerDocument,s?s.closed?[!1,"window closed"]:i?.documentElement.isConnected?[!0,"ok"]:[!1,"documentElement not connected"]:[!1,"no window"]):[!1,"not connected"]}catch(e){return[!1,e]}})(e)[0]||this.unregisterNode(e)})},this.options.interval))},this.stop=()=>{this.interval&&clearInterval(this.interval)},this.options={...ss,...s}}}class ns{constructor(e){this.nodes=new Map,this.totalNodeAmount=0,this.nodeCallbacks=[],this.elementListeners=new Map,this.nextNodeId=0,this.attachNodeCallback=e=>this.nodeCallbacks.push(e),this.scanTree=t=>{this.nodes.forEach(e=>e?t(e):void 0)},this.attachNodeListener=(t,s,i,n=!0)=>{var r=this.getID(t);if(void 0!==r){We(t,s,i,n,this.forceNgOff);let e=this.elementListeners.get(r);void 0===e&&(e=[],this.elementListeners.set(r,e)),e.push([s,i,n])}},this.unregisterNode=t=>{var e,s=t[this.node_id];return void 0!==s&&(t[this.node_id]=void 0,delete t[this.node_id],this.nodes.delete(s),void 0!==(e=this.elementListeners.get(s))&&(this.elementListeners.delete(s),e.forEach(e=>$e(t,e[0],e[1],e[2],this.forceNgOff))),this.totalNodeAmount--),s},this.node_id=e.node_id,this.forceNgOff=e.forceNgOff,this.maintainer=new is(this.nodes,this.unregisterNode,e.maintainer),this.maintainer.start()}syntheticMode(e){e*=99999999;if(Number.MAX_SAFE_INTEGER<e)throw new Error("Placeholder id overflow");this.nextNodeId=e}registerNode(e){let t=e[this.node_id];var s=void 0===t;return s&&(t=this.nextNodeId,this.totalNodeAmount++,this.nextNodeId++,this.nodes.set(t,e),e[this.node_id]=t),[t,s]}cleanTree(){for(var[e,t]of this.nodes)t&&!document.contains(t)&&this.unregisterNode(t)}callNodeCallbacks(t,s){this.nodeCallbacks.forEach(e=>e(t,s))}getID(e){if(e)return e[this.node_id]}getNode(e){return this.nodes.get(e)}getNodeCount(){return this.totalNodeAmount}clear(){for(var[e,t]of this.nodes)t&&this.unregisterNode(t);this.nextNodeId=0,this.nodes.clear()}}let rs=99e6;function as(t,e,s,i,n,r,a,o,h){let l=o?null:i();o||r(l,e);i=t.sheet;if(i&&!a)try{var c=u(i);if(c)return void d(c)}catch(e){}function d(e){var t=(t=>{let s=[],i=!1,n=!1,r="",a=0,o="";for(let e=0;e<t.length;e++){var h=t[e],l=t[e+1]||"";n||"/"!==h||"*"!==l?i?"*"===h&&"/"===l&&(i=!1,e++):n||'"'!==h&&"'"!==h?n?(o+=h,h===r&&"\\"!==t[e-1]&&(n=!1)):(o+=h,"{"===h?a++:"}"===h&&0===--a&&(s.push(o.trim()),o="")):(n=!0,r=h,o+=h):(i=!0,e++)}return o.trim()&&s.push(o.trim()),s})(e=e.replace(/\/\*[\s\S]*?\*\//g,""));for(let e=0;e<t.length;e++)n(l,t[e],e,s)}function u(t){try{var s=t.rules||t.cssRules;if(!s)return null;let e=t.href;!e&&t.ownerNode&&t.ownerNode.ownerDocument&&(e=t.ownerNode.ownerDocument.location.href);var i=Array.from(s,s=>{var t=e;if("styleSheet"in s){let t;try{t=u(s.styleSheet)||(e=>{var t=e.cssText;return t.split('"').length<3?t:(t=["@import",`url(${JSON.stringify(e.href)})`],""===e.layerName?t.push("layer"):e.layerName&&t.push(`layer(${e.layerName})`),e.supportsText&&t.push(`supports(${e.supportsText})`),e.media.length&&t.push(e.media.mediaText),t.join(" ")+";")})(s)}catch(e){t=s.cssText}return s.styleSheet.href?p(t,s.styleSheet.href):t}{let e=s.cssText;return"selectorText"in s&&s.selectorText.includes(":")&&(e=(e=>e.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2"))(e)),t?p(e,t):e}}).join("");return i=i.includes(" background-clip: text;")&&!i.includes(" -webkit-background-clip: text;")?i.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;"):i}catch(e){return null}}function p(e,h){if(!e)return"";let l=/^(?:[a-z+]+:)?\/\//i,c=/^www\..*/i,d=/^(data:)([^,]*),(.*)/i;return e.replace(/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,(e,t,s,i,n,r)=>{s=s||n||r,n=t||i||"";if(!s)return e;if(l.test(s)||c.test(s))return`url(${n}${s}${n})`;if(d.test(s))return`url(${n}${s}${n})`;if("/"===s[0])return`url(${n}${(e=>{let t="";return t=(t=-1<e.indexOf("//")?e.split("/").slice(0,3).join("/"):e.split("/")[0]).split("?")[0]})(h)+s}${n})`;var a,o=h.split("/"),r=s.split("/");o.pop();for(a of r)"."!==a&&(".."===a?o.pop():o.push(a));return`url(${n}${o.join("/")}${n})`})}t.href&&fetch(t.href).then(e=>{if(e.ok)return e.text();throw new Error("response status "+e.status)}).then(e=>{o&&h?h(e,rs++):d(e)}).catch(e=>{console.error(`OpenReplay: Failed to fetch CSS from ${t.href}:`,e)})}function os(e){return Qt(e)&&e.adoptedStyleSheets}let hs=15;function ls(){return hs++}let cs=new Map,ds={},c={};function us(e){var t,s;return e.nodeType===Node.COMMENT_NODE||!(Kt(e)||u(e)&&("LINK"===(t=e.tagName.toUpperCase())?(s=e.getAttribute("rel"),e=e.getAttribute("as"),s?.includes("stylesheet")||"style"===e||"font"===e):"SCRIPT"!==t&&"NOSCRIPT"!==t&&"META"!==t&&"TITLE"!==t&&"BASE"!==t))}function ps(e){return Qt(e)||!us(e)}(t=d=d||{})[t.New=0]="New",t[t.Removed=1]="Removed",t[t.Changed=2]="Changed";class fs{constructor(e,t=!1,s={}){this.app=e,this.isTopContext=t,this.commited=[],this.recents=new Map,this.indexes=[],this.attributesMap=new Map,this.textSet=new Set,this.disableSprites=!1,this.inlineRemoteCss=!1,this.inlinerOptions=void 0,this.domParser=new DOMParser,this.disableSprites=Boolean(s.disableSprites),this.inlineRemoteCss=Boolean(s.inlineRemoteCss),this.inlinerOptions=s.inlinerOptions,this.observer=qe(this.app.safe(e=>{for(var t of e){var s=t.target,i=t.type;if(ps(s))if("childList"===i){for(let e=0;e<t.removedNodes.length;e++)ps(t.removedNodes[e])&&this.bindNode(t.removedNodes[e]);for(let e=0;e<t.addedNodes.length;e++)this.bindTree(t.addedNodes[e])}else{s=this.app.nodes.getID(s);if(void 0!==s)if(this.recents.has(s)||this.recents.set(s,d.Changed),"attributes"===i){var n=t.attributeName;if(null===n)continue;let e=this.attributesMap.get(s);void 0===e&&this.attributesMap.set(s,e=new Set),e.add(n)}else"characterData"===i&&this.textSet.add(s)}}this.commitNodes()}),this.app.options.forceNgOff)}clear(){this.commited.length=0,this.recents.clear(),this.indexes.length=1,this.attributesMap.clear(),this.textSet.clear()}handleIframeSrcChange(t){var s=t.contentDocument;if(s&&void 0!==this.app.nodes.getID(s)){var i=document.createTreeWalker(s,NodeFilter.SHOW_ELEMENT+NodeFilter.SHOW_TEXT,{acceptNode:e=>us(e)||void 0===this.app.nodes.getID(e)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},!1);let e=0;for(var s=this.app.nodes.getNodeCount(),n=t.contentDocument,r=this.app.nodes.unregisterNode.bind(this.app.nodes);i.nextNode();)n.contains(i.currentNode)||(e+=1,r(i.currentNode));t=Math.floor(e/s*100);30<t&&this.app.send(ht(t))}}sendNodeAttribute(s,e,t,i){if(Jt(e))return t.startsWith("xlink:")&&(t=t.substring(6)),null===i&&this.app.send(Je(s,t)),"use"!==e.localName||"href"!==t||this.disableSprites?void("href"===t?(1e5<i.length&&(i=""),this.app.send(Ye(s,t,i,this.app.getBaseHref()))):this.app.attributeSender.sendSetAttribute(s,t,i)):void(async(e,t,s)=>{try{var i,r,a=e.getAttribute("xlink:href")||e.getAttribute("href");if(a){let[n,e]=a.split("#");if(!n&&e)return(i=document.querySelector(a))?(r=`
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="${i.getAttribute("viewBox")||"0 0 24 24"}">
          ${i.innerHTML}
        </svg>
      `,ds[e]=r):void console.warn("Openreplay: Sprite symbol not found in the document.");if(n||e){if(ds[e])return ds[e];let i;c[n]?1===c[n]?await new Promise(e=>{let t=0,s=setInterval(()=>{100<t&&(clearInterval(s),e(!1)),1!==c[n]?(i=c[n],clearInterval(s),e(!0)):t++},100)}):i=c[n]??'<svg xmlns="http://www.w3.org/2000/svg"></svg>':(c[n]=1,o=await(await fetch(n)).text(),i=s.parseFromString(o,"image/svg+xml"),c[n]=i);var o,h,l=i.getElementById(e);return l?"svgtext"===t?(h=`
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="${l.getAttribute("viewBox")||"0 0 24 24"}">
          ${l.innerHTML}
        </svg>
      `,ds[e]=h):void console.debug(`Openreplay: Unknown mode: ${t}. Use "inline" or "dataurl".`):(console.debug("Openreplay: Symbol not found in SVG."),"")}console.warn("Openreplay: Invalid xlink:href or href found on <use>.")}else console.debug("Openreplay: xlink:href or href not found on <use>.")}catch(e){console.error("Openreplay: Error processing <use> element:",e)}})(e,"svgtext",this.domParser).then(e=>{e&&this.app.send([12,s,t,"_$OPENREPLAY_SPRITE$_"+e])}).catch(e=>{console.error("Openreplay: Error parsing <use> element:",e)});if("src"!==t&&"srcset"!==t&&"integrity"!==t&&"crossorigin"!==t&&"autocomplete"!==t&&"on"!==t.substring(0,2)&&("value"!==t||!x(e,"input")||"button"===e.type||"reset"===e.type||"submit"===e.type))if(null===i)this.app.send(Je(s,t));else{if("style"===t||"href"===t&&x(e,"link"))return"rel"in e&&"stylesheet"===e.rel&&this.inlineRemoteCss?void setTimeout(()=>{as(e,s,this.app.getBaseHref(),ls,(e,t,s,i)=>{this.app.send(st(e,t,s,i))},(e,t)=>{this.app.send(nt(e,t))},this.inlinerOptions?.forceFetch,this.inlinerOptions?.forcePlain,(e,t)=>{this.app.send([9,t,s,0]),setTimeout(()=>{this.app.send(Ze(t,e,this.app.getBaseHref()))},10)})},0):void this.app.send(Ye(s,t,i,this.app.getBaseHref()));("href"===t||1e5<i.length)&&(i=""),["alt","placeholder"].includes(t)&&this.app.sanitizer.privateMode&&(i=i.replaceAll(/./g,"*")),this.app.attributeSender.sendSetAttribute(s,t,i)}}sendNodeData(e,t,s){x(t,"style")?this.app.send(Ze(e,s,this.app.getBaseHref())):(s=this.app.sanitizer.sanitize(e,s),this.app.send([14,e,s]))}bindNode(e){var[e,t]=this.app.nodes.registerNode(e);t?this.recents.set(e,d.New):this.recents.get(e)!==d.New&&this.recents.set(e,d.Removed)}bindTree(e){if(ps(e)){this.bindNode(e);for(var t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT+NodeFilter.SHOW_TEXT,{acceptNode:e=>(void 0!==this.app.nodes.getID(e)&&this.app.debug.info("! Node is already bound",e),us(e)||void 0!==this.app.nodes.getID(e)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT)},!1);t.nextNode();)this.bindNode(t.currentNode)}}unbindTree(t){var s=this.app.nodes.unregisterNode(t);if(void 0!==s&&this.recents.get(s)===d.Removed){this.app.send([11,s]);var i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT+NodeFilter.SHOW_TEXT,{acceptNode:e=>us(e)||void 0===this.app.nodes.getID(e)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},!1);let e=0;for(s=this.app.nodes.getNodeCount();i.nextNode();)e+=1,this.app.nodes.unregisterNode(i.currentNode);t=Math.floor(e/s*100);30<t&&this.app.send(ht(t))}}_commitNode(i,n){if(!Qt(n)){var s=n.assignedSlot||n.parentNode;let t;if(!x(n,"html")||!this.isTopContext){if(null===s)return this.unbindTree(n),!1;if(void 0===(t=this.app.nodes.getID(s)))return this.unbindTree(n),!1;if(!this.commitNode(t))return this.unbindTree(n),!1;if(this.app.sanitizer.handleNode(i,t,n),this.app.sanitizer.isHidden(t))return!1}let e=n.previousSibling;for(;null!==e;){var r=this.app.nodes.getID(e);if(void 0!==r){this.commitNode(r),this.indexes[i]=this.indexes[r]+1;break}e=e.previousSibling}null===e&&(this.indexes[i]=0);var a,o=this.recents.get(i),h=o===d.New,l=this.indexes[i];if(void 0===l)throw"commitNode: missing node index";if(h)if(u(n)){let s=n;void 0!==t&&(this.app.sanitizer.isHidden(i)&&(h=s.clientWidth,a=s.clientHeight,(s=n.cloneNode()).style.width=h+"px",s.style.height=a+"px"),"rel"in s&&"stylesheet"===s.rel&&this.inlineRemoteCss?this.app.send([8,i,t,l,"STYLE",!1]):this.app.send([8,i,t,l,s.tagName,Jt(n)]));for(let t=0;t<s.attributes.length;t++){let e=s.attributes[t];this.sendNodeAttribute(i,s,e.nodeName,e.value)}}else Kt(n)&&(this.app.send([9,i,t,l]),this.sendNodeData(i,s,n.data));else{o===d.Removed&&void 0!==t&&this.app.send([10,i,t,l]);let e=this.attributesMap.get(i);if(void 0!==e){if(!u(n))throw"commitNode: node is not an element";for(var c of e)this.sendNodeAttribute(i,n,c,n.getAttribute(c))}if(this.textSet.has(i)){if(!Kt(n))throw"commitNode: node is not a text";this.sendNodeData(i,s,n.data)}}}return!0}commitNode(e){var t,s=this.app.nodes.getNode(e);return!!s&&(void 0!==(t=this.commited[e])?t:this.commited[e]=this._commitNode(e,s))}commitNodes(e=!1){let t;for(var[s,i]of this.recents.entries())this.commitNode(s),i===d.New&&(t=this.app.nodes.getNode(s))&&this.app.nodes.callNodeCallbacks(t,e);this.clear()}observeRoot(e,t,s=e){this.observer.observe(e,{childList:!0,attributes:!0,characterData:!0,subtree:!0,attributeOldValue:!1,characterDataOldValue:!1}),this.bindTree(s),t(this.app.nodes.getID(e)),this.commitNodes(!0)}disconnect(){this.observer.disconnect(),this.clear()}}class gs extends fs{observe(e){var t=e.contentDocument;let s=this.app.nodes.getID(e);t&&void 0!==s&&this.observeRoot(t,e=>{void 0===e?this.app.debug.log("OpenReplay: Iframe document not bound"):(this.docId=e,this.app.send(et(s,e)))})}syntheticObserve(t,e){this.observeRoot(e,e=>{void 0===e?this.app.debug.log("OpenReplay: Iframe document not bound"):this.app.send(et(t,e))})}}class ms extends fs{observe(e){var t=e.shadowRoot;let s=this.app.nodes.getID(e);t&&void 0!==s&&this.observeRoot(t,e=>{void 0===e?this.app.debug.error("OpenReplay: Shadow Root was not bound"):this.app.send(et(s,e))})}}class vs{constructor(){this.states=new WeakMap}calcOffset(s){let e=0,t=0;if(s.parent&&([e,t]=this.calcOffset(s.parent)),!s.offset){let{left:e,top:t}=s.iFrame.getBoundingClientRect();s.offset=[e,t]}let[i,n]=s.offset;return[e+i,t+n]}getDocumentOffset(e){e=this.states.get(e);return e?this.calcOffset(e):[0,0]}observe(e){var t=e.contentDocument;if(!t)return;let s=e.ownerDocument,i={offset:null,iFrame:e,parent:this.states.get(s)||null,clear:()=>{s.removeEventListener("scroll",n),s.defaultView?.removeEventListener("resize",n)}},n=()=>{i.offset=null};s.addEventListener("scroll",n),s.defaultView?.addEventListener("resize",n),this.states.set(t,i)}clear(){this.states=new WeakMap}}(e=n=n||{})[e.Disabled=0]="Disabled",e[e.Inline=1]="Inline",e[e.InlineFetched=2]="InlineFetched",e[e.PlainFetched=3]="PlainFetched";let ys=h?Element.prototype.attachShadow:()=>new ShadowRoot;class bs extends fs{constructor(e){var t=Object.assign({captureIFrames:!0,disableSprites:!1,inlineCss:0},e.options),s={disableSprites:t.disableSprites,...(e=>{switch(e){case n.Inline:return{inlineRemoteCss:!0,inlinerOptions:{forceFetch:!1,forcePlain:!1}};case n.InlineFetched:return{inlineRemoteCss:!0,inlinerOptions:{forceFetch:!0,forcePlain:!1}};case n.PlainFetched:return{inlineRemoteCss:!0,inlinerOptions:{forceFetch:!0,forcePlain:!0}};default:n.Disabled;return{inlineRemoteCss:!1,inlinerOptions:{forceFetch:!1,forcePlain:!1}}}})(t.inlineCss)};super(e.app,!0,s),this.iframeOffsets=new vs,this.contextCallbacks=[],this.contextsSet=new WeakSet,this.iframeObserversArr=[],this.iframeObservers=new WeakMap,this.docObservers=new WeakMap,this.shadowRootObservers=new WeakMap,this.app=e.app,this.options=t,this.app.nodes.attachNodeCallback(e=>{x(e,"iframe")&&(this.options.captureIFrames&&!a(e,"obscured")||a(e,"capture"))&&this.handleIframe(e)}),this.app.nodes.attachNodeCallback(e=>{u(e)&&null!==e.shadowRoot&&this.handleShadowRoot(e.shadowRoot)})}attachContextCallback(e){this.contextCallbacks.push(e)}getDocumentOffset(e){return this.iframeOffsets.getDocumentOffset(e)}handleIframe(i){var e=this.app.safe(()=>setTimeout(()=>{var e=this.app.nodes.getID(i);if(void 0!==e&&(e=>{try{return Boolean(e.contentDocument)}catch(e){}})(i)){let t=i.contentWindow;var s=i.contentDocument;s?s&&this.docObservers.has(s)?this.app.debug.info("doc already observed for",e):(e=new gs(this.app,!1,{}),this.iframeObservers.set(i,e),this.docObservers.set(s,e),this.iframeObserversArr.push(e),e.observe(i),this.iframeOffsets.observe(i),t&&t===t.window&&!this.contextsSet.has(t)&&(this.contextsSet.add(t),this.contextCallbacks.forEach(e=>e(t)))):this.app.debug.warn("no doc for iframe found",i)}},250));i.addEventListener("load",e),e()}handleShadowRoot(e){var t=new ms(this.app);this.shadowRootObservers.set(e,t),t.observe(e.host)}observe(){let t=this;Element.prototype.attachShadow=function(){var e=ys.apply(this,arguments);return t.handleShadowRoot(e),e},this.app.nodes.clear(),this.observeRoot(window.document,()=>{this.app.send([7]),this.app.nodes.callNodeCallbacks(document,!0)},window.document.documentElement)}crossdomainObserve(e,t){let s=this;Element.prototype.attachShadow=function(){var e=ys.apply(this,arguments);return s.handleShadowRoot(e),e},this.app.nodes.clear(),this.app.nodes.syntheticMode(t);t=new gs(this.app);this.iframeObservers.set(window.document,t),t.syntheticObserve(e,window.document)}disconnect(){this.iframeOffsets.clear(),Element.prototype.attachShadow=ys,this.iframeObserversArr.forEach(e=>e.disconnect()),this.iframeObserversArr=[],this.iframeObservers=new WeakMap,this.shadowRootObservers=new WeakMap,this.docObservers=new WeakMap,super.disconnect()}}(t=M=M||{})[t.Plain=0]="Plain",t[t.Obscured=1]="Obscured",t[t.Hidden=2]="Hidden";let Ss=e=>e.trim().replace(/[^\f\n\r\t\v\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff\s]/g,"*");class ws{constructor(e){this.obscured=new Set,this.hidden=new Set,this.app=e.app;var t={obscureTextEmails:!0,obscureTextNumbers:!1,privateMode:!1,domSanitizer:void 0};this.privateMode=e.options?.privateMode??!1,this.options=Object.assign(t,e.options)}handleNode(e,t,s){if(this.options.privateMode){if(u(s)&&!a(s,"unmask"))return this.obscured.add(e);if(Kt(s)&&!a(s.parentNode,"unmask"))return this.obscured.add(e)}(this.obscured.has(t)||u(s)&&(a(s,"masked")||a(s,"obscured")))&&this.obscured.add(e),(this.hidden.has(t)||u(s)&&(a(s,"htmlmasked")||a(s,"hidden")))&&this.hidden.add(e),void 0!==this.options.domSanitizer&&u(s)&&((t=this.options.domSanitizer(s))===M.Obscured&&this.obscured.add(e),t===M.Hidden)&&this.hidden.add(e)}sanitize(e,t){return this.obscured.has(e)?Ss(t):(this.options.obscureTextNumbers&&(t=t.replace(/\d/g,"0")),this.options.obscureTextEmails?t.replace(/^\w+([+.-]\w+)*@\w+([.-]\w+)*\.\w{2,3}$/g,e=>{var[e,t]=e.split("@"),[t,s]=t.split(".");return`${De(e)}@${De(t)}.`+De(s)}):t)}isObscured(e){return this.obscured.has(e)}isHidden(e){return this.hidden.has(e)}getInnerTextSecure(e){var t=this.app.nodes.getID(e);return t?this.sanitize(t,e.innerText):""}clear(){this.obscured.clear(),this.hidden.clear()}}class ks{constructor(e){this.metadata={},this.userID=null,this.callbacks=[],this.timestamp=0,this.getPageNumber=()=>{var e=this.app.sessionStorage.getItem(this.options.session_pageno_key);if(null!=e)return parseInt(e)},this.incPageNo=()=>{let e=this.getPageNumber();return void 0===e?e=0:e++,this.app.sessionStorage.setItem(this.options.session_pageno_key,e.toString()),e},this.app=e.app,this.options=e.options,this.createTabId()}attachUpdateCallback(e){this.callbacks.push(e)}handleUpdate(t){null==t.userID&&delete t.userID,null==t.sessionID&&delete t.sessionID,this.callbacks.forEach(e=>e(t))}assign(e){void 0!==e.userID&&(this.userID=e.userID),void 0!==e.metadata&&Object.entries(e.metadata).forEach(([e,t])=>this.metadata[e]=t),void 0!==e.sessionID&&(this.sessionID=e.sessionID),void 0!==e.timestamp&&(this.timestamp=e.timestamp),void 0!==e.projectID&&(this.projectID=e.projectID),this.handleUpdate(e)}setMetadata(e,t){this.metadata[e]=t,this.handleUpdate({metadata:{[e]:t}})}setUserID(e){this.userID=e,this.handleUpdate({userID:e})}setUserInfo(e){this.userInfo=e}getSessionToken(){return this.token||this.app.sessionStorage.getItem(this.options.session_token_key)||void 0}setSessionToken(e){this.token=e,this.app.sessionStorage.setItem(this.options.session_token_key,e)}applySessionHash(e){var t=decodeURI(e).split("&");let s=e,i="100500";2==t.length&&([i,s]=t),i&&s&&(this.app.sessionStorage.setItem(this.options.session_token_key,s),this.app.sessionStorage.setItem(this.options.session_pageno_key,i))}getSessionHash(){var e=this.getPageNumber(),t=this.getSessionToken();if(void 0!==e&&void 0!==t)return encodeURI(String(e)+"&"+t)}getTabId(){return this.tabId||this.createTabId(),this.tabId}regenerateTabId(){e=12,t=new Uint8Array(e/2);var e,t,s=((s=window.crypto||window.msCrypto)?(s.getRandomValues(t),Array.from(t,He)):Array.from({length:e},()=>He(Math.floor(16*Math.random())))).join("");this.app.sessionStorage.setItem(this.options.session_tabid_key,s),this.tabId=s}createTabId(){var e=this.app.sessionStorage.getItem(this.options.session_tabid_key);e?this.tabId=e:this.regenerateTabId()}getInfo(){return{sessionID:this.sessionID,metadata:this.metadata,userID:this.userID,timestamp:this.timestamp,projectID:this.projectID}}reset(){this.app.sessionStorage.removeItem(this.options.session_token_key),this.metadata={},this.userID=null,this.sessionID=void 0,this.timestamp=0}}class Ts{constructor(e){this.app=e,this.timer=null,this.callbacks=[]}attach(e,t=0,s=!0,i){i&&(e=e.bind(i)),s&&(e=this.app.safe(e)),this.callbacks.unshift(t?((e,t)=>{let s=0;return()=>{s++>=t&&(s=0,e())}})(e,t):e)}start(){null===this.timer&&(this.timer=setInterval(()=>this.callbacks.forEach(e=>{e&&e()}),30))}stop(){null!==this.timer&&(clearInterval(this.timer),this.timer=null)}}let Is="canceled",xs="or_uxt_active",Es="or_buffer_1",z=e=>({reason:e,success:!1}),Cs=e=>({...e,success:!0});function _s(){var e=-1*(new Date).getTimezoneOffset(),t=0<=e?"+":"-",s=Math.floor(Math.abs(e)/60),e=Math.abs(e)%60;return`UTC${t}${String(s).padStart(2,"0")}:`+String(e).padStart(2,"0")}(e=L=L||{})[e.NotActive=0]="NotActive",e[e.Starting=1]="Starting",e[e.Active=2]="Active",e[e.ColdStart=3]="ColdStart";let l={ask:"never-gonna-give-you-up",resp:"never-gonna-let-you-down",reg:"never-gonna-run-around-and-desert-you",iframeSignal:"tracker inside a child iframe",iframeId:"getting node id for child iframe",iframeBatch:"batch of messages from an iframe window",parentAlive:"signal that parent is live",killIframe:"stop tracker inside frame",startIframe:"start tracker inside frame",polling:"hello-how-are-you-im-under-the-water-please-help-me"};class Ns{constructor(e,t,s,i,n){this.signalError=i,this.insideIframe=n,this.messages=[],this.bufferedMessages1=[],this.bufferedMessages2=[],this.startCallbacks=[],this.stopCallbacks=[],this.commitCallbacks=[],this.activityState=L.NotActive,this.version="16.4.1",this.socketMode=!1,this.compressionThreshold=24e3,this.bc=null,this.canvasRecorder=null,this.conditionsManager=null,this.canStart=!1,this.rootId=null,this.pageFrames=[],this.frameOderNumber=0,this.features={"feature-flags":!0,"usability-test":!0},this.emptyBatchCounter=0,this.parentActive=!1,this.checkStatus=()=>this.parentActive,this.parentCrossDomainFrameListener=e=>{var t=e.data;if(t&&e.source!==window){if(t.line===l.startIframe){if(this.active())return;try{this.allowAppStart(),this.start()}catch(e){console.error("children frame restart failed:",e)}}t.line===l.parentAlive&&(this.parentActive=!0),t.line===l.iframeId&&(this.parentActive=!0,this.rootId=t.id,this.session.setSessionToken(t.token),this.frameOderNumber=t.frameOrderNumber,this.debug.log("starting iframe tracking",t),this.allowAppStart()),t.line===l.killIframe&&this.active()&&this.stop()}},this.trackedFrames=[],this.crossDomainIframeListener=p=>{if(this.active()&&p.source!==window){let n=p.data;var e;if(n)if(n.line===l.iframeSignal&&(p.source?.postMessage({ping:!0,line:l.parentAlive},"*"),(async()=>{if(null===p.source)return console.error("Couldnt connect to event.source for child iframe tracking");var e=await this.checkNodeId(p.source);if(e)try{this.trackedFrames.includes(n.context)?this.debug.log("Trying to observe already added iframe; ignore if its a restart"):this.trackedFrames.push(n.context),await this.waitStarted();var t=this.session.getSessionToken(),s=this.trackedFrames.findIndex(e=>e===n.context)+1,i=(0===s&&this.debug.error("Couldnt get order number for iframe",n.context,this.trackedFrames),{line:l.iframeId,id:e,token:t,frameOrderNumber:s});this.debug.log("Got child frame signal; nodeId",e,p.source,i),p.source?.postMessage(i,"*")}catch(e){console.error(e)}else this.debug.log("Couldnt get node id for iframe",p.source)})()),n.line===l.iframeBatch&&(e=n.messages.map(u=>{if(20===u[0]){let r=u;return this.pageFrames.forEach(e=>{var t,s,i,n;e.contentWindow===p.source&&([t,s,i]=u,{left:e,top:n}=e.getBoundingClientRect(),r=[t,s+e,i+n])}),r}if(68!==u[0])return u;{let d=u;return this.pageFrames.forEach(e=>{var t,s,i,n,r,a,o,h,l,c;e.contentWindow===p.source&&([t,s,i,n,r,l,a]=u,{left:e,top:o,width:c,height:h}=e.getBoundingClientRect(),l=(l/100*c+e)/document.documentElement.scrollWidth,c=(a/100*h+o)/document.documentElement.scrollHeight,d=[t,s,i,n,r,Math.round(1e3*l)/10,Math.round(1e3*c)/10])}),d}}),this.messages.push(...e)),n.line===l.polling&&this.pollingQueue.order.length){let t=this.pollingQueue.order[0];t&&0===this.pollingQueue[t].length?this.pollingQueue.order=this.pollingQueue.order.filter(e=>e!==t):this.pollingQueue[t].includes(n.context)&&(this.pollingQueue[t]=this.pollingQueue[t].filter(e=>e!==n.context),p.source?.postMessage({line:t},"*"),0===this.pollingQueue[t].length)&&this.pollingQueue.order.shift()}}},this.pollingQueue={order:[]},this.addCommand=e=>{this.pollingQueue.order.push(e),this.pollingQueue[e]=[...this.trackedFrames]},this.bootChildrenFrames=async()=>{await this.waitStarted(),this.addCommand(l.startIframe)},this.killChildrenFrames=()=>{this.addCommand(l.killIframe)},this.signalIframeTracker=()=>{let e=this.session.getTabId();window.parent.postMessage({line:l.iframeSignal,source:e,context:this.contextId},this.options.crossdomain?.parentDomain??"*");let t=0,s=250,i=0,n=!1,r=()=>{n||this.checkStatus()?n=!0:(window.parent.postMessage({line:l.iframeSignal,source:e,context:this.contextId},this.options.crossdomain?.parentDomain??"*"),this.debug.info("Trying to signal to parent, attempt:",t+1),t++)};for(let e=0;e<10;e++){if(this.checkStatus()){n=!0;break}i+=s,setTimeout(()=>{r()},i),s*=1.5}},this.startTimeout=null,this.send=(e,t=!1)=>{this.activityState!==L.NotActive&&(this.activityState===L.ColdStart?(this.bufferedMessages1.push(e),this.singleBuffer||this.bufferedMessages2.push(e),this.conditionsManager?.processMessage(e)):this.messages.push(e),this.activityState===L.Active)&&t&&this.commit()},this.coldStartCommitN=0,this.delay=0,this.attachStartCallback=(e,t=!1)=>{t&&(e=this.safe(e)),this.startCallbacks.push(e)},this.attachStopCallback=(e,t=!1)=>{t&&(e=this.safe(e)),this.stopCallbacks.push(e)},this.attachEventListener=(e,t,s,i=!0,n=!0)=>{i&&(s=this.safe(s));this.attachStartCallback(()=>e?We(e,t,s,n,this.options.forceNgOff):null,i),this.attachStopCallback(()=>e?$e(e,t,s,n,this.options.forceNgOff):null,i)},this.coldInterval=null,this.orderNumber=0,this.coldStartTs=0,this.singleBuffer=!1,this.onSessionSent=()=>{},this.prevOpts={},this.restartCanvasTracking=()=>{this.canvasRecorder?.restartTracking()},this.flushBuffer=async n=>new Promise((s,i)=>{if(0===n.length)s(null);else{let t=1;for(;t<n.length&&0!==n[t][0];)t++;Xe(()=>{try{var e=n.splice(0,t);this.postToWorker(e.map(e=>[...e])),s(null)}catch(e){this._debug("flushBuffer",e),i(new Error("flushBuffer failed"))}})}}),this.onUxtCb=[],this.contextId=Math.random().toString(36).slice(2),this.projectKey=e,this.networkOptions=s.network;i={revID:"",node_id:"__openreplay_id",session_token_key:"__openreplay_token",session_pageno_key:"__openreplay_pageno",session_reset_key:"__openreplay_reset",session_tabid_key:"__openreplay_tabid",local_uuid_key:"__openreplay_uuid",ingestPoint:"https://api.openreplay.com/ingest",resourceBaseHref:null,__is_snippet:!1,__debug_report_edp:null,__debug__:o.Silent,localStorage:null,sessionStorage:null,forceSingleTab:!1,assistSocketHost:"",captureIFrames:!0,obscureTextEmails:!1,obscureTextNumbers:!1,disableStringDict:!1,crossdomain:{parentDomain:"*"},canvas:{disableCanvas:!1,fixedCanvasScaling:!1,__save_canvas_locally:!1,useAnimationFrame:!1},forceNgOff:!1,inlineCss:0,disableSprites:!1};this.options=function e(t,s){var i,n,r,a={...t};for(i in s)s.hasOwnProperty(i)&&(n=s[i],r=t[i],"object"!=typeof n||Array.isArray(n)||null===n?a[i]=n:a[i]=e(r||{},n));return a}(i,s),!this.insideIframe&&!this.options.forceSingleTab&&globalThis&&"BroadcastChannel"in globalThis?(n=location.hostname.split(".").slice(-2).join("_"),this.bc=new BroadcastChannel("rick_"+n)):this.options.forceSingleTab&&this.allowAppStart(),this.revID=this.options.revID,this.localStorage=this.options.localStorage??window.localStorage,this.sessionStorage=this.options.sessionStorage??window.sessionStorage,this.sanitizer=new ws({app:this,options:s}),this.nodes=new ns({node_id:this.options.node_id,forceNgOff:Boolean(s.forceNgOff),maintainer:this.options.nodes?.maintainer}),this.observer=new bs({app:this,options:s}),this.ticker=new Ts(this),this.ticker.attach(()=>this.commit()),this.debug=new es(this.options.__debug__),this.session=new ks({app:this,options:this.options}),this.attributeSender=new Ee({app:this,isDictDisabled:Boolean(this.options.disableStringDict||this.options.crossdomain?.enabled)}),this.featureFlags=new Ne(this),this.tagWatcher=new mt({sessionStorage:this.sessionStorage,errLog:this.debug.error,onTag:e=>this.send([120,e])}),this.session.attachUpdateCallback(({userID:e,metadata:t})=>{null!=e&&this.send([28,e]),null!=t&&Object.entries(t).forEach(([e,t])=>this.send(Ke(e,t)))}),null!=t&&this.session.applySessionHash(t);let r=this.session.getTabId();this.insideIframe?(window.addEventListener("message",this.parentCrossDomainFrameListener),setInterval(()=>{document.hidden||window.parent.postMessage({line:l.polling,context:this.contextId},s.crossdomain?.parentDomain??"*")},250)):(this.initWorker(),window.addEventListener("message",this.crossDomainIframeListener)),null!==this.bc&&(this.bc.postMessage({line:l.ask,source:r,context:this.contextId}),this.startTimeout=setTimeout(()=>{this.allowAppStart()},250),this.bc.onmessage=e=>{var t,s;e.data.context!==this.contextId&&(this.debug.log(e),e.data.line===l.resp&&(t=e.data.token,this.session.setSessionToken(t),this.allowAppStart()),e.data.line===l.reg&&(t=e.data.token,this.session.regenerateTabId(),this.session.setSessionToken(t),this.allowAppStart()),e.data.line===l.ask)&&(s=this.session.getSessionToken())&&this.bc&&this.bc.postMessage({line:e.data.source===r?l.reg:l.resp,token:s,source:r,context:this.contextId})})}allowAppStart(){this.canStart=!0,this.startTimeout&&(clearTimeout(this.startTimeout),this.startTimeout=null)}async checkNodeId(t){let s;var e;if((s=0<this.pageFrames.length?this.pageFrames.find(e=>e.contentWindow===t):s)&&this.pageFrames.length||(e=Array.from(document.querySelectorAll("iframe")),this.pageFrames=e,s=e.find(e=>e.contentWindow===t)),s){let e=0;for(;e<100;){var i=s[this.options.node_id];if(void 0!==i)return e=100,i;e++,await(t=>new Promise(e=>setTimeout(e,t)))(100)}}return null}initWorker(){try{this.worker=new Worker(URL.createObjectURL(new Blob(['!function(){"use strict";class t{constructor(t,s,i,e=10,n=250,h,r){this.onUnauthorised=s,this.onFailure=i,this.MAX_ATTEMPTS_COUNT=e,this.ATTEMPT_TIMEOUT=n,this.onCompress=h,this.pageNo=r,this.attemptsCount=0,this.busy=!1,this.queue=[],this.token=null,this.lastBatchNum=0,this.ingestURL=t+"/v1/web/i",this.isCompressing=void 0!==h}getQueueStatus(){return 0===this.queue.length&&!this.busy}authorise(t){this.token=t,this.busy||this.sendNext()}push(t){if(this.busy||!this.token)this.queue.push(t);else if(this.busy=!0,this.isCompressing&&this.onCompress)this.onCompress(t);else{const s=++this.lastBatchNum;this.sendBatch(t,!1,s)}}sendNext(){const t=this.queue.shift();if(t)if(this.busy=!0,this.isCompressing&&this.onCompress)this.onCompress(t);else{const s=++this.lastBatchNum;this.sendBatch(t,!1,s)}else this.busy=!1}retry(t,s,i){this.attemptsCount>=this.MAX_ATTEMPTS_COUNT?this.onFailure(`Failed to send batch after ${this.attemptsCount} attempts.`):(this.attemptsCount++,setTimeout((()=>this.sendBatch(t,s,i)),this.ATTEMPT_TIMEOUT*this.attemptsCount))}sendBatch(t,s,i){var e;const n=null==i?void 0:i.toString().replace(/^([^_]+)_([^_]+).*/,"$1_$2_$3");this.busy=!0;const h={Authorization:`Bearer ${this.token}`};s&&(h["Content-Encoding"]="gzip"),null!==this.token?fetch(`${this.ingestURL}?batch=${null!==(e=this.pageNo)&&void 0!==e?e:"noPageNum"}_${null!=n?n:"noBatchNum"}`,{body:t,method:"POST",headers:h,keepalive:t.length<65536}).then((e=>{if(401===e.status)return this.busy=!1,void this.onUnauthorised();e.status>=400?this.retry(t,s,`${null!=i?i:"noBatchNum"}_network:${e.status}`):(this.attemptsCount=0,this.sendNext())})).catch((e=>{console.warn("OpenReplay:",e),this.retry(t,s,`${null!=i?i:"noBatchNum"}_reject:${e.message}`)})):setTimeout((()=>{this.sendBatch(t,s,`${null!=i?i:"noBatchNum"}_newToken`)}),500)}sendCompressed(t){const s=++this.lastBatchNum;this.sendBatch(t,!0,s)}sendUncompressed(t){const s=++this.lastBatchNum;this.sendBatch(t,!1,s)}clean(){this.sendNext(),setTimeout((()=>{this.token=null,this.queue.length=0}),10)}}const s="function"==typeof TextEncoder?new TextEncoder:{encode(t){const s=t.length,i=new Uint8Array(3*s);let e=-1;for(let n=0,h=0,r=0;r!==s;){if(n=t.charCodeAt(r),r+=1,n>=55296&&n<=56319){if(r===s){i[e+=1]=239,i[e+=1]=191,i[e+=1]=189;break}if(h=t.charCodeAt(r),!(h>=56320&&h<=57343)){i[e+=1]=239,i[e+=1]=191,i[e+=1]=189;continue}if(n=1024*(n-55296)+h-56320+65536,r+=1,n>65535){i[e+=1]=240|n>>>18,i[e+=1]=128|n>>>12&63,i[e+=1]=128|n>>>6&63,i[e+=1]=128|63&n;continue}}n<=127?i[e+=1]=0|n:n<=2047?(i[e+=1]=192|n>>>6,i[e+=1]=128|63&n):(i[e+=1]=224|n>>>12,i[e+=1]=128|n>>>6&63,i[e+=1]=128|63&n)}return i.subarray(0,e+1)}};class i{constructor(t){this.size=t,this.offset=0,this.checkpointOffset=0,this.data=new Uint8Array(t)}getCurrentOffset(){return this.offset}checkpoint(){this.checkpointOffset=this.offset}get isEmpty(){return 0===this.offset}skip(t){return this.offset+=t,this.offset<=this.size}set(t,s){this.data.set(t,s)}boolean(t){return this.data[this.offset++]=+t,this.offset<=this.size}uint(t){for((t<0||t>Number.MAX_SAFE_INTEGER)&&(t=0);t>=128;)this.data[this.offset++]=t%256|128,t=Math.floor(t/128);return this.data[this.offset++]=t,this.offset<=this.size}int(t){return t=Math.round(t),this.uint(t>=0?2*t:-2*t-1)}string(t){const i=s.encode(t),e=i.byteLength;return!(!this.uint(e)||this.offset+e>this.size)&&(this.data.set(i,this.offset),this.offset+=e,!0)}reset(){this.offset=0,this.checkpointOffset=0}flush(){const t=this.data.slice(0,this.checkpointOffset);return this.reset(),t}}class e extends i{encode(t){switch(t[0]){case 0:case 11:case 114:case 115:return this.uint(t[1]);case 4:case 44:case 47:return this.string(t[1])&&this.string(t[2])&&this.uint(t[3]);case 5:case 20:case 38:case 70:case 75:case 76:case 77:case 82:return this.uint(t[1])&&this.uint(t[2]);case 6:return this.int(t[1])&&this.int(t[2]);case 7:return!0;case 8:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.string(t[4])&&this.boolean(t[5]);case 9:case 10:case 24:case 35:case 51:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3]);case 12:case 52:case 61:case 71:return this.uint(t[1])&&this.string(t[2])&&this.string(t[3]);case 13:case 14:case 17:case 34:case 50:case 54:return this.uint(t[1])&&this.string(t[2]);case 16:return this.uint(t[1])&&this.int(t[2])&&this.int(t[3]);case 18:return this.uint(t[1])&&this.string(t[2])&&this.int(t[3]);case 19:return this.uint(t[1])&&this.boolean(t[2]);case 21:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4])&&this.string(t[5])&&this.uint(t[6])&&this.uint(t[7])&&this.uint(t[8]);case 22:case 27:case 30:case 41:case 45:case 46:case 43:case 63:case 64:case 79:case 124:return this.string(t[1])&&this.string(t[2]);case 23:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.uint(t[4])&&this.uint(t[5])&&this.uint(t[6])&&this.uint(t[7])&&this.uint(t[8])&&this.uint(t[9]);case 28:case 29:case 42:case 117:case 118:return this.string(t[1]);case 37:return this.uint(t[1])&&this.string(t[2])&&this.uint(t[3]);case 39:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4])&&this.uint(t[5])&&this.uint(t[6])&&this.uint(t[7]);case 40:return this.string(t[1])&&this.uint(t[2])&&this.string(t[3])&&this.string(t[4]);case 48:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4])&&this.int(t[5]);case 49:return this.int(t[1])&&this.int(t[2])&&this.uint(t[3])&&this.uint(t[4]);case 53:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.uint(t[4])&&this.uint(t[5])&&this.uint(t[6])&&this.string(t[7])&&this.string(t[8]);case 55:return this.boolean(t[1]);case 57:case 60:return this.uint(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4]);case 58:case 120:return this.int(t[1]);case 59:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.uint(t[4])&&this.string(t[5])&&this.string(t[6])&&this.string(t[7]);case 67:case 73:return this.uint(t[1])&&this.string(t[2])&&this.uint(t[3])&&this.string(t[4]);case 68:return this.uint(t[1])&&this.uint(t[2])&&this.string(t[3])&&this.string(t[4])&&this.uint(t[5])&&this.uint(t[6]);case 69:return this.uint(t[1])&&this.uint(t[2])&&this.string(t[3])&&this.string(t[4]);case 78:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4]);case 81:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.int(t[4])&&this.string(t[5]);case 83:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4])&&this.string(t[5])&&this.uint(t[6])&&this.uint(t[7])&&this.uint(t[8])&&this.uint(t[9]);case 84:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.uint(t[4])&&this.string(t[5])&&this.string(t[6]);case 112:return this.uint(t[1])&&this.string(t[2])&&this.boolean(t[3])&&this.string(t[4])&&this.int(t[5])&&this.int(t[6]);case 113:return this.uint(t[1])&&this.uint(t[2])&&this.string(t[3]);case 116:return this.uint(t[1])&&this.uint(t[2])&&this.uint(t[3])&&this.uint(t[4])&&this.uint(t[5])&&this.uint(t[6])&&this.string(t[7])&&this.string(t[8])&&this.uint(t[9])&&this.boolean(t[10]);case 119:return this.string(t[1])&&this.uint(t[2]);case 121:return this.string(t[1])&&this.string(t[2])&&this.uint(t[3])&&this.uint(t[4]);case 122:return this.string(t[1])&&this.string(t[2])&&this.uint(t[3])&&this.string(t[4]);case 123:return this.string(t[1])&&this.string(t[2])&&this.string(t[3])&&this.string(t[4])&&this.uint(t[5])}}}class n{constructor(t,s,i,n,h,r){this.pageNo=t,this.timestamp=s,this.url=i,this.onBatch=n,this.tabId=h,this.onOfflineEnd=r,this.nextIndex=0,this.beaconSize=2e5,this.encoder=new e(this.beaconSize),this.sizeBuffer=new Uint8Array(3),this.isEmpty=!0,this.checkpoints=[],this.beaconSizeLimit=1e6,this.prepare()}writeType(t){return this.encoder.uint(t[0])}writeFields(t){return this.encoder.encode(t)}writeSizeAt(t,s){for(let s=0;s<3;s++)this.sizeBuffer[s]=t>>8*s;this.encoder.set(this.sizeBuffer,s)}prepare(){if(!this.encoder.isEmpty)return;this.checkpoints.length=0;const t=[81,1,this.pageNo,this.nextIndex,this.timestamp,this.url],s=[0,this.timestamp],i=[118,this.tabId];this.writeType(t),this.writeFields(t),this.writeWithSize(s),this.writeWithSize(i),this.isEmpty=!0}writeWithSize(t){const s=this.encoder;if(!this.writeType(t)||!s.skip(3))return!1;const i=s.getCurrentOffset(),e=this.writeFields(t);if(e){const e=s.getCurrentOffset()-i;if(e>16777215)return console.warn("OpenReplay: max message size overflow."),!1;this.writeSizeAt(e,i-3),s.checkpoint(),this.checkpoints.push(s.getCurrentOffset()),this.isEmpty=this.isEmpty&&0===t[0],this.nextIndex++}return e}setBeaconSizeLimit(t){this.beaconSizeLimit=t}writeMessage(t){if("q_end"===t[0])return this.finaliseBatch(),this.onOfflineEnd();0===t[0]&&(this.timestamp=t[1]),122===t[0]&&(this.url=t[1]),this.writeWithSize(t)||(this.finaliseBatch(),this.writeWithSize(t)||(this.encoder=new e(this.beaconSizeLimit),this.prepare(),this.writeWithSize(t)?this.finaliseBatch():console.warn("OpenReplay: beacon size overflow. Skipping large message.",t,this),this.encoder=new e(this.beaconSize),this.prepare()))}finaliseBatch(t=!1){if(this.isEmpty)return;const s=this.encoder.flush();this.onBatch(s,t),this.prepare()}clean(){this.encoder.reset(),this.checkpoints.length=0}}var h;!function(t){t[t.NotActive=0]="NotActive",t[t.Starting=1]="Starting",t[t.Stopping=2]="Stopping",t[t.Active=3]="Active",t[t.Stopped=4]="Stopped"}(h||(h={}));let r=null,a=null,u=h.NotActive;function o(t){a&&a.finaliseBatch(t)}function c(){return new Promise((t=>{u=h.Stopping,null!==p&&(clearInterval(p),p=null),a&&(a.clean(),a=null),r&&(r.clean(),setTimeout((()=>{r=null}),20)),setTimeout((()=>{u=h.NotActive,t(null)}),100)}))}function g(){[h.Stopped,h.Stopping].includes(u)||(postMessage("a_stop"),c().then((()=>{postMessage("a_start")})))}let l,p=null;self.onmessage=({data:s})=>{if("stop"===s)return o(),void c().then((()=>{u=h.Stopped}));if("forceFlushBatch"!==s)if("closing"!==s){if(!Array.isArray(s)){if("compressed"===s.type){if(!r)return console.debug("OR WebWorker: sender not initialised. Compressed batch."),void g();s.batch&&r.sendCompressed(s.batch)}if("uncompressed"===s.type){if(!r)return console.debug("OR WebWorker: sender not initialised. Uncompressed batch."),void g();s.batch&&r.sendUncompressed(s.batch)}return"start"===s.type?(u=h.Starting,r=new t(s.ingestPoint,(()=>{g()}),(t=>{!function(t){postMessage({type:"failure",reason:t}),c()}(t)}),s.connAttemptCount,s.connAttemptGap,(t=>{postMessage({type:"compress",batch:t},[t.buffer])}),s.pageNo),a=new n(s.pageNo,s.timestamp,s.url,((t,s)=>{r&&(s?r.sendUncompressed(t):r.push(t))}),s.tabId,(()=>postMessage({type:"queue_empty"}))),null===p&&(p=setInterval(o,3e4)),u=h.Active):"auth"===s.type?r?a?(r.authorise(s.token),void(s.beaconSizeLimit&&a.setBeaconSizeLimit(s.beaconSizeLimit))):(console.debug("OR WebWorker: writer not initialised. Received auth."),void g()):(console.debug("OR WebWorker: sender not initialised. Received auth."),void g()):void 0}if(a){const t=a;s.forEach((s=>{55===s[0]&&(s[1]?l=setTimeout((()=>g()),18e5):clearTimeout(l)),t.writeMessage(s)}))}else postMessage("not_init"),g()}else o(!0);else o()}}();\n'],{type:"text/javascript"}))),this.worker.onerror=e=>{this._debug("webworker_error",e)};let e=!(this.worker.onmessage=({data:e})=>{this.handleWorkerMsg(e)}),t=()=>{e||(e=!0,setTimeout(()=>{e=!1},500),this.worker&&this.worker.postMessage("closing"))};this.attachEventListener(document.body,"mouseleave",t,!1,!1),this.attachEventListener(window,"pagehide",t,!1,!1),this.attachEventListener(document,"visibilitychange",e=>"hidden"===document.visibilityState&&t(),!1)}catch(e){this._debug("worker_start",e)}}handleWorkerMsg(e){if("a_stop"===e)this.stop(!1);else if("a_start"===e)this.waitStatus(L.NotActive).then(()=>{this.allowAppStart(),this.start(this.prevOpts,!0).then(e=>{this.debug.info("Worker restart, session too long",e)}).catch(e=>{this.debug.error("Worker restart failed",e)})});else if("not_init"===e)this.debug.warn("OR WebWorker: writer not initialised. Restarting tracker");else if("failure"===e.type)this.stop(!1),this.debug.error("worker_failed",e.reason),this._debug("worker_failed",e.reason);else if("compress"===e.type){let s=e.batch;s.byteLength>this.compressionThreshold?Te(e.batch,{mtime:0},(e,t)=>{e?(this.debug.error("Openreplay compression error:",e),this.worker?.postMessage({type:"uncompressed",batch:s})):this.worker?.postMessage({type:"compressed",batch:t})}):this.worker?.postMessage({type:"uncompressed",batch:s})}else"queue_empty"===e.type&&this.onSessionSent()}_debug(e,t){null!==this.options.__debug_report_edp&&fetch(this.options.__debug_report_edp,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({context:e,error:""+t})}),this.debug.error("OpenReplay error: ",e,t)}_nCommit(){if(this.socketMode)this.messages.unshift([0,this.timestamp()],dt(this.session.getTabId())),this.commitCallbacks.forEach(e=>e(this.messages)),this.messages.length=0;else if(this.insideIframe)window.parent.postMessage({line:l.iframeBatch,messages:this.messages},this.options.crossdomain?.parentDomain??"*"),this.commitCallbacks.forEach(e=>e(this.messages)),this.messages.length=0;else if(void 0!==this.worker&&this.messages.length)if(!this.messages.length&&this.emptyBatchCounter<1e3)this.emptyBatchCounter++;else{this.emptyBatchCounter=0;try{Xe(()=>{this.messages.unshift([0,this.timestamp()],dt(this.session.getTabId())),this.worker?.postMessage(this.messages),this.commitCallbacks.forEach(e=>e(this.messages)),this.messages.length=0})}catch(e){this._debug("worker_commit",e),this.stop(!0),setTimeout(()=>{this.start()},500)}}}_cStartCommit(){var e;this.coldStartCommitN+=1,2===this.coldStartCommitN&&(e=[[0,this.timestamp()],dt(this.session.getTabId())],this.bufferedMessages1.push(...e),this.bufferedMessages2.push(...e),this.coldStartCommitN=0)}commit(){this.activityState===L.ColdStart?this._cStartCommit():this._nCommit()}postToWorker(t){this.worker?.postMessage(t),this.commitCallbacks.forEach(e=>e(t))}timestamp(){return U()+this.delay}safe(t){let s=this;return function(...e){try{t.apply(this,e)}catch(e){s._debug("safe_fn_call",e)}}}attachCommitCallback(e){this.commitCallbacks.push(e)}checkRequiredVersion(e){var t=e.split(/[.-]/),s=this.version.split(/[.-]/);for(let e=0;e<3;e++){if(isNaN(Number(s[e]))||isNaN(Number(t[e])))return!1;if(Number(s[e])>Number(t[e]))return!0;if(Number(s[e])<Number(t[e]))return!1}return!0}getTrackerInfo(){return{userUUID:this.localStorage.getItem(this.options.local_uuid_key),projectKey:this.projectKey,revID:this.revID,trackerVersion:this.version,isSnippet:this.options.__is_snippet}}getSessionInfo(){return{...this.session.getInfo(),...this.getTrackerInfo()}}getSessionToken(){return this.session.getSessionToken()}getSessionID(){return this.session.getInfo().sessionID||void 0}getSessionURL(e){var t,{projectID:s,sessionID:i,timestamp:n}=this.session.getInfo();if(s&&i)return t=this.options.ingestPoint,t=(/api\.openreplay\.com/.test(t)?"https://app.openreplay.com/ingest":t).replace(/ingest$/,s+"/session/"+i),e?.withCurrentTime?t+"?jumpto="+(U()-n):t;this.debug.error("OpenReplay error: Unable to build session URL")}getHost(){return new URL(this.options.ingestPoint).host}getProjectKey(){return this.projectKey}getBaseHref(){return"string"==typeof this.options.resourceBaseHref?this.options.resourceBaseHref:(this.options.resourceBaseHref,document.baseURI||document.head?.getElementsByTagName("base")[0]?.getAttribute("href")||location.origin+location.pathname)}resolveResourceURL(e){var t=new URL(this.getBaseHref());return t.pathname+="/"+new URL(e).pathname,t.pathname.replace(/\/+/g,"/"),t.toString()}isServiceURL(e){return e.startsWith(this.options.ingestPoint)}active(){return this.activityState===L.Active}resetNextPageSession(e){e?this.sessionStorage.setItem(this.options.session_reset_key,"t"):this.sessionStorage.removeItem(this.options.session_reset_key)}checkSessionToken(e){var t=null!==this.sessionStorage.getItem(this.options.session_reset_key),e=e||t,t=this.session.getSessionToken();return e||!t}async coldStart(e={},t){this.singleBuffer=!1;let s=this.checkSessionToken(e.forceNew),i=(t&&await this.setupConditionalStart(e),()=>{this.orderNumber+=1,Me(),this.coldStartTs=U(),(this.orderNumber%2==0?(this.bufferedMessages1.length=0,this.bufferedMessages1.push([0,this.timestamp()]),this.bufferedMessages1):(this.bufferedMessages2.length=0,this.bufferedMessages2.push([0,this.timestamp()]),this.bufferedMessages2)).push(dt(this.session.getTabId())),this.stop(!1),this.activityState=L.ColdStart,e.sessionHash&&this.session.applySessionHash(e.sessionHash),e.forceNew&&this.session.reset(),this.session.assign({userID:e.userID,metadata:e.metadata}),s||(this.debug.log("continuing session on new tab",this.session.getTabId()),this.send(ct(this.session.getTabId()))),this.observer.observe(),this.ticker.start()});this.coldInterval=setInterval(()=>{i()},3e4),i()}async setupConditionalStart(e){this.conditionsManager=new Ce(this,e);var{token:e,userBrowser:t,userCity:s,userCountry:i,userDevice:n,userOS:r,userState:a,projectID:o,features:h}=await(await fetch(this.options.ingestPoint+"/v1/web/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...this.getTrackerInfo(),timestamp:U(),doNotRecord:!0,bufferDiff:0,userID:this.session.getInfo().userID,token:void 0,deviceMemory:pt,jsHeapSizeLimit:ft,timezone:_s(),width:window.screen.width,height:window.screen.height})})).json();this.features=h||this.features,this.session.assign({projectID:o}),this.session.setUserInfo({userBrowser:t,userCity:s,userCountry:i,userDevice:n,userOS:r,userState:a});let l={sessionToken:e,userUUID:"",sessionID:""};this.startCallbacks.forEach(e=>e(l)),await this.conditionsManager?.fetchConditions(o,e),this.features["feature-flags"]&&(await this.featureFlags.reloadFlags(e),this.conditionsManager?.processFlags(this.featureFlags.flags)),await this.tagWatcher.fetchTags(this.options.ingestPoint,e)}offlineRecording(e={},t){this.onSessionSent=t,this.singleBuffer=!0;var t=this.checkSessionToken(e.forceNew),s=(Me(),this.coldStartTs=U(),this.localStorage.getItem(Es));s&&(s=JSON.parse(s),this.bufferedMessages1=Array.isArray(s)?s:this.bufferedMessages1,this.localStorage.removeItem(Es)),this.bufferedMessages1.push([0,this.timestamp()]),this.bufferedMessages1.push(dt(this.session.getTabId())),this.activityState=L.ColdStart,e.sessionHash&&this.session.applySessionHash(e.sessionHash),e.forceNew&&this.session.reset(),this.session.assign({userID:e.userID,metadata:e.metadata});let i={sessionToken:"",userUUID:"",sessionID:""};return this.startCallbacks.forEach(e=>e(i)),t||this.send(ct(this.session.getTabId())),this.observer.observe(),this.ticker.start(),{saveBuffer:this.saveBuffer,getBuffer:this.getBuffer,setBuffer:this.setBuffer}}saveBuffer(){this.localStorage.setItem(Es,JSON.stringify(this.bufferedMessages1))}getBuffer(){return this.bufferedMessages1}setBuffer(e){this.bufferedMessages1=e}async uploadOfflineRecording(){this.stop(!1);var e=U();this.worker?.postMessage({type:"start",pageNo:this.session.incPageNo(),ingestPoint:this.options.ingestPoint,timestamp:this.coldStartTs,url:document.URL,connAttemptCount:this.options.connAttemptCount,connAttemptGap:this.options.connAttemptGap,tabId:this.session.getTabId()});var{token:e,userBrowser:t,userCity:s,userCountry:i,userDevice:n,userOS:r,userState:a,beaconSizeLimit:o,projectID:h}=await(await fetch(this.options.ingestPoint+"/v1/web/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...this.getTrackerInfo(),timestamp:e,doNotRecord:!1,bufferDiff:e-this.coldStartTs,userID:this.session.getInfo().userID,token:void 0,deviceMemory:pt,jsHeapSizeLimit:ft,timezone:_s()})})).json();for(this.worker?.postMessage({type:"auth",token:e,beaconSizeLimit:o}),this.session.assign({projectID:h}),this.session.setUserInfo({userBrowser:t,userCity:s,userCountry:i,userDevice:n,userOS:r,userState:a});0<this.bufferedMessages1.length;)await this.flushBuffer(this.bufferedMessages1);this.postToWorker([["q_end"]]),this.clearBuffers()}async _start(e={},t,s){0!==Object.keys(e).length&&(this.prevOpts=e);var i=this.activityState===L.ColdStart;if(i&&this.coldInterval&&clearInterval(this.coldInterval),!this.worker&&!this.insideIframe)return this.signalError(n="No worker found: perhaps, CSP is not set.",[]),Promise.resolve(z(n));if(this.activityState===L.Active||this.activityState===L.Starting)return Promise.resolve(z("OpenReplay: trying to call `start()` on the instance that has been started already."));this.activityState=L.Starting,i||Me(),e.sessionHash&&this.session.applySessionHash(e.sessionHash),e.forceNew&&this.session.reset(),this.session.assign({userID:e.userID,metadata:e.metadata});var n=U(),r=(this.worker?.postMessage({type:"start",pageNo:this.session.incPageNo(),ingestPoint:this.options.ingestPoint,timestamp:i?this.coldStartTs:n,url:document.URL,connAttemptCount:this.options.connAttemptCount,connAttemptGap:this.options.connAttemptGap,tabId:this.session.getTabId()}),this.session.getSessionToken()),a=this.checkSessionToken(e.forceNew);this.sessionStorage.removeItem(this.options.session_reset_key),this.debug.log("OpenReplay: starting session; need new session id?",a,"session token: ",r);try{var o,h,l,c=await window.fetch(this.options.ingestPoint+"/v1/web/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...this.getTrackerInfo(),timestamp:n,doNotRecord:!1,bufferDiff:n-this.coldStartTs,userID:this.session.getInfo().userID,token:a?void 0:r,deviceMemory:pt,jsHeapSizeLimit:ft,timezone:_s(),condition:s,assistOnly:e.assistOnly??this.socketMode,width:window.screen.width,height:window.screen.height})});if(200!==c.status)return h=(o=await c.text())===Is?Is:`Server error: ${c.status}. `+o,z(h);if(!this.worker&&!this.insideIframe)return l="no worker found after start request (this should not happen in real world)",this.signalError(l,[]),z(l);var d,{token:u,userUUID:p,projectID:f,beaconSizeLimit:g,compressionThreshold:m,delay:v,sessionID:y,startTimestamp:b,userBrowser:S,userCity:w,userCountry:k,userDevice:T,userOS:I,userState:x,canvasEnabled:E,canvasQuality:C,canvasFPS:_,assistOnly:N,features:O}=await c.json();if(this.features=O||this.features,"string"!=typeof u||"string"!=typeof p||"number"!=typeof b&&void 0!==b||"string"!=typeof y||"number"!=typeof v||"number"!=typeof g&&void 0!==g)return d="Incorrect server response: "+JSON.stringify(c),this.signalError(d,[]),z(d);this.delay=v,this.session.setSessionToken(u),this.session.setUserInfo({userBrowser:S,userCity:w,userCountry:k,userDevice:T,userOS:I,userState:x}),this.session.assign({sessionID:y,timestamp:b||n,projectID:f}),N?(this.socketMode=!0,this.worker?.postMessage("stop")):this.worker?.postMessage({type:"auth",token:u,beaconSizeLimit:g}),a||u!==r||(this.debug.log("continuing session on new tab",this.session.getTabId()),this.send(ct(this.session.getTabId()))),Object.entries(this.session.getInfo().metadata).forEach(([e,t])=>this.send(Ke(e,t))),this.localStorage.setItem(this.options.local_uuid_key,p),this.compressionThreshold=m;let t={sessionToken:u,userUUID:p,sessionID:y};if(this.startCallbacks.forEach(e=>e(t)),e.startCallback&&e.startCallback(Cs(t)),this.features["feature-flags"]&&this.featureFlags.reloadFlags(),await this.tagWatcher.fetchTags(this.options.ingestPoint,u),this.activityState=L.Active,this.options.crossdomain?.enabled&&!this.insideIframe&&this.bootChildrenFrames(),E&&!this.options.canvas.disableCanvas&&(this.canvasRecorder=this.canvasRecorder??new Yt(this,{fps:_,quality:C,isDebug:this.options.canvas.__save_canvas_locally,fixedScaling:this.options.canvas.fixedCanvasScaling,useAnimationFrame:this.options.canvas.useAnimationFrame})),i){for(var R=this.bufferedMessages1.length>this.bufferedMessages2.length?this.bufferedMessages1:this.bufferedMessages2;0<R.length;)await this.flushBuffer(R);this.clearBuffers(),this.commit()}else this.insideIframe&&this.rootId?this.observer.crossdomainObserve(this.rootId,this.frameOderNumber):this.observer.observe(),this.ticker.start();if(this.canvasRecorder?.startTracking(),this.features["usability-test"]&&!this.insideIframe){this.uxtManager=this.uxtManager||new Vt(this,xs);let t;var M,D,A=this.localStorage.getItem(xs);A&&(t=parseInt(A,10)),location?.search&&(M=new URLSearchParams(location.search)).has("oruxt")&&(D=M.get("oruxt"),t=D?parseInt(D,10):void 0),t&&(this.uxtManager.isActive?this.onUxtCb.forEach(e=>e(t)):this.uxtManager.getTest(t,u,Boolean(A)).then(t=>{t&&this.onUxtCb.forEach(e=>e(t))}))}return Cs(t)}catch(e){if(this.stop(),this.session.reset(),!e)return console.error("Unknown error during start"),this.signalError("Unknown error",[]),z("Unknown error");if(e===Is)return this.signalError(Is,[]),z(Is);this._debug("session_start",e);s=e instanceof Error?e.message:e.toString();return this.signalError(s,[]),z(s)}}addOnUxtCb(e){this.onUxtCb.push(e)}getUxtId(){return this.uxtManager?.getTestId()}async waitStart(){return new Promise(e=>{let t=setInterval(()=>{this.canStart&&(clearInterval(t),e(!0))},100)})}async waitStarted(){return this.waitStatus(L.Active)}async waitStatus(s){return new Promise(e=>{let t=()=>{this.activityState===s?e(!0):setTimeout(t,25)};t()})}async start(...s){return this.activityState===L.Active||this.activityState===L.Starting?Promise.resolve(z("OpenReplay: trying to call `start()` on the instance that has been started already.")):(this.insideIframe&&this.signalIframeTracker(),document.hidden?new Promise(e=>{let t=async()=>{document.hidden||(await this.waitStart(),document.removeEventListener("visibilitychange",t),e(this._start(...s)))};document.addEventListener("visibilitychange",t)}):(await this.waitStart(),this._start(...s)))}forceFlushBatch(){this.worker?.postMessage("forceFlushBatch")}getTabId(){return this.session.getTabId()}clearBuffers(){this.bufferedMessages1.length=0,this.bufferedMessages2.length=0}trackWs(e){let i=e;return(e,t,s="down")=>{"string"!=typeof e||"string"!=typeof t||5242880<t.length||255<e.length||this.send([84,"websocket",i,t,this.timestamp(),s,e])}}stop(e=!0){if(this.activityState!==L.NotActive)try{!this.insideIframe&&this.options.crossdomain?.enabled&&this.killChildrenFrames(),this.attributeSender.clear(),this.sanitizer.clear(),this.observer.disconnect(),this.nodes.clear(),this.ticker.stop(),this.stopCallbacks.forEach(e=>e()),this.tagWatcher.clear(),this.worker&&e&&this.worker.postMessage("stop"),this.canvasRecorder?.clear(),this.messages.length=0,this.parentActive=!1}finally{this.activityState=L.NotActive,this.debug.log("OpenReplay tracking stopped.")}}}let Os=h&&"InstallTrigger"in window?e=>e.message+"\n"+e.stack:e=>e.stack||e.message;function Rs(e){return void 0===e?"undefined":null===e?"null":e instanceof Error?Os(e):Array.isArray(e)?`Array(${e.length})`:String(e)}function Ms(t){if(void 0===t)return"undefined";if(null===t)return"null";if(t instanceof Error)return Os(t);if(Array.isArray(t))return`Array(${t.length})[${t.slice(0,10).map(Rs).join(", ")}]`;if("object"!=typeof t)return t.toString();{var s,i=[];let e=0;for(s in t){if(10==++e)break;var n=t[s];i.push(s+": "+Rs(n))}return"{"+i.join(", ")+"}"}}function Ds(n){return"string"==typeof n[0]&&n.unshift(n.shift().replace(/%(o|s|f|d|i)/g,(e,t)=>{var s,i=n.shift();if(void 0===i)return e;switch(t){case"o":return Ms(i);case"s":return Rs(i);case"f":return"number"!=typeof(s=i)?"NaN":s.toString();case"d":case"i":return"number"!=typeof(s=i)?"NaN":Math.floor(s).toString();default:return e}})),n.map(Ms).join(" ")}let As=["log","info","warn","error","debug","assert"];function Ls(o,e){let h=Object.assign({consoleMethods:As,consoleThrottling:30},e);if(Array.isArray(h.consoleMethods)&&0!==h.consoleMethods.length){let r=o.safe((e,t)=>{let s=Ds(t);o.sanitizer.privateMode&&(s=s.replaceAll(/./g,"*")),o.send([22,e,s])}),a=0;e=()=>{a=0};o.attachStartCallback(e),o.ticker.attach(e,33,!1);e=o.safe(e=>((s,i)=>{let n={apply:function(e,t,s){Reflect.apply(e,i,s),(a+=1)>h.consoleThrottling||r(e.name,s)}};h.consoleMethods.forEach(e=>{var t;-1===As.indexOf(e)?o.debug.error(`OpenReplay: unsupported console method "${e}"`):(t=i.console[e],s[e]=new Proxy(t,n))})})(e.console,e));e(window),o.observer.attachContextCallback(e)}}let Fs=/(^|@)\S+:\d+/,js=/^\s*at .*(\S+:\d+|\(native\))/m,Bs=/^(eval@)?(\[native code\])?$/;function Ps(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return(!(t=e).stacktrace||t.message.includes("\n")&&t.message.split("\n").length>t.stacktrace.split("\n").length?e=>{var s=/Line (\d+).*script (?:in )?(\S+)/i,i=e.message.split("\n"),n=[];for(let e=2,t=i.length;e<t;e+=2){var r=s.exec(i[e]);r&&n.push({file:r[2],line:+r[1],raw:i[e]})}return n}:t.stack?e=>(e=Us(e.stack.split("\n").filter(e=>!!e.match(Fs)&&!e.match(/^Error created at/)))).map(e=>{var t=e.split("@"),s=Hs(t.pop()),t=t.shift()||"";let i;return{function:t.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0,args:void 0===(i=t.match(/\(([^)]*)\)/)?t.replace(/^[^(]+\(([^)]*)\)$/,"$1"):i)||"[arguments not available]"===i?void 0:i.split(","),file:s[0],line:s[1]?+s[1]:void 0,col:s[2]?+s[2]:void 0,raw:e}}):e=>{var s=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,i=e.stacktrace.split("\n"),n=[];for(let e=0,t=i.length;e<t;e+=2){var r=s.exec(i[e]);r&&n.push({function:r[3]||void 0,file:r[2],line:r[1]?+r[1]:void 0,raw:i[e]})}return n})(t);var t;if(e.stack&&e.stack.match(js))return Us(e.stack.split("\n").filter(e=>!!e.match(js))).map(e=>{let t=(e=e.includes("(eval ")?e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""):e).replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,"");var s=t.match(/ (\(.+\)$)/),i=(t=s?t.replace(s[0],""):t,Hs(s?s[1]:t));return{function:s&&t||void 0,file:["eval","<anonymous>"].includes(i[0])?void 0:i[0],line:i[1]?+i[1]:void 0,col:i[2]?+i[2]:void 0,raw:e}});if(e.stack)return Us(e.stack.split("\n").filter(e=>!e.match(Bs))).map(e=>{var t,s;return(e=e.includes(" > eval")?e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1"):e).includes("@")||e.includes(":")?{function:(s=e.match(t=/(([^\n\r"\u2028\u2029]*".[^\n\r"\u2028\u2029]*"[^\n\r@\u2028\u2029]*(?:@[^\n\r"\u2028\u2029]*"[^\n\r@\u2028\u2029]*)*(?:[\n\r\u2028\u2029][^@]*)?)?[^@]*)@/))&&s[1]?s[1]:void 0,file:(s=Hs(e.replace(t,"")))[0],line:s[1]?+s[1]:void 0,col:s[2]?+s[2]:void 0,raw:e}:{function:e}});throw new Error("Cannot parse given Error object")}function Hs(e){var t;return e.includes(":")?[(t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,"")))[1],t[2]||void 0,t[3]||void 0]:[e,void 0,void 0]}function Us(e){return e}function zs(e){return[{columnNumber:e.colno,lineNumber:e.lineno,fileName:e.filename,functionName:"",source:""}]}function qs(e,t,s={}){let i=t;try{i=Ps(e).map(e=>({functionName:e.function,args:e.args,fileName:e.file,lineNumber:e.line,columnNumber:e.col,source:e.raw}))}catch(e){}return rt(e.name,e.message,JSON.stringify(i),JSON.stringify(s))}function Ws(s,e=window,i={}){if(s instanceof ErrorEvent){if(s.error instanceof Error)return qs(s.error,zs(s),i);{let[e,t]=s.message.split(":");return t||(e="Error",t=s.message),rt(e,t,JSON.stringify(zs(s)),JSON.stringify(i))}}if("PromiseRejectionEvent"in e&&s instanceof e.PromiseRejectionEvent){if(s.reason instanceof Error)return qs(s.reason,[],i);{let t;try{t=JSON.stringify(s.reason)}catch(e){t=String(s.reason)}return rt("Unhandled Promise Rejection",t,"[]",JSON.stringify(i))}}return null}function $s(e,t=document.location){return(e=e.trim()).startsWith("//")||e.startsWith("http://")||e.startsWith("https://")||e.startsWith("data:")?e:e.startsWith("/")?t.origin+e:t.origin+t.pathname+e}function Vs(n){function i(e,t){n.attributeSender.sendSetAttribute(e,"src","https://static.openreplay.com/tracker/placeholder.jpeg");var{width:s,height:i}=t.getBoundingClientRect();t.hasAttribute("width")||n.attributeSender.sendSetAttribute(e,"width",String(s)),t.hasAttribute("height")||n.attributeSender.sendSetAttribute(e,"height",String(i))}function r(e,t){(t=t.srcset)&&(t=t.split(t.match(/,\s+/)?/,\s+/:",").map(e=>$s(e)).join(", "),n.attributeSender.sendSetAttribute(e,"srcset",t))}function a(e,t){1e5<t.src.length&&i(e,t),n.send(Ye(e,"src",t.src,n.getBaseHref()))}let o=n.safe(function(e){e=$s(e.src||"");Le(e)&&n.send(lt(n.timestamp(),0,0,0,0,0,e,"img",0,!1))}),t=n.safe(function(e){var t,s=n.nodes.getID(e);void 0!==s&&e.complete&&(0!==e.naturalHeight||0!==e.naturalWidth||(t=e.src,Re&&(t.startsWith("data:image/svg+xml")||t.match(/.svg$|/i)))?(n.sanitizer.isHidden(s)||n.sanitizer.isObscured(s)?i:(a(s,e),r))(s,e):o(e))}),s=qe(n.safe(e=>{for(var t of e)if("attributes"===t.type){var s=t.target,i=n.nodes.getID(s);if(void 0===i)return;"src"===t.attributeName&&a(i,s),"srcset"===t.attributeName&&r(i,s)}}),n.options.forceNgOff);n.attachStopCallback(()=>{s.disconnect()}),n.nodes.attachNodeCallback(e=>{x(e,"img")&&(n.nodes.attachNodeListener(e,"error",()=>o(e)),n.nodes.attachNodeListener(e,"load",()=>t(e)),t(e),s.observe(e,{attributes:!0,attributeFilter:["src","srcset"]}))})}let Xs=["text","password","email","search","number","range","date","tel","time"],Js=h&&"labels"in HTMLInputElement.prototype?e=>{let t=e;for(;null!==(t=t.parentNode);)if(x(t,"label"))return t;e=e.labels;if(null!==e&&1===e.length)return e[0]}:e=>{let t=e;for(;null!==(t=t.parentNode);)if(x(t,"label"))return t;var s=e.id;if(s){e=e.ownerDocument.querySelectorAll('label[for="'+s+'"]');if(null!==e&&1===e.length)return e[0]}};function Ks(e){let t=Be(e);var s;return null===t&&(s=Js(e),t=s&&s.innerText||e.placeholder||e.name||e.id||e.className||e.type),Ae(t).slice(0,100)}let p={Plain:0,Obscured:1,Hidden:2};function Gs(d,e){let r=Object.assign({obscureInputNumbers:!0,obscureInputEmails:!0,defaultInputMode:p.Obscured,obscureInputDates:!1},e);function u(e,t){let s=t.value,i=r.defaultInputMode,n=("password"===t.type||d.sanitizer.isHidden(e)?i=p.Hidden:(d.sanitizer.isObscured(e)||i===p.Plain&&(r.obscureInputNumbers&&"date"!==t.type&&/\d\d\d\d/.test(s)||r.obscureInputDates&&"date"===t.type||r.obscureInputEmails&&("email"===t.type||~s.indexOf("@"))))&&(i=p.Obscured),0);switch(i){case p.Hidden:n=-1,s="";break;case p.Obscured:n=s.length,s=""}return{value:s,mask:n}}function s(e,t){var{value:t,mask:s}=u(e,t);d.send([18,e,t,s])}let i=new Map,n=new Map;function a(e,t){i.get(e)!==t.value&&(i.set(e,t.value),s(e,t))}function o(e,t){n.get(e)!==t&&(n.set(e,t),d.send([19,e,t]))}d.attachStopCallback(()=>{i.clear(),n.clear()}),d.ticker.attach(()=>{i.forEach((e,t)=>{var s=d.nodes.getNode(t);if(!s)return i.delete(t);a(t,s)}),n.forEach((e,t)=>{var s=d.nodes.getNode(t);if(!s)return n.delete(t);o(t,s.checked)})},3),d.nodes.attachNodeCallback(d.safe(l=>{let c=d.nodes.getID(l);if(void 0!==c)if(x(l,"select")&&(s(c,l),d.nodes.attachNodeListener(l,"change",()=>s(c,l))),x(e=l,"textarea")||x(e,"input")&&Xs.includes(e.type)){a(c,l);let e=0,o=0,h=0;d.nodes.attachNodeListener(l,"focus",()=>{e=U()}),d.nodes.attachNodeListener(l,"input",()=>{0===o&&0!==e&&(o=U()-e)}),void d.nodes.attachNodeListener(l,"change",()=>{0!==e&&(h=U()-e);{var t=c,s=l,i=o,n=h,{value:r,mask:a}=u(t,s);let e=Ks(s);d.sanitizer.privateMode&&(e=e.replaceAll(/./g,"*")),d.send([112,t,r,0!==a,e,i,n])}o=0,h=0,e=0})}else{var e;(e=>x(e,"input")&&("checkbox"===(e=e.type)||"radio"===e))(l)&&(o(c,l.checked),d.nodes.attachNodeListener(l,"change",()=>o(c,l.checked)))}}))}let Qs="undefined"!=typeof CSS&&CSS.escape||(e=>e),Ys=new WeakMap;function Zs(e){return(s=>{if(!s||1!==s.nodeType)return!1;if(s.id)return"#"+Qs(s.id);for(var e=[];s&&1===s.nodeType&&s!==s.ownerDocument;){if(s.id){e.unshift("#"+Qs(s.id));break}var i=s.tagName.toLowerCase();if(s.classList?.length)for(var t of s.classList)if(ri(t)&&ni(t,s.ownerDocument))return e.unshift(i+"."+Qs(t)),e.join(" > ");var n=(e=>{if(e.classList?.length&&e.parentNode){var t=e.parentNode.children;e:for(var s of e.classList)if(ri(s)&&ni(s,e.ownerDocument)){for(var i of t)if(i!==e&&i.classList?.contains(s))continue e;return s}}return null})(s);if(n)e.unshift(i+"."+Qs(n));else if(s===s.ownerDocument.body||s===s.ownerDocument.documentElement)e.unshift(i);else{let t=1;for(let e=s.previousElementSibling;e;e=e.previousElementSibling)e.tagName.toLowerCase()===i&&t++;e.unshift(`${i}:nth-of-type(${t})`)}s=s.parentNode}return e.join(" > ")})(e)||""}function ei(e){var t=e.tagName.toUpperCase();return"BUTTON"===t||"A"===t||"LI"===t||"SELECT"===t||"TR"===t||"TH"===t||null!=e.onclick||"button"===e.getAttribute("role")}function ti(t,s){if(t instanceof Element){var i=t;var n=s;let e=i;for(;null!==e&&e!==n.documentElement;){if(a(e,"masked"))return null;e=e.parentElement}if(Jt(i)){let e=i.ownerSVGElement;for(;null!==e;)e=(i=e).ownerSVGElement}for(e=i;null!==e&&e!==n.documentElement;){var r=e.tagName.toUpperCase();if("LABEL"===r)return null;if("INPUT"===r)return e;if(ei(e)||null!==Be(e))return e;e=e.parentElement}return i===n.documentElement?null:i}return null}function si(o,e){let{disableClickmaps:h=!1}=e||{};let i=-1,n=-1,r=!1,l=null,c=0,d={},s=0,u=0,p=0,f=0,t;o.attachStartCallback(()=>{t=setInterval(()=>{var e,t;t=f/225,s=(s&&(e=(t-s)/225,4<p&&.008<e&&o.send([114,U()]),f=0,p=0),t)},225)}),o.attachStopCallback(()=>{i=-1,n=-1,r=!1,l=null,d={},t&&clearInterval(t)});let g=()=>{r&&(o.send([20,i,n]),r=!1)},a=(a,e=!1)=>{e=e?o.attachEventListener.bind(o):o.nodes.attachNodeListener.bind(o.nodes);e(a.documentElement,"mouseover",e=>{e=ti(e.target,a);e!==l&&(l=e,c=performance.now())}),e(a,"mousemove",e=>{var[t,s]=o.observer.getDocumentOffset(a),t=(i=e.clientX+t,n=e.clientY+s,r=!0,Math.sign(e.movementX));f+=Math.abs(e.movementX)+Math.abs(e.movementY),t!==u&&(u=t,p++)},!1),e(a,"click",e=>{var t,s,i,n,r=ti(e.target,a);(e.clientX||e.clientY)&&null!==r&&(void 0!==(t=o.nodes.getID(r))&&(s=e.pageX,e=e.pageY,i=a.documentElement.scrollWidth,n=a.documentElement.scrollHeight,s=ii(s/i),i=ii(e/n),g(),e=(t=>{var e=Be(t);if(null!==e)return e;if(x(t,"input"))return Ks(t);if(ei(t)){let e="";return Ae(e=(e=t instanceof HTMLElement?o.sanitizer.getInnerTextSecure(t):e)||t.id||t.className).slice(0,100)}return""})(r),o.send([68,t,l===r?Math.round(performance.now()-c):0,o.sanitizer.privateMode?e.replaceAll(/./g,"*"):e,ei(r)&&!h?(n=t,e=r,d[n]=d[n]||Zs(e)):"",s,i],!0)),l=null)})};o.nodes.attachNodeCallback(e=>{Gt(e)&&a(e)}),a(document,!0),o.ticker.attach(g,e?.trackingOffset||7)}function ii(e){return Math.round(1e4*e)}function ni(e,t){let s=Ys.get(t);return s||(s=Object.create(null),Ys.set(t,s)),e in s?s[e]:(t=1===t.querySelectorAll("."+Qs(e)).length,s[e]=t)}function ri(e){if(/^[a-z\-]{3,}$/i.test(e)){var t;for(t of e.split(/-|[A-Z]/)){if(t.length<=2)return;if(/[^aeiou]{4,}/i.test(t))return}return 1}}function ai(a,o){o=o||{},Ii(mi(function(){function e(e){e.forEach(function(e){var t,s;e.hadRecentInput||(t=n[0],s=n[n.length-1],i&&e.startTime-s.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,n.push(e)):(i=e.value,n=[e]))}),i>s.value&&(s.value=i,s.entries=n,t())}var t,s=m("CLS",0),i=0,n=[],r=pi("layout-shift",e);r&&(t=S(a,s,xi,o.reportAllChanges),gi(function(){e(r.takeRecords()),t(!0)}),g(function(){s=m("CLS",i=0),t=S(a,s,xi,o.reportAllChanges),fi(function(){return t()})}),setTimeout(t,0))}))}function oi(r,a){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(a=a||{},ki(function(){var e;Ri();function t(t){ji(function(){t.forEach(Fi);var e=Ai();e&&e.latency!==s.value&&(s.value=e.latency,s.entries=e.entries,n())})}var s=m("INP"),i=pi("event",t,{durationThreshold:null!=(e=a.durationThreshold)?e:40}),n=S(r,s,Bi,a.reportAllChanges);i&&(i.observe({type:"first-input",buffered:!0}),gi(function(){t(i.takeRecords()),n(!0)}),g(function(){Di=Oi(),k.length=0,Mi.clear(),s=m("INP"),n=S(r,s,Bi,a.reportAllChanges)}))}))}function hi(a,o){o=o||{},ki(function(){function e(e){(e=o.reportAllChanges?e:e.slice(-1)).forEach(function(e){e.startTime<i.firstHiddenTime&&(n.value=Math.max(e.startTime-ui(),0),n.entries=[e],t())})}var t,s,i=wi(),n=m("LCP"),r=pi("largest-contentful-paint",e);r&&(t=S(a,n,Pi,o.reportAllChanges),s=mi(function(){Hi[n.id]||(e(r.takeRecords()),r.disconnect(),Hi[n.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){addEventListener(e,function(){return ji(s)},{once:!0,capture:!0})}),gi(s),g(function(e){n=m("LCP"),t=S(a,n,Pi,o.reportAllChanges),fi(function(){n.value=performance.now()-e.timeStamp,Hi[n.id]=!0,t(!0)})}))})}var li,f,ci=-1,g=function(t){addEventListener("pageshow",function(e){e.persisted&&(ci=e.timeStamp,t(e))},!0)},di=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&0<e.responseStart&&e.responseStart<performance.now())return e},ui=function(){var e=di();return e&&e.activationStart||0},m=function(e,t){var s=di(),i="navigate";return 0<=ci?i="back-forward-cache":s&&(document.prerendering||0<ui()?i="prerender":document.wasDiscarded?i="restore":s.type&&(i=s.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},pi=function(e,t,s){try{var i;if(PerformanceObserver.supportedEntryTypes.includes(e))return(i=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})})).observe(Object.assign({type:e,buffered:!0},s||{})),i}catch(e){}},S=function(t,s,i,n){var r,a;return function(e){0<=s.value&&(e||n)&&((a=s.value-(r||0))||void 0===r)&&(r=s.value,s.delta=a,s.rating=(e=s.value)>i[1]?"poor":e>i[0]?"needs-improvement":"good",t(s))}},fi=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},gi=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},mi=function(e){var t=!1;return function(){t||(e(),t=!0)}},w=-1,vi=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},yi=function(e){"hidden"===document.visibilityState&&-1<w&&(w="visibilitychange"===e.type?e.timeStamp:0,Si())},bi=function(){addEventListener("visibilitychange",yi,!0),addEventListener("prerenderingchange",yi,!0)},Si=function(){removeEventListener("visibilitychange",yi,!0),removeEventListener("prerenderingchange",yi,!0)},wi=function(){return w<0&&(w=vi(),bi(),g(function(){setTimeout(function(){w=vi(),bi()},0)})),{get firstHiddenTime(){return w}}},ki=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},Ti=[1800,3e3],Ii=function(r,a){a=a||{},ki(function(){var t,s=wi(),i=m("FCP"),n=pi("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(n.disconnect(),e.startTime<s.firstHiddenTime)&&(i.value=Math.max(e.startTime-ui(),0),i.entries.push(e),t(!0))})});n&&(t=S(r,i,Ti,a.reportAllChanges),g(function(e){i=m("FCP"),t=S(r,i,Ti,a.reportAllChanges),fi(function(){i.value=performance.now()-e.timeStamp,t(!0)})}))})},xi=[.1,.25],Ei=0,Ci=1/0,_i=0,Ni=function(e){e.forEach(function(e){e.interactionId&&(Ci=Math.min(Ci,e.interactionId),_i=Math.max(_i,e.interactionId),Ei=_i?(_i-Ci)/7+1:0)})},Oi=function(){return li?Ei:performance.interactionCount||0},Ri=function(){"interactionCount"in performance||(li=li||pi("event",Ni,{type:"event",buffered:!0,durationThreshold:0}))},k=[],Mi=new Map,Di=0,Ai=function(){var e=Math.min(k.length-1,Math.floor((Oi()-Di)/50));return k[e]},Li=[],Fi=function(t){var e,s;Li.forEach(function(e){return e(t)}),!t.interactionId&&"first-input"!==t.entryType||(s=k[k.length-1],((e=Mi.get(t.interactionId))||k.length<10||t.duration>s.latency)&&(e?t.duration>e.latency?(e.entries=[t],e.latency=t.duration):t.duration===e.latency&&t.startTime===e.entries[0].startTime&&e.entries.push(t):(s={id:t.interactionId,latency:t.duration,entries:[t]},Mi.set(s.id,s),k.push(s)),k.sort(function(e,t){return t.latency-e.latency}),10<k.length)&&k.splice(10).forEach(function(e){return Mi.delete(e.id)}))},ji=function(e){var t=self.requestIdleCallback||self.setTimeout,s=-1;return e=mi(e),"hidden"===document.visibilityState?e():(s=t(e),gi(e)),s},Bi=[200,500],Pi=[2500,4e3],Hi={},Ui=[800,1800],zi=function e(t){document.prerendering?ki(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)};function qi(d,e){let i=Object.assign({captureResourceTimings:!0,capturePageLoadTimings:!0,capturePageRenderTimings:!0,excludedResourceUrls:[]},e);if("PerformanceObserver"in window||(i.captureResourceTimings=!1),i.captureResourceTimings){let h={},r=new PerformanceObserver(e=>e.getEntries().forEach(o)),a,l=(d.attachStartCallback(function({sessionID:e}){var t,s,i,n;e!==a&&(performance.getEntriesByType("resource").forEach(o),a=e),r.observe({entryTypes:["resource"]}),ai(u),oi(u),hi(u),t=u,s=s||{},i=m("TTFB"),n=S(t,i,Ui,s.reportAllChanges),zi(function(){var e=di();e&&(i.value=Math.max(e.responseStart-ui(),0),i.entries=[e],n(!0),g(function(){i=m("TTFB",0),(n=S(t,i,Ui,s.reportAllChanges))(!0)}))})}),d.attachStopCallback(function(){r.disconnect()}),0),c=0;if(i.capturePageLoadTimings){let h=!1;d.ticker.attach(()=>{var e,t,s,i,n,r,a,o;!h&&(0!==l&&0!==c||performance.getEntriesByType("paint").forEach(e=>{var{name:e,startTime:t}=e;switch(e){case"first-paint":l=t;break;case"first-contentful-paint":c=t}}),performance.timing.loadEventEnd||3e4<performance.now())&&(h=!0,{navigationStart:e,requestStart:t,responseStart:s,responseEnd:i,domContentLoadedEventStart:n,domContentLoadedEventEnd:r,loadEventStart:a,loadEventEnd:o}=performance.timing,d.send([23,t-e||0,s-e||0,i-e||0,n-e||0,r-e||0,a-e||0,o-e||0,l,c]))},30)}if(i.capturePageRenderTimings){let i=0,n=0,r=0,a=null,o=!1;d.ticker.attach(()=>{var e,t,s;!o&&(e=performance.now(),null!==h&&1e3<e-(i=Math.max.apply(null,Object.keys(h).map(e=>h[e])))&&(a=(s=>{var i=[],n=document.getElementsByTagName("*"),r=/url\(("[^"]*"|'[^']*'|[^)]*)\)/i;for(let t=0;t<n.length;t++){var a,o,h,l,c=n[t];let e="";(e=x(c,"img")?c.currentSrc||c.src:e)||(a=getComputedStyle(c).getPropertyValue("background-image"))&&null!==(a=r.exec(a))&&((e=a[1]).startsWith('"')||e.startsWith("'"))&&(e=e.substr(1,e.length-2)),e&&(void 0===(a="data:image"===e.substr(0,10)?0:s[e])||(c=c.getBoundingClientRect(),o=Math.max(c.top,0),h=Math.max(c.left,0),l=Math.min(c.bottom,window.innerHeight||document.documentElement&&document.documentElement.clientHeight||0),c=Math.min(c.right,window.innerWidth||document.documentElement&&document.documentElement.clientWidth||0),l<=o)||c<=h||i.push({time:a,area:(l-o)*(c-h)}))}return i})(h),h=null),null!==r&&(50<e-r&&(n=e),r=5e3<e-n?null:e),null!==a&&null===r||3e4<e)&&(o=!0,e=(h=null)===a?0:((t,s)=>{let i=Math.max(document.documentElement&&document.documentElement.clientWidth||0,window.innerWidth||0)*Math.max(document.documentElement&&document.documentElement.clientHeight||0,window.innerHeight||0)/10,n=i*t;for(let e=0;e<s.length;e++){var{time:r,area:a}=s[e];i+=a,n+=a*(t<r?r:t)}return 0===i?0:n/i})(c||l,a),{domContentLoadedEventEnd:s,navigationStart:t}=performance.timing,s=null===r?Math.max(n,c,s-t||0):0,d.send([24,e,c>i?c:i,s]))})}function o(s){if(!(s.duration<0||!Le(s.name)||d.isServiceURL(s.name))){null!==h&&(h[s.name]=s.startTime+s.duration);let t=!1;i.excludedResourceUrls?.forEach(e=>{s.name.startsWith(e)&&(t=!0)}),t||((0===s.responseEnd||0===s.transferSize&&0===s.decodedBodySize)&&d.send(lt(s.startTime+T,0,0,0,0,0,s.name,s.initiatorType,0,!0)),d.send(lt(s.startTime+T,s.duration,s.responseStart&&s.startTime?s.responseStart-s.startTime:0,s.encodedBodySize<s.transferSize?s.transferSize-s.encodedBodySize:0,s.encodedBodySize||0,s.decodedBodySize||0,d.sanitizer.privateMode?s.name.replaceAll(/./g,"*"):s.name,s.initiatorType,s.transferSize,s.responseStatus&&304===s.responseStatus||0===s.transferSize)))}}function u(e){if(d.active())return d.send([124,e.name,String(e.value)])}}}function Wi(e){var t=e.defaultView;return[t&&t.scrollX||e.documentElement&&e.documentElement.scrollLeft||e.body&&e.body.scrollLeft||0,t&&t.scrollY||e.documentElement&&e.documentElement.scrollTop||e.body&&e.body.scrollTop||0]}function $i(s,i){let t=!1,n=new Map;function r(e){Xt(e)&&(u(e)&&n.set(e,[e.scrollLeft,e.scrollTop]),Gt(e))&&n.set(e,Wi(e))}let e=s.safe(()=>{var e,t;i||s.send(([e,t]=[...Wi(document)],[6,e,t]))}),a=s.safe((e,t)=>{t=s.nodes.getID(t);void 0!==t&&s.send([16,t,e[0],e[1]])});s.attachStartCallback(e),s.attachStopCallback(()=>{t=!1,n.clear()}),s.nodes.attachNodeCallback((e,t)=>{t&&(u(e)&&0<e.scrollLeft+e.scrollTop?n.set(e,[e.scrollLeft,e.scrollTop]):Gt(e)&&n.set(e,Wi(e))),Qt(e)&&s.nodes.attachNodeListener(e,"scroll",e=>{r(e.target)})}),s.attachEventListener(document,"scroll",e=>{e=e.target;e===document?t=!0:r(e)}),s.ticker.attach(()=>{t&&(e(),t=!1),n.forEach(a),n.clear()},5,!1)}let Vi={checkCssInterval:200,scanInMemoryCSS:!1,checkLimit:void 0};function Xi(m,e,v,y,b){function s(e){m.debug.log("Openreplay: capturing axios response data",e);var{headers:t,data:s,method:i,url:n}=e.config,{data:r,headers:a,status:o,response:h}=e,{data:h,headers:l,status:c}=h||{};let d=v.ignoreHeaders,u=Array.isArray(d)?e=>d.includes(e):()=>d;function p(e,t){u(t[0])||(e[t[0]]=t[1])}let f={},g={};t.toJSON?f=t.toJSON():t instanceof Headers?t.forEach((e,t)=>p(f,[t,e])):Array.isArray(t)?t.forEach(e=>p(f,e)):"object"==typeof t&&Object.entries(t).forEach(e=>p(f,e));var t=l||a,l=(t.toJSON?g=t.toJSON():t instanceof Headers?t.forEach((e,t)=>p(g,[t,e])):Array.isArray(t)?t.forEach(e=>p(g,e)):"object"==typeof t&&Object.entries(t).forEach(([e,t])=>{u(e)||(g[e]=t)}),y({url:n,method:i||"",status:o||c||0,request:{headers:f,body:s},response:{headers:g,body:h||r}}));l?(a=e.config.__openreplay_timing,t=performance.now()-a,m.debug.log("Openreplay: final req object",l),m.send(at("xhr",String(i),String(l.url),b(l.request),b(l.response),l.status,a+T,t,0))):m.debug.log("Openreplay: empty request/response info, skipping")}m.debug.log("Openreplay: attaching axios spy to instance",e);let t=e.interceptors.request.use(function(e){var t,s;return m.debug.log("Openreplay: capturing API request",e),e.__openreplay_timing=performance.now(),v.sessionTokenHeader&&(t="string"==typeof v.sessionTokenHeader?v.sessionTokenHeader:"X-OpenReplay-Session-Token",s=m.getSessionToken())&&e.headers.set(t,s),e},function(e){m.debug.log("Openreplay: failed API request, skipping",e)},{synchronous:!0}),i=e.interceptors.response.use(function(e){return v.failuresOnly||s(e),e},function(e){var t;return m.debug.log("Openreplay: capturing API request error",e),(e=>null!==e&&"object"==typeof e)(t=e)&&!0===t.isAxiosError&&Boolean(e.response)?s(e.response):e instanceof Error&&m.send(qs(e,[])),Promise.reject(e)},{synchronous:!0});m.attachStopCallback(()=>{e.interceptors.request.eject?.(t),e.interceptors.response.eject?.(i)})}let Ji=new Set(["password","pass","pwd","mdp","token","bearer","jwt","api_key","api-key","apiKey","secret","ssn","zip","zipcode","x-api-key","www-authenticate","x-csrf-token","x-requested-with","x-forwarded-for","x-real-ip","cookie","authorization","auth","proxy-authorization","set-cookie","account_key"]);function Ki(e){var t;return"number"==typeof e?(t=1+(0|Math.log10((e^e>>31)-(e>>31))),"9".repeat(t)):"string"==typeof e?e.replace(/[^\f\n\r\t\v\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff\s]/g,"*"):e}function Gi(e){let s={};if(Array.isArray(e))e.forEach(({name:e,value:t})=>{Ji.has(e.toLowerCase())?s[e]=Ki(t):s[e]=t});else for(var[t,i]of Object.entries(e))Ji.has(t.toLowerCase())?s[t]=Ki(i):s[t]=i;return s}function Qi(t){if(!t)return t;let e,s=!1;try{e=JSON.parse(t),s=!0}catch(e){}if(s)return Yi(e),JSON.stringify(e);if(!("string"==typeof t&&t.includes("?")&&t.includes("=")))return t;try{var i,n,r=new URLSearchParams(t);for(i of r.keys())Ji.has(i.toLowerCase())&&(n=Ki(r.get(i)),r.set(i,n));return r.toString()}catch(e){return t}}function Yi(e){if(Array.isArray(e))e.forEach(Yi);else if(e&&"object"==typeof e)for(var t in e)Object.hasOwn(e,t)&&(Ji.has(t.toLowerCase())?e[t]=Ki(e[t]):null!==e[t]&&"object"==typeof e[t]&&Yi(e[t]))}class Zi{constructor(e=[],t,s){this.ignoredHeaders=e,this.setSessionTokenHeader=t,this.sanitize=s,this.id="",this.name="",this.method="",this.url="",this.status=0,this.statusText="",this.cancelState=0,this.readyState=0,this.header={},this.responseType="",this.requestType="xhr",this.requestHeader={},this.responseSize=0,this.responseSizeText="",this.startTime=0,this.endTime=0,this.duration=0,this.getData={},this.requestData=null}getMessage(){var{reqHs:e,resHs:t}=this.writeHeaders(),s="GET"===this.method?JSON.stringify((Yi(s=this.getData),s)):Qi(this.requestData),e={headers:Gi(e),body:s},t={headers:Gi(t),body:Qi(this.response)},e=this.sanitize({url:(t=>{if(!t)return"";try{var e=new URL(t);if(e.searchParams)for(var s of e.searchParams.keys())Ji.has(s.toLowerCase())&&e.searchParams.set(s,"******");return e.toString()}catch(e){return t}})(this.url),method:this.method,status:this.status,request:e,response:t});return e?(e.url.includes("/graphql")&&e.response.body&&"string"==typeof e.response.body&&(t=e.response.body.includes("errors"),e.status=t?400:200,this.requestType="graphql"),{requestType:this.requestType,method:e.method,url:e.url,request:JSON.stringify(e.request),response:JSON.stringify(e.response),status:e.status,startTime:this.startTime,duration:this.duration,responseSize:this.responseSize}):null}writeHeaders(){let s={},i=(Object.entries(this.requestHeader).forEach(([e,t])=>{this.isHeaderIgnored(e)||(s[e]=t)}),this.setSessionTokenHeader((e,t)=>{s[e]=t}),{});return Object.entries(this.header).forEach(([e,t])=>{this.isHeaderIgnored(e)||(i[e]=t)}),{reqHs:s,resHs:i}}isHeaderIgnored(e){return Array.isArray(this.ignoredHeaders)?this.ignoredHeaders.map(e=>e.toLowerCase()).includes(e.toLowerCase()):this.ignoredHeaders}}let en=(e,t)=>{let s="";switch(e){case"":case"text":case"json":"string"==typeof t?s=t:sn(t)||Array.isArray(t)?s=JSON.stringify(t):void 0!==t&&(s=Object.prototype.toString.call(t));break;default:void 0!==t&&(s=Object.prototype.toString.call(t))}return s},tn=e=>{if(!e)return null;let t;if("string"==typeof e){"{"!==e[0]&&"["!==e[0]||(t=e);var s=e.split("&");t=1===s.length?e:s.join(",")}else if((e=>null!=e&&!ArrayBuffer.isView(e)&&"undefined"!=typeof Symbol&&"function"==typeof e[Symbol.iterator])(e)){var i,n,r=[];for([i,n]of e)r.push(i+"="+("string"==typeof n?n:"[object Object]"));t=r.join(",")}else t=e instanceof Blob||e instanceof ReadableStream||e instanceof ArrayBuffer?"byte data":sn(e)?e:"can't parse body "+typeof e;return t};function sn(e){return null!==e&&"object"==typeof e}function nn(e){return e<=0?"":1e6<=e?(e/1e3/1e3).toFixed(1)+" MB":1e3<=e?(e/1e3).toFixed(1)+" KB":e+"B"}let rn=e=>(e=e.startsWith("//")?""+new URL(window.location.href).protocol+e:e).startsWith("http")?new URL(e):new URL(e,window.location.href);class an{constructor(e,t,s,i,n){this.ignoredHeaders=e,this.setSessionTokenHeader=t,this.sanitize=s,this.sendMessage=i,this.isServiceUrl=n}apply(e,t,s){var i=s[0],n=s[1],r=new Zi(this.ignoredHeaders,this.setSessionTokenHeader,this.sanitize);if(this.isServiceUrl(i))return e.apply(t,s);var a=rn(i);if(r.method="POST",r.url=i,r.name=(a.pathname.split("/").pop()||"")+a.search,r.requestType="beacon",r.requestHeader={"Content-Type":(i=n)instanceof Blob?i.type:i instanceof FormData?"multipart/form-data":i instanceof URLSearchParams?"application/x-www-form-urlencoded;charset=UTF-8":"text/plain;charset=UTF-8"},r.status=0,r.statusText="Pending",a.search&&a.searchParams){r.getData={};for(var[o,h]of a.searchParams)r.getData[o]=h}r.requestData=tn(n),r.startTime||(r.startTime=performance.now());i=e.apply(t,s),i?(r.endTime=performance.now(),r.duration=r.endTime-(r.startTime||r.endTime),r.status=0,r.statusText="Sent",r.readyState=4):(r.status=500,r.statusText="Unknown"),a=r.getMessage();return a&&this.sendMessage(a),i}}class on{static create(e,t,s,i,n,r){if(e)return new Proxy(e,new an(t,s,i,n,r))}}(t=f=f||{})[t.UNSENT=0]="UNSENT",t[t.OPENED=1]="OPENED",t[t.HEADERS_RECEIVED=2]="HEADERS_RECEIVED",t[t.LOADING=3]="LOADING",t[t.DONE=4]="DONE";class hn{constructor(e,t){this.resp=e,this.item=t,this.mockReader()}set(e,t,s){return Reflect.set(e,t,s)}get(e,t){let s=Reflect.get(e,t);switch(t){case"arrayBuffer":case"blob":case"formData":case"json":case"text":return()=>(this.item.responseType=t.toLowerCase(),s.apply(e).then(e=>(this.item.response=en(this.item.responseType,e),e)))}return"function"==typeof s?s.bind(e):s}mockReader(){let i;if(this.resp.body&&"function"==typeof this.resp.body.getReader){var t=this.resp.clone();let e=t.body.getReader;t.body.getReader=()=>{let s=e.apply(this.resp.body);if(this.item.readyState!==f.DONE){let e=s.read,t=s.cancel;this.item.responseType="arraybuffer",s.read=()=>e.apply(s).then(e=>{var t;return i=i?((t=new Uint8Array(i.length+e.value.length)).set(i),t.set(e.value,i.length),t):new Uint8Array(e.value),this.item.endTime=performance.now(),this.item.duration=this.item.endTime-(this.item.startTime||this.item.endTime),this.item.readyState=e.done?4:3,this.item.statusText=e.done?String(this.item.status):"Loading",this.item.responseSize=i.length,this.item.responseSizeText=nn(this.item.responseSize),e.done&&(this.item.response=en(this.item.responseType,i)),e}),s.cancel=(...e)=>(this.item.cancelState=2,this.item.statusText="Cancel",this.item.endTime=performance.now(),this.item.duration=this.item.endTime-(this.item.startTime||this.item.endTime),this.item.response=en(this.item.responseType,i),t.apply(s,e))}return s}}}}class ln{constructor(e,t,s,i,n,r){this.ignoredHeaders=e,this.setSessionTokenHeader=t,this.sanitize=s,this.sendMessage=i,this.isServiceUrl=n,this.tokenUrlMatcher=r}apply(e,t,s){var i=s[0],n=s[1];if(!i||"string"!=typeof i&&(null==i||!i.url))return e.apply(window,s);if(i instanceof URL||"string"==typeof i?this.isServiceUrl(String(i)):this.isServiceUrl(String(i.url)))return e.apply(window,s);let r=new Zi(this.ignoredHeaders,this.setSessionTokenHeader,this.sanitize);return this.beforeFetch(r,i,n),this.setSessionTokenHeader((e,t)=>{if(void 0===this.tokenUrlMatcher||this.tokenUrlMatcher(r.url))return void 0===s[1]&&s[0]instanceof Request?s[0].headers.append(e,t):(s[1]||(s[1]={}),void 0===s[1].headers&&(s[1]=Object.assign(Object.assign({},s[1]),{headers:{}})),void(s[1].headers instanceof Headers?s[1].headers.append(e,t):Array.isArray(s[1].headers)?s[1].headers.push([e,t]):s[1].headers[e]=t))}),e.apply(window,s).then(this.afterFetch(r)).catch(e=>{throw r.endTime=performance.now(),r.duration=r.endTime-(r.startTime||r.endTime),e})}beforeFetch(e,t,s){let i,n="GET",r={};if(r="string"==typeof t?(n=(null==s?void 0:s.method)||"GET",i=rn(t),(null==s?void 0:s.headers)||{}):(n=t.method||"GET",i=rn(t.url),t.headers),e.method=n,e.requestType="fetch",e.requestHeader=r,e.url=i.toString(),e.name=(i.pathname.split("/").pop()||"")+i.search,e.status=0,e.statusText="Pending",e.readyState=1,e.startTime||(e.startTime=performance.now()),"[object Headers]"===Object.prototype.toString.call(r)){e.requestHeader={};for(var[a,o]of r)e.requestHeader[a]=o}else e.requestHeader=r;if(i.search&&i.searchParams){e.getData={};for(var[h,l]of i.searchParams)e.getData[h]=l}null!=s&&s.body&&(e.requestData=tn(s.body))}afterFetch(n){return e=>{n.endTime=performance.now(),n.duration=n.endTime-(n.startTime||n.endTime),n.status=e.status,n.statusText=String(e.status);let t=!1;n.header={};for(var[s,i]of e.headers)n.header[s]=i,t=-1<i.toLowerCase().indexOf("chunked")||t;return t?n.readyState=3:(n.readyState=4,this.handleResponseBody(e.clone(),n).then(e=>{n.responseSize="string"==typeof e?e.length:e.byteLength,n.responseSizeText=nn(n.responseSize),n.response=en(n.responseType,e);e=n.getMessage();e&&this.sendMessage(e)}).catch(e=>{if("AbortError"!==e.name)throw e})),new Proxy(e,new hn(e,n))}}handleResponseBody(e,t){var s=e.headers.get("content-type");return s&&s.includes("application/json")?(t.responseType="json",e.text()):s&&(s.includes("text/html")||s.includes("text/plain"))?(t.responseType="text",e.text()):(t.responseType="arraybuffer",e.arrayBuffer())}}class cn{static create(e,t,s,i,n,r){return new Proxy(fetch,new ln(e,t,s,i,n,r))}}class dn{constructor(e,t,s,i,n,r,a){this.ignoredHeaders=t,this.setSessionTokenHeader=s,this.sanitize=i,this.sendMessage=n,this.isServiceUrl=r,this.tokenUrlMatcher=a,this.XMLReq=e,this.XMLReq.onreadystatechange=()=>{this.onReadyStateChange()},this.XMLReq.onabort=()=>{this.onAbort()},this.XMLReq.ontimeout=()=>{this.onTimeout()},this.item=new Zi(t,s,i),this.item.requestType="xhr"}get(s,e){switch(e){case"open":return this.getOpen(s);case"send":return this.setSessionTokenHeader((e,t)=>{void 0!==this.tokenUrlMatcher&&!this.tokenUrlMatcher(this.item.url)||s.setRequestHeader(e,t)}),this.getSend(s);case"setRequestHeader":return this.getSetRequestHeader(s);default:var t=Reflect.get(s,e);return"function"==typeof t?t.bind(s):t}}set(e,t,s){switch(t){case"onreadystatechange":return this.setOnReadyStateChange(e,t,s);case"onabort":return this.setOnAbort(e,t,s);case"ontimeout":return this.setOnTimeout(e,t,s)}return Reflect.set(e,t,s)}onReadyStateChange(){var e;this.item.url&&this.isServiceUrl(this.item.url)||(this.item.readyState=this.XMLReq.readyState,this.item.responseType=this.XMLReq.responseType,this.item.endTime=performance.now(),this.item.duration=this.item.endTime-this.item.startTime,this.updateItemByReadyState(),setTimeout(()=>{this.item.response=en(this.item.responseType,this.item.response)},0),this.XMLReq.readyState===f.DONE&&(e=this.item.getMessage())&&this.sendMessage(e))}onAbort(){this.item.cancelState=1,this.item.statusText="Abort";var e=this.item.getMessage();e&&this.sendMessage(e)}onTimeout(){this.item.cancelState=3,this.item.statusText="Timeout";var e=this.item.getMessage();e&&this.sendMessage(e)}getOpen(i){let n=Reflect.get(i,"open");return(...e)=>{var t=e[0],s=e[1];return this.item.method=t?t.toUpperCase():"GET",this.item.url=(null==(t=s.toString)?void 0:t.call(s))||"",this.item.name=null!=(s=null==(t=this.item.url)?void 0:t.replace(new RegExp("/*$"),"").split("/").pop())?s:"",this.item.getData=((e,t)=>{sn(t)||(t={});let s=e?e.split("?"):[];if(s.shift(),0<s.length)for(var i of s=s.join("?").split("&")){i=i.split("=");try{t[i[0]]=decodeURIComponent(i[1])}catch(e){t[i[0]]=i[1]}}return t})(this.item.url,{}),n.apply(i,e)}}getSend(s){let i=Reflect.get(s,"send");return(...e)=>{var t=e[0];return this.item.requestData=tn(t),i.apply(s,e)}}getSetRequestHeader(t){let s=Reflect.get(t,"setRequestHeader");return(...e)=>(this.item.requestHeader||(this.item.requestHeader={}),this.item.requestHeader[e[0]]=e[1],s.apply(t,e))}setOnReadyStateChange(t,e,s){return Reflect.set(t,e,(...e)=>{this.onReadyStateChange(),null!=s&&s.apply(t,e)})}setOnAbort(t,e,s){return Reflect.set(t,e,(...e)=>{this.onAbort(),s.apply(t,e)})}setOnTimeout(t,e,s){return Reflect.set(t,e,(...e)=>{this.onTimeout(),s.apply(t,e)})}updateItemByReadyState(){switch(this.XMLReq.readyState){case f.UNSENT:case f.OPENED:this.item.status=f.UNSENT,this.item.statusText="Pending",this.item.startTime||(this.item.startTime=performance.now());break;case f.HEADERS_RECEIVED:this.item.status=this.XMLReq.status,this.item.statusText="Loading",this.item.header={};var t=(this.XMLReq.getAllResponseHeaders()||"").split("\n");for(let e=0;e<t.length;e++){var s,i=t[e];i&&(s=(i=i.split(": "))[0],this.item.header[s]=i.slice(1).join(": "))}break;case f.LOADING:this.item.status=this.XMLReq.status,this.item.statusText="Loading",this.XMLReq.response&&this.XMLReq.response.length&&(this.item.responseSize=this.XMLReq.response.length,this.item.responseSizeText=nn(this.item.responseSize));break;case f.DONE:this.item.status=this.XMLReq.status||this.item.status||0,this.item.statusText=String(this.item.status),this.item.endTime=performance.now(),this.item.duration=this.item.endTime-(this.item.startTime||this.item.endTime),this.item.response=this.XMLReq.response,this.XMLReq.response&&this.XMLReq.response.length&&(this.item.responseSize=this.XMLReq.response.length,this.item.responseSizeText=nn(this.item.responseSize));break;default:this.item.status=this.XMLReq.status,this.item.statusText="Unknown"}}}class un{static create(t,s,i,n,r,a){return new Proxy(XMLHttpRequest,{construct(e){e=new e;return new Proxy(e,new dn(e,t,s,i,n,r,a))}})}}let pn=e=>{console.warn(`Openreplay: Can't find ${e} in global context.`)};function fn(e){return e.__or_req_data__||(e.__or_req_data__={body:void 0,headers:{}}),e.__or_req_data__}function gn(e){return"string"==typeof e?e.toUpperCase():"GET"}function mn(m,e={}){if(!e.disabled){let p=Object.assign({failuresOnly:!1,ignoreHeaders:["cookie","set-cookie","authorization"],capturePayload:!1,sessionTokenHeader:!1,captureInIframes:!0,axiosInstances:void 0,useProxy:!0},e),f=(!1===p.useProxy&&m.debug.warn("Network module is migrating to proxy api, to gradually migrate and test it set useProxy to true"),p.ignoreHeaders),g=Array.isArray(f)?e=>f.includes(e):()=>f,s=!0===p.sessionTokenHeader?"X-OpenReplay-SessionToken":p.sessionTokenHeader;e=n=>{if(p.useProxy)r=n,a=!!m.sanitizer.privateMode||p.ignoreHeaders,o=v,h=y,l=e=>{var t;p.failuresOnly&&e.status<400||(t=m.sanitizer.privateMode?"************":e.url,m.send(at(e.requestType,e.method,t,e.request,e.response,e.status,e.startTime+T,e.duration,e.responseSize)))},c=e=>m.isServiceURL(e),d={xhr:!0,fetch:!0,beacon:!0},u=p.tokenUrlMatcher,r&&(d.xhr&&(r.XMLHttpRequest?r.XMLHttpRequest=un.create(a,o,h,l,c,u):pn("XMLHttpRequest")),d.fetch&&(r.fetch?r.fetch=cn.create(a,o,h,l,c,u):pn("fetch")),d.beacon)&&null!=(u=r.navigator)&&u.sendBeacon&&(d=r.navigator.sendBeacon,r.navigator.sendBeacon=on.create(d,a,o,h,l,c));else{var r,a,o,h,l,c,d,u;let e=n.fetch.bind(n);n.fetch=(a,o={})=>{if(!("string"==typeof a||a instanceof URL)||m.isServiceURL(String(a)))return e(a,o);v(function(e,t){void 0===o.headers&&(o.headers={}),o.headers instanceof Headers?o.headers.append(e,t):Array.isArray(o.headers)?o.headers.push([e,t]):o.headers[e]=t});let h=performance.now();return e(a,o).then(e=>{let s=performance.now()-h;if(!(p.failuresOnly&&e.status<400)){let r=e.clone();r.text().then(e=>{let i={},n={};if(!0!==f){let s=([e,t])=>{g(e)||(i[e]=t)};o.headers instanceof Headers?o.headers.forEach((e,t)=>s([t,e])):Array.isArray(o.headers)?o.headers.forEach(s):"object"==typeof o.headers&&Object.entries(o.headers).forEach(s),r.headers.forEach((e,t)=>{g(t)||(n[t]=e)})}var t=gn(o.method),e=y({url:String(a),method:t,status:r.status,request:{headers:i,body:o.body||null},response:{headers:n,body:e}});e&&m.send(at("fetch",t,String(e.url),b(e.request),b(e.response),r.status,h+T,s,0))}).catch(e=>m.debug.error("Could not process Fetch response:",e))}return e})};let t=n.XMLHttpRequest.prototype.open,s=n.XMLHttpRequest.prototype.setRequestHeader,i=n.XMLHttpRequest.prototype.send;p.axiosInstances||(n.XMLHttpRequest.prototype.open=function(r,a){let o=this,h=(v((e,t)=>o.setRequestHeader(e,t)),0);return o.addEventListener("loadstart",e=>{h=e.timeStamp}),o.addEventListener("load",m.safe(e=>{var{headers:t,body:s}=fn(o),e=0<h?e.timeStamp-h:0,i=(o.getAllResponseHeaders()||"").trim().split(/[\r\n]+/);let n={};i.forEach(function(e){var e=e.split(": "),t=e.shift();g(t)||(n[t]=e.join(": "))});i=gn(r),t=y({url:String(a),method:i,status:o.status,request:{headers:t,body:s||null},response:{headers:n,body:o.response}});t&&m.send(at("xhr",i,String(t.url),b(t.request),b(t.response),o.status,h+T,e,0))})),t.apply(this,arguments)},n.XMLHttpRequest.prototype.send=function(e){return fn(this).body=e,i.apply(this,arguments)},n.XMLHttpRequest.prototype.setRequestHeader=function(e,t){return g(e)||(fn(this).headers[e]=t),s.apply(this,arguments)})}};function v(e){var t;s&&(t=m.getSessionToken())&&m.safe(e)(s,t)}function y(e){if(p.capturePayload&&!m.sanitizer.privateMode||(delete e.request.body,delete e.response.body),p.sanitizer){var t=e.response.body;if("string"==typeof t)try{e.response.body=JSON.parse(t)}catch{}return p.sanitizer(e)}return e}function b(e){if(e&&"string"!=typeof e.body)try{e.body=JSON.stringify(e.body)}catch{e.body="<unable to stringify>",m.notify.warn("Openreplay fetch couldn't stringify body:",e.body)}return JSON.stringify(e)}e(window),p.axiosInstances&&p.axiosInstances.forEach(e=>{Xi(m,e,p,y,b)}),p.captureInIframes&&m.observer.attachContextCallback(m.safe(e))}}let vn="/en/sdk";class yn{constructor(n){if(this.options=n,this.app=null,this.crossdomainMode=!1,this.checkDoNotTrack=()=>this.options.respectDoNotTrack&&("1"==navigator.doNotTrack||"1"==window.doNotTrack),this.signalStartIssue=(e,t)=>{var s=this.checkDoNotTrack();console.log("Tracker couldn't start due to:",JSON.stringify({trackerVersion:"16.4.1",projectKey:this.options.projectKey,doNotTrack:s,reason:t.length?"missing api: "+t.join(","):e}))},this.restartCanvasTracking=()=>{null!==this.app&&this.app.restartCanvasTracking()},this.handleError=(e,t={})=>{var s;null!==this.app&&(e instanceof Error?(s=qs(e,[],t),this.app.send(s)):(e instanceof ErrorEvent||"PromiseRejectionEvent"in window&&e instanceof PromiseRejectionEvent)&&null!=(s=Ws(e,void 0,t))&&this.app.send(s))},this.crossdomainMode=Boolean(Ue()&&n.crossdomain?.enabled),h&&(e=>{if(null!=e){if("string"!=typeof e.projectKey)if("number"!=typeof e.projectKey){if("number"!=typeof e.projectID)return console.error(`OpenReplay: projectKey is missing or wrong type (string is expected). Please, check ${Fe}${vn} for more information.`),0;e.projectKey=e.projectID.toString(),I("`projectID` option","`projectKey` option",vn)}else console.warn("OpenReplay: projectKey is expected to have a string type."),e.projectKey=e.projectKey.toString();return null!=e.sessionToken&&I("`sessionToken` option","`sessionHash` start() option","/"),1}console.error("OpenReplay: invalid options argument type. Please, check documentation on "+Fe+vn)})(n))if(window.__OPENREPLAY__||!this.crossdomainMode&&Ue()&&(()=>{try{return Boolean(window.top?.document)}catch{return!1}})()&&window.top.__OPENREPLAY__)console.error("OpenReplay: one tracker instance has been initialised already");else if(n.__DISABLE_SECURE_MODE||"https:"===location.protocol){var t,e=[];if(this.checkDoNotTrack())e.push("doNotTrack");else for(var s of["Map","Set","MutationObserver","performance","timing","startsWith","Blob","Worker"])if("timing"===s){if("performance"in window&&!(s in performance)){e.push(s);break}}else if("startsWith"===s){if(!(s in String.prototype)){e.push(s);break}}else if(!(s in window)){e.push(s);break}if(0<e.length)t=e.join(","),console.error("OpenReplay: browser doesn't support API required for tracking or doNotTrack is set to 1. Reason: "+t),this.signalStartIssue("missing_api",e);else{let h=new Ns(n.projectKey,n.sessionToken,n,this.signalStartIssue,this.crossdomainMode);if(this.app=h,!this.crossdomainMode){{var l=h;let i,s,n,r,a=document.referrer,e=l.safe(()=>{var e,t,s=document.URL;s!==i&&(i=s,s=l.sanitizer.privateMode?Ss(document.title):document.title,e=l.sanitizer.privateMode?Ss(i):i,t=l.sanitizer.privateMode?Ss(a):a,l.send([122,e,t,r,s]),r=0,a=i)}),t=l.safe(()=>{var{innerWidth:e,innerHeight:t}=window;e===s&&t===n||(s=e,n=t,l.send([5,s,n]))}),o=void 0===document.hidden?Function.prototype:l.safe(()=>l.send([55,document.hidden]));l.attachStartCallback(()=>{i=null,r=T,s=n=-1,e(),t(),o()}),void 0!==document.hidden&&l.attachEventListener(document,"visibilitychange",o,!1,!1),l.ticker.attach(e,1,!1),l.ticker.attach(t,5,!1)}{var r=h;let e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;void 0!==e&&((t=()=>r.send([54,Math.round(1e3*e.downlink),e.type||"unknown"]))(),e.addEventListener("change",t))}var a=h,d=n;if(Object.assign({capturePerformance:!0},d).capturePerformance){let e,t,s=()=>{void 0!==e&&-1!==e&&(e++,requestAnimationFrame(s))},i=(a.ticker.attach(()=>{void 0!==t&&-1!==t&&t++},0,!1),()=>{void 0!==e&&void 0!==t&&(a.send([49,e,t,ut.memory.totalJSHeapSize||0,ut.memory.usedJSHeapSize||0]),t=e=document.hidden?-1:0)});a.attachStartCallback(()=>{t=e=-1,i(),s()}),a.attachStopCallback(()=>{t=e=void 0}),a.ticker.attach(i,165,!1),void 0!==document.hidden&&a.attachEventListener(document,"visibilitychange",i,!1,!1)}(o=h).attachEventListener(window,"focus",function(){document.hidden||(o.debug.log("Openreplay: tab change to"+o.session.getTabId()),o.send(ct(o.session.getTabId())))},!1,!1)}si(h,n.mouse),$i(h,this.crossdomainMode);var o,c,u=h,d=n.css;if(null!==u)if(window.CSSStyleSheet){let o={...Vi,...d},h=new Map,e=null,l=new Set,t=o.checkCssInterval||200,c={},s=/{\s*}/,r=u.safe((e,t,s)=>{var i=cs.get(e);i&&("string"==typeof s?(u.send(st(i,s,t,u.getBaseHref())),f(s)&&(h.set(i+":"+t,s),l.add(e))):(u.send(it(i,t)),h.has(i+":"+t)&&h.delete(i+":"+t)))}),a=u.safe(e=>{let t=e;for(;t.parentRule;)t=t.parentRule;var s,i,n,e=t.parentStyleSheet;e&&(s=cs.get(e))&&(i=t.cssText,0<=(n=Array.from(e.cssRules).indexOf(t)))&&(u.send(st(s,i,n,u.getBaseHref())),u.send(it(s,n+1)),f(i))&&(h.set(s+":"+n,i),l.add(e))});d=u.safe(e=>{if(!e.__css_tracking_patched__){e.__css_tracking_patched__=!0;let{insertRule:s,deleteRule:t}=e.CSSStyleSheet.prototype,{insertRule:i,deleteRule:n}=e.CSSGroupingRule.prototype;e.CSSStyleSheet.prototype.insertRule=function(e,t=0){t=s.call(this,e,t);return r(this,t,e),t},e.CSSStyleSheet.prototype.deleteRule=function(e){return r(this,e),t.call(this,e)},e.CSSGroupingRule.prototype.insertRule=function(e,t=0){e=i.call(this,e,t);return a(this),e},e.CSSGroupingRule.prototype.deleteRule=function(e){e=n.call(this,e);return a(this),e}}});function p(){var s,e;if(o.scanInMemoryCSS)for(s of l.values())try{let t=cs.get(s);if(t){if(o.checkLimit&&(c[t]?c[t]++:c[t]=0,c[t]>o.checkLimit))return void l.delete(s);for(let e=0;e<s.cssRules.length;e++)try{var i=s.cssRules[e],n=t+":"+e,r=h.get(n),a=i.cssText;r!==a&&(void 0!==r&&u.send(it(t,e)),u.send(st(t,a,e,u.getBaseHref())),h.set(n,a))}catch(e){}for(e of Array.from(h.keys()).filter(e=>e.startsWith(t+":")))parseInt(e.split(":")[1],10)>=s.cssRules.length&&h.delete(e)}}catch(e){l.delete(s)}}function f(e){return s.test(e)}d(window),u.observer.attachContextCallback(d),u.nodes.attachNodeCallback(e=>{if(x(e,"style")&&e.sheet&&!(null!==e.textContent&&0<e.textContent.trim().length)){var t=u.nodes.getID(e);if(t){var s=e.sheet,e=hs++;cs.set(s,e),u.send(nt(e,t));for(let e=0;e<s.cssRules.length;e++)try{r(s,e,s.cssRules[e].cssText)}catch(e){}}}}),setTimeout(function(){!e&&o.scanInMemoryCSS&&(e=window.setInterval(p,t))},50),u.attachStopCallback(()=>{e&&(clearInterval(e),e=null),h.clear()})}else u.send([63,"no_stylesheet_prototype_in_window",""]);var g=h;if(null!==g&&os(document)){let l=new Map,c=new Map,s=h=>setTimeout(()=>{let s=g.nodes.getID(h);if(void 0!==(s=h===document?0:s)){let e=c.get(s);e=e||[];var i=[],t=h.adoptedStyleSheets;if(t&&Symbol.iterator in t)for(var n of t){let t=l.get(n);var r=!t;if(t||(t=hs++,l.set(n,t)),e.includes(t)||g.send(nt(t,s)),r){var a=n.cssRules;for(let e=0;e<a.length;e++)g.send(st(t,a[e].cssText,e,g.getBaseHref()))}i.push(t)}if(Symbol.iterator in e)for(var o of e)i.includes(o)||g.send([77,o,s]);c.set(s,i)}},20);var m=t=>{if(!t.__openreplay_adpss_patched__){t.__openreplay_adpss_patched__=!0,v(t.Document.prototype),v(t.ShadowRoot.prototype);let{replace:e,replaceSync:s}=t.CSSStyleSheet.prototype;t.CSSStyleSheet.prototype.replace=function(s){return e.call(this,s).then(e=>{var t=l.get(this);return t&&g.send(tt(t,s,g.getBaseHref())),e})},t.CSSStyleSheet.prototype.replaceSync=function(e){var t=l.get(this);return t&&g.send(tt(t,e,g.getBaseHref())),s.call(this,e)}}};function v(e){let t=Object.getOwnPropertyDescriptor(e,"adoptedStyleSheets");t&&Object.defineProperty(e,"adoptedStyleSheets",{...t,set:function(e){e=t.set.call(this,e);return s(this),e}})}m(window),g.observer.attachContextCallback(g.safe(m)),g.attachStopCallback(()=>{l.clear(),c.clear()}),g.attachStartCallback(()=>{s(document)}),g.nodes.attachNodeCallback(e=>{os(e)&&s(e)})}Ls(h,n),c=h,Object.assign({captureExceptions:!0},n).captureExceptions&&(c.observer.attachContextCallback(k),k(window)),Vs(h),Gs(h,n),qi(h,n);{var y=h;function b(e){e=y.nodes.getID(e);void 0!==e&&y.send(Qe(e))}let t=!1;y.nodes.attachNodeCallback(e=>{x(e,"body")&&(y.nodes.attachNodeListener(e,"focus",e=>{Xt(e.target)&&(b(e.target),t=!1)}),y.nodes.attachNodeListener(e,"blur",e=>{null===e.relatedTarget&&(t=!0,setTimeout(()=>{t&&y.send(Qe(-1))},0))}))}),y.attachStartCallback(()=>{let e=document.activeElement;for(;e&&x(e,"iframe")&&e.contentDocument;)e=e.contentDocument.activeElement;e&&e!==e.ownerDocument.body&&b(e)},!0)}var S,w=h;if(window.FontFace){let r=new Map;m=n=>{class e extends n.FontFace{constructor(...t){if("string"==typeof t[1]){let e="";t[2]&&w.safe(()=>{e=JSON.stringify(t[2])});var s=[t[0],t[1],e],i=r.get(n.document)||[],i=(i.push(s),r.set(n.document,i),n===window?0:w.nodes.getID(n.document));if(void 0===i)return;w.active()&&w.send(Ge(i,...s))}super(...t)}}n.FontFace=e};w.observer.attachContextCallback(m),m(window),w.nodes.attachNodeCallback(w.safe(e=>{if(Gt(e)){var s=r.get(e);if(s){let t=e.defaultView===window?0:w.nodes.getID(e);void 0!==t&&s.forEach(e=>{w.send(Ge(t,...e))})}}}))}n.network?.disabled||mn(h,n.network),(S=h).attachEventListener(document,"selectionchange",()=>{var e,t,s=document.getSelection();null===s||s.isCollapsed?S.send(ot(-1,-1,"")):(e=S.nodes.getID(s.anchorNode),t=S.nodes.getID(s.focusNode),s=s.toString().replace(/\s+/g," "),e&&t&&S.send(ot(e,t,s)))}),window.__OPENREPLAY__=this,n.flags&&n.flags.onFlagsLoad&&this.onFlagsLoad(n.flags.onFlagsLoad);let i=window.open;function k(t){function e(e){e=Ws(e,t);null!=e&&c.send(e)}try{c.attachEventListener(t,"unhandledrejection",e),c.attachEventListener(t,"error",e)}catch(e){console.error("Error while attaching to error proto contexts",e)}}(n.autoResetOnWindowOpen||n.resetTabOnWindowOpen)&&(h.attachStartCallback(()=>{let t=h.getTabId(),s=h.sessionStorage??window.sessionStorage;window.open=function(...e){return n.autoResetOnWindowOpen&&h.resetNextPageSession(!0),n.resetTabOnWindowOpen&&s.removeItem(n.session_tabid_key||"__openreplay_tabid"),h.resetNextPageSession(!1),s.setItem(n.session_tabid_key||"__openreplay_tabid",t),i.call(window,...e)}}),h.attachStopCallback(()=>{window.open=i}))}}else console.error("OpenReplay: Your website must be publicly accessible and running on SSL in order for OpenReplay to properly capture and replay the user session. You can disable this check by setting `__DISABLE_SECURE_MODE` option to `true` if you are testing in localhost. Keep in mind, that asset files on a local machine are not available to the outside world. This might affect tracking if you use css files.")}isFlagEnabled(e){return this.featureFlags.isFlagEnabled(e)}onFlagsLoad(e){this.app?.featureFlags.onFlagsLoad(e)}clearPersistFlags(){this.app?.featureFlags.clearPersistFlags()}reloadFlags(){return this.app?.featureFlags.reloadFlags()}getFeatureFlag(e){return this.app?.featureFlags.getFeatureFlag(e)}getAllFeatureFlags(){return this.app?.featureFlags.flags}use(e){return e(this.app,this.options)}isActive(){return null!==this.app&&this.app.active()}trackWs(e){if(null!==this.app)return this.app.trackWs(e)}start(e){return this.browserEnvCheck()?null===this.app?Promise.reject("Browser doesn't support required api, or doNotTrack is active."):this.app.start(e):Promise.reject("Trying to start not in browser.")}browserEnvCheck(){return h||(console.error("OpenReplay: you are trying to start Tracker on a node.js environment. If you want to use OpenReplay with SSR, please, use componentDidMount or useEffect API for placing the `tracker.start()` line. Check documentation on "+Fe+vn),!1)}coldStart(e,t){return this.browserEnvCheck()?null===this.app?Promise.reject("Tracker not initialized"):void this.app.coldStart(e,t):Promise.reject("Trying to start not in browser.")}startOfflineRecording(e,t){return this.browserEnvCheck()?null===this.app?Promise.reject("Tracker not initialized"):this.app.offlineRecording(e,t):Promise.reject("Trying to start not in browser.")}uploadOfflineRecording(){if(null!==this.app)return this.app.uploadOfflineRecording()}stop(){if(null!==this.app)return this.app.stop(),this.app.session.getSessionHash()}forceFlushBatch(){null!==this.app&&this.app.forceFlushBatch()}getSessionToken(){return null===this.app?null:this.app.getSessionToken()}getSessionInfo(){return null===this.app?null:this.app.session.getInfo()}getSessionID(){return null===this.app?null:this.app.getSessionID()}getTabId(){return null===this.app?null:this.app.getTabId()}getUxId(){return null===this.app?null:this.app.getUxtId()}sessionID(){return I("'sessionID' method","'getSessionID' method","/"),this.getSessionID()}getSessionURL(e){if(null!==this.app)return this.app.getSessionURL(e)}setUserID(e){"string"==typeof e&&null!==this.app&&this.app.session.setUserID(e)}userID(e){I("'userID' method","'setUserID' method","/"),this.setUserID(e)}setUserAnonymousID(e){"string"==typeof e&&null!==this.app&&this.app.send([29,e])}userAnonymousID(e){I("'userAnonymousID' method","'setUserAnonymousID' method","/"),this.setUserAnonymousID(e)}setMetadata(e,t){"string"==typeof e&&"string"==typeof t&&null!==this.app&&this.app.session.setMetadata(e,t)}metadata(e,t){I("'metadata' method","'setMetadata' method","/"),this.setMetadata(e,t)}event(e,t=null,s=!1){if("string"==typeof e&&null!==this.app){if(s)return this.issue(e,t);try{t=JSON.stringify(t)}catch(e){return}this.app.send([27,e,t])}}issue(e,t=null){if("string"==typeof e&&null!==this.app){try{t=JSON.stringify(t)}catch(e){return}this.app.send([64,e,t])}}}!function(e){var e=0<arguments.length&&void 0!==e?e:{},t=window.OpenReplay||window.asayer,s=t.shift(),i=t.shift();let n=t.shift();"object"==typeof(n="number"==typeof n?{obscureTextEmails:!!(1&n),obscureTextNumbers:!!(n>>1&1),obscureInputNumbers:!!(n>>3&1),obscureInputEmails:!!(n>>4&1),defaultInputMode:n>>5}:n)&&null!==n||(n={});var s=Object.assign({projectKey:s,sessionToken:i},n,e,{__is_snippet:!0}),r=(t.i&&(s.ingestPoint=t.i),window.OpenReplay=window.asayer=new yn(s));let a=r.setUserID.bind(r),o=(r.setUserID=r.userID=e=>{null!==e&&a(e.toString())},r.setUserAnonymousID.bind(r)),h=(r.setUserAnonymousID=r.userAnonymousID=e=>{null!==e&&o(e.toString())},r.setMetadata.bind(r)),l=(r.setMetadata=r.metadata=(e,t)=>{"number"==typeof t&&NaN!==t&&(t=t.toString()),"string"==typeof e&&"string"==typeof t&&h(e,t)},r.event.bind(r));for(r.event=(e,t)=>{"string"==typeof e&&l(e,t)};t.length;){var c=t.shift();switch(c[0]){case 0:r.start(c[1]);break;case 1:r.stop();break;case 2:r.setUserID(c[1]);break;case 3:r.setUserAnonymousID(c[1]);break;case 4:r.setMetadata(c[1],c[2]);break;case 5:r.event(c[1],c[2],c[3]);break;case 6:r.issue(c[1],c[2])}}}()})();