const metadata = params => ({
  breakpoints: [{
    hash: "1kfa1md",
    mediaQuery: "(min-width: 1920px)"
  }, {
    hash: "xuqmhe",
    mediaQuery: "(min-width: 1280px) and (max-width: 1919px)"
  }, {
    hash: "zykb3b",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "18kw8xt",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | Marketplace | The all-in-one tutor platform for private tutors and group classes",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};