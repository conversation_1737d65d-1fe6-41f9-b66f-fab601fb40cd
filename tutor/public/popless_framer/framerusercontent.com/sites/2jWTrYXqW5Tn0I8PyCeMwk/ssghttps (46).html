import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter-Bold"]);
export const fonts = [];
export const css = [".framer-y4sJy h2.framer-styles-preset-1m9bzi2, .framer-y4sJy .framer-styles-preset-1m9bzi2 h2 { --framer-font-family: \"Inter-Bold\", \"Inter\", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 28px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 1.4em; --framer-text-alignment: start; }"];
export const className = "framer-y4sJy";
export const __FramerMetadata__ = {
  "exports": {
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./stylesPresetHeading2.map