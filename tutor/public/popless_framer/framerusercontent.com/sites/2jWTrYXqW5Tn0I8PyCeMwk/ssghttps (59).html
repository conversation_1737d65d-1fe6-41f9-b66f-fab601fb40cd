const metadata = params => ({
  breakpoints: [{
    hash: "1dq2i2x",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "1dba2xq",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "o1m2nr",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  robots: "noindex",
  title: "Popless | Careers",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};