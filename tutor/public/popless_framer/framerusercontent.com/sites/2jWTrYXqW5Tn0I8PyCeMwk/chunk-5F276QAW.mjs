import{a as nm,b as Hr,c as W}from"./chunk-OIST4OYN.mjs";var We={};nm(We,{Children:()=>$r,Component:()=>ue,Fragment:()=>Hn,Profiler:()=>bm,PureComponent:()=>Ls,StrictMode:()=>Sm,Suspense:()=>wm,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:()=>Cm,cloneElement:()=>Ut,createContext:()=>fe,createElement:()=>re,createFactory:()=>Tm,createRef:()=>Os,default:()=>h,forwardRef:()=>Ie,isValidElement:()=>mr,lazy:()=>Em,memo:()=>km,startTransition:()=>Ur,unstable_act:()=>Rm,useCallback:()=>oe,useContext:()=>M,useDebugValue:()=>_m,useDeferredValue:()=>Pm,useEffect:()=>N,useId:()=>Wt,useImperativeHandle:()=>Im,useInsertionEffect:()=>jt,useLayoutEffect:()=>Wr,useMemo:()=>ne,useReducer:()=>Fm,useRef:()=>O,useState:()=>Ue,useSyncExternalStore:()=>Mm,useTransition:()=>Lm,version:()=>Om});var h={},Nr=Symbol.for("react.element"),om=Symbol.for("react.portal"),am=Symbol.for("react.fragment"),sm=Symbol.for("react.strict_mode"),lm=Symbol.for("react.profiler"),cm=Symbol.for("react.provider"),um=Symbol.for("react.context"),fm=Symbol.for("react.forward_ref"),dm=Symbol.for("react.suspense"),hm=Symbol.for("react.memo"),mm=Symbol.for("react.lazy"),Cs=Symbol.iterator;function pm(e){return e===null||typeof e!="object"?null:(e=Cs&&e[Cs]||e["@@iterator"],typeof e=="function"?e:null)}var ks={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Rs=Object.assign,_s={};function hr(e,t,r){this.props=e,this.context=t,this.refs=_s,this.updater=r||ks}hr.prototype.isReactComponent={};hr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ps(){}Ps.prototype=hr.prototype;function Wi(e,t,r){this.props=e,this.context=t,this.refs=_s,this.updater=r||ks}var ji=Wi.prototype=new Ps;ji.constructor=Wi;Rs(ji,hr.prototype);ji.isPureReactComponent=!0;var Ts=Array.isArray,Is=Object.prototype.hasOwnProperty,Gi={current:null},Fs={key:!0,ref:!0,__self:!0,__source:!0};function Ms(e,t,r){var n,i={},o=null,a=null;if(t!=null)for(n in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(o=""+t.key),t)Is.call(t,n)&&!Fs.hasOwnProperty(n)&&(i[n]=t[n]);var s=arguments.length-2;if(s===1)i.children=r;else if(1<s){for(var l=Array(s),c=0;c<s;c++)l[c]=arguments[c+2];i.children=l}if(e&&e.defaultProps)for(n in s=e.defaultProps,s)i[n]===void 0&&(i[n]=s[n]);return{$$typeof:Nr,type:e,key:o,ref:a,props:i,_owner:Gi.current}}function vm(e,t){return{$$typeof:Nr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Xi(e){return typeof e=="object"&&e!==null&&e.$$typeof===Nr}function gm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Es=/\/+/g;function Ui(e,t){return typeof e=="object"&&e!==null&&e.key!=null?gm(""+e.key):t.toString(36)}function Bn(e,t,r,n,i){var o=typeof e;o!=="undefined"&&o!=="boolean"||(e=null);var a=!1;if(e===null)a=!0;else switch(o){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Nr:case om:a=!0}}if(a)return a=e,i=i(a),e=n===""?"."+Ui(a,0):n,Ts(i)?(r="",e!=null&&(r=e.replace(Es,"$&/")+"/"),Bn(i,t,r,"",function(c){return c})):i!=null&&(Xi(i)&&(i=vm(i,r+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(Es,"$&/")+"/")+e)),t.push(i)),1;if(a=0,n=n===""?".":n+":",Ts(e))for(var s=0;s<e.length;s++){o=e[s];var l=n+Ui(o,s);a+=Bn(o,t,r,l,i)}else if(l=pm(e),typeof l=="function")for(e=l.call(e),s=0;!(o=e.next()).done;)o=o.value,l=n+Ui(o,s++),a+=Bn(o,t,r,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Dn(e,t,r){if(e==null)return e;var n=[],i=0;return Bn(e,n,"","",function(o){return t.call(r,o,i++)}),n}function ym(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){e._status!==0&&e._status!==-1||(e._status=1,e._result=r)},function(r){e._status!==0&&e._status!==-1||(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},zn={transition:null},xm={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:zn,ReactCurrentOwner:Gi};h.Children={map:Dn,forEach:function(e,t,r){Dn(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Dn(e,function(){t++}),t},toArray:function(e){return Dn(e,function(t){return t})||[]},only:function(e){if(!Xi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};h.Component=hr;h.Fragment=am;h.Profiler=lm;h.PureComponent=Wi;h.StrictMode=sm;h.Suspense=dm;h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=xm;h.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=Rs({},e.props),i=e.key,o=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,a=Gi.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)Is.call(t,l)&&!Fs.hasOwnProperty(l)&&(n[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)n.children=r;else if(1<l){s=Array(l);for(var c=0;c<l;c++)s[c]=arguments[c+2];n.children=s}return{$$typeof:Nr,type:e.type,key:i,ref:o,props:n,_owner:a}};h.createContext=function(e){return e={$$typeof:um,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:cm,_context:e},e.Consumer=e};h.createElement=Ms;h.createFactory=function(e){var t=Ms.bind(null,e);return t.type=e,t};h.createRef=function(){return{current:null}};h.forwardRef=function(e){return{$$typeof:fm,render:e}};h.isValidElement=Xi;h.lazy=function(e){return{$$typeof:mm,_payload:{_status:-1,_result:e},_init:ym}};h.memo=function(e,t){return{$$typeof:hm,type:e,compare:t===void 0?null:t}};h.startTransition=function(e){var t=zn.transition;zn.transition={};try{e()}finally{zn.transition=t}};h.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};h.useCallback=function(e,t){return Oe.current.useCallback(e,t)};h.useContext=function(e){return Oe.current.useContext(e)};h.useDebugValue=function(){};h.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};h.useEffect=function(e,t){return Oe.current.useEffect(e,t)};h.useId=function(){return Oe.current.useId()};h.useImperativeHandle=function(e,t,r){return Oe.current.useImperativeHandle(e,t,r)};h.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};h.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};h.useMemo=function(e,t){return Oe.current.useMemo(e,t)};h.useReducer=function(e,t,r){return Oe.current.useReducer(e,t,r)};h.useRef=function(e){return Oe.current.useRef(e)};h.useState=function(e){return Oe.current.useState(e)};h.useSyncExternalStore=function(e,t,r){return Oe.current.useSyncExternalStore(e,t,r)};h.useTransition=function(){return Oe.current.useTransition()};h.version="18.1.0";var $r=h.Children,ue=h.Component,Hn=h.Fragment,bm=h.Profiler,Ls=h.PureComponent,Sm=h.StrictMode,wm=h.Suspense,Cm=h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ut=h.cloneElement,fe=h.createContext,re=h.createElement,Tm=h.createFactory,Os=h.createRef,Ie=h.forwardRef,mr=h.isValidElement,Em=h.lazy,km=h.memo,Ur=h.startTransition,Rm=h.unstable_act,oe=h.useCallback,M=h.useContext,_m=h.useDebugValue,Pm=h.useDeferredValue,N=h.useEffect,Wt=h.useId,Im=h.useImperativeHandle,jt=h.useInsertionEffect,Wr=h.useLayoutEffect,ne=h.useMemo,Fm=h.useReducer,O=h.useRef,Ue=h.useState,Mm=h.useSyncExternalStore,Lm=h.useTransition,Om=h.version;var Am=Object.create,Xl=Object.defineProperty,Vm=Object.getOwnPropertyDescriptor,Yl=Object.getOwnPropertyNames,Dm=Object.getPrototypeOf,Bm=Object.prototype.hasOwnProperty,st=(e,t)=>function(){return t||(0,e[Yl(e)[0]])((t={exports:{}}).exports,t),t.exports},zm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Yl(t))!Bm.call(e,i)&&i!==r&&Xl(e,i,{get:()=>t[i],enumerable:!(n=Vm(t,i))||n.enumerable});return e},be=(e,t,r)=>(r=e!=null?Am(Dm(e)):{},zm(t||!e||!e.__esModule?Xl(r,"default",{value:e,enumerable:!0}):r,e)),Kl=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)},He=(e,t,r)=>(Kl(e,t,"read from private field"),r?r.call(e):t.get(e)),on=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},_o=(e,t,r,n)=>(Kl(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),Hm=st({"../../../node_modules/@emotion/memoize/dist/memoize.browser.cjs.js"(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function t(r){var n={};return function(i){return n[i]===void 0&&(n[i]=r(i)),n[i]}}e.default=t}}),Nm=st({"../../../node_modules/@emotion/is-prop-valid/dist/is-prop-valid.browser.cjs.js"(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function t(o){return o&&typeof o=="object"&&"default"in o?o.default:o}var r=t(Hm()),n=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,i=r(function(o){return n.test(o)||o.charCodeAt(0)===111&&o.charCodeAt(1)===110&&o.charCodeAt(2)<91});e.default=i}}),Jt=fe({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),an=fe({}),Sr=fe(null),sn=typeof document<"u",Ft=sn?Wr:N,en=fe({}),ti=fe({}),ql=fe({strict:!1});function $m(e,t,r,n){let{visualElement:i}=M(an),o=M(ql),a=M(Sr),s=M(Jt).reducedMotion,l=O();n=n||o.renderer,!l.current&&n&&(l.current=n(e,{visualState:t,parent:i,props:r,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:s}));let c=l.current;return jt(()=>{c&&c.update(r,a)}),Ft(()=>{c&&c.render()}),N(()=>{c&&c.updateFeatures()}),(W.HandoffAppearAnimations?Ft:N)(()=>{c&&c.animationState&&c.animationState.animateChanges()}),c}function vr(e){return typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Um(e,t,r){return oe(n=>{n&&e.mount&&e.mount(n),t&&(n?t.mount(n):t.unmount()),r&&(typeof r=="function"?r(n):vr(r)&&(r.current=n))},[t])}function tn(e){return typeof e=="string"||Array.isArray(e)}function ri(e){return typeof e=="object"&&typeof e.start=="function"}var Po=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Io=["initial",...Po];function ni(e){return ri(e.animate)||Io.some(t=>tn(e[t]))}function Zl(e){return Boolean(ni(e)||e.variants)}function Wm(e,t){if(ni(e)){let{initial:r,animate:n}=e;return{initial:r===!1||tn(r)?r:void 0,animate:tn(n)?n:void 0}}return e.inherit!==!1?t:{}}function jm(e){let{initial:t,animate:r}=Wm(e,M(an));return ne(()=>({initial:t,animate:r}),[As(t),As(r)])}function As(e){return Array.isArray(e)?e.join(" "):e}var Vs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rn={};for(let e in Vs)rn[e]={isEnabled:t=>Vs[e].some(r=>!!t[r])};function Gm(e){for(let t in e)rn[t]={...rn[t],...e[t]}}var Fo=Symbol.for("motionComponentSymbol");function Jl({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){e&&Gm(e);function o(s,l){let c,u={...M(Jt),...s,layoutId:Xm(s)},{isStatic:f}=u,d=jm(s),m=n(s,f);if(!f&&sn){d.visualElement=$m(i,m,u,t);let p=M(ti),g=M(ql).strict;d.visualElement&&(c=d.visualElement.loadFeatures(u,g,e,p))}return re(an.Provider,{value:d},c&&d.visualElement?re(c,{visualElement:d.visualElement,...u}):null,r(i,s,Um(m,d.visualElement,l),m,f,d.visualElement))}let a=Ie(o);return a[Fo]=i,a}function Xm({layoutId:e}){let t=M(en).id;return t&&e!==void 0?t+"-"+e:e}var Yn={};function Mo(e){Object.assign(Yn,e)}var xe=e=>Boolean(e&&e.getVelocity),ln=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],er=new Set(ln),Ym={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Km=ln.length;function Ql(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,i){let o="";for(let a=0;a<Km;a++){let s=ln[a];if(e[s]!==void 0){let l=Ym[s]||s;o+=`${l}(${e[s]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,n?"":o):r&&n&&(o="none"),o}var Mt=(e,t,r)=>Math.min(Math.max(r,e),t),Kr=e=>Math.round(e*1e5)/1e5,ii=/(-)?([\d]*\.?[\d])+/g,ec=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,qm=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function cn(e){return typeof e=="string"}var un=e=>({test:t=>cn(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),_t=un("deg"),nt=un("%"),H=un("px"),Zm=un("vh"),Jm=un("vw"),Ds={...nt,parse:e=>nt.parse(e)/100,transform:e=>nt.transform(e*100)},Qm=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","ignoreStrict","viewport"]);function br(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||Qm.has(e)}var tc=e=>!br(e);function rc(e){e&&(tc=t=>t.startsWith("on")?!br(t):e(t))}try{rc(Nm().default)}catch{}function nc(e,t,r){let n={};for(let i in e)i==="values"&&typeof e.values=="object"||(tc(i)||r===!0&&br(i)||!t&&!br(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}var Kn=e=>Array.isArray(e),ep=e=>Boolean(e&&typeof e=="object"&&e.mix&&e.toValue),tp=e=>Kn(e)?e[e.length-1]||0:e;function Me(e){let t=xe(e)?e.get():e;return ep(t)?t.toValue():t}function Lo(e,t,r,n={},i={}){return typeof t=="function"&&(t=t(r!==void 0?r:e.custom,n,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(r!==void 0?r:e.custom,n,i)),t}function lt(e){let t=O(null);return t.current===null&&(t.current=e()),t.current}function rp({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,i,o){let a={latestValues:np(n,i,o,e),renderState:t()};return r&&(a.mount=s=>r(n,s,a)),a}var oi=e=>(t,r)=>{let n=M(an),i=M(Sr),o=()=>rp(e,t,n,i);return r?o():lt(o)};function np(e,t,r,n){let i={},o=n(e,{});for(let d in o)i[d]=Me(o[d]);let{initial:a,animate:s}=e,l=ni(e),c=Zl(e);t&&c&&!l&&e.inherit!==!1&&(a===void 0&&(a=t.initial),s===void 0&&(s=t.animate));let u=r?r.initial===!1:!1;u=u||a===!1;let f=u?s:a;return f&&typeof f!="boolean"&&!ri(f)&&(Array.isArray(f)?f:[f]).forEach(m=>{let p=Lo(e,m);if(!p)return;let{transitionEnd:g,transition:x,...v}=p;for(let b in v){let y=v[b];if(Array.isArray(y)){let S=u?y.length-1:0;y=y[S]}y!==null&&(i[b]=y)}for(let b in g)i[b]=g[b]}),i}var ic=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function ai(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}var oc=e=>t=>ic(t)&&e(t,ai(t));function rt(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function it(e,t,r,n){return rt(e,t,oc(r),n)}var ip=(e,t)=>r=>t(e(r)),St=(...e)=>e.reduce(ip);function ac(e){let t=null;return()=>{let r=()=>{t=null};return t===null?(t=e,r):!1}}var Bs=ac("dragHorizontal"),zs=ac("dragVertical");function sc(e){let t=!1;if(e==="y")t=zs();else if(e==="x")t=Bs();else{let r=Bs(),n=zs();r&&n?t=()=>{r(),n()}:(r&&r(),n&&n())}return t}function Oo(){let e=sc(!0);return e?(e(),!1):!0}var he={delta:0,timestamp:0,isProcessing:!1};function op(e){let t=[],r=[],n=0,i=!1,o=!1,a=new WeakSet,s={schedule:(l,c=!1,u=!1)=>{let f=u&&i,d=f?t:r;return c&&a.add(l),d.indexOf(l)===-1&&(d.push(l),f&&i&&(n=t.length)),l},cancel:l=>{let c=r.indexOf(l);c!==-1&&r.splice(c,1),a.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,r]=[r,t],r.length=0,n=t.length,n)for(let c=0;c<n;c++){let u=t[c];u(l),a.has(u)&&(s.schedule(u),e())}i=!1,o&&(o=!1,s.process(l))}};return s}var ap=40,so=!0,nn=!1,wr=["read","update","preRender","render","postRender"],qt=wr.reduce((e,t)=>(e[t]=op(()=>nn=!0),e),{}),sp=e=>qt[e].process(he),lc=e=>{nn=!1,he.delta=so?1e3/60:Math.max(Math.min(e-he.timestamp,ap),1),he.timestamp=e,he.isProcessing=!0,wr.forEach(sp),he.isProcessing=!1,nn&&(so=!1,requestAnimationFrame(lc))},lp=()=>{nn=!0,so=!0,he.isProcessing||requestAnimationFrame(lc)},K=wr.reduce((e,t)=>{let r=qt[t];return e[t]=(n,i=!1,o=!1)=>(nn||lp(),r.schedule(n,i,o)),e},{});function je(e){wr.forEach(t=>qt[t].cancel(e))}var Ao=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),cp="framerAppearId",cc="data-"+Ao(cp),ye=e=>e,fn=ye,Ee=ye,uc=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e,up=1e-7,fp=12;function dp(e,t,r,n,i){let o,a,s=0;do a=t+(r-t)/2,o=uc(a,n,i)-e,o>0?r=a:t=a;while(Math.abs(o)>up&&++s<fp);return a}function Cr(e,t,r,n){if(e===t&&r===n)return ye;let i=o=>dp(o,0,1,e,r);return o=>o===0||o===1?o:uc(i(o),t,n)}var fc=Cr(.42,0,1,1),dc=Cr(0,0,.58,1),Vo=Cr(.42,0,.58,1),Do=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Bo=e=>t=>1-e(1-t),zo=e=>1-Math.sin(Math.acos(e)),si=Bo(zo),hc=Do(si),Ho=Cr(.33,1.53,.69,.99),li=Bo(Ho),mc=Do(li),pc=e=>(e*=2)<1?.5*li(e):.5*(2-Math.pow(2,-10*(e-1))),tr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},qr={...tr,transform:e=>Mt(0,1,e)},Nn={...tr,default:1},No=(e,t)=>r=>Boolean(cn(r)&&qm.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),vc=(e,t,r)=>n=>{if(!cn(n))return n;let[i,o,a,s]=n.match(ii);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(a),alpha:s!==void 0?parseFloat(s):1}},hp=e=>Mt(0,255,e),Yi={...tr,transform:e=>Math.round(hp(e))},Kt={test:No("rgb","red"),parse:vc("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+Yi.transform(e)+", "+Yi.transform(t)+", "+Yi.transform(r)+", "+Kr(qr.transform(n))+")"};function mp(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}}var lo={test:No("#"),parse:mp,transform:Kt.transform},gr={test:No("hsl","hue"),parse:vc("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+nt.transform(Kr(t))+", "+nt.transform(Kr(r))+", "+Kr(qr.transform(n))+")"},Fe={test:e=>Kt.test(e)||lo.test(e)||gr.test(e),parse:e=>Kt.test(e)?Kt.parse(e):gr.test(e)?gr.parse(e):lo.parse(e),transform:e=>cn(e)?e:e.hasOwnProperty("red")?Kt.transform(e):gr.transform(e)},ae=(e,t,r)=>-r*e+r*t+e,gc=e=>t=>typeof t=="string"&&t.startsWith(e),yc=gc("--"),co=gc("var(--"),pp=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g;function vp(e){var t,r;return isNaN(e)&&cn(e)&&(((t=e.match(ii))===null||t===void 0?void 0:t.length)||0)+(((r=e.match(ec))===null||r===void 0?void 0:r.length)||0)>0}var xc={regex:pp,countKey:"Vars",token:"${v}",parse:ye},bc={regex:ec,countKey:"Colors",token:"${c}",parse:Fe.parse},Sc={regex:ii,countKey:"Numbers",token:"${n}",parse:tr.parse};function Ki(e,{regex:t,countKey:r,token:n,parse:i}){let o=e.tokenised.match(t);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...o.map(i)))}function qn(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&Ki(r,xc),Ki(r,bc),Ki(r,Sc),r}function wc(e){return qn(e).values}function Cc(e){let{values:t,numColors:r,numVars:n,tokenised:i}=qn(e),o=t.length;return a=>{let s=i;for(let l=0;l<o;l++)l<n?s=s.replace(xc.token,a[l]):l<n+r?s=s.replace(bc.token,Fe.transform(a[l])):s=s.replace(Sc.token,Kr(a[l]));return s}}var gp=e=>typeof e=="number"?0:e;function yp(e){let t=wc(e);return Cc(e)(t.map(gp))}var at={test:vp,parse:wc,createTransformer:Cc,getAnimatableNone:yp},Lt=(e,t,r)=>{let n=t-e;return n===0?1:(r-e)/n};function qi(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function xp({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,t/=100,r/=100;let i=0,o=0,a=0;if(!t)i=o=a=r;else{let s=r<.5?r*(1+t):r+t-r*t,l=2*r-s;i=qi(l,s,e+1/3),o=qi(l,s,e),a=qi(l,s,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(a*255),alpha:n}}var Zi=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},bp=[lo,Kt,gr],Sp=e=>bp.find(t=>t.test(e));function Hs(e){let t=Sp(e);Ee(Boolean(t),`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===gr&&(r=xp(r)),r}var Tc=(e,t)=>{let r=Hs(e),n=Hs(t),i={...r};return o=>(i.red=Zi(r.red,n.red,o),i.green=Zi(r.green,n.green,o),i.blue=Zi(r.blue,n.blue,o),i.alpha=ae(r.alpha,n.alpha,o),Kt.transform(i))},Ec=(e,t)=>r=>`${r>0?t:e}`;function kc(e,t){return typeof e=="number"?r=>ae(e,t,r):Fe.test(e)?Tc(e,t):e.startsWith("var(")?Ec(e,t):_c(e,t)}var Rc=(e,t)=>{let r=[...e],n=r.length,i=e.map((o,a)=>kc(o,t[a]));return o=>{for(let a=0;a<n;a++)r[a]=i[a](o);return r}},wp=(e,t)=>{let r={...e,...t},n={};for(let i in r)e[i]!==void 0&&t[i]!==void 0&&(n[i]=kc(e[i],t[i]));return i=>{for(let o in n)r[o]=n[o](i);return r}},_c=(e,t)=>{let r=at.createTransformer(t),n=qn(e),i=qn(t);return n.numVars===i.numVars&&n.numColors===i.numColors&&n.numNumbers>=i.numNumbers?St(Rc(n.values,i.values),r):(fn(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),Ec(e,t))},Ns=(e,t)=>r=>ae(e,t,r);function Cp(e){return typeof e=="number"?Ns:typeof e=="string"?Fe.test(e)?Tc:_c:Array.isArray(e)?Rc:typeof e=="object"?wp:Ns}function Tp(e,t,r){let n=[],i=r||Cp(e[0]),o=e.length-1;for(let a=0;a<o;a++){let s=i(e[a],e[a+1]);if(t){let l=Array.isArray(t)?t[a]||ye:t;s=St(l,s)}n.push(s)}return n}function Tr(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if(Ee(o===t.length,"Both input and output ranges must be the same length"),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=Tp(t,n,i),s=a.length,l=c=>{let u=0;if(s>1)for(;u<e.length-2&&!(c<e[u+1]);u++);let f=Lt(e[u],e[u+1],c);return a[u](f)};return r?c=>l(Mt(e[0],e[o-1],c)):l}var wt=e=>e*1e3,ot=e=>e/1e3;function $o(e,t){return t?e*(1e3/t):0}var Ep=5;function Pc(e,t,r){let n=Math.max(t-Ep,0);return $o(r-e(n),t-n)}var Ji=.001,kp=.01,$s=10,Rp=.05,_p=1;function Pp({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let i,o;fn(e<=wt($s),"Spring duration must be 10 seconds or less");let a=1-t;a=Mt(Rp,_p,a),e=Mt(kp,$s,ot(e)),a<1?(i=c=>{let u=c*a,f=u*e,d=u-r,m=uo(c,a),p=Math.exp(-f);return Ji-d/m*p},o=c=>{let f=c*a*e,d=f*r+r,m=Math.pow(a,2)*Math.pow(c,2)*e,p=Math.exp(-f),g=uo(Math.pow(c,2),a);return(-i(c)+Ji>0?-1:1)*((d-m)*p)/g}):(i=c=>{let u=Math.exp(-c*e),f=(c-r)*e+1;return-Ji+u*f},o=c=>{let u=Math.exp(-c*e),f=(r-c)*(e*e);return u*f});let s=5/e,l=Fp(i,o,s);if(e=wt(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{let c=Math.pow(l,2)*n;return{stiffness:c,damping:a*2*Math.sqrt(n*c),duration:e}}}var Ip=12;function Fp(e,t,r){let n=r;for(let i=1;i<Ip;i++)n=n-e(n)/t(n);return n}function uo(e,t){return e*Math.sqrt(1-t*t)}var Mp=["duration","bounce"],Lp=["stiffness","damping","mass"];function Us(e,t){return t.some(r=>e[r]!==void 0)}function Op(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Us(e,Lp)&&Us(e,Mp)){let r=Pp(e);t={...t,...r,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}function ci({keyframes:e,restDelta:t,restSpeed:r,...n}){let i=e[0],o=e[e.length-1],a={done:!1,value:i},{stiffness:s,damping:l,mass:c,velocity:u,duration:f,isResolvedFromDuration:d}=Op(n),m=u?-ot(u):0,p=l/(2*Math.sqrt(s*c)),g=o-i,x=ot(Math.sqrt(s/c)),v=Math.abs(g)<5;r||(r=v?.01:2),t||(t=v?.005:.5);let b;if(p<1){let y=uo(x,p);b=S=>{let C=Math.exp(-p*x*S);return o-C*((m+p*x*g)/y*Math.sin(y*S)+g*Math.cos(y*S))}}else if(p===1)b=y=>o-Math.exp(-x*y)*(g+(m+x*g)*y);else{let y=x*Math.sqrt(p*p-1);b=S=>{let C=Math.exp(-p*x*S),w=Math.min(y*S,300);return o-C*((m+p*x*g)*Math.sinh(w)+y*g*Math.cosh(w))/y}}return{calculatedDuration:d&&f||null,next:y=>{let S=b(y);if(d)a.done=y>=f;else{let C=m;y!==0&&(p<1?C=Pc(b,y,S):C=0);let w=Math.abs(C)<=r,T=Math.abs(o-S)<=t;a.done=w&&T}return a.value=a.done?o:S,a}}}var Ic=e=>Array.isArray(e)&&typeof e[0]!="number",Ws={linear:ye,easeIn:fc,easeInOut:Vo,easeOut:dc,circIn:zo,circInOut:hc,circOut:si,backIn:li,backInOut:mc,backOut:Ho,anticipate:pc},js=e=>{if(Array.isArray(e)){Ee(e.length===4,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return Cr(t,r,n,i)}else if(typeof e=="string")return Ee(Ws[e]!==void 0,`Invalid easing type '${e}'`),Ws[e];return e};function Fc(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=Lt(0,t,n);e.push(ae(r,1,i))}}function Uo(e){let t=[0];return Fc(t,e.length-1),t}function Ap(e,t){return e.map(r=>r*t)}function Vp(e,t){return e.map(()=>t||Vo).splice(0,e.length-1)}function Zn({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=Ic(n)?n.map(js):js(n),o={done:!1,value:t[0]},a=Ap(r&&r.length===t.length?r:Uo(t),e),s=Tr(a,t,{ease:Array.isArray(i)?i:Vp(t,i)});return{calculatedDuration:e,next:l=>(o.value=s(l),o.done=l>=e,o)}}function Gs({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:c=.5,restSpeed:u}){let f=e[0],d={done:!1,value:f},m=E=>s!==void 0&&E<s||l!==void 0&&E>l,p=E=>s===void 0?l:l===void 0||Math.abs(s-E)<Math.abs(l-E)?s:l,g=r*t,x=f+g,v=a===void 0?x:a(x);v!==x&&(g=v-f);let b=E=>-g*Math.exp(-E/n),y=E=>v+b(E),S=E=>{let R=b(E),_=y(E);d.done=Math.abs(R)<=c,d.value=d.done?v:_},C,w,T=E=>{m(d.value)&&(C=E,w=ci({keyframes:[d.value,p(d.value)],velocity:Pc(y,E,d.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return T(0),{calculatedDuration:null,next:E=>{let R=!1;return!w&&C===void 0&&(R=!0,S(E),T(E)),C!==void 0&&E>C?w.next(E-C):(!R&&S(E),d)}}}var Dp=e=>{let t=({timestamp:r})=>e(r);return{start:()=>K.update(t,!0),stop:()=>je(t),now:()=>he.isProcessing?he.timestamp:performance.now()}},fo=2e4;function ho(e){let t=0,r=50,n=e.next(t);for(;!n.done&&t<fo;)t+=r,n=e.next(t);return t>=fo?1/0:t}var Bp={decay:Gs,inertia:Gs,tween:Zn,keyframes:Zn,spring:ci};function Ot({autoplay:e=!0,delay:t=0,driver:r=Dp,keyframes:n,type:i="keyframes",repeat:o=0,repeatDelay:a=0,repeatType:s="loop",onPlay:l,onStop:c,onComplete:u,onUpdate:f,...d}){let m=1,p=!1,g,x,v=()=>{g&&g(),x=new Promise(V=>{g=V})};v();let b,y=Bp[i]||Zn,S;y!==Zn&&typeof n[0]!="number"&&(S=Tr([0,100],n,{clamp:!1}),n=[0,100]);let C=y({...d,keyframes:n}),w;s==="mirror"&&(w=y({...d,keyframes:[...n].reverse(),velocity:-(d.velocity||0)}));let T="idle",E=null,R=null,_=null;C.calculatedDuration===null&&o&&(C.calculatedDuration=ho(C));let{calculatedDuration:F}=C,B=1/0,L=1/0;F!==null&&(B=F+a,L=B*(o+1)-a);let P=0,I=V=>{if(R===null)return;m>0&&(R=Math.min(R,V)),E!==null?P=E:P=(V-R)*m;let Z=P-t,Y=Z<0;P=Math.max(Z,0),T==="finished"&&E===null&&(P=L);let $=P,ie=C;if(o){let ge=P/B,Be=Math.floor(ge),Le=ge%1;!Le&&ge>=1&&(Le=1),Le===1&&Be--,Be=Math.min(Be,o+1);let Xe=Boolean(Be%2);Xe&&(s==="reverse"?(Le=1-Le,a&&(Le-=a/B)):s==="mirror"&&(ie=w));let Ye=Mt(0,1,Le);P>L&&(Ye=s==="reverse"&&Xe?1:0),$=Ye*B}let Te=Y?{done:!1,value:n[0]}:ie.next($);S&&(Te.value=S(Te.value));let{done:q}=Te;!Y&&F!==null&&(q=P>=L);let le=E===null&&(T==="finished"||T==="running"&&q||m<0&&P<=0);return f&&f(Te.value),le&&A(),Te},U=()=>{b&&b.stop(),b=void 0},k=()=>{T="idle",U(),v(),R=_=null},A=()=>{T="finished",u&&u(),U(),v()},X=()=>{if(p)return;b||(b=r(I));let V=b.now();l&&l(),E!==null?R=V-E:(!R||T==="finished")&&(R=V),_=R,E=null,T="running",b.start()};e&&X();let j={then(V,Z){return x.then(V,Z)},get time(){return ot(P)},set time(V){V=wt(V),P=V,E!==null||!b||m===0?E=V:R=b.now()-V/m},get duration(){let V=C.calculatedDuration===null?ho(C):C.calculatedDuration;return ot(V)},get speed(){return m},set speed(V){V===m||!b||(m=V,j.time=ot(P))},get state(){return T},play:X,pause:()=>{T="paused",E=P},stop:()=>{p=!0,T!=="idle"&&(T="idle",c&&c(),k())},cancel:()=>{_!==null&&I(_),k()},complete:()=>{T="finished"},sample:V=>(R=0,I(V))};return j}function Wo(e,t){e.indexOf(t)===-1&&e.push(t)}function ui(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}function zp([...e],t,r){let n=t<0?e.length+t:t;if(n>=0&&n<e.length){let i=r<0?e.length+r:r,[o]=e.splice(t,1);e.splice(i,0,o)}return e}var jo=class{constructor(){this.subscriptions=[]}add(e){return Wo(this.subscriptions,e),()=>ui(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(n===1)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let o=this.subscriptions[i];o&&o(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}},Hp=e=>!isNaN(parseFloat(e)),fi=class{constructor(e,t={}){this.version="10.12.13",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,n=!0)=>{this.prev=this.current,this.current=r;let{delta:i,timestamp:o}=he;this.lastUpdated!==o&&(this.timeDelta=i,this.lastUpdated=o,K.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),n&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>K.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Hp(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new jo);let r=this.events[e].add(t);return e==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){!t||!this.passiveEffect?this.updateAndNotify(e,t):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?$o(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}};function ve(e,t){return new fi(e,t)}var Np=e=>/^\-?\d*\.?\d+$/.test(e),Mc=e=>/^0[^.\s]+$/.test(e),$p=new Set(["brightness","contrast","saturate","opacity"]);function Up(e){let[t,r]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;let[n]=r.match(ii)||[];if(!n)return e;let i=r.replace(n,""),o=$p.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}var Wp=/([a-z-]*)\(.*?\)/g,mo={...at,getAnimatableNone:e=>{let t=e.match(Wp);return t?t.map(Up).join(" "):e}},Xs={...tr,transform:Math.round},Lc={borderWidth:H,borderTopWidth:H,borderRightWidth:H,borderBottomWidth:H,borderLeftWidth:H,borderRadius:H,radius:H,borderTopLeftRadius:H,borderTopRightRadius:H,borderBottomRightRadius:H,borderBottomLeftRadius:H,width:H,maxWidth:H,height:H,maxHeight:H,size:H,top:H,right:H,bottom:H,left:H,padding:H,paddingTop:H,paddingRight:H,paddingBottom:H,paddingLeft:H,margin:H,marginTop:H,marginRight:H,marginBottom:H,marginLeft:H,rotate:_t,rotateX:_t,rotateY:_t,rotateZ:_t,scale:Nn,scaleX:Nn,scaleY:Nn,scaleZ:Nn,skew:_t,skewX:_t,skewY:_t,distance:H,translateX:H,translateY:H,translateZ:H,x:H,y:H,z:H,perspective:H,transformPerspective:H,opacity:qr,originX:Ds,originY:Ds,originZ:H,zIndex:Xs,fillOpacity:qr,strokeOpacity:qr,numOctaves:Xs},jp={...Lc,color:Fe,backgroundColor:Fe,outlineColor:Fe,fill:Fe,stroke:Fe,borderColor:Fe,borderTopColor:Fe,borderRightColor:Fe,borderBottomColor:Fe,borderLeftColor:Fe,filter:mo,WebkitFilter:mo},Go=e=>jp[e];function Oc(e,t){let r=Go(e);return r!==mo&&(r=at),r.getAnimatableNone?r.getAnimatableNone(t):void 0}var Ac=e=>t=>t.test(e),Gp={test:e=>e==="auto",parse:e=>e},Vc=[tr,H,nt,_t,Jm,Zm,Gp],jr=e=>Vc.find(Ac(e)),Xp=[...Vc,Fe,at],Yp=e=>Xp.find(Ac(e));function Kp(e){let t={};return e.values.forEach((r,n)=>t[n]=r.get()),t}function qp(e){let t={};return e.values.forEach((r,n)=>t[n]=r.getVelocity()),t}function di(e,t,r){let n=e.getProps();return Lo(n,t,r!==void 0?r:n.custom,Kp(e),qp(e))}function Zp(e,t,r){e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,ve(r))}function Xo(e,t){let r=di(e,t),{transitionEnd:n={},transition:i={},...o}=r?e.makeTargetAnimatable(r,!1):{};o={...o,...n};for(let a in o){let s=tp(o[a]);Zp(e,a,s)}}function po(e,t){[...t].reverse().forEach(n=>{let i=e.getVariant(n);i&&Xo(e,i),e.variantChildren&&e.variantChildren.forEach(o=>{po(o,t)})})}function Jp(e,t){if(Array.isArray(t))return po(e,t);if(typeof t=="string")return po(e,[t]);Xo(e,t)}function Dc(e,t,r){var n,i;let o=Object.keys(t).filter(s=>!e.hasValue(s)),a=o.length;if(a)for(let s=0;s<a;s++){let l=o[s],c=t[l],u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(i=(n=r[l])!==null&&n!==void 0?n:e.readValue(l))!==null&&i!==void 0?i:t[l]),u!=null&&(typeof u=="string"&&(Np(u)||Mc(u))?u=parseFloat(u):!Yp(u)&&at.test(c)&&(u=Oc(l,c)),e.addValue(l,ve(u,{owner:e})),r[l]===void 0&&(r[l]=u),u!==null&&e.setBaseTarget(l,u))}}function Qp(e,t){return t?(t[e]||t.default||t).from:void 0}function ev(e,t,r){let n={};for(let i in e){let o=Qp(i,t);if(o!==void 0)n[i]=o;else{let a=r.getValue(i);a&&(n[i]=a.get())}}return n}var vo={current:!1},Bc=e=>Array.isArray(e)&&typeof e[0]=="number";function zc(e){return Boolean(!e||typeof e=="string"&&Hc[e]||Bc(e)||Array.isArray(e)&&e.every(zc))}var Yr=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,Hc={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Yr([0,.65,.55,1]),circOut:Yr([.55,0,1,.45]),backIn:Yr([.31,.01,.66,-.59]),backOut:Yr([.33,1.53,.69,.99])};function Nc(e){if(e)return Bc(e)?Yr(e):Array.isArray(e)?e.map(Nc):Hc[e]}function tv(e,t,r,{delay:n=0,duration:i,repeat:o=0,repeatType:a="loop",ease:s,times:l}={}){let c={[t]:r};l&&(c.offset=l);let u=Nc(s);return Array.isArray(u)&&(c.easing=u),e.animate(c,{delay:n,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:a==="reverse"?"alternate":"normal"})}var Ys={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Qi={},$c={};for(let e in Ys)$c[e]=()=>(Qi[e]===void 0&&(Qi[e]=Ys[e]()),Qi[e]);function rv(e,{repeat:t,repeatType:r="loop"}){let n=t&&r!=="loop"&&t%2===1?0:e.length-1;return e[n]}var nv=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),$n=10,iv=2e4,ov=(e,t)=>t.type==="spring"||e==="backgroundColor"||!zc(t.ease);function av(e,t,{onUpdate:r,onComplete:n,...i}){if(!($c.waapi()&&nv.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let a=!1,s,l,c=()=>{l=new Promise(v=>{s=v})};c();let{keyframes:u,duration:f=300,ease:d,times:m}=i;if(ov(t,i)){let v=Ot({...i,repeat:0,delay:0}),b={done:!1,value:u[0]},y=[],S=0;for(;!b.done&&S<iv;)b=v.sample(S),y.push(b.value),S+=$n;m=void 0,u=y,f=S-$n,d="linear"}let p=tv(e.owner.current,t,u,{...i,duration:f,ease:d,times:m}),g=()=>p.cancel(),x=()=>{K.update(g),s(),c()};return p.onfinish=()=>{e.set(rv(u,i)),n&&n(),x()},{then(v,b){return l.then(v,b)},get time(){return ot(p.currentTime||0)},set time(v){p.currentTime=wt(v)},get speed(){return p.playbackRate},set speed(v){p.playbackRate=v},get duration(){return ot(f)},play:()=>{a||(p.play(),je(g))},pause:()=>p.pause(),stop:()=>{if(a=!0,p.playState==="idle")return;let{currentTime:v}=p;if(v){let b=Ot({...i,autoplay:!1});e.setWithVelocity(b.sample(v-$n).value,b.sample(v).value,$n)}x()},complete:()=>p.finish(),cancel:x}}function sv({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let i=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:ye,pause:ye,stop:ye,then:o=>(o(),Promise.resolve()),cancel:ye,complete:ye});return t?Ot({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}var lv={type:"spring",stiffness:500,damping:25,restSpeed:10},cv=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),uv={type:"keyframes",duration:.8},fv={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dv=(e,{keyframes:t})=>t.length>2?uv:er.has(e)?e.startsWith("scale")?cv(t[1]):lv:fv,go=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(at.test(t)||t==="0")&&!t.startsWith("url("));function hv(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Mc(e)}function mv(e,t,r,n){let i=go(t,r),o;Array.isArray(r)?o=[...r]:o=[null,r];let a=n.from!==void 0?n.from:e.get(),s,l=[];for(let c=0;c<o.length;c++)o[c]===null&&(o[c]=c===0?a:o[c-1]),hv(o[c])&&l.push(c),typeof o[c]=="string"&&o[c]!=="none"&&o[c]!=="0"&&(s=o[c]);if(i&&l.length&&s)for(let c=0;c<l.length;c++){let u=l[c];o[u]=Oc(t,s)}return o}function pv({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function Uc(e,t){return e[t]||e.default||e}var Yo=(e,t,r,n={})=>i=>{let o=Uc(n,e)||{},a=o.delay||n.delay||0,{elapsed:s=0}=n;s=s-wt(a);let l=mv(t,e,r,o),c=l[0],u=l[l.length-1],f=go(e,c),d=go(e,u);fn(f===d,`You are trying to animate ${e} from "${c}" to "${u}". ${c} is not an animatable value - to enable this animation set ${c} to a value animatable to ${u} via the \`style\` property.`);let m={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-s,onUpdate:p=>{t.set(p),o.onUpdate&&o.onUpdate(p)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(pv(o)||(m={...m,...dv(e,m)}),m.duration&&(m.duration=wt(m.duration)),m.repeatDelay&&(m.repeatDelay=wt(m.repeatDelay)),!f||!d||vo.current||o.type===!1)return sv(m);if(t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let p=av(t,e,m);if(p)return p}return Ot(m)};function Jn(e){return Boolean(xe(e)&&e.add)}function vv({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&t[r]!==!0;return t[r]=!1,n}function Ko(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(o=n);let c=[],u=i&&e.animationState&&e.animationState.getState()[i];for(let f in s){let d=e.getValue(f),m=s[f];if(!d||m===void 0||u&&vv(u,f))continue;let p={delay:r,elapsed:0,...o};if(W.HandoffAppearAnimations&&!d.hasAnimated){let x=e.getProps()[cc];x&&(p.elapsed=W.HandoffAppearAnimations(x,f,d,K))}d.start(Yo(f,d,m,e.shouldReduceMotion&&er.has(f)?{type:!1}:p));let g=d.animation;Jn(l)&&(l.add(f),g.then(()=>l.remove(f))),c.push(g)}return a&&Promise.all(c).then(()=>{a&&Xo(e,a)}),c}function yo(e,t,r={}){let n=di(e,t,r.custom),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(Ko(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(l=0)=>{let{delayChildren:c=0,staggerChildren:u,staggerDirection:f}=i;return gv(e,t,c+l,u,f,r)}:()=>Promise.resolve(),{when:s}=i;if(s){let[l,c]=s==="beforeChildren"?[o,a]:[a,o];return l().then(()=>c())}else return Promise.all([o(),a(r.delay)])}function gv(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=i===1?(c=0)=>c*n:(c=0)=>s-c*n;return Array.from(e.variantChildren).sort(yv).forEach((c,u)=>{c.notify("AnimationStart",t),a.push(yo(c,t,{...o,delay:r+l(u)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(a)}function yv(e,t){return e.sortNodePosition(t)}function qo(e,t,r={}){e.notify("AnimationStart",t);let n;if(Array.isArray(t)){let i=t.map(o=>yo(e,o,r));n=Promise.all(i)}else if(typeof t=="string")n=yo(e,t,r);else{let i=typeof t=="function"?di(e,t,r.custom):t;n=Promise.all(Ko(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))}function Wc(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}var xv=[...Po].reverse(),bv=Po.length;function Sv(e){return t=>Promise.all(t.map(({animation:r,options:n})=>qo(e,r,n)))}function wv(e){let t=Sv(e),r=Tv(),n=!0,i=(l,c)=>{let u=di(e,c);if(u){let{transition:f,transitionEnd:d,...m}=u;l={...l,...m,...d}}return l};function o(l){t=l(e)}function a(l,c){let u=e.getProps(),f=e.getVariantContext(!0)||{},d=[],m=new Set,p={},g=1/0;for(let v=0;v<bv;v++){let b=xv[v],y=r[b],S=u[b]!==void 0?u[b]:f[b],C=tn(S),w=b===c?y.isActive:null;w===!1&&(g=v);let T=S===f[b]&&S!==u[b]&&C;if(T&&n&&e.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...p},!y.isActive&&w===null||!S&&!y.prevProp||ri(S)||typeof S=="boolean")continue;let E=Cv(y.prevProp,S),R=E||b===c&&y.isActive&&!T&&C||v>g&&C,_=Array.isArray(S)?S:[S],F=_.reduce(i,{});w===!1&&(F={});let{prevResolvedValues:B={}}=y,L={...B,...F},P=I=>{R=!0,m.delete(I),y.needsAnimating[I]=!0};for(let I in L){let U=F[I],k=B[I];p.hasOwnProperty(I)||(U!==k?Kn(U)&&Kn(k)?!Wc(U,k)||E?P(I):y.protectedKeys[I]=!0:U!==void 0?P(I):m.add(I):U!==void 0&&m.has(I)?P(I):y.protectedKeys[I]=!0)}y.prevProp=S,y.prevResolvedValues=F,y.isActive&&(p={...p,...F}),n&&e.blockInitialAnimation&&(R=!1),R&&!T&&d.push(..._.map(I=>({animation:I,options:{type:b,...l}})))}if(m.size){let v={};m.forEach(b=>{let y=e.getBaseTarget(b);y!==void 0&&(v[b]=y)}),d.push({animation:v})}let x=Boolean(d.length);return n&&u.initial===!1&&!e.manuallyAnimateOnMount&&(x=!1),n=!1,x?t(d):Promise.resolve()}function s(l,c,u){var f;if(r[l].isActive===c)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(m=>{var p;return(p=m.animationState)===null||p===void 0?void 0:p.setActive(l,c)}),r[l].isActive=c;let d=a(u,l);for(let m in r)r[m].protectedKeys={};return d}return{animateChanges:a,setActive:s,setAnimateFunction:o,getState:()=>r}}function Cv(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Wc(t,e):!1}function Gt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Tv(){return{animate:Gt(!0),whileInView:Gt(),whileHover:Gt(),whileTap:Gt(),whileDrag:Gt(),whileFocus:Gt(),exit:Gt()}}var At=class{constructor(e){this.isMounted=!1,this.node=e}update(){}},Ev=class extends At{constructor(e){super(e),e.animationState||(e.animationState=wv(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),ri(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}},kv=0,Rv=class extends At{constructor(){super(...arguments),this.id=kv++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e,{custom:r??this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}},Zo={animation:{Feature:Ev},exit:{Feature:Rv}},xo=(e,t)=>Math.abs(e-t);function jc(e,t){let r=xo(e.x,t.x),n=xo(e.y,t.y);return Math.sqrt(r**2+n**2)}function ze(e){return e.max-e.min}function bo(e,t=0,r=.01){return Math.abs(e-t)<=r}function Ks(e,t,r,n=.5){e.origin=n,e.originPoint=ae(t.min,t.max,e.origin),e.scale=ze(r)/ze(t),(bo(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=ae(r.min,r.max,e.origin)-e.originPoint,(bo(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Zr(e,t,r,n){Ks(e.x,t.x,r.x,n?n.originX:void 0),Ks(e.y,t.y,r.y,n?n.originY:void 0)}function qs(e,t,r){e.min=r.min+t.min,e.max=e.min+ze(t)}function _v(e,t,r){qs(e.x,t.x,r.x),qs(e.y,t.y,r.y)}function Zs(e,t,r){e.min=t.min-r.min,e.max=e.min+ze(t)}function Jr(e,t,r){Zs(e.x,t.x,r.x),Zs(e.y,t.y,r.y)}var Js=()=>({translate:0,scale:1,origin:0,originPoint:0}),yr=()=>({x:Js(),y:Js()}),Qs=()=>({min:0,max:0}),pe=()=>({x:Qs(),y:Qs()});function Gc(){let e=M(Sr);if(e===null)return[!0,null];let{isPresent:t,onExitComplete:r,register:n}=e,i=Wt();return N(()=>n(i),[]),!t&&r?[!1,()=>r&&r(i)]:[!0]}var Pv=(e,t)=>e.depth-t.depth,Xc=class{constructor(){this.children=[],this.isDirty=!1}add(e){Wo(this.children,e),this.isDirty=!0}remove(e){ui(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Pv),this.isDirty=!1,this.children.forEach(e)}};function Yc(e,t){let r=performance.now(),n=({timestamp:i})=>{let o=i-r;o>=t&&(je(n),e(o-t))};return K.read(n,!0),()=>je(n)}var Qt=new WeakMap,Qn={current:null},Jo={current:!1};function Kc(){if(Jo.current=!0,!!sn)if(W.matchMedia){let e=W.matchMedia("(prefers-reduced-motion)"),t=()=>Qn.current=e.matches;e.addListener(t),t()}else Qn.current=!1}function Iv(e,t,r){let{willChange:n}=t;for(let i in t){let o=t[i],a=r[i];if(xe(o))e.addValue(i,o),Jn(n)&&n.add(i);else if(xe(a))e.addValue(i,ve(o,{owner:e})),Jn(n)&&n.remove(i);else if(a!==o)if(e.hasValue(i)){let s=e.getValue(i);!s.hasAnimated&&s.set(o)}else{let s=e.getStaticValue(i);e.addValue(i,ve(s!==void 0?s:o,{owner:e}))}}for(let i in r)t[i]===void 0&&e.removeValue(i);return t}var qc=Object.keys(rn),Fv=qc.length,el=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Mv=Io.length,Zc=class{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>K.render(this.render,!1,!0);let{latestValues:a,renderState:s}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.isControllingVariants=ni(t),this.isVariantNode=Zl(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);let{willChange:l,...c}=this.scrapeMotionValuesFromProps(t,{});for(let u in c){let f=c[u];a[u]!==void 0&&xe(f)&&(f.set(a[u],!1),Jn(l)&&l.add(u))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,Qt.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,r)=>this.bindToMotionValue(r,t)),Jo.current||Kc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Qn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Qt.delete(this.current),this.projection&&this.projection.unmount(),je(this.notifyUpdate),je(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(let e in this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=er.has(e),n=t.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&K.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),i()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...t},r,n,i){let o,a;for(let s=0;s<Fv;s++){let l=qc[s],{isEnabled:c,Feature:u,ProjectionNode:f,MeasureLayout:d}=rn[l];f&&(o=f),c(t)&&(!this.features[l]&&u&&(this.features[l]=new u(this)),d&&(a=d))}if(!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:s,layout:l,drag:c,dragConstraints:u,layoutScroll:f,layoutRoot:d}=t;this.projection.setOptions({layoutId:s,layout:l,alwaysMeasureLayout:Boolean(c)||u&&vr(u),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof l=="string"?l:"both",initialPromotionConfig:i,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):pe()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let r=0;r<el.length;r++){let n=el[r];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let i=e["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=Iv(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}let t={};for(let r=0;r<Mv;r++){let n=Io[r],i=this.props[n];(tn(i)||i===!1)&&(t[n]=i)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return r===void 0&&t!==void 0&&(r=ve(t,{owner:this}),this.addValue(e,r)),r}readValue(e){return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n=typeof r=="string"||typeof r=="object"?(t=Lo(this.props,r))===null||t===void 0?void 0:t[e]:void 0;if(r&&n!==void 0)return n;let i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!xe(i)?i:this.initialValues[e]!==void 0&&n===void 0?void 0:this.baseTarget[e]}on(e,t){return this.events[e]||(this.events[e]=new jo),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}};function Jc(e){function t(n,i={}){return Jl(e(n,i))}if(typeof Proxy>"u")return t;let r=new Map;return new Proxy(t,{get:(n,i)=>(r.has(i)||r.set(i,t(i)),r.get(i))})}var Lv=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qo(e){return typeof e!="string"||e.includes("-")?!1:!!(Lv.indexOf(e)>-1||/[A-Z]/.test(e))}function Qc(e,{layout:t,layoutId:r}){return er.has(e)||e.startsWith("origin")||(t||r!==void 0)&&(!!Yn[e]||e==="opacity")}var Ov=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function ea(e,t,r,n){let{style:i,vars:o,transform:a,transformOrigin:s}=e,l=!1,c=!1,u=!0;for(let f in t){let d=t[f];if(yc(f)){o[f]=d;continue}let m=Lc[f],p=Ov(d,m);if(er.has(f)){if(l=!0,a[f]=p,!u)continue;d!==(m.default||0)&&(u=!1)}else f.startsWith("origin")?(c=!0,s[f]=p):i[f]=p}if(t.transform||(l||n?i.transform=Ql(e.transform,r,u,n):i.transform&&(i.transform="none")),c){let{originX:f="50%",originY:d="50%",originZ:m=0}=s;i.transformOrigin=`${f} ${d} ${m}`}}var ta=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eu(e,t,r){for(let n in t)!xe(t[n])&&!Qc(n,r)&&(e[n]=t[n])}function Av({transformTemplate:e},t,r){return ne(()=>{let n=ta();return ea(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}function Vv(e,t,r){let n=e.style||{},i={};return eu(i,n,e),Object.assign(i,Av(e,t,r)),e.transformValues?e.transformValues(i):i}function Dv(e,t,r){let n={},i=Vv(e,t,r);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=i,n}function tl(e,t,r){return typeof e=="string"?e:H.transform(t+r*e)}function Bv(e,t,r){let n=tl(t,e.x,e.width),i=tl(r,e.y,e.height);return`${n} ${i}`}var zv={offset:"stroke-dashoffset",array:"stroke-dasharray"},Hv={offset:"strokeDashoffset",array:"strokeDasharray"};function Nv(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?zv:Hv;e[o.offset]=H.transform(-n);let a=H.transform(t),s=H.transform(r);e[o.array]=`${a} ${s}`}function ra(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...c},u,f,d){if(ea(e,c,u,d),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:m,style:p,dimensions:g}=e;m.transform&&(g&&(p.transform=m.transform),delete m.transform),g&&(i!==void 0||o!==void 0||p.transform)&&(p.transformOrigin=Bv(g,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(m.x=t),r!==void 0&&(m.y=r),n!==void 0&&(m.scale=n),a!==void 0&&Nv(m,a,s,l,!1)}var tu=()=>({...ta(),attrs:{}}),na=e=>typeof e=="string"&&e.toLowerCase()==="svg";function $v(e,t,r,n){let i=ne(()=>{let o=tu();return ra(o,t,{enableHardwareAcceleration:!1},na(n),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){let o={};eu(o,e.style,e),i.style={...o,...i.style}}return i}function Uv(e=!1){return(r,n,i,{latestValues:o},a)=>{let l=(Qo(r)?$v:Dv)(n,o,a,r),u={...nc(n,typeof r=="string",e),...l,ref:i},{children:f}=n,d=ne(()=>xe(f)?f.get():f,[f]);return re(r,{...u,children:d})}}function ru(e,{style:t,vars:r},n,i){Object.assign(e.style,t,i&&i.getProjectionStyles(n));for(let o in r)e.style.setProperty(o,r[o])}var nu=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function iu(e,t,r,n){ru(e,t,void 0,n);for(let i in t.attrs)e.setAttribute(nu.has(i)?i:Ao(i),t.attrs[i])}function ia(e,t){let{style:r}=e,n={};for(let i in r)(xe(r[i])||t.style&&xe(t.style[i])||Qc(i,e))&&(n[i]=r[i]);return n}function ou(e,t){let r=ia(e,t);for(let n in e)if(xe(e[n])||xe(t[n])){let i=ln.indexOf(n)!==-1?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n;r[i]=e[n]}return r}var Wv={useVisualState:oi({scrapeMotionValuesFromProps:ou,createRenderState:tu,onMount:(e,t,{renderState:r,latestValues:n})=>{try{r.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{r.dimensions={x:0,y:0,width:0,height:0}}ra(r,n,{enableHardwareAcceleration:!1},na(t.tagName),e.transformTemplate),iu(t,r)}})},jv={useVisualState:oi({scrapeMotionValuesFromProps:ia,createRenderState:ta})};function au(e,{forwardMotionProps:t=!1},r,n){return{...Qo(e)?Wv:jv,preloadedFeatures:r,useRender:Uv(t),createVisualElement:n,Component:e}}function rl(e,t){let r="pointer"+(t?"enter":"leave"),n="onHover"+(t?"Start":"End"),i=(o,a)=>{if(o.type==="touch"||Oo())return;let s=e.getProps();e.animationState&&s.whileHover&&e.animationState.setActive("whileHover",t),s[n]&&K.update(()=>s[n](o,a))};return it(e.current,r,i,{passive:!e.getProps()[n]})}var Gv=class extends At{mount(){this.unmount=St(rl(this.node,!0),rl(this.node,!1))}unmount(){}},Xv=class extends At{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=St(rt(this.node.current,"focus",()=>this.onFocus()),rt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}},su=(e,t)=>t?e===t?!0:su(e,t.parentElement):!1;function eo(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,ai(r))}var Yv=class extends At{constructor(){super(...arguments),this.removeStartListeners=ye,this.removeEndListeners=ye,this.removeAccessibleListeners=ye,this.startPointerPress=(e,t)=>{if(this.removeEndListeners(),this.isPressing)return;let r=this.node.getProps(),i=it(W,"pointerup",(a,s)=>{if(!this.checkPressEnd())return;let{onTap:l,onTapCancel:c}=this.node.getProps();K.update(()=>{su(this.node.current,a.target)?l&&l(a,s):c&&c(a,s)})},{passive:!(r.onTap||r.onPointerUp)}),o=it(W,"pointercancel",(a,s)=>this.cancelPress(a,s),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=St(i,o),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=i=>{if(i.key!=="Enter"||this.isPressing)return;let o=a=>{a.key!=="Enter"||!this.checkPressEnd()||eo("up",(s,l)=>{let{onTap:c}=this.node.getProps();c&&K.update(()=>c(s,l))})};this.removeEndListeners(),this.removeEndListeners=rt(this.node.current,"keyup",o),eo("down",(a,s)=>{this.startPress(a,s)})},t=rt(this.node.current,"keydown",e),r=()=>{this.isPressing&&eo("cancel",(i,o)=>this.cancelPress(i,o))},n=rt(this.node.current,"blur",r);this.removeAccessibleListeners=St(t,n)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.update(()=>r(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Oo()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&K.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=it(this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=rt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=St(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}},So=new WeakMap,to=new WeakMap,Kv=e=>{let t=So.get(e.target);t&&t(e)},qv=e=>{e.forEach(Kv)};function Zv({root:e,...t}){let r=e||document;to.has(r)||to.set(r,{});let n=to.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(qv,{root:e,...t})),n[i]}function Jv(e,t,r){let n=Zv(t);return So.set(e,r),n.observe(e),()=>{So.delete(e),n.unobserve(e)}}var Qv={some:0,all:1},eg=class extends At{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:typeof n=="number"?n:Qv[n]},a=s=>{let{isIntersecting:l}=s;if(this.isInView===l||(this.isInView=l,i&&!l&&this.hasEnteredView))return;l&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",l);let{onViewportEnter:c,onViewportLeave:u}=this.node.getProps(),f=l?c:u;f&&f(s)};return Jv(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(tg(e,t))&&this.startObserver()}unmount(){}};function tg({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}var lu={inView:{Feature:eg},tap:{Feature:Yv},focus:{Feature:Xv},hover:{Feature:Gv}},cu=class{constructor(e,t,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let l=no(this.lastMoveEventInfo,this.history),c=this.startEvent!==null,u=jc(l.offset,{x:0,y:0})>=3;if(!c&&!u)return;let{point:f}=l,{timestamp:d}=he;this.history.push({...f,timestamp:d});let{onStart:m,onMove:p}=this.handlers;c||(m&&m(this.lastMoveEvent,l),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,l)},this.handlePointerMove=(l,c)=>{this.lastMoveEvent=l,this.lastMoveEventInfo=ro(c,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(l,c)=>{if(this.end(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let{onEnd:u,onSessionEnd:f}=this.handlers,d=no(l.type==="pointercancel"?this.lastMoveEventInfo:ro(c,this.transformPagePoint),this.history);this.startEvent&&u&&u(l,d),f&&f(l,d)},!ic(e))return;this.handlers=t,this.transformPagePoint=r;let n=ai(e),i=ro(n,this.transformPagePoint),{point:o}=i,{timestamp:a}=he;this.history=[{...o,timestamp:a}];let{onSessionStart:s}=t;s&&s(e,no(i,this.history)),this.removeListeners=St(it(W,"pointermove",this.handlePointerMove),it(W,"pointerup",this.handlePointerUp),it(W,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),je(this.updatePoint)}};function ro(e,t){return t?{point:t(e.point)}:e}function nl(e,t){return{x:e.x-t.x,y:e.y-t.y}}function no({point:e},t){return{point:e,delta:nl(e,uu(t)),offset:nl(e,rg(t)),velocity:ng(t,.1)}}function rg(e){return e[0]}function uu(e){return e[e.length-1]}function ng(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=uu(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>wt(t)));)r--;if(!n)return{x:0,y:0};let o=ot(i.timestamp-n.timestamp);if(o===0)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function ig(e,{min:t,max:r},n){return t!==void 0&&e<t?e=n?ae(t,e,n.min):Math.max(e,t):r!==void 0&&e>r&&(e=n?ae(r,e,n.max):Math.min(e,r)),e}function il(e,t,r){return{min:t!==void 0?e.min+t:void 0,max:r!==void 0?e.max+r-(e.max-e.min):void 0}}function og(e,{top:t,left:r,bottom:n,right:i}){return{x:il(e.x,r,i),y:il(e.y,t,n)}}function ol(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function ag(e,t){return{x:ol(e.x,t.x),y:ol(e.y,t.y)}}function sg(e,t){let r=.5,n=ze(e),i=ze(t);return i>n?r=Lt(t.min,t.max-n,e.min):n>i&&(r=Lt(e.min,e.max-i,t.min)),Mt(0,1,r)}function lg(e,t){let r={};return t.min!==void 0&&(r.min=t.min-e.min),t.max!==void 0&&(r.max=t.max-e.min),r}var wo=.35;function cg(e=wo){return e===!1?e=0:e===!0&&(e=wo),{x:al(e,"left","right"),y:al(e,"top","bottom")}}function al(e,t,r){return{min:sl(e,t),max:sl(e,r)}}function sl(e,t){return typeof e=="number"?e:e[t]||0}function tt(e){return[e("x"),e("y")]}function fu({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function ug({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function fg(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}function io(e){return e===void 0||e===1}function Co({scale:e,scaleX:t,scaleY:r}){return!io(e)||!io(t)||!io(r)}function Xt(e){return Co(e)||du(e)||e.z||e.rotate||e.rotateX||e.rotateY}function du(e){return ll(e.x)||ll(e.y)}function ll(e){return e&&e!=="0%"}function ei(e,t,r){let n=e-r,i=t*n;return r+i}function cl(e,t,r,n,i){return i!==void 0&&(e=ei(e,i,n)),ei(e,r,n)+t}function To(e,t=0,r=1,n,i){e.min=cl(e.min,t,r,n,i),e.max=cl(e.max,t,r,n,i)}function hu(e,{x:t,y:r}){To(e.x,t.translate,t.scale,t.originPoint),To(e.y,r.translate,r.scale,r.originPoint)}function dg(e,t,r,n=!1){let i=r.length;if(!i)return;t.x=t.y=1;let o,a;for(let s=0;s<i;s++){o=r[s],a=o.projectionDelta;let l=o.instance;l&&l.style&&l.style.display==="contents"||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&xr(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,hu(e,a)),n&&Xt(o.latestValues)&&xr(e,o.latestValues))}t.x=ul(t.x),t.y=ul(t.y)}function ul(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function It(e,t){e.min=e.min+t,e.max=e.max+t}function fl(e,t,[r,n,i]){let o=t[i]!==void 0?t[i]:.5,a=ae(e.min,e.max,o);To(e,t[r],t[n],a,t.scale)}var hg=["x","scaleX","originX"],mg=["y","scaleY","originY"];function xr(e,t){fl(e.x,t,hg),fl(e.y,t,mg)}function mu(e,t){return fu(fg(e.getBoundingClientRect(),t))}function pg(e,t,r){let n=mu(e,r),{scroll:i}=t;return i&&(It(n.x,i.offset.x),It(n.y,i.offset.y)),n}var vg=new WeakMap,gg=class{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=pe(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;let n=s=>{this.stopAnimation(),t&&this.snapToCursor(ai(s,"page").point)},i=(s,l)=>{let{drag:c,dragPropagation:u,onDragStart:f}=this.getProps();if(c&&!u&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=sc(c),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),tt(m=>{let p=this.getAxisMotionValue(m).get()||0;if(nt.test(p)){let{projection:g}=this.visualElement;if(g&&g.layout){let x=g.layout.layoutBox[m];x&&(p=ze(x)*(parseFloat(p)/100))}}this.originPoint[m]=p}),f&&K.update(()=>f(s,l),!1,!0);let{animationState:d}=this.visualElement;d&&d.setActive("whileDrag",!0)},o=(s,l)=>{let{dragPropagation:c,dragDirectionLock:u,onDirectionLock:f,onDrag:d}=this.getProps();if(!c&&!this.openGlobalLock)return;let{offset:m}=l;if(u&&this.currentDirection===null){this.currentDirection=yg(m),this.currentDirection!==null&&f&&f(this.currentDirection);return}this.updateAxis("x",l.point,m),this.updateAxis("y",l.point,m),this.visualElement.render(),d&&d(s,l)},a=(s,l)=>this.stop(s,l);this.panSession=new cu(e,{onSessionStart:n,onStart:i,onMove:o,onSessionEnd:a},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&K.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!Un(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=ig(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),{layout:r}=this.visualElement.projection||{},n=this.constraints;e&&vr(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=og(r.layoutBox,e):this.constraints=!1,this.elastic=cg(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&tt(i=>{this.getAxisMotionValue(i)&&(this.constraints[i]=lg(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){let{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!vr(e))return!1;let r=e.current;Ee(r!==null,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let i=pg(r,n.root,this.visualElement.getTransformPagePoint()),o=ag(n.layout.layoutBox,i);if(t){let a=t(ug(o));this.hasMutatedConstraints=!!a,a&&(o=fu(a))}return o}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{},l=tt(c=>{if(!Un(c,t,this.currentDirection))return;let u=s&&s[c]||{};o&&(u={min:0,max:0});let f=n?200:1e6,d=n?40:1e7,m={type:"inertia",velocity:r?e[c]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...i,...u};return this.startAxisValueAnimation(c,m)});return Promise.all(l).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(Yo(e,r,0,t))}stopAnimation(){tt(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),n=r[t];return n||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){tt(t=>{let{drag:r}=this.getProps();if(!Un(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:o,max:a}=n.layout.layoutBox[t];i.set(e[t]-ae(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!vr(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};tt(o=>{let a=this.getAxisMotionValue(o);if(a){let s=a.get();n[o]=sg({min:s,max:s},this.constraints[o])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),tt(o=>{if(!Un(o,e,null))return;let a=this.getAxisMotionValue(o),{min:s,max:l}=this.constraints[o];a.set(ae(s,l,n[o]))})}addListeners(){if(!this.visualElement.current)return;vg.set(this.visualElement,this);let e=this.visualElement.current,t=it(e,"pointerdown",s=>{let{drag:l,dragListener:c=!0}=this.getProps();l&&c&&this.start(s)}),r=()=>{let{dragConstraints:s}=this.getProps();vr(s)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",r);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),r();let o=rt(W,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:s,hasLayoutChanged:l})=>{this.isDragging&&l&&(tt(c=>{let u=this.getAxisMotionValue(c);u&&(this.originPoint[c]+=s[c].translate,u.set(u.get()+s[c].translate))}),this.visualElement.render())});return()=>{o(),t(),i(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=wo,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}};function Un(e,t,r){return(t===!0||t===e)&&(r===null||r===e)}function yg(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}var xg=class extends At{constructor(e){super(e),this.removeGroupControls=ye,this.removeListeners=ye,this.controls=new gg(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ye}unmount(){this.removeGroupControls(),this.removeListeners()}},dl=e=>(t,r)=>{e&&K.update(()=>e(t,r))},bg=class extends At{constructor(){super(...arguments),this.removePointerDownListener=ye}onPointerDown(e){this.session=new cu(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:dl(e),onStart:dl(t),onMove:r,onEnd:(i,o)=>{delete this.session,n&&K.update(()=>n(i,o))}}}mount(){this.removePointerDownListener=it(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}},jn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function hl(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}var Gr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(H.test(e))e=parseFloat(e);else return e;let r=hl(e,t.target.x),n=hl(e,t.target.y);return`${r}% ${n}%`}},Sg={correct:(e,{treeScale:t,projectionDelta:r})=>{let n=e,i=at.parse(e);if(i.length>5)return n;let o=at.createTransformer(e),a=typeof i[0]!="number"?1:0,s=r.x.scale*t.x,l=r.y.scale*t.y;i[0+a]/=s,i[1+a]/=l;let c=ae(s,l,.5);return typeof i[2+a]=="number"&&(i[2+a]/=c),typeof i[3+a]=="number"&&(i[3+a]/=c),o(i)}},wg=class extends h.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Mo(Cg),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),jn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||t===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||K.postRender(()=>{let a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}};function pu(e){let[t,r]=Gc(),n=M(en);return h.createElement(wg,{...e,layoutGroup:n,switchLayoutGroup:M(ti),isPresent:t,safeToRemove:r})}var Cg={borderRadius:{...Gr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Gr,borderTopRightRadius:Gr,borderBottomLeftRadius:Gr,borderBottomRightRadius:Gr,boxShadow:Sg},vu=["TopLeft","TopRight","BottomLeft","BottomRight"],Tg=vu.length,ml=e=>typeof e=="string"?parseFloat(e):e,pl=e=>typeof e=="number"||H.test(e);function Eg(e,t,r,n,i,o){i?(e.opacity=ae(0,r.opacity!==void 0?r.opacity:1,kg(n)),e.opacityExit=ae(t.opacity!==void 0?t.opacity:1,0,Rg(n))):o&&(e.opacity=ae(t.opacity!==void 0?t.opacity:1,r.opacity!==void 0?r.opacity:1,n));for(let a=0;a<Tg;a++){let s=`border${vu[a]}Radius`,l=vl(t,s),c=vl(r,s);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||pl(l)===pl(c)?(e[s]=Math.max(ae(ml(l),ml(c),n),0),(nt.test(c)||nt.test(l))&&(e[s]+="%")):e[s]=c}(t.rotate||r.rotate)&&(e.rotate=ae(t.rotate||0,r.rotate||0,n))}function vl(e,t){return e[t]!==void 0?e[t]:e.borderRadius}var kg=gu(0,.5,si),Rg=gu(.5,.95,ye);function gu(e,t,r){return n=>n<e?0:n>t?1:r(Lt(e,t,n))}function gl(e,t){e.min=t.min,e.max=t.max}function Ke(e,t){gl(e.x,t.x),gl(e.y,t.y)}function yl(e,t,r,n,i){return e-=t,e=ei(e,1/r,n),i!==void 0&&(e=ei(e,1/i,n)),e}function _g(e,t=0,r=1,n=.5,i,o=e,a=e){if(nt.test(t)&&(t=parseFloat(t),t=ae(a.min,a.max,t/100)-a.min),typeof t!="number")return;let s=ae(o.min,o.max,n);e===o&&(s-=t),e.min=yl(e.min,t,r,s,i),e.max=yl(e.max,t,r,s,i)}function xl(e,t,[r,n,i],o,a){_g(e,t[r],t[n],t[i],t.scale,o,a)}var Pg=["x","scaleX","originX"],Ig=["y","scaleY","originY"];function bl(e,t,r,n){xl(e.x,t,Pg,r?r.x:void 0,n?n.x:void 0),xl(e.y,t,Ig,r?r.y:void 0,n?n.y:void 0)}function Sl(e){return e.translate===0&&e.scale===1}function yu(e){return Sl(e.x)&&Sl(e.y)}function Eo(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function wl(e){return ze(e.x)/ze(e.y)}var Fg=class{constructor(){this.members=[]}add(e){Wo(this.members,e),e.scheduleRender()}remove(e){if(ui(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(e){let t=this.members.findIndex(n=>e===n);if(t===0)return!1;let r;for(let n=t;n>=0;n--){let i=this.members[n];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;n===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}};function Cl(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(n=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:l,rotateX:c,rotateY:u}=r;l&&(n+=`rotate(${l}deg) `),c&&(n+=`rotateX(${c}deg) `),u&&(n+=`rotateY(${u}deg) `)}let a=e.x.scale*t.x,s=e.y.scale*t.y;return(a!==1||s!==1)&&(n+=`scale(${a}, ${s})`),n||"none"}function Mg(e){W.MotionDebug&&W.MotionDebug.record(e)}function xu(e){return e instanceof SVGElement&&e.tagName!=="svg"}function oa(e,t,r){let n=xe(e)?e:ve(e);return n.start(Yo("",n,t,r)),n.animation}var Tl=["","X","Y","Z"],El=1e3,Lg=0,Yt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function bu({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(a={},s=t?.()){this.id=Lg++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{Yt.totalNodes=Yt.resolvedTargetDeltas=Yt.recalculatedProjection=0,this.nodes.forEach(Vg),this.nodes.forEach(Hg),this.nodes.forEach(Ng),this.nodes.forEach(Dg),Mg(Yt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Xc)}addEventListener(a,s){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new jo),this.eventHandlers.get(a).add(s)}notifyListeners(a,...s){let l=this.eventHandlers.get(a);l&&l.notify(...s)}hasListeners(a){return this.eventHandlers.has(a)}mount(a,s=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=xu(a),this.instance=a;let{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(a),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),s&&(c||l)&&(this.isLayoutDirty=!0),e){let f,d=()=>this.root.updateBlockedByResize=!1;e(a,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=Yc(d,250),jn.hasAnimatedSinceResize&&(jn.hasAnimatedSinceResize=!1,this.nodes.forEach(Rl))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:m,layout:p})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let g=this.options.transition||u.getDefaultTransition()||Gg,{onLayoutAnimationStart:x,onLayoutAnimationComplete:v}=u.getProps(),b=!this.targetLayout||!Eo(this.targetLayout,p)||m,y=!d&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||d&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,y);let S={...Uc(g,"layout"),onPlay:x,onComplete:v};(u.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else d||Rl(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=p})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,je(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($g),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){let f=this.path[u];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}let{layoutId:s,layout:l}=this.options;if(s===void 0&&!l)return;let c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(kl);return}this.isUpdating&&(this.isUpdating=!1,this.nodes.forEach(zg),this.nodes.forEach(Og),this.nodes.forEach(Ag),this.clearAllSnapshots(),qt.update.process(he),qt.preRender.process(he),qt.render.process(he))}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Bg),this.sharedNodes.forEach(Ug)}scheduleUpdateProjection(){K.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=pe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:s}=this.options;s&&s.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let s=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(s=!1),s&&(this.scroll={animationId:this.root.animationId,phase:a,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;let a=this.isLayoutDirty||this.shouldResetTransform,s=this.projectionDelta&&!yu(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;a&&(s||Xt(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){let s=this.measurePageBox(),l=this.removeElementScroll(s);return a&&(l=this.removeTransform(l)),Xg(l),{animationId:this.root.animationId,measuredBox:s,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return pe();let s=a.measureViewportBox(),{scroll:l}=this.root;return l&&(It(s.x,l.offset.x),It(s.y,l.offset.y)),s}removeElementScroll(a){let s=pe();Ke(s,a);for(let l=0;l<this.path.length;l++){let c=this.path[l],{scroll:u,options:f}=c;if(c!==this.root&&u&&f.layoutScroll){if(u.isRoot){Ke(s,a);let{scroll:d}=this.root;d&&(It(s.x,-d.offset.x),It(s.y,-d.offset.y))}It(s.x,u.offset.x),It(s.y,u.offset.y)}}return s}applyTransform(a,s=!1){let l=pe();Ke(l,a);for(let c=0;c<this.path.length;c++){let u=this.path[c];!s&&u.options.layoutScroll&&u.scroll&&u!==u.root&&xr(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Xt(u.latestValues)&&xr(l,u.latestValues)}return Xt(this.latestValues)&&xr(l,this.latestValues),l}removeTransform(a){let s=pe();Ke(s,a);for(let l=0;l<this.path.length;l++){let c=this.path[l];if(!c.instance||!Xt(c.latestValues))continue;Co(c.latestValues)&&c.updateSnapshot();let u=pe(),f=c.measurePageBox();Ke(u,f),bl(s,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Xt(this.latestValues)&&bl(s,this.latestValues),s}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:a.crossfade!==void 0?a.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==he.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){var s;let l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);let c=Boolean(this.resumingFrom)||this!==l;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty||this.attemptToResolveRelativeTarget))return;let{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=he.timestamp,!this.targetDelta&&!this.relativeTarget){let m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=pe(),this.relativeTargetOrigin=pe(),Jr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Ke(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=pe(),this.targetWithTransforms=pe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),_v(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ke(this.target,this.layout.layoutBox),hu(this.target,this.targetDelta)):Ke(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let m=this.getClosestProjectingParent();m&&Boolean(m.resumingFrom)===Boolean(this.resumingFrom)&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=pe(),this.relativeTargetOrigin=pe(),Jr(this.relativeTargetOrigin,this.target,m.target),Ke(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Yt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Co(this.parent.latestValues)||du(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var a;let s=this.getLead(),l=Boolean(this.resumingFrom)||this!==s,c=!0;if((this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===he.timestamp&&(c=!1),c)return;let{layout:u,layoutId:f}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||f))return;Ke(this.layoutCorrected,this.layout.layoutBox);let d=this.treeScale.x,m=this.treeScale.y;dg(this.layoutCorrected,this.treeScale,this.path,l),s.layout&&!s.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(s.target=s.layout.layoutBox);let{target:p}=s;if(!p){this.projectionTransform&&(this.projectionDelta=yr(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=yr(),this.projectionDeltaWithTransform=yr());let g=this.projectionTransform;Zr(this.projectionDelta,this.layoutCorrected,p,this.latestValues),this.projectionTransform=Cl(this.projectionDelta,this.treeScale),(this.projectionTransform!==g||this.treeScale.x!==d||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p)),Yt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),a){let s=this.getStack();s&&s.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(a,s=!1){let l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},f=yr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!s;let d=pe(),m=l?l.source:void 0,p=this.layout?this.layout.source:void 0,g=m!==p,x=this.getStack(),v=!x||x.members.length<=1,b=Boolean(g&&!v&&this.options.crossfade===!0&&!this.path.some(jg));this.animationProgress=0;let y;this.mixTargetDelta=S=>{let C=S/1e3;_l(f.x,a.x,C),_l(f.y,a.y,C),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Jr(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Wg(this.relativeTarget,this.relativeTargetOrigin,d,C),y&&Eo(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=pe()),Ke(y,this.relativeTarget)),g&&(this.animationValues=u,Eg(u,c,this.latestValues,C,b,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(je(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{jn.hasAnimatedSinceResize=!0,this.currentAnimation=oa(0,El,{...a,onUpdate:s=>{this.mixTargetDelta(s),a.onUpdate&&a.onUpdate(s)},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(El),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:s,target:l,layout:c,latestValues:u}=a;if(!(!s||!l||!c)){if(this!==a&&this.layout&&c&&Su(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||pe();let f=ze(this.layout.layoutBox.x);l.x.min=a.target.x.min,l.x.max=l.x.min+f;let d=ze(this.layout.layoutBox.y);l.y.min=a.target.y.min,l.y.max=l.y.min+d}Ke(s,l),xr(s,u),Zr(this.projectionDeltaWithTransform,this.layoutCorrected,s,u)}}registerSharedNode(a,s){this.sharedNodes.has(a)||this.sharedNodes.set(a,new Fg),this.sharedNodes.get(a).add(s);let c=s.options.initialPromotionConfig;s.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(s):void 0})}isLead(){let a=this.getStack();return a?a.lead===this:!0}getLead(){var a;let{layoutId:s}=this.options;return s?((a=this.getStack())===null||a===void 0?void 0:a.lead)||this:this}getPrevLead(){var a;let{layoutId:s}=this.options;return s?(a=this.getStack())===null||a===void 0?void 0:a.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:s,preserveFollowOpacity:l}={}){let c=this.getStack();c&&c.promote(this,l),a&&(this.projectionDelta=void 0,this.needsReset=!0),s&&this.setOptions({transition:s})}relegate(){let a=this.getStack();return a?a.relegate(this):!1}resetRotation(){let{visualElement:a}=this.options;if(!a)return;let s=!1,{latestValues:l}=a;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(s=!0),!s)return;let c={};for(let u=0;u<Tl.length;u++){let f="rotate"+Tl[u];l[f]&&(c[f]=l[f],a.setStaticValue(f,0))}a.render();for(let u in c)a.setStaticValue(u,c[u]);a.scheduleRender()}getProjectionStyles(a={}){var s,l;let c={};if(!this.instance||this.isSVG)return c;if(this.isVisible)c.visibility="";else return{visibility:"hidden"};let u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Me(a.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;let f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){let g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=Me(a.pointerEvents)||""),this.hasProjected&&!Xt(this.latestValues)&&(g.transform=u?u({},""):"none",this.hasProjected=!1),g}let d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),c.transform=Cl(this.projectionDeltaWithTransform,this.treeScale,d),u&&(c.transform=u(d,c.transform));let{x:m,y:p}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${p.origin*100}% 0`,f.animationValues?c.opacity=f===this?(l=(s=d.opacity)!==null&&s!==void 0?s:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:c.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(let g in Yn){if(d[g]===void 0)continue;let{correct:x,applyTo:v}=Yn[g],b=c.transform==="none"?d[g]:x(d[g],f);if(v){let y=v.length;for(let S=0;S<y;S++)c[v[S]]=b}else c[g]=b}return this.options.layoutId&&(c.pointerEvents=f===this?Me(a.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var s;return(s=a.currentAnimation)===null||s===void 0?void 0:s.stop()}),this.root.nodes.forEach(kl),this.root.sharedNodes.clear()}}}function Og(e){e.updateLayout()}function Ag(e){var t;let r=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:i}=e.layout,{animationType:o}=e.options,a=r.source!==e.layout.source;o==="size"?tt(f=>{let d=a?r.measuredBox[f]:r.layoutBox[f],m=ze(d);d.min=n[f].min,d.max=d.min+m}):Su(o,r.layoutBox,n)&&tt(f=>{let d=a?r.measuredBox[f]:r.layoutBox[f],m=ze(n[f]);d.max=d.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+m)});let s=yr();Zr(s,n,r.layoutBox);let l=yr();a?Zr(l,e.applyTransform(i,!0),r.measuredBox):Zr(l,n,r.layoutBox);let c=!yu(s),u=!1;if(!e.resumeFrom){let f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){let{snapshot:d,layout:m}=f;if(d&&m){let p=pe();Jr(p,r.layoutBox,d.layoutBox);let g=pe();Jr(g,n,m.layoutBox),Eo(p,g)||(u=!0),f.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=p,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:r,delta:l,layoutDelta:s,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Vg(e){Yt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Dg(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Bg(e){e.clearSnapshot()}function kl(e){e.clearMeasurements()}function zg(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Rl(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Hg(e){e.resolveTargetDelta()}function Ng(e){e.calcProjection()}function $g(e){e.resetRotation()}function Ug(e){e.removeLeadSnapshot()}function _l(e,t,r){e.translate=ae(t.translate,0,r),e.scale=ae(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function Pl(e,t,r,n){e.min=ae(t.min,r.min,n),e.max=ae(t.max,r.max,n)}function Wg(e,t,r,n){Pl(e.x,t.x,r.x,n),Pl(e.y,t.y,r.y,n)}function jg(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}var Gg={duration:.45,ease:[.4,0,.1,1]};function Il(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function Xg(e){Il(e.x),Il(e.y)}function Su(e,t,r){return e==="position"||e==="preserve-aspect"&&!bo(wl(t),wl(r),.2)}var Yg=bu({attachResizeListener:(e,t)=>rt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Zt={current:void 0},wu=bu({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Zt.current){let e=new Yg({});e.mount(W),e.setOptions({layoutScroll:!0}),Zt.current=e}return Zt.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>Boolean(W.getComputedStyle(e).position==="fixed")}),Cu={pan:{Feature:bg},drag:{Feature:xg,ProjectionNode:wu,MeasureLayout:pu}},Kg=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function qg(e){let t=Kg.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}var Zg=4;function ko(e,t,r=1){Ee(r<=Zg,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,i]=qg(e);if(!n)return;let o=W.getComputedStyle(t).getPropertyValue(n);return o?o.trim():co(i)?ko(i,t,r+1):i}function Jg(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};r&&(r={...r}),e.values.forEach(i=>{let o=i.get();if(!co(o))return;let a=ko(o,n);a&&i.set(a)});for(let i in t){let o=t[i];if(!co(o))continue;let a=ko(o,n);a&&(t[i]=a,r||(r={}),r[i]===void 0&&(r[i]=o))}return{target:t,transitionEnd:r}}var Qg=new Set(["width","height","top","left","right","bottom","x","y"]),Tu=e=>Qg.has(e),ey=e=>Object.keys(e).some(Tu),Wn=e=>e===tr||e===H,Fl=(e,t)=>parseFloat(e.split(", ")[t]),Ml=(e,t)=>(r,{transform:n})=>{if(n==="none"||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/);if(i)return Fl(i[1],t);{let o=n.match(/^matrix\((.+)\)$/);return o?Fl(o[1],e):0}},ty=new Set(["x","y","z"]),ry=ln.filter(e=>!ty.has(e));function ny(e){let t=[];return ry.forEach(r=>{let n=e.getValue(r);n!==void 0&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}var Ll={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Ml(4,13),y:Ml(5,14)},iy=(e,t,r)=>{let n=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:a}=o,s={};a==="none"&&t.setStaticValue("display",e.display||"block"),r.forEach(c=>{s[c]=Ll[c](n,o)}),t.render();let l=t.measureViewportBox();return r.forEach(c=>{let u=t.getValue(c);u&&u.jump(s[c]),e[c]=Ll[c](l,o)}),e},oy=(e,t,r={},n={})=>{t={...t},n={...n};let i=Object.keys(t).filter(Tu),o=[],a=!1,s=[];if(i.forEach(l=>{let c=e.getValue(l);if(!e.hasValue(l))return;let u=r[l],f=jr(u),d=t[l],m;if(Kn(d)){let p=d.length,g=d[0]===null?1:0;u=d[g],f=jr(u);for(let x=g;x<p&&d[x]!==null;x++)m?Ee(jr(d[x])===m,"All keyframes must be of the same type"):(m=jr(d[x]),Ee(m===f||Wn(f)&&Wn(m),"Keyframes must be of the same dimension as the current value"))}else m=jr(d);if(f!==m)if(Wn(f)&&Wn(m)){let p=c.get();typeof p=="string"&&c.set(parseFloat(p)),typeof d=="string"?t[l]=parseFloat(d):Array.isArray(d)&&m===H&&(t[l]=d.map(parseFloat))}else f?.transform&&m?.transform&&(u===0||d===0)?u===0?c.set(m.transform(u)):t[l]=f.transform(d):(a||(o=ny(e),a=!0),s.push(l),n[l]=n[l]!==void 0?n[l]:t[l],c.jump(d))}),s.length){let l=s.indexOf("height")>=0?W.pageYOffset:null,c=iy(t,e,s);return o.length&&o.forEach(([u,f])=>{e.getValue(u).set(f)}),e.render(),sn&&l!==null&&W.scrollTo({top:l}),{target:c,transitionEnd:n}}else return{target:t,transitionEnd:n}};function ay(e,t,r,n){return ey(t)?oy(e,t,r,n):{target:t,transitionEnd:n}}var sy=(e,t,r,n)=>{let i=Jg(e,t,n);return t=i.target,n=i.transitionEnd,ay(e,t,r,n)},Eu=class extends Zc{sortInstanceNodePosition(e,t){return e.compareDocumentPosition(t)&2?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},i){let o=ev(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),o&&(o=n(o))),i){Dc(this,r,o);let a=sy(this,r,o,t);t=a.transitionEnd,r=a.target}return{transition:e,transitionEnd:t,...r}}};function ly(e){return W.getComputedStyle(e)}var ku=class extends Eu{readValueFromInstance(e,t){if(er.has(t)){let r=Go(t);return r&&r.default||0}else{let r=ly(e),n=(yc(t)?r.getPropertyValue(t):r[t])||0;return typeof n=="string"?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return mu(e,t)}build(e,t,r,n){ea(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return ia(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;xe(e)&&(this.childSubscription=e.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(e,t,r,n){ru(e,t,r,n)}},Ru=class extends Eu{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(er.has(t)){let r=Go(t);return r&&r.default||0}return t=nu.has(t)?t:Ao(t),e.getAttribute(t)}measureInstanceViewportBox(){return pe()}scrapeMotionValuesFromProps(e,t){return ou(e,t)}build(e,t,r,n){ra(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){iu(e,t,r,n)}mount(e){this.isSVGTag=na(e.tagName),super.mount(e)}},_u=(e,t)=>Qo(e)?new Ru(t,{enableHardwareAcceleration:!1}):new ku(t,{enableHardwareAcceleration:!0}),Pu={layout:{ProjectionNode:wu,MeasureLayout:pu}},cy={...Zo,...lu,...Cu,...Pu},ke=Jc((e,t)=>au(e,t,cy,_u));var uy=Jc(au);function Iu(){let e=O(!1);return Ft(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function hi(){let e=Iu(),[t,r]=Ue(0),n=oe(()=>{e.current&&r(t+1)},[t]);return[oe(()=>K.postRender(n),[n]),t]}function Fu(e){return N(()=>()=>e(),[])}var fy=class extends ue{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}};function dy({children:e,isPresent:t}){let r=Wt(),n=O(null),i=O({width:0,height:0,top:0,left:0});return jt(()=>{let{width:o,height:a,top:s,left:l}=i.current;if(t||!n.current||!o||!a)return;n.current.dataset.motionPopId=r;let c=document.createElement("style");return document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${a}px !important;
            top: ${s}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),re(fy,{isPresent:t,childRef:n,sizeRef:i},Ut(e,{ref:n}))}var oo=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:i,presenceAffectsLayout:o,mode:a})=>{let s=lt(hy),l=Wt(),c=ne(()=>({id:l,initial:t,isPresent:r,custom:i,onExitComplete:u=>{s.set(u,!0);for(let f of s.values())if(!f)return;n&&n()},register:u=>(s.set(u,!1),()=>s.delete(u))}),o?void 0:[r]);return ne(()=>{s.forEach((u,f)=>s.set(f,!1))},[r]),N(()=>{!r&&!s.size&&n&&n()},[r]),a==="popLayout"&&(e=re(dy,{isPresent:r},e)),re(Sr.Provider,{value:c},e)};function hy(){return new Map}var pr=e=>e.key||"";function my(e,t){e.forEach(r=>{let n=pr(r);t.set(n,r)})}function py(e){let t=[];return $r.forEach(e,r=>{mr(r)&&t.push(r)}),t}var aa=({children:e,custom:t,initial:r=!0,onExitComplete:n,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:a="sync"})=>{Ee(!i,"Replace exitBeforeEnter with mode='wait'");let s=M(en).forceRender||hi()[0],l=Iu(),c=py(e),u=c,f=O(new Map).current,d=O(u),m=O(new Map).current,p=O(!0);if(Ft(()=>{p.current=!1,my(c,m),d.current=u}),Fu(()=>{p.current=!0,m.clear(),f.clear()}),p.current)return re(Hn,null,u.map(b=>re(oo,{key:pr(b),isPresent:!0,initial:r?void 0:!1,presenceAffectsLayout:o,mode:a},b)));u=[...u];let g=d.current.map(pr),x=c.map(pr),v=g.length;for(let b=0;b<v;b++){let y=g[b];x.indexOf(y)===-1&&!f.has(y)&&f.set(y,void 0)}return a==="wait"&&f.size&&(u=[]),f.forEach((b,y)=>{if(x.indexOf(y)!==-1)return;let S=m.get(y);if(!S)return;let C=g.indexOf(y),w=b;if(!w){let T=()=>{m.delete(y),f.delete(y);let E=d.current.findIndex(R=>R.key===y);if(d.current.splice(E,1),!f.size){if(d.current=c,l.current===!1)return;s(),n&&n()}};w=re(oo,{key:pr(S),isPresent:!1,onExitComplete:T,custom:t,presenceAffectsLayout:o,mode:a},S),f.set(y,w)}u.splice(C,0,w)}),u=u.map(b=>{let y=b.key;return f.has(y)?b:re(oo,{key:pr(b),isPresent:!0,presenceAffectsLayout:o,mode:a},b)}),re(Hn,null,f.size?u:u.map(b=>Ut(b)))};function Mu({children:e,isValidProp:t,...r}){t&&rc(t),r={...M(Jt),...r},r.isStatic=lt(()=>r.isStatic);let n=ne(()=>r,[JSON.stringify(r.transition),r.transformPagePoint,r.reducedMotion]);return re(Jt.Provider,{value:n},e)}var Lu=fe(null),vy=e=>!e.isLayoutDirty&&e.willUpdate(!1);function Ol(){let e=new Set,t=new WeakMap,r=()=>e.forEach(vy);return{add:n=>{e.add(n),t.set(n,n.addEventListener("willUpdate",r))},remove:n=>{e.delete(n);let i=t.get(n);i&&(i(),t.delete(n)),r()},dirty:r}}var Ou=e=>e===!0,gy=e=>Ou(e===!0)||e==="id",Au=({children:e,id:t,inherit:r=!0})=>{let n=M(en),i=M(Lu),[o,a]=hi(),s=O(null),l=n.id||i;s.current===null&&(gy(r)&&l&&(t=t?l+"-"+t:l),s.current={id:t,group:Ou(r)&&n.group||Ol()});let c=ne(()=>({...s.current,forceRender:o}),[a]);return re(en.Provider,{value:c},e)};function ct(e){let t=lt(()=>ve(e)),{isStatic:r}=M(Jt);if(r){let[,n]=Ue(e);N(()=>t.on("change",n),[])}return t}var yy=e=>typeof e=="object"&&e.mix,xy=e=>yy(e)?e.mix:void 0;function dn(...e){let t=!Array.isArray(e[0]),r=t?0:-1,n=e[0+r],i=e[1+r],o=e[2+r],a=e[3+r],s=Tr(i,o,{mixer:xy(o[0]),...a});return t?s(n):s}function by(e,t){let r=ct(t()),n=()=>r.set(t());return n(),Ft(()=>{let i=()=>K.update(n,!1,!0),o=e.map(a=>a.on("change",i));return()=>{o.forEach(a=>a()),je(n)}}),r}function Je(e,t,r,n){let i=typeof t=="function"?t:dn(t,r,n);return Array.isArray(e)?Al(e,i):Al([e],([o])=>i(o))}function Al(e,t){let r=lt(()=>[]);return by(e,()=>{r.length=0;let n=e.length;for(let i=0;i<n;i++)r[i]=e[i].get();return t(r)})}var Vu=fe(null);function Sy(e,t,r,n){if(!n)return e;let i=e.findIndex(u=>u.value===t);if(i===-1)return e;let o=n>0?1:-1,a=e[i+o];if(!a)return e;let s=e[i],l=a.layout,c=ae(l.min,l.max,.5);return o===1&&s.layout.max+r>c||o===-1&&s.layout.min+r<c?zp(e,i,i+o):e}function wy({children:e,as:t="ul",axis:r="y",onReorder:n,values:i,...o},a){let s=lt(()=>ke(t)),l=[],c=O(!1);Ee(Boolean(i),"Reorder.Group must be provided a values prop");let u={axis:r,registerItem:(f,d)=>{d&&l.findIndex(m=>f===m.value)===-1&&(l.push({value:f,layout:d[r]}),l.sort(Ty))},updateOrder:(f,d,m)=>{if(c.current)return;let p=Sy(l,f,d,m);l!==p&&(c.current=!0,n(p.map(Cy).filter(g=>i.indexOf(g)!==-1)))}};return N(()=>{c.current=!1}),re(s,{...o,ref:a,ignoreStrict:!0},re(Vu.Provider,{value:u},e))}var _k=Ie(wy);function Cy(e){return e.value}function Ty(e,t){return e.layout.min-t.layout.min}function Vl(e,t=0){return xe(e)?e:ct(t)}function Ey({children:e,style:t={},value:r,as:n="li",onDrag:i,layout:o=!0,...a},s){let l=lt(()=>ke(n)),c=M(Vu),u={x:Vl(t.x),y:Vl(t.y)},f=Je([u.x,u.y],([x,v])=>x||v?1:"unset"),d=O(null);Ee(Boolean(c),"Reorder.Item must be a child of Reorder.Group");let{axis:m,registerItem:p,updateOrder:g}=c;return N(()=>{p(r,d.current)},[c]),re(l,{drag:m,...a,dragSnapToOrigin:!0,style:{...t,x:u.x,y:u.y,zIndex:f},layout:o,onDrag:(x,v)=>{let{velocity:b}=v;b[m]&&g(r,u[m].get(),b[m]),i&&i(x,v)},onLayoutMeasure:x=>{d.current=x},ref:s,ignoreStrict:!0},e)}var Ik=Ie(Ey);var Du={renderer:_u,...Zo,...lu},ky={...Du,...Cu,...Pu};function sa(e,t,r){var n;if(typeof e=="string"){let i=document;t&&(Ee(Boolean(t.current),"Scope provided, but no element detected."),i=t.current),r?((n=r[e])!==null&&n!==void 0||(r[e]=i.querySelectorAll(e)),e=r[e]):e=i.querySelectorAll(e)}else e instanceof Element&&(e=[e]);return Array.from(e||[])}var Gn=new WeakMap,Pt;function Ry(e,t){if(t){let{inlineSize:r,blockSize:n}=t[0];return{width:r,height:n}}else return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}function _y({target:e,contentRect:t,borderBoxSize:r}){var n;(n=Gn.get(e))===null||n===void 0||n.forEach(i=>{i({target:e,contentSize:t,get size(){return Ry(e,r)}})})}function Py(e){e.forEach(_y)}function Iy(){typeof ResizeObserver>"u"||(Pt=new ResizeObserver(Py))}function Fy(e,t){Pt||Iy();let r=sa(e);return r.forEach(n=>{let i=Gn.get(n);i||(i=new Set,Gn.set(n,i)),i.add(t),Pt?.observe(n)}),()=>{r.forEach(n=>{let i=Gn.get(n);i?.delete(t),i?.size||Pt?.unobserve(n)})}}var Xn=new Set,Qr;function My(){Qr=()=>{let e={width:W.innerWidth,height:W.innerHeight},t={target:W,size:e,contentSize:e};Xn.forEach(r=>r(t))},W.addEventListener("resize",Qr)}function Ly(e){return Xn.add(e),Qr||My(),()=>{Xn.delete(e),!Xn.size&&Qr&&(Qr=void 0)}}function Oy(e,t){return typeof e=="function"?Ly(e):Fy(e,t)}var Ay=50,Dl=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),Vy=()=>({time:0,x:Dl(),y:Dl()}),Dy={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Bl(e,t,r,n){let i=r[t],{length:o,position:a}=Dy[t],s=i.current,l=r.time;i.current=e["scroll"+a],i.scrollLength=e["scroll"+o]-e["client"+o],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=Lt(0,i.scrollLength,i.current);let c=n-l;i.velocity=c>Ay?0:$o(i.current-s,c)}function By(e,t,r){Bl(e,"x",t,r),Bl(e,"y",t,r),t.time=r}function zy(e,t){let r={x:0,y:0},n=e;for(;n&&n!==t;)if(n instanceof HTMLElement)r.x+=n.offsetLeft,r.y+=n.offsetTop,n=n.offsetParent;else if(n instanceof SVGGraphicsElement&&"getBBox"in n){let{top:i,left:o}=n.getBBox();for(r.x+=o,r.y+=i;n&&n.tagName!=="svg";)n=n.parentNode}return r}var Hy={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Ro={start:0,center:.5,end:1};function zl(e,t,r=0){let n=0;if(Ro[e]!==void 0&&(e=Ro[e]),typeof e=="string"){let i=parseFloat(e);e.endsWith("px")?n=i:e.endsWith("%")?e=i/100:e.endsWith("vw")?n=i/100*document.documentElement.clientWidth:e.endsWith("vh")?n=i/100*document.documentElement.clientHeight:e=i}return typeof e=="number"&&(n=t*e),r+n}var Ny=[0,0];function $y(e,t,r,n){let i=Array.isArray(e)?e:Ny,o=0,a=0;return typeof e=="number"?i=[e,e]:typeof e=="string"&&(e=e.trim(),e.includes(" ")?i=e.split(" "):i=[e,Ro[e]?e:"0"]),o=zl(i[0],r,n),a=zl(i[1],t),o-a}var Uy={x:0,y:0};function Wy(e,t,r){let{offset:n=Hy.All}=r,{target:i=e,axis:o="y"}=r,a=o==="y"?"height":"width",s=i!==e?zy(i,e):Uy,l=i===e?{width:e.scrollWidth,height:e.scrollHeight}:{width:i.clientWidth,height:i.clientHeight},c={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let u=!t[o].interpolate,f=n.length;for(let d=0;d<f;d++){let m=$y(n[d],c[a],l[a],s[o]);!u&&m!==t[o].interpolatorOffsets[d]&&(u=!0),t[o].offset[d]=m}u&&(t[o].interpolate=Tr(t[o].offset,Uo(n)),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=t[o].interpolate(t[o].current)}function jy(e,t=e,r){if(r.x.targetOffset=0,r.y.targetOffset=0,t!==e){let n=t;for(;n&&n!==e;)r.x.targetOffset+=n.offsetLeft,r.y.targetOffset+=n.offsetTop,n=n.offsetParent}r.x.targetLength=t===e?t.scrollWidth:t.clientWidth,r.y.targetLength=t===e?t.scrollHeight:t.clientHeight,r.x.containerLength=e.clientWidth,r.y.containerLength=e.clientHeight}function Gy(e,t,r,n={}){return{measure:()=>jy(e,n.target,r),update:i=>{By(e,r,i),(n.offset||n.target)&&Wy(e,r,n)},notify:()=>t(r)}}var Xr=new WeakMap,Hl=new WeakMap,ao=new WeakMap,Nl=e=>e===document.documentElement?W:e;function hn(e,{container:t=document.documentElement,...r}={}){let n=ao.get(t);n||(n=new Set,ao.set(t,n));let i=Vy(),o=Gy(t,e,i,r);if(n.add(o),!Xr.has(t)){let s=()=>{for(let d of n)d.measure()},l=()=>{for(let d of n)d.update(he.timestamp)},c=()=>{for(let d of n)d.notify()},u=()=>{K.read(s,!1,!0),K.update(l,!1,!0),K.update(c,!1,!0)};Xr.set(t,u);let f=Nl(t);W.addEventListener("resize",u,{passive:!0}),t!==document.documentElement&&Hl.set(t,Oy(t,u)),f.addEventListener("scroll",u,{passive:!0})}let a=Xr.get(t);return K.read(a,!1,!0),()=>{var s;je(a);let l=ao.get(t);if(!l||(l.delete(o),l.size))return;let c=Xr.get(t);Xr.delete(t),c&&(Nl(t).removeEventListener("scroll",c),(s=Hl.get(t))===null||s===void 0||s(),W.removeEventListener("resize",c))}}function $l(e,t){fn(Boolean(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}var Xy=()=>({scrollX:ve(0),scrollY:ve(0),scrollXProgress:ve(0),scrollYProgress:ve(0)});function Bu({container:e,target:t,layoutEffect:r=!0,...n}={}){let i=lt(Xy);return(r?Ft:N)(()=>($l("target",t),$l("container",e),hn(({x:a,y:s})=>{i.scrollX.set(a.current),i.scrollXProgress.set(a.progress),i.scrollY.set(s.current),i.scrollYProgress.set(s.progress)},{...n,container:e?.current||void 0,target:t?.current||void 0})),[]),i}function zu(){!Jo.current&&Kc();let[e]=Ue(Qn.current);return e}function mn(){let e=zu(),{reducedMotion:t}=M(Jt);return t==="never"?!1:t==="always"?!0:e}function Yy(e){e.values.forEach(t=>t.stop())}function Hu(){let e=!1,t=new Set,r={subscribe(n){return t.add(n),()=>void t.delete(n)},start(n,i){Ee(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let o=[];return t.forEach(a=>{o.push(qo(a,n,{transitionOverride:i}))}),Promise.all(o)},set(n){return Ee(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),t.forEach(i=>{Jp(i,n)})},stop(){t.forEach(n=>{Yy(n)})},mount(){return e=!0,()=>{e=!1,r.stop()}}};return r}var Nu=(e,t,r)=>{let n=t-e;return((r-e)%n+n)%n+e},$u=class{constructor(e){this.animations=e.filter(Boolean)}then(e,t){return Promise.all(this.animations).then(e).catch(t)}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}stop(){this.runAll("stop")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}};function Ky(e){return typeof e=="object"&&!Array.isArray(e)}function qy(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},r=xu(e)?new Ru(t,{enableHardwareAcceleration:!1}):new ku(t,{enableHardwareAcceleration:!0});r.mount(e),Qt.set(e,r)}function Zy(e,t=100){let r=ci({keyframes:[0,t],...e}),n=Math.min(ho(r),fo);return{type:"keyframes",ease:i=>r.next(n*i).value/t,duration:ot(n)}}function Ul(e,t,r,n){var i;return typeof t=="number"?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):t==="<"?r:(i=n.get(t))!==null&&i!==void 0?i:e}function Jy(e,t){return Ic(e)?e[Nu(0,e.length,t)]:e}function Qy(e,t,r){for(let n=0;n<e.length;n++){let i=e[n];i.at>t&&i.at<r&&(ui(e,i),n--)}}function e0(e,t,r,n,i,o){Qy(e,i,o);for(let a=0;a<t.length;a++)e.push({value:t[a],at:ae(i,o,n[a]),easing:Jy(r,a)})}function t0(e,t){return e.at===t.at?e.value===null?1:t.value===null?-1:0:e.at-t.at}var r0="easeInOut";function n0(e,{defaultTransition:t={},...r}={},n){let i=t.duration||.3,o=new Map,a=new Map,s={},l=new Map,c=0,u=0,f=0;for(let d=0;d<e.length;d++){let m=e[d];if(typeof m=="string"){l.set(m,u);continue}else if(!Array.isArray(m)){l.set(m.name,Ul(u,m.at,c,l));continue}let[p,g,x={}]=m;x.at!==void 0&&(u=Ul(u,x.at,c,l));let v=0,b=(y,S,C,w=0,T=0)=>{let E=i0(y),{delay:R=0,times:_=Uo(E),type:F="keyframes",...B}=S,{ease:L=t.ease||"easeOut",duration:P}=S,I=typeof R=="function"?R(w,T):R,U=E.length;if(U<=2&&F==="spring"){let j=100;if(U===2&&s0(E)){let Y=E[1]-E[0];j=Math.abs(Y)}let V={...B};P!==void 0&&(V.duration=wt(P));let Z=Zy(V,j);L=Z.ease,P=Z.duration}P??(P=i);let k=u+I,A=k+P;_.length===1&&_[0]===0&&(_[1]=1);let X=_.length-E.length;X>0&&Fc(_,X),E.length===1&&E.unshift(null),e0(C,E,L,_,k,A),v=Math.max(I+P,v),f=Math.max(A,f)};if(xe(p)){let y=Wl(p,a);b(g,x,jl("default",y))}else{let y=sa(p,n,s),S=y.length;for(let C=0;C<S;C++){g=g,x=x;let w=y[C],T=Wl(w,a);for(let E in g)b(g[E],o0(x,E),jl(E,T),C,S)}c=u,u+=v}}return a.forEach((d,m)=>{for(let p in d){let g=d[p];g.sort(t0);let x=[],v=[],b=[];for(let S=0;S<g.length;S++){let{at:C,value:w,easing:T}=g[S];x.push(w),v.push(Lt(0,f,C)),b.push(T||"easeOut")}v[0]!==0&&(v.unshift(0),x.unshift(x[0]),b.unshift(r0)),v[v.length-1]!==1&&(v.push(1),x.push(null)),o.has(m)||o.set(m,{keyframes:{},transition:{}});let y=o.get(m);y.keyframes[p]=x,y.transition[p]={...t,duration:f,ease:b,times:v,...r}}}),o}function Wl(e,t){return!t.has(e)&&t.set(e,{}),t.get(e)}function jl(e,t){return t[e]||(t[e]=[]),t[e]}function i0(e){return Array.isArray(e)?e:[e]}function o0(e,t){return e[t]?{...e,...e[t]}:{...e}}var a0=e=>typeof e=="number",s0=e=>e.every(a0);function Uu(e,t,r,n){let i=sa(e,n),o=i.length;Ee(Boolean(o),"No valid element provided.");let a=[];for(let s=0;s<o;s++){let l=i[s];Qt.has(l)||qy(l);let c=Qt.get(l),u={...r};typeof u.delay=="function"&&(u.delay=u.delay(s,o)),a.push(...Ko(c,{...t,transition:u},{}))}return new $u(a)}var l0=e=>Array.isArray(e)&&Array.isArray(e[0]);function c0(e,t,r){let n=[];return n0(e,t,r).forEach(({keyframes:o,transition:a},s)=>{let l;xe(s)?l=oa(s,o.default,a.default):l=Uu(s,o,a),n.push(l)}),new $u(n)}var Wu=e=>{function t(r,n,i){let o;return l0(r)?o=c0(r,n,e):Ky(n)?o=Uu(r,n,i,e):o=oa(r,n,i),e&&e.animations.push(o),o}return t},mi=Wu();function ju(){let e=lt(Hu);return Ft(e.mount,[]),e}var Gu=ju;var Xu=class{constructor(){this.componentControls=new Set}subscribe(e){return this.componentControls.add(e),()=>this.componentControls.delete(e)}start(e,t){this.componentControls.forEach(r=>{r.start(e.nativeEvent||e,t)})}},u0=()=>new Xu;function Yu(){return lt(u0)}function Ku(e,t,r,n){N(()=>{let i=e.current;if(r&&i)return rt(i,t,r,n)},[e,t,r,n])}function la(e){return e!==null&&typeof e=="object"&&Fo in e}function qu(e){if(la(e))return e[Fo]}function ca(){return f0}function f0(e){Zt.current&&(Zt.current.isUpdating=!1,Zt.current.blockUpdate(),e&&e())}function Zu(){let[e,t]=hi(),r=ca();return N(()=>{K.postRender(()=>K.postRender(()=>vo.current=!1))},[t]),n=>{r(()=>{vo.current=!0,e(),n()})}}function Ju(){return oe(()=>{let t=Zt.current;t&&t.resetTree()},[])}var Gl=()=>({});var $k=oi({scrapeMotionValuesFromProps:Gl,createRenderState:Gl});var d0=wr.reduce((e,t)=>(e[t]=r=>je(r),e),{});var h0=st({"../../../node_modules/hsluv/hsluv.js"(e,t){var r=r||{};r.Geometry=function(){},r.Geometry.intersectLineLine=function(i,o){var a=(i.intercept-o.intercept)/(o.slope-i.slope),s=i.slope*a+i.intercept;return{x:a,y:s}},r.Geometry.distanceFromOrigin=function(i){return Math.sqrt(Math.pow(i.x,2)+Math.pow(i.y,2))},r.Geometry.distanceLineFromOrigin=function(i){return Math.abs(i.intercept)/Math.sqrt(Math.pow(i.slope,2)+1)},r.Geometry.perpendicularThroughPoint=function(i,o){var a=-1/i.slope,s=o.y-a*o.x;return{slope:a,intercept:s}},r.Geometry.angleFromOrigin=function(i){return Math.atan2(i.y,i.x)},r.Geometry.normalizeAngle=function(i){var o=2*Math.PI;return(i%o+o)%o},r.Geometry.lengthOfRayUntilIntersect=function(i,o){return o.intercept/(Math.sin(i)-o.slope*Math.cos(i))},r.Hsluv=function(){},r.Hsluv.getBounds=function(i){for(var o=[],a=Math.pow(i+16,3)/1560896,s=a>r.Hsluv.epsilon?a:i/r.Hsluv.kappa,l=0;l<3;)for(var c=l++,u=r.Hsluv.m[c][0],f=r.Hsluv.m[c][1],d=r.Hsluv.m[c][2],m=0;m<2;){var p=m++,g=(284517*u-94839*d)*s,x=(838422*d+769860*f+731718*u)*i*s-769860*p*i,v=(632260*d-126452*f)*s+126452*p;o.push({slope:g/v,intercept:x/v})}return o},r.Hsluv.maxSafeChromaForL=function(i){for(var o=r.Hsluv.getBounds(i),a=1/0,s=0;s<o.length;){var l=o[s];++s;var c=r.Geometry.distanceLineFromOrigin(l);a=Math.min(a,c)}return a},r.Hsluv.maxChromaForLH=function(i,o){for(var a=o/360*Math.PI*2,s=r.Hsluv.getBounds(i),l=1/0,c=0;c<s.length;){var u=s[c];++c;var f=r.Geometry.lengthOfRayUntilIntersect(a,u);f>=0&&(l=Math.min(l,f))}return l},r.Hsluv.dotProduct=function(i,o){for(var a=0,s=0,l=i.length;s<l;){var c=s++;a+=i[c]*o[c]}return a},r.Hsluv.fromLinear=function(i){return i<=.0031308?12.92*i:1.055*Math.pow(i,.4166666666666667)-.055},r.Hsluv.toLinear=function(i){return i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92},r.Hsluv.xyzToRgb=function(i){return[r.Hsluv.fromLinear(r.Hsluv.dotProduct(r.Hsluv.m[0],i)),r.Hsluv.fromLinear(r.Hsluv.dotProduct(r.Hsluv.m[1],i)),r.Hsluv.fromLinear(r.Hsluv.dotProduct(r.Hsluv.m[2],i))]},r.Hsluv.rgbToXyz=function(i){var o=[r.Hsluv.toLinear(i[0]),r.Hsluv.toLinear(i[1]),r.Hsluv.toLinear(i[2])];return[r.Hsluv.dotProduct(r.Hsluv.minv[0],o),r.Hsluv.dotProduct(r.Hsluv.minv[1],o),r.Hsluv.dotProduct(r.Hsluv.minv[2],o)]},r.Hsluv.yToL=function(i){return i<=r.Hsluv.epsilon?i/r.Hsluv.refY*r.Hsluv.kappa:116*Math.pow(i/r.Hsluv.refY,.3333333333333333)-16},r.Hsluv.lToY=function(i){return i<=8?r.Hsluv.refY*i/r.Hsluv.kappa:r.Hsluv.refY*Math.pow((i+16)/116,3)},r.Hsluv.xyzToLuv=function(i){var o=i[0],a=i[1],s=i[2],l=o+15*a+3*s,c=4*o,u=9*a;l!=0?(c/=l,u/=l):(c=NaN,u=NaN);var f=r.Hsluv.yToL(a);if(f==0)return[0,0,0];var d=13*f*(c-r.Hsluv.refU),m=13*f*(u-r.Hsluv.refV);return[f,d,m]},r.Hsluv.luvToXyz=function(i){var o=i[0],a=i[1],s=i[2];if(o==0)return[0,0,0];var l=a/(13*o)+r.Hsluv.refU,c=s/(13*o)+r.Hsluv.refV,u=r.Hsluv.lToY(o),f=0-9*u*l/((l-4)*c-l*c),d=(9*u-15*c*u-c*f)/(3*c);return[f,u,d]},r.Hsluv.luvToLch=function(i){var o=i[0],a=i[1],s=i[2],l=Math.sqrt(a*a+s*s),c;if(l<1e-8)c=0;else{var u=Math.atan2(s,a);c=u*180/Math.PI,c<0&&(c=360+c)}return[o,l,c]},r.Hsluv.lchToLuv=function(i){var o=i[0],a=i[1],s=i[2],l=s/360*2*Math.PI,c=Math.cos(l)*a,u=Math.sin(l)*a;return[o,c,u]},r.Hsluv.hsluvToLch=function(i){var o=i[0],a=i[1],s=i[2];if(s>99.9999999)return[100,0,o];if(s<1e-8)return[0,0,o];var l=r.Hsluv.maxChromaForLH(s,o),c=l/100*a;return[s,c,o]},r.Hsluv.lchToHsluv=function(i){var o=i[0],a=i[1],s=i[2];if(o>99.9999999)return[s,0,100];if(o<1e-8)return[s,0,0];var l=r.Hsluv.maxChromaForLH(o,s),c=a/l*100;return[s,c,o]},r.Hsluv.hpluvToLch=function(i){var o=i[0],a=i[1],s=i[2];if(s>99.9999999)return[100,0,o];if(s<1e-8)return[0,0,o];var l=r.Hsluv.maxSafeChromaForL(s),c=l/100*a;return[s,c,o]},r.Hsluv.lchToHpluv=function(i){var o=i[0],a=i[1],s=i[2];if(o>99.9999999)return[s,0,100];if(o<1e-8)return[s,0,0];var l=r.Hsluv.maxSafeChromaForL(o),c=a/l*100;return[s,c,o]},r.Hsluv.rgbToHex=function(i){for(var o="#",a=0;a<3;){var s=a++,l=i[s],c=Math.round(l*255),u=c%16,f=(c-u)/16|0;o+=r.Hsluv.hexChars.charAt(f)+r.Hsluv.hexChars.charAt(u)}return o},r.Hsluv.hexToRgb=function(i){i=i.toLowerCase();for(var o=[],a=0;a<3;){var s=a++,l=r.Hsluv.hexChars.indexOf(i.charAt(s*2+1)),c=r.Hsluv.hexChars.indexOf(i.charAt(s*2+2)),u=l*16+c;o.push(u/255)}return o},r.Hsluv.lchToRgb=function(i){return r.Hsluv.xyzToRgb(r.Hsluv.luvToXyz(r.Hsluv.lchToLuv(i)))},r.Hsluv.rgbToLch=function(i){return r.Hsluv.luvToLch(r.Hsluv.xyzToLuv(r.Hsluv.rgbToXyz(i)))},r.Hsluv.hsluvToRgb=function(i){return r.Hsluv.lchToRgb(r.Hsluv.hsluvToLch(i))},r.Hsluv.rgbToHsluv=function(i){return r.Hsluv.lchToHsluv(r.Hsluv.rgbToLch(i))},r.Hsluv.hpluvToRgb=function(i){return r.Hsluv.lchToRgb(r.Hsluv.hpluvToLch(i))},r.Hsluv.rgbToHpluv=function(i){return r.Hsluv.lchToHpluv(r.Hsluv.rgbToLch(i))},r.Hsluv.hsluvToHex=function(i){return r.Hsluv.rgbToHex(r.Hsluv.hsluvToRgb(i))},r.Hsluv.hpluvToHex=function(i){return r.Hsluv.rgbToHex(r.Hsluv.hpluvToRgb(i))},r.Hsluv.hexToHsluv=function(i){return r.Hsluv.rgbToHsluv(r.Hsluv.hexToRgb(i))},r.Hsluv.hexToHpluv=function(i){return r.Hsluv.rgbToHpluv(r.Hsluv.hexToRgb(i))},r.Hsluv.m=[[3.240969941904521,-1.537383177570093,-.498610760293],[-.96924363628087,1.87596750150772,.041555057407175],[.055630079696993,-.20397695888897,1.056971514242878]],r.Hsluv.minv=[[.41239079926595,.35758433938387,.18048078840183],[.21263900587151,.71516867876775,.072192315360733],[.019330818715591,.11919477979462,.95053215224966]],r.Hsluv.refY=1,r.Hsluv.refU=.19783000664283,r.Hsluv.refV=.46831999493879,r.Hsluv.kappa=903.2962962,r.Hsluv.epsilon=.0088564516,r.Hsluv.hexChars="0123456789abcdef";var n={hsluvToRgb:r.Hsluv.hsluvToRgb,rgbToHsluv:r.Hsluv.rgbToHsluv,hpluvToRgb:r.Hsluv.hpluvToRgb,rgbToHpluv:r.Hsluv.rgbToHpluv,hsluvToHex:r.Hsluv.hsluvToHex,hexToHsluv:r.Hsluv.hexToHsluv,hpluvToHex:r.Hsluv.hpluvToHex,hexToHpluv:r.Hsluv.hexToHpluv,lchToHpluv:r.Hsluv.lchToHpluv,hpluvToLch:r.Hsluv.hpluvToLch,lchToHsluv:r.Hsluv.lchToHsluv,hsluvToLch:r.Hsluv.hsluvToLch,lchToLuv:r.Hsluv.lchToLuv,luvToLch:r.Hsluv.luvToLch,xyzToLuv:r.Hsluv.xyzToLuv,luvToXyz:r.Hsluv.luvToXyz,xyzToRgb:r.Hsluv.xyzToRgb,rgbToXyz:r.Hsluv.rgbToXyz,lchToRgb:r.Hsluv.lchToRgb,rgbToLch:r.Hsluv.rgbToLch};t.exports=n}}),m0=st({"../../../node_modules/eventemitter3/index.js"(e,t){"use strict";var r=Object.prototype.hasOwnProperty,n="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(n=!1));function o(c,u,f){this.fn=c,this.context=u,this.once=f||!1}function a(c,u,f,d,m){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new o(f,d||c,m),g=n?n+u:u;return c._events[g]?c._events[g].fn?c._events[g]=[c._events[g],p]:c._events[g].push(p):(c._events[g]=p,c._eventsCount++),c}function s(c,u){--c._eventsCount===0?c._events=new i:delete c._events[u]}function l(){this._events=new i,this._eventsCount=0}l.prototype.eventNames=function(){var u=[],f,d;if(this._eventsCount===0)return u;for(d in f=this._events)r.call(f,d)&&u.push(n?d.slice(1):d);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(f)):u},l.prototype.listeners=function(u){var f=n?n+u:u,d=this._events[f];if(!d)return[];if(d.fn)return[d.fn];for(var m=0,p=d.length,g=new Array(p);m<p;m++)g[m]=d[m].fn;return g},l.prototype.listenerCount=function(u){var f=n?n+u:u,d=this._events[f];return d?d.fn?1:d.length:0},l.prototype.emit=function(u,f,d,m,p,g){var x=n?n+u:u;if(!this._events[x])return!1;var v=this._events[x],b=arguments.length,y,S;if(v.fn){switch(v.once&&this.removeListener(u,v.fn,void 0,!0),b){case 1:return v.fn.call(v.context),!0;case 2:return v.fn.call(v.context,f),!0;case 3:return v.fn.call(v.context,f,d),!0;case 4:return v.fn.call(v.context,f,d,m),!0;case 5:return v.fn.call(v.context,f,d,m,p),!0;case 6:return v.fn.call(v.context,f,d,m,p,g),!0}for(S=1,y=new Array(b-1);S<b;S++)y[S-1]=arguments[S];v.fn.apply(v.context,y)}else{var C=v.length,w;for(S=0;S<C;S++)switch(v[S].once&&this.removeListener(u,v[S].fn,void 0,!0),b){case 1:v[S].fn.call(v[S].context);break;case 2:v[S].fn.call(v[S].context,f);break;case 3:v[S].fn.call(v[S].context,f,d);break;case 4:v[S].fn.call(v[S].context,f,d,m);break;default:if(!y)for(w=1,y=new Array(b-1);w<b;w++)y[w-1]=arguments[w];v[S].fn.apply(v[S].context,y)}}return!0},l.prototype.on=function(u,f,d){return a(this,u,f,d,!1)},l.prototype.once=function(u,f,d){return a(this,u,f,d,!0)},l.prototype.removeListener=function(u,f,d,m){var p=n?n+u:u;if(!this._events[p])return this;if(!f)return s(this,p),this;var g=this._events[p];if(g.fn)g.fn===f&&(!m||g.once)&&(!d||g.context===d)&&s(this,p);else{for(var x=0,v=[],b=g.length;x<b;x++)(g[x].fn!==f||m&&!g[x].once||d&&g[x].context!==d)&&v.push(g[x]);v.length?this._events[p]=v.length===1?v[0]:v:s(this,p)}return this},l.prototype.removeAllListeners=function(u){var f;return u?(f=n?n+u:u,this._events[f]&&s(this,f)):(this._events=new i,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,typeof t<"u"&&(t.exports=l)}}),gt=st({"../../../node_modules/process/browser.js"(e,t){var r=t.exports={},n,i;function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?n=setTimeout:n=o}catch{n=o}try{typeof clearTimeout=="function"?i=clearTimeout:i=a}catch{i=a}})();function s(v){if(n===setTimeout)return setTimeout(v,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(v,0);try{return n(v,0)}catch{try{return n.call(null,v,0)}catch{return n.call(this,v,0)}}}function l(v){if(i===clearTimeout)return clearTimeout(v);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(v);try{return i(v)}catch{try{return i.call(null,v)}catch{return i.call(this,v)}}}var c=[],u=!1,f,d=-1;function m(){!u||!f||(u=!1,f.length?c=f.concat(c):d=-1,c.length&&p())}function p(){if(!u){var v=s(m);u=!0;for(var b=c.length;b;){for(f=c,c=[];++d<b;)f&&f[d].run();d=-1,b=c.length}f=null,u=!1,l(v)}}r.nextTick=function(v){var b=new Array(arguments.length-1);if(arguments.length>1)for(var y=1;y<arguments.length;y++)b[y-1]=arguments[y];c.push(new g(v,b)),c.length===1&&!u&&s(p)};function g(v,b){this.fun=v,this.array=b}g.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={};function x(){}r.on=x,r.addListener=x,r.once=x,r.off=x,r.removeListener=x,r.removeAllListeners=x,r.emit=x,r.prependListener=x,r.prependOnceListener=x,r.listeners=function(v){return[]},r.binding=function(v){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(v){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}}}),p0=st({"../../../node_modules/react-is/cjs/react-is.production.min.js"(e){"use strict";var t=typeof Symbol=="function"&&Symbol.for,r=t?Symbol.for("react.element"):60103,n=t?Symbol.for("react.portal"):60106,i=t?Symbol.for("react.fragment"):60107,o=t?Symbol.for("react.strict_mode"):60108,a=t?Symbol.for("react.profiler"):60114,s=t?Symbol.for("react.provider"):60109,l=t?Symbol.for("react.context"):60110,c=t?Symbol.for("react.async_mode"):60111,u=t?Symbol.for("react.concurrent_mode"):60111,f=t?Symbol.for("react.forward_ref"):60112,d=t?Symbol.for("react.suspense"):60113,m=t?Symbol.for("react.suspense_list"):60120,p=t?Symbol.for("react.memo"):60115,g=t?Symbol.for("react.lazy"):60116,x=t?Symbol.for("react.block"):60121,v=t?Symbol.for("react.fundamental"):60117,b=t?Symbol.for("react.responder"):60118,y=t?Symbol.for("react.scope"):60119;function S(w){if(typeof w=="object"&&w!==null){var T=w.$$typeof;switch(T){case r:switch(w=w.type,w){case c:case u:case i:case a:case o:case d:return w;default:switch(w=w&&w.$$typeof,w){case l:case f:case g:case p:case s:return w;default:return T}}case n:return T}}}function C(w){return S(w)===u}e.AsyncMode=c,e.ConcurrentMode=u,e.ContextConsumer=l,e.ContextProvider=s,e.Element=r,e.ForwardRef=f,e.Fragment=i,e.Lazy=g,e.Memo=p,e.Portal=n,e.Profiler=a,e.StrictMode=o,e.Suspense=d,e.isAsyncMode=function(w){return C(w)||S(w)===c},e.isConcurrentMode=C,e.isContextConsumer=function(w){return S(w)===l},e.isContextProvider=function(w){return S(w)===s},e.isElement=function(w){return typeof w=="object"&&w!==null&&w.$$typeof===r},e.isForwardRef=function(w){return S(w)===f},e.isFragment=function(w){return S(w)===i},e.isLazy=function(w){return S(w)===g},e.isMemo=function(w){return S(w)===p},e.isPortal=function(w){return S(w)===n},e.isProfiler=function(w){return S(w)===a},e.isStrictMode=function(w){return S(w)===o},e.isSuspense=function(w){return S(w)===d},e.isValidElementType=function(w){return typeof w=="string"||typeof w=="function"||w===i||w===u||w===a||w===o||w===d||w===m||typeof w=="object"&&w!==null&&(w.$$typeof===g||w.$$typeof===p||w.$$typeof===s||w.$$typeof===l||w.$$typeof===f||w.$$typeof===v||w.$$typeof===b||w.$$typeof===y||w.$$typeof===x)},e.typeOf=S}}),v0=st({"../../../node_modules/react-is/index.js"(e,t){"use strict";t.exports=p0()}}),Li=st({"../../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"(e,t){"use strict";var r=v0(),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};s[r.ForwardRef]=o,s[r.Memo]=a;function l(x){return r.isMemo(x)?a:s[x.$$typeof]||n}var c=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,p=Object.prototype;function g(x,v,b){if(typeof v!="string"){if(p){var y=m(v);y&&y!==p&&g(x,y,b)}var S=u(v);f&&(S=S.concat(f(v)));for(var C=l(x),w=l(v),T=0;T<S.length;++T){var E=S[T];if(!i[E]&&!(b&&b[E])&&!(w&&w[E])&&!(C&&C[E])){var R=d(v,E);try{c(x,E,R)}catch{}}}}return x}t.exports=g}}),g0=st({"../../../node_modules/fontfaceobserver/fontfaceobserver.standalone.js"(e,t){(function(){function r(v,b){document.addEventListener?v.addEventListener("scroll",b,!1):v.attachEvent("scroll",b)}function n(v){document.body?v():document.addEventListener?document.addEventListener("DOMContentLoaded",function b(){document.removeEventListener("DOMContentLoaded",b),v()}):document.attachEvent("onreadystatechange",function b(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",b),v())})}function i(v){this.a=document.createElement("div"),this.a.setAttribute("aria-hidden","true"),this.a.appendChild(document.createTextNode(v)),this.b=document.createElement("span"),this.c=document.createElement("span"),this.h=document.createElement("span"),this.f=document.createElement("span"),this.g=-1,this.b.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.c.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.f.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.h.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.b.appendChild(this.h),this.c.appendChild(this.f),this.a.appendChild(this.b),this.a.appendChild(this.c)}function o(v,b){v.a.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+b+";"}function a(v){var b=v.a.offsetWidth,y=b+100;return v.f.style.width=y+"px",v.c.scrollLeft=y,v.b.scrollLeft=v.b.scrollWidth+100,v.g!==b?(v.g=b,!0):!1}function s(v,b){function y(){var C=S;a(C)&&C.a.parentNode&&b(C.g)}var S=v;r(v.b,y),r(v.c,y),a(v)}function l(v,b){var y=b||{};this.family=v,this.style=y.style||"normal",this.weight=y.weight||"normal",this.stretch=y.stretch||"normal"}var c=null,u=null,f=null,d=null;function m(){if(u===null)if(p()&&/Apple/.test(W.navigator.vendor)){var v=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(W.navigator.userAgent);u=!!v&&603>parseInt(v[1],10)}else u=!1;return u}function p(){return d===null&&(d=!!document.fonts),d}function g(){if(f===null){var v=document.createElement("div");try{v.style.font="condensed 100px sans-serif"}catch{}f=v.style.font!==""}return f}function x(v,b){return[v.style,v.weight,g()?v.stretch:"","100px",b].join(" ")}l.prototype.load=function(v,b){var y=this,S=v||"BESbswy",C=0,w=b||3e3,T=new Date().getTime();return new Promise(function(E,R){if(p()&&!m()){var _=new Promise(function(B,L){function P(){new Date().getTime()-T>=w?L(Error(""+w+"ms timeout exceeded")):document.fonts.load(x(y,'"'+y.family+'"'),S).then(function(I){1<=I.length?B():setTimeout(P,25)},L)}P()}),F=new Promise(function(B,L){C=setTimeout(function(){L(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([F,_]).then(function(){clearTimeout(C),E(y)},R)}else n(function(){function B(){var $;($=k!=-1&&A!=-1||k!=-1&&X!=-1||A!=-1&&X!=-1)&&(($=k!=A&&k!=X&&A!=X)||(c===null&&($=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(W.navigator.userAgent),c=!!$&&(536>parseInt($[1],10)||parseInt($[1],10)===536&&11>=parseInt($[2],10))),$=c&&(k==j&&A==j&&X==j||k==V&&A==V&&X==V||k==Z&&A==Z&&X==Z)),$=!$),$&&(Y.parentNode&&Y.parentNode.removeChild(Y),clearTimeout(C),E(y))}function L(){if(new Date().getTime()-T>=w)Y.parentNode&&Y.parentNode.removeChild(Y),R(Error(""+w+"ms timeout exceeded"));else{var $=document.hidden;($===!0||$===void 0)&&(k=P.a.offsetWidth,A=I.a.offsetWidth,X=U.a.offsetWidth,B()),C=setTimeout(L,50)}}var P=new i(S),I=new i(S),U=new i(S),k=-1,A=-1,X=-1,j=-1,V=-1,Z=-1,Y=document.createElement("div");Y.dir="ltr",o(P,x(y,"sans-serif")),o(I,x(y,"serif")),o(U,x(y,"monospace")),Y.appendChild(P.a),Y.appendChild(I.a),Y.appendChild(U.a),document.body.appendChild(Y),j=P.a.offsetWidth,V=I.a.offsetWidth,Z=U.a.offsetWidth,L(),s(P,function($){k=$,B()}),o(P,x(y,'"'+y.family+'",sans-serif')),s(I,function($){A=$,B()}),o(I,x(y,'"'+y.family+'",serif')),s(U,function($){X=$,B()}),o(U,x(y,'"'+y.family+'",monospace'))})})},typeof t=="object"?t.exports=l:(W.FontFaceObserver=l,W.FontFaceObserver.prototype.load=l.prototype.load)})()}});function Id(e,t){if(!e.startsWith("/")||!t.startsWith("/"))throw new Error("from/to paths are expected to be absolute");let[r]=Qu(e),[n,i]=Qu(t),o=y0(r,n);return o===""&&(o="."),!o.startsWith(".")&&!o.startsWith("/")&&(o="./"+o),o+"/"+i}function Qu(e){let t=e.lastIndexOf("/");return[e.substring(0,t+1),e.substring(t+1)]}var ua=46,Rr=47,zt=(e,t)=>e.charCodeAt(t),ef=(e,t)=>e.lastIndexOf(t),Pr=(e,t,r)=>e.slice(t,r);function y0(e,t){if(e===t||(e="/"+rf(e),t="/"+rf(t),e===t))return"";let r=1,n=e.length,i=n-r,o=1,a=t.length-o,s=i<a?i:a,l=-1,c=0;for(;c<s;c++){let f=zt(e,r+c);if(f!==zt(t,o+c))break;f===Rr&&(l=c)}if(c===s)if(a>s){if(zt(t,o+c)===Rr)return Pr(t,o+c+1);if(c===0)return Pr(t,o+c)}else i>s&&(zt(e,r+c)===Rr?l=c:c===0&&(l=0));let u="";for(c=r+l+1;c<=n;++c)(c===n||zt(e,c)===Rr)&&(u+=u.length===0?"..":"/..");return`${u}${Pr(t,o+l)}`}var x0=!1,pi="/",tf=e=>e===Rr;function rf(e){let t="",r=0,n=-1,i=0,o=0;for(let a=0;a<=e.length;++a){if(a<e.length)o=zt(e,a);else{if(tf(o))break;o=Rr}if(tf(o)){if(!(n===a-1||i===1))if(i===2){if(t.length<2||r!==2||zt(t,t.length-1)!==ua||zt(t,t.length-2)!==ua){if(t.length>2){let s=ef(t,pi);s===-1?(t="",r=0):(t=Pr(t,0,s),r=t.length-1-ef(t,pi)),n=a,i=0;continue}else if(t.length!==0){t="",r=0,n=a,i=0;continue}}x0&&(t+=t.length>0?`${pi}..`:"..",r=2)}else t.length>0?t+=`${pi}${Pr(e,n+1,a)}`:t=Pr(e,n+1,a),r=a-n-1;n=a,i=0}else o===ua&&i!==-1?++i:i=-1}return t}function Ia(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}function nf(e){return typeof e=="string"}var b0="preload";function Fd(e){return typeof e=="object"&&b0 in e}function NR(e){let t=h.lazy(e),r,n,i=h.forwardRef(function(a,s){return h.createElement(n??t,Object.assign(s?{ref:s}:{},a))});return i.preload=()=>(r||(r=e().then(o=>(n=o.default,n))),r),i}function Oi(e,t){if(t&&e)return e.elements&&t in e.elements?e.elements[t]:t}function Md(e,t={}){let r=h.isValidElement(e)?h.cloneElement(e,{style:t}):h.createElement(e,{style:t});return Fd(r.type)?h.createElement(h.Suspense,{fallback:null},r):r}var S0=class extends Error{},w0=class extends ue{constructor(e){super(e),this.state={error:void 0,forceUpdateKey:e.forceUpdateKey}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){if(e.forceUpdateKey!==t.forceUpdateKey){let r={forceUpdateKey:e.forceUpdateKey};return t.error&&(r.error=void 0),r}return null}render(){if(this.state.error===void 0)return this.props.children;if(!(this.state.error instanceof S0))throw this.state.error;let{notFoundPage:e,defaultPageStyle:t}=this.props;if(!e)throw this.state.error;return Md(e,t)}},C0=":([a-zA-Z][a-zA-Z0-9_]*)",Rn=new RegExp(C0,"g");function of(e,t,{currentRoutePath:r,hash:n,pathVariables:i}={}){let{path:o}=t;if(o)try{let a=Ai(t,{currentRoutePath:r,hash:n,pathVariables:i});W.history.pushState({routeId:e,hash:n,pathVariables:i},"",a)}catch{}}function T0({disabled:e,routeId:t,initialPathVariables:r}){h.useEffect(()=>{e||W.history.replaceState({routeId:t,pathVariables:r},"")},[])}function E0(e){let t=h.useCallback(({state:r})=>{if(!Ia(r))return;let{routeId:n,hash:i,pathVariables:o}=r;nf(n)&&e(n,nf(i)?i:void 0,Ia(o)?o:void 0)},[e]);h.useEffect(()=>(W.addEventListener("popstate",t),()=>W.removeEventListener("popstate",t)),[t])}function k0(e,t,r){let n=Oi(t,e);if(!n)return;let i=Object.assign({},t?.elements,r);return n.replace(Rn,(o,a)=>{var s;return String((s=i[a])!==null&&s!==void 0?s:o)})}function Ai(e,{currentRoutePath:t,hash:r,pathVariables:n,hashVariables:i,relative:o=!0}){var a;let s=t??"/",c=(a=e?.path)!==null&&a!==void 0?a:"/";n&&(c=c.replace(Rn,(f,d)=>{var m;return String((m=n[d])!==null&&m!==void 0?m:f)})),o&&(c=Id(s,c));let u=k0(r,e,i);return u?`${c}#${u}`:c}var vi,fa,af;function R0(e){if(af!==e){vi={};for(let[t,{path:r}]of Object.entries(e))r&&(vi[r]={path:r,depth:P0(r),routeId:t});fa=Object.values(vi),fa.sort(({depth:t},{depth:r})=>r-t),af=e}return[vi,fa]}function _0(e,t,r=!0){let[n,i]=R0(e),o=n[t];if(o){let l=sf(t,o.path);if(l.isMatch)return{routeId:o.routeId,pathVariables:l.pathVariables}}for(let{path:l,routeId:c}of i){let u=sf(t,l);if(u.isMatch)return{routeId:c,pathVariables:u.pathVariables}}if(!r)throw new Error("No exact match found for path");let a=n["/"];if(a)return{routeId:a.routeId};let s=Object.keys(e)[0];if(!s)throw new Error("Router should not have undefined routes");return{routeId:s}}function P0(e){let t=e.replace(/(?:^\/|\/$)/g,"");return t===""?0:t.split("/").length}function sf(e,t){let r=[],i=I0(t).replace(Rn,(c,u)=>(r.push(u),"([^/]+)")),o=new RegExp(i+"$"),a=e.match(o);if(!a)return{isMatch:!1};if(a.length===1)return{isMatch:!0};let s={},l=a.slice(1);for(let c=0;c<r.length;++c){let u=r[c];if(u===void 0)continue;let f=l[c],d=s[u];if(d){if(d!==f)return{isMatch:!1};continue}if(f===void 0)throw new Error("Path variable values cannot be undefined");s[u]=f}return{isMatch:!0,pathVariables:s}}function I0(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var F0="page";function lf(e){return Ia(e)&&F0 in e&&e.page!==void 0}function M0(e,t){return e.replace(Rn,(r,n)=>{let i=t[n];return typeof i!="string"||i.length===0?r:encodeURIComponent(i)})}function L0(e,t){if(e.routeId!==t.routeId)return!1;if(e.pathVariables===t.pathVariables)return!0;let r=e.pathVariables||{},n=t.pathVariables||{};return r.length===n.length&&Object.keys(r).every(i=>r[i]===n[i])}function Ld(e){return h.useCallback(t=>e[t],[e])}var Ya=h.createContext({});function O0({api:e,children:t}){return h.createElement(Ya.Provider,{value:e},t)}function lr(){return h.useContext(Ya)}function A0({routes:e,children:t}){let r=Ld(e);return h.createElement(Ya.Provider,{value:{getRoute:r}},t)}function V0(){let[e,t]=h.useState(0);return[e,h.useCallback(()=>t(r=>r+1),[])]}function cf(e,t){let r=e&&document.getElementById(e);if(r){z0(r,t);return}W.scrollTo(0,0)}function D0(e){let t=h.useRef([]);return h.useLayoutEffect(()=>{var r;!((r=t.current)===null||r===void 0)&&r.length&&(t.current.forEach(n=>n()),t.current=[])},[e]),h.useCallback(r=>{t.current.push(r)},[])}function B0({defaultPageStyle:e,disableHistory:t,initialPathVariables:r,initialRoute:n,notFoundPage:i,routes:o}){T0({disabled:t,routeId:n,initialPathVariables:r});let a=h.useRef(n),s=h.useRef(r),[l,c]=V0(),u=D0(l),f=h.useCallback((y,S,C,w=!1)=>{a.current=y,s.current=C,u(()=>{cf(S,w)}),c()},[c,u]);E0(f);let d=h.useCallback((y,S,C,w)=>{var T,E;let R=o[y];if(C){let F=new Set,B=(T=R?.path)!==null&&T!==void 0?T:"/";for(let L of B.matchAll(Rn)){let P=L[1];if(P===void 0)throw new Error("A matching path variable should not be undefined");F.add(P)}C=Object.fromEntries(Object.entries(C).filter(([L])=>F.has(L)))}let _=Oi(R,S);if(L0({routeId:a.current,pathVariables:s.current},{routeId:y,pathVariables:C})){if(((E=W.history.state)===null||E===void 0?void 0:E.hash)!==S&&!t){let F=o[y];F&&of(y,F,{currentRoutePath:F.path,pathVariables:C,hash:S})}cf(_,w);return}if(R){if(!t){let F=o[a.current];of(y,R,{currentRoutePath:F?.path,hash:S,pathVariables:C})}Ur(()=>f(y,_,C,w))}},[o,t,f]),m=Ld(o),p=a.current,g=s.current,x=h.useMemo(()=>({navigate:d,getRoute:m,currentRouteId:p,currentPathVariables:g,routes:o}),[d,m,p,g,o]),v=o[a.current];if(!v)throw new Error(`Router cannot find route for ${a.current}`);let b=v.path&&g?M0(v.path,g):v.path;return h.createElement(O0,{api:x},h.createElement(w0,{notFoundPage:i,defaultPageStyle:e,forceUpdateKey:l},h.createElement(h.Fragment,{key:b},Md(v.page,e))))}function z0(e,t){let r=t?{behavior:"smooth",block:"start",inline:"nearest"}:void 0;e.scrollIntoView(r)}var H0=h.createContext(void 0);function cr(){var e;let t=lr(),r=M(H0),n=r??t.currentRouteId;if(!n)return;let i=(e=t.getRoute)===null||e===void 0?void 0:e.call(t,n);if(i)return{...i,id:n,pathVariables:r?void 0:t.currentPathVariables}}function YR(){var e;return(e=cr())===null||e===void 0?void 0:e.pathVariables}function N0(e){var t;let r=lr();if(e)return(t=r.getRoute)===null||t===void 0?void 0:t.call(r,e)}function Ka(e,t=!0){let{getRoute:r}=lr();h.useEffect(()=>{if(!(!r||!t))for(let n of e){let i=r(n);i?.page&&qa(i.page)}},[e,r,t])}function qa(e){e&&!h.isValidElement(e)&&Fd(e)&&e.preload()}function JR(e,t){var r;let n=cr(),i=(r=N0(t))!==null&&r!==void 0?r:n;return h.useMemo(()=>Oi(i,e),[e,i])}function e_(e,t=!1,r){let{navigate:n}=lr();return Ka([e],t),h.useCallback(()=>n?.(e,r),[n,r,e])}var uf=new Set;function Za(e,...t){uf.has(e)||(uf.add(e),console.warn(e,...t))}function Od(e,t,r){let n=r?`, use ${r} instead`:"",i=`Deprecation warning: ${e} will be removed in version ${t}${n}.`;Za(i)}var Ad=class{constructor(){this.observers=new Set,this.transactions={}}add(e){this.observers.add(e);let t=!1;return()=>{t||(t=!0,this.remove(e))}}remove(e){this.observers.delete(e)}notify(e,t){if(t){let r=this.transactions[t]||e;r.value=e.value,this.transactions[t]=r}else this.callObservers(e)}finishTransaction(e){let t=this.transactions[e];return delete this.transactions[e],this.callObservers(t,e)}callObservers(e,t){let r=[];return new Set(this.observers).forEach(n=>{typeof n=="function"?n(e,t):(n.update(e,t),r.push(n.finish))}),r}},de=(()=>{function e(t){return Od("Animatable()","2.0.0","the new animation API (https://www.framer.com/api/animation/)"),Ae(t)?t:new U0(t)}return e.transaction=t=>{let r=Math.random(),n=new Set;t((a,s)=>{a.set(s,r),n.add(a)},r);let o=[];n.forEach(a=>{o.push(...a.finishTransaction(r))}),o.forEach(a=>{a(r)})},e.getNumber=(t,r=0)=>e.get(t,r),e.get=(t,r)=>t==null?r:Ae(t)?t.get():t,e.objectToValues=t=>{if(!t)return t;let r={};for(let n in t){let i=t[n];Ae(i)?r[n]=i.get():r[n]=i}return r},e})(),ff="onUpdate",df="finishTransaction";function Ae(e){return e!==null&&typeof e=="object"&&ff in e&&e[ff]instanceof Function&&df in e&&e[df]instanceof Function}function $0(e,t){return{interpolate(r,n){let i=r.get(),o=n.get(),a=de(i);return s=>{let l=t.interpolate(i,o)(s);return a.set(l),a}},difference(r,n){let i=r.get();return t.difference(i,n.get())}}}var U0=class{constructor(e){this.value=e,this.observers=new Ad}static interpolationFor(e,t){if(Ae(e))return $0(e,t)}get(){return this.value}set(e,t){let r=this.value;Ae(e)&&(e=e.get()),this.value=e;let n={value:e,oldValue:r};this.observers.notify(n,t)}finishTransaction(e){return this.observers.finishTransaction(e)}onUpdate(e){return this.observers.add(e)}},Qe=e=>e instanceof fi;function gi(e,t){let r=Math.round(Math.abs(t)),n=Math.pow(10,r);return Math.round(e*n)/n}function hf(e,t){return t===0?Math.round(e):(t-=t|0,t<0&&(t=1-t),Math.round(e-t)+t)}function Re(e,t){return{x:e,y:t}}(e=>{e.add=(...r)=>r.reduce((n,i)=>({x:n.x+i.x,y:n.y+i.y}),{x:0,y:0}),e.subtract=(r,n)=>({x:r.x-n.x,y:r.y-n.y}),e.multiply=(r,n)=>({x:r.x*n,y:r.y*n}),e.divide=(r,n)=>({x:r.x/n,y:r.y/n}),e.absolute=r=>({x:Math.abs(r.x),y:Math.abs(r.y)}),e.reverse=r=>({x:r.x*-1,y:r.y*-1}),e.pixelAligned=(r,n={x:0,y:0})=>({x:hf(r.x,n.x),y:hf(r.y,n.y)}),e.distance=(r,n)=>{let i=Math.abs(r.x-n.x),o=Math.abs(r.y-n.y);return Math.sqrt(i*i+o*o)},e.angle=(r,n)=>Math.atan2(n.y-r.y,n.x-r.x)*180/Math.PI-90,e.isEqual=(r,n)=>r.x===n.x&&r.y===n.y,e.rotationNormalizer=()=>{let r;return n=>{typeof r!="number"&&(r=n);let i=r-n,o=Math.abs(i)+180,a=Math.floor(o/360);return i<180&&(n-=a*360),i>180&&(n+=a*360),r=n,n}};function t(r,n){return{x:(r.x+n.x)/2,y:(r.y+n.y)/2}}e.center=t})(Re||(Re={}));var W0={curve:"ease",duration:1};function j0(e){switch(e){case"linear":return[0,0,1,1];case"ease":return[.25,.1,.25,1];case"ease-in":return[.42,0,1,1];case"ease-out":return[0,0,.58,1];case"ease-in-out":return[.42,0,.58,1]}}var Vd=class{constructor(e,t){this.interpolation=t,this.progress=0,this.next=s=>{let{duration:l}=this.options;this.progress+=s/l;let c=this.unitBezier.solve(this.progress,this.solveEpsilon(l));return this.current=this.interpolator(c),this.current},this.options={...W0,...e};let r;typeof this.options.curve=="string"?r=j0(this.options.curve):r=this.options.curve;let[n,i,o,a]=r;this.unitBezier=new G0(Re(n,i),Re(o,a))}setFrom(e){this.current=e,this.updateInterpolator()}setTo(e){this.destination=e,this.updateInterpolator()}isReady(){return this.interpolator!==void 0}updateInterpolator(){this.current===void 0||this.destination===void 0||(this.interpolator=this.interpolation.interpolate(this.current,this.destination))}isFinished(){return this.progress>=1}solveEpsilon(e){return 1/(200*e)}},G0=class{constructor(e,t){this.c=Re.multiply(e,3),this.b=Re.subtract(Re.multiply(Re.subtract(t,e),3),this.c),this.a=Re.subtract(Re.subtract(Re(1,1),this.c),this.b)}solve(e,t){return this.sampleY(this.solveForT(e,t))}sampleX(e){return((this.a.x*e+this.b.x)*e+this.c.x)*e}sampleY(e){return((this.a.y*e+this.b.y)*e+this.c.y)*e}sampleDerivativeX(e){return(3*this.a.x*e+2*this.b.x)*e+this.c.x}solveForT(e,t){let r,n,i,o,a,s;for(i=e,s=0;s<8;++s){if(o=this.sampleX(i)-e,Math.abs(o)<t)return i;if(a=this.sampleDerivativeX(i),Math.abs(a)<t)break;i=i-o/a}if(r=0,n=1,i=e,i<r)return r;if(i>n)return n;for(;r<n;){if(o=this.sampleX(i),Math.abs(o-e)<t)return i;e>o?r=i:n=i,i=(n-r)*.5+r}return i}},X0=class{constructor(e){this.accelerationForState=e}integrateState(e,t){let r=this.evaluateState(e),n=this.evaluateStateWithDerivative(e,t*.5,r),i=this.evaluateStateWithDerivative(e,t*.5,n),o=this.evaluateStateWithDerivative(e,t,i),a=1/6*(r.dx+2*(n.dx+i.dx)+o.dx),s=1/6*(r.dv+2*(n.dv+i.dv)+o.dv);return e.x=e.x+a*t,e.v=e.v+s*t,e}evaluateState(e){let t=this.accelerationForState(e);return{dx:e.v,dv:t}}evaluateStateWithDerivative(e,t,r){let n={x:e.x+r.dx*t,v:e.v+r.dv*t};return{dx:n.v,dv:this.accelerationForState(n)}}};function Y0(e){return typeof e=="function"&&e.interpolationFor&&typeof e.interpolationFor=="function"}var _n={handleUndefined:(e,t)=>(e===void 0&&(e=t),t===void 0&&(t=e),[e,t])},K0={interpolate(e,t){[e,t]=_n.handleUndefined(e,t);let r=+e,n=t-r;return i=>r+n*i},difference(e,t){return t-e}},yi=.001,q0=.01,Z0=10,J0=Number.MIN_VALUE,Q0=1;function ex(e,t,r,n=12){let i=r;for(let o=1,a=n,s=1<=a;s?o<a:o>a;s?o++:o--)i=i-e(i)/t(i);return i}function mf(e,t){return e*Math.sqrt(1-Math.pow(t,2))}var Dd={computeDampingRatio:(e,t,r=1)=>t/(2*Math.sqrt(r*e)),computeDuration:(e,t,r=0,n=1)=>{let i,o=Dd.computeDampingRatio(e,t),a=Math.sqrt(e/n);if(o<1){let s=Math.sqrt(1-Math.pow(o,2)),l=r/(s*a),c=o/s,u=-((l-c)/yi);if(u<=0)return null;i=Math.log(u)/(o*a)}else return null;return i},computeDerivedCurveOptions:(e,t,r=0,n=1)=>{let i,o;e=Math.max(Math.min(e,Q0),J0),t=Math.max(Math.min(t,Z0),q0),e<1?(o=function(c){let u=c*e,f=u*t,d=u-r,m=mf(c,e),p=Math.exp(-f);return yi-d/m*p},i=function(c){let f=c*e*t,d=f*r+r,m=Math.pow(e,2)*Math.pow(c,2)*t,p=Math.exp(-f),g=mf(Math.pow(c,2),e);return(-o(c)+yi>0?-1:1)*((d-m)*p)/g}):(o=function(c){let u=Math.exp(-c*t),f=(c-r)*t+1;return-yi+u*f},i=function(c){let u=Math.exp(-c*t),f=(r-c)*Math.pow(t,2);return u*f});let a={tension:100,friction:10,velocity:r},s=5/t,l=ex(o,i,s);return isNaN(l)||(a.tension=Math.pow(l,2)*n,a.friction=e*2*Math.sqrt(n*a.tension)),a}},tx={tension:500,friction:10,tolerance:1/1e4,velocity:0},rx={dampingRatio:1,duration:1,velocity:0,mass:1};function nx(e){return e?typeof e.dampingRatio=="number"||typeof e.duration=="number"||typeof e.mass=="number":!1}var ix=class{constructor(e,t){this.interpolation=t;let r;if(nx(e)){let n={...rx,...e};r=Dd.computeDerivedCurveOptions(n.dampingRatio,n.duration,n.velocity,n.mass)}else r=e;this.options={...tx,...r},this.state={x:0,v:this.options.velocity},this.integrator=new X0(n=>-this.options.tension*n.x-this.options.friction*n.v)}isReady(){return this.interpolator!==void 0&&this.difference!==void 0}next(e){return this.state=this.integrator.integrateState(this.state,e),this.interpolator(this.progress())}isFinished(){let e=Math.abs(this.state.x)<this.options.tolerance,t=Math.abs(this.state.v)<this.options.tolerance;return e&&t}setFrom(e){this.current=e,this.updateInterpolator()}setVelocity(e){this.state.v=e}progress(){return 1-this.state.x/this.difference}setTo(e){this.destination=e,this.difference=this.interpolation.difference(this.destination,this.current),this.state.x=this.difference,this.updateInterpolator()}getState(){return this.state}updateInterpolator(){this.current===void 0||this.destination===void 0||(this.interpolator=this.interpolation.interpolate(this.current,this.destination))}};var ox=be(h0(),1),Ri={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function Vt(e,t,r,n=!1){let[i,o]=t,[a,s]=r,l=o-i;if(l===0)return(s+a)/2;let c=s-a;if(c===0)return a;let u=a+(e-i)/l*c;if(n===!0)if(a<s){if(u<a)return a;if(u>s)return s}else{if(u>a)return a;if(u<s)return s}return u}function Ir(e){return!isNaN(e)&&isFinite(e)}function Dt(e){let t=Fa(e);return t!==void 0?e.includes("%")?t/100:t:0}function Fa(e){let t=e.match(/\d?\.?\d+/);return t?Number(t[0]):void 0}var{hsluvToRgb:ax,rgbToHsluv:sx}=ox.default;function lx(e,t,r){let[n,i,o]=sx([e/255,t/255,r/255]);return{h:n,s:i,l:o}}function cx(e,t,r,n=1){let i=ax([e,t,r]);return{r:i[0]*255,g:i[1]*255,b:i[2]*255,a:n}}function pf(e,t,r,n){let i=Math.round(e),o=Math.round(t*100),a=Math.round(r*100);return n===void 0||n===1?"hsv("+i+", "+o+"%, "+a+"%)":"hsva("+i+", "+o+"%, "+a+"%, "+n+")"}function ux(e,t,r){return{r:Ir(e)?De(e,255)*255:0,g:Ir(t)?De(t,255)*255:0,b:Ir(r)?De(r,255)*255:0}}function vf(e,t,r,n){let i=[ha(Math.round(e).toString(16)),ha(Math.round(t).toString(16)),ha(Math.round(r).toString(16))];return n&&i[0].charAt(0)===i[0].charAt(1)&&i[1].charAt(0)===i[1].charAt(1)&&i[2].charAt(0)===i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Ja(e,t,r){let n,i,o=De(e,255),a=De(t,255),s=De(r,255),l=Math.max(o,a,s),c=Math.min(o,a,s),u=i=n=(l+c)/2;if(l===c)u=i=0;else{let f=l-c;switch(i=n>.5?f/(2-l-c):f/(l+c),l){case o:u=(a-s)/f+(a<s?6:0);break;case a:u=(s-o)/f+2;break;case s:u=(o-a)/f+4;break}u/=6}return{h:u*360,s:i,l:n}}function da(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function fx(e,t,r){let n,i,o;if(e=De(e,360),t=De(t*100,100),r=De(r*100,100),t===0)n=i=o=r;else{let a=r<.5?r*(1+t):r+t-r*t,s=2*r-a;n=da(s,a,e+1/3),i=da(s,a,e),o=da(s,a,e-1/3)}return{r:n*255,g:i*255,b:o*255}}function gf(e,t,r){e=De(e,255),t=De(t,255),r=De(r,255);let n=Math.max(e,t,r),i=Math.min(e,t,r),o=n-i,a,s=n===0?0:o/n,l=n;if(n===i)a=0;else{switch(n){case e:a=(t-r)/o+(t<r?6:0);break;case t:a=(r-e)/o+2;break;case r:a=(e-t)/o+4;break}a/=6}return{h:a,s,v:l}}function dx(e,t,r){e=De(e,360)*6,t=De(t*100,100),r=De(r*100,100);let n=Math.floor(e),i=e-n,o=r*(1-t),a=r*(1-i*t),s=r*(1-(1-i)*t),l=n%6,c=[r,a,o,o,s,r][l],u=[s,r,r,a,o,o][l],f=[o,o,s,r,r,a][l];return{r:c*255,g:u*255,b:f*255}}function De(e,t){let r,n;if(typeof t=="string"?r=parseFloat(t):r=t,typeof e=="string"){hx(e)&&(e="100%");let i=mx(e);n=Math.min(r,Math.max(0,parseFloat(e))),i&&(n=Math.floor(n*r)/100)}else n=e;return Math.abs(n-r)<1e-6?1:n%r/r}function hx(e){return typeof e=="string"&&e.includes(".")&&parseFloat(e)===1}function mx(e){return typeof e=="string"&&e.includes("%")}function ha(e){return e.length===1?"0"+e:""+e}var ut=function(){let e="[-\\+]?\\d+%?",r="(?:"+"[-\\+]?\\d*\\.\\d+%?"+")|(?:"+e+")",n="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?",i="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?";return{rgb:new RegExp("rgb"+n),rgba:new RegExp("rgba"+i),hsl:new RegExp("hsl"+n),hsla:new RegExp("hsla"+i),hsv:new RegExp("hsv"+n),hsva:new RegExp("hsva"+i),hex3:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function Qa(e){var t,r,n,i,o,a,s,l,c,u,f,d,m,p,g,x,v,b,y,S,C,w,T,E,R,_,F,B;if(e.includes("gradient(")||e.includes("var("))return!1;let L=/^[\s,#]+/,P=/\s+$/,I=e.replace(L,"").replace(P,"").toLowerCase(),U=!1;if(Ri[I]&&(I=Ri[I],U=!0),I==="transparent")return{r:0,g:0,b:0,a:0,format:"name"};let k;return(k=ut.rgb.exec(I))?{r:parseInt((t=k[1])!=null?t:""),g:parseInt((r=k[2])!=null?r:""),b:parseInt((n=k[3])!=null?n:""),a:1,format:"rgb"}:(k=ut.rgba.exec(I))?{r:parseInt((i=k[1])!=null?i:""),g:parseInt((o=k[2])!=null?o:""),b:parseInt((a=k[3])!=null?a:""),a:parseFloat((s=k[4])!=null?s:""),format:"rgb"}:(k=ut.hsl.exec(I))?{h:parseInt((l=k[1])!=null?l:""),s:Dt((c=k[2])!=null?c:""),l:Dt((u=k[3])!=null?u:""),a:1,format:"hsl"}:(k=ut.hsla.exec(I))?{h:parseInt((f=k[1])!=null?f:""),s:Dt((d=k[2])!=null?d:""),l:Dt((m=k[3])!=null?m:""),a:parseFloat((p=k[4])!=null?p:""),format:"hsl"}:(k=ut.hsv.exec(I))?{h:parseInt((g=k[1])!=null?g:""),s:Dt((x=k[2])!=null?x:""),v:Dt((v=k[3])!=null?v:""),a:1,format:"hsv"}:(k=ut.hsva.exec(I))?{h:parseInt((b=k[1])!=null?b:""),s:Dt((y=k[2])!=null?y:""),v:Dt((S=k[3])!=null?S:""),a:parseFloat((C=k[4])!=null?C:""),format:"hsv"}:(k=ut.hex8.exec(I))?{r:Ge((w=k[1])!=null?w:""),g:Ge((T=k[2])!=null?T:""),b:Ge((E=k[3])!=null?E:""),a:yf((R=k[4])!=null?R:""),format:U?"name":"hex"}:(k=ut.hex6.exec(I))?{r:Ge((_=k[1])!=null?_:""),g:Ge((F=k[2])!=null?F:""),b:Ge((B=k[3])!=null?B:""),a:1,format:U?"name":"hex"}:(k=ut.hex4.exec(I))?{r:Ge(`${k[1]}${k[1]}`),g:Ge(`${k[2]}${k[2]}`),b:Ge(`${k[3]}${k[3]}`),a:yf(k[4]+""+k[4]),format:U?"name":"hex"}:(k=ut.hex3.exec(I))?{r:Ge(`${k[1]}${k[1]}`),g:Ge(`${k[2]}${k[2]}`),b:Ge(`${k[3]}${k[3]}`),a:1,format:U?"name":"hex"}:!1}function Ge(e){return parseInt(e,16)}function yf(e){return Ge(e)/255}var xf=new Map,z=(()=>{function e(o,a,s,l){if(typeof o=="string"){let u=xf.get(o);return u||(u=t(o),u===void 0?{...e("black"),isValid:!1}:(xf.set(o,u),u))}let c=t(o,a,s,l);return c!==void 0?c:{...e("black"),isValid:!1}}function t(o,a,s,l){if(o==="")return;let c=px(o,a,s,l);if(c){let u={r:c.r,g:c.g,b:c.b,a:c.a,h:c.h,s:c.s,l:c.l,initialValue:typeof o=="string"&&c.format!=="hsv"?o:void 0,roundA:Math.round(100*c.a)/100,format:c.format,mix:e.mix,toValue:()=>e.toRgbString(u)};return u}else return}let r={isRGB(o){return o==="rgb"||o==="rgba"},isHSL(o){return o==="hsl"||o==="hsla"}};e.inspect=(o,a)=>o.format==="hsl"?`<${o.constructor.name} h:${o.h} s:${o.s} l:${o.l} a:${o.a}>`:o.format==="hex"||o.format==="name"?`<${o.constructor.name} "${a}">`:`<${o.constructor.name} r:${o.r} g:${o.g} b:${o.b} a:${o.a}>`,e.isColor=o=>typeof o=="string"?e.isColorString(o):e.isColorObject(o),e.isColorString=o=>typeof o=="string"?Qa(o)!==!1:!1,e.isColorObject=o=>o&&typeof o!="string"&&typeof o.r=="number"&&typeof o.g=="number"&&typeof o.b=="number"&&typeof o.h=="number"&&typeof o.s=="number"&&typeof o.l=="number"&&typeof o.a=="number"&&typeof o.roundA=="number"&&typeof o.format=="string",e.toString=o=>e.toRgbString(o),e.toHex=(o,a=!1)=>vf(o.r,o.g,o.b,a),e.toHexString=(o,a=!1)=>`#${e.toHex(o,a)}`,e.toRgbString=o=>o.a===1?"rgb("+Math.round(o.r)+", "+Math.round(o.g)+", "+Math.round(o.b)+")":"rgba("+Math.round(o.r)+", "+Math.round(o.g)+", "+Math.round(o.b)+", "+o.roundA+")",e.toHusl=o=>({...lx(o.r,o.g,o.b),a:o.roundA}),e.toHslString=o=>{let a=e.toHsl(o),s=Math.round(a.h),l=Math.round(a.s*100),c=Math.round(a.l*100);return o.a===1?"hsl("+s+", "+l+"%, "+c+"%)":"hsla("+s+", "+l+"%, "+c+"%, "+o.roundA+")"},e.toHsv=o=>{let a=gf(o.r,o.g,o.b);return{h:a.h*360,s:a.s,v:a.v,a:o.a}},e.toHsvString=o=>{let a=gf(o.r,o.g,o.b),s=Math.round(a.h*360),l=Math.round(a.s*100),c=Math.round(a.v*100);return o.a===1?"hsv("+s+", "+l+"%, "+c+"%)":"hsva("+s+", "+l+"%, "+c+"%, "+o.roundA+")"},e.toName=o=>{if(o.a===0)return"transparent";if(o.a<1)return!1;let a=vf(o.r,o.g,o.b,!0);for(let s of Object.keys(Ri))if(Ri[s]===a)return s;return!1},e.toHsl=o=>({h:Math.round(o.h),s:o.s,l:o.l,a:o.a}),e.toRgb=o=>({r:Math.round(o.r),g:Math.round(o.g),b:Math.round(o.b),a:o.a}),e.brighten=(o,a=10)=>{let s=e.toRgb(o);return s.r=Math.max(0,Math.min(255,s.r-Math.round(255*-(a/100)))),s.g=Math.max(0,Math.min(255,s.g-Math.round(255*-(a/100)))),s.b=Math.max(0,Math.min(255,s.b-Math.round(255*-(a/100)))),e(s)},e.lighten=(o,a=10)=>{let s=e.toHsl(o);return s.l+=a/100,s.l=Math.min(1,Math.max(0,s.l)),e(s)},e.darken=(o,a=10)=>{let s=e.toHsl(o);return s.l-=a/100,s.l=Math.min(1,Math.max(0,s.l)),e(s)},e.saturate=(o,a=10)=>{let s=e.toHsl(o);return s.s+=a/100,s.s=Math.min(1,Math.max(0,s.s)),e(s)},e.desaturate=(o,a=10)=>{let s=e.toHsl(o);return s.s-=a/100,s.s=Math.min(1,Math.max(0,s.s)),e(s)},e.grayscale=o=>e.desaturate(o,100),e.hueRotate=(o,a)=>{let s=e.toHsl(o);return s.h+=a,s.h=s.h>360?s.h-360:s.h,e(s)},e.alpha=(o,a=1)=>e({r:o.r,g:o.g,b:o.b,a}),e.transparent=o=>e.alpha(o,0),e.multiplyAlpha=(o,a=1)=>e({r:o.r,g:o.g,b:o.b,a:o.a*a}),e.interpolate=(o,a,s="rgb")=>{if(!e.isColorObject(o)||!e.isColorObject(a))throw new TypeError("Both arguments for Color.interpolate must be Color objects");return l=>e.mixAsColor(o,a,l,!1,s)},e.mix=(o,a,{model:s="rgb"}={})=>{let l=typeof o=="string"?e(o):o,c=e.interpolate(l,a,s);return u=>e.toRgbString(c(u))},e.mixAsColor=(o,a,s=.5,l=!1,c="rgb")=>{let u=null;if(r.isRGB(c))u=e({r:Vt(s,[0,1],[o.r,a.r],l),g:Vt(s,[0,1],[o.g,a.g],l),b:Vt(s,[0,1],[o.b,a.b],l),a:Vt(s,[0,1],[o.a,a.a],l)});else{let f,d;r.isHSL(c)?(f=e.toHsl(o),d=e.toHsl(a)):(f=e.toHusl(o),d=e.toHusl(a)),f.s===0?f.h=d.h:d.s===0&&(d.h=f.h);let m=f.h,p=d.h,g=p-m;g>180?g=p-360-m:g<-180&&(g=p+360-m);let x={h:Vt(s,[0,1],[m,m+g],l),s:Vt(s,[0,1],[f.s,d.s],l),l:Vt(s,[0,1],[f.l,d.l],l),a:Vt(s,[0,1],[o.a,a.a],l)};r.isHSL(c)?u=e(x):u=e(cx(x.h,x.s,x.l,x.a))}return u},e.random=(o=1)=>{function a(){return Math.floor(Math.random()*255)}return e("rgba("+a()+", "+a()+", "+a()+", "+o+")")},e.grey=(o=.5,a=1)=>(o=Math.floor(o*255),e("rgba("+o+", "+o+", "+o+", "+a+")")),e.gray=e.grey,e.rgbToHsl=(o,a,s)=>Ja(o,a,s),e.isValidColorProperty=(o,a)=>!!((o.toLowerCase().slice(-5)==="color"||o==="fill"||o==="stroke")&&typeof a=="string"&&e.isColorString(a)),e.difference=(o,a)=>{let s=(o.r+a.r)/2,l=o.r-a.r,c=o.g-a.g,u=o.b-a.b,f=Math.pow(l,2),d=Math.pow(c,2),m=Math.pow(u,2);return Math.sqrt(2*f+4*d+3*m+s*(f-m)/256)},e.equal=(o,a,s=.1)=>!(Math.abs(o.r-a.r)>=s||Math.abs(o.g-a.g)>=s||Math.abs(o.b-a.b)>=s||Math.abs(o.a-a.a)*256>=s);let n=Tr([0,255],[0,1]);function i(o){o=n(o);let a=Math.abs(o);return a<.04045?o/12.92:(Math.sign(o)||1)*Math.pow((a+.055)/1.055,2.4)}return e.luminance=o=>{let{r:a,g:s,b:l}=e.toRgb(o);return .2126*i(a)+.7152*i(s)+.0722*i(l)},e.contrast=(o,a)=>{let s=e.luminance(o),l=e.luminance(a);return(Math.max(s,l)+.05)/(Math.min(s,l)+.05)},e})();function px(e,t,r,n=1){let i;return typeof e=="number"&&!Number.isNaN(e)&&typeof t=="number"&&!Number.isNaN(t)&&typeof r=="number"&&!Number.isNaN(r)?i=Ma({r:e,g:t,b:r,a:n}):typeof e=="string"?i=vx(e):typeof e=="object"&&(e.hasOwnProperty("r")&&e.hasOwnProperty("g")&&e.hasOwnProperty("b")?i=Ma(e):i=Bd(e)),i}function vx(e){let t=Qa(e);if(t)return t.format==="hsl"?Bd(t):t.format==="hsv"?gx(t):Ma(t)}function gx(e){let t=dx(e.h,e.s,e.v);return{...Ja(t.r,t.g,t.b),...t,format:"rgb",a:e.a!==void 0?zd(e.a):1}}function Ma(e){let t=ux(e.r,e.g,e.b);return{...Ja(t.r,t.g,t.b),...t,format:"rgb",a:e.a!==void 0?zd(e.a):1}}function Bd(e){let t,r,n,i={r:0,g:0,b:0},o={h:0,s:0,l:0};return t=Ir(e.h)?e.h:0,t=(t+360)%360,r=Ir(e.s)?e.s:1,typeof e.s=="string"&&(r=Fa(e.s)),n=Ir(e.l)?e.l:.5,typeof e.l=="string"&&(n=Fa(e.l)),i=fx(t,r,n),o={h:t,s:r,l:n},{...i,...o,a:e.a===void 0?1:e.a,format:"hsl"}}function zd(e){return e=parseFloat(e),e<0&&(e=0),(isNaN(e)||e>1)&&(e=1),e}var yx=(e="husl")=>({interpolate(t,r){return[t,r]=_n.handleUndefined(t,r),z.interpolate(z(t),z(r),e)},difference(t,r){return z.difference(z(t),z(r))}}),ma={interpolate(e,t){return[e,t]=_n.handleUndefined(e,t),r=>r<.5?e:t},difference(e,t){return e===t?0:1}},xx=e=>({interpolate(t,r){[t,r]=_n.handleUndefined(t,r);let n=Object.assign({},t),i={},o=new Set;for(let a in t)i[a]=e.interpolate(t[a],r[a]),o.add(a);for(let a in r)o.has(a)||(i[a]=e.interpolate(t[a],r[a]),o.add(a));return a=>{for(let s in i)n[s]=i[s](a);return n}},difference(t,r){let n=0;for(let i in t){let o=e.difference(t[i],r[i]);n+=Math.pow(o,2)}return Math.sqrt(n)}}),bf={colorModel:"husl"},Hd=class{constructor(e=bf){this.interpolate=(t,r)=>([t,r]=_n.handleUndefined(t,r),this.interPolationForValue(t).interpolate(t,r)),this.difference=(t,r)=>this.interPolationForValue(t).difference(t,r),this.options={...bf,...e}}interPolationForValue(e){let t=typeof e;if(t==="number")return K0;if(t==="boolean"||t==="function")return ma;if(z.isColor(e))return yx(this.options.colorModel);if(t==="object"){if(e===null)return ma;let r=e.constructor;if(r&&Y0(r)){let n=r.interpolationFor(e,this);if(n&&n!==this&&n.constructor!==Hd)return n}return xx(this)}return console.warn(`No interpolation defined for ${e}`),ma}};var bx={delta:1/60,maxValues:1e4},Sx=class{constructor(e){this.currentTime=0,this.options={...bx,...e},this.animator=e.animator}preCalculate(){if(!this.animator.isReady())return;let{delta:e}=this.options;for(this.values=[];!this.animator.isFinished()&&this.values.length<this.options.maxValues;){let t=this.animator.next(this.options.delta);typeof t=="object"&&t&&(t={...t}),this.values.push(t)}this.totalTime=this.values.length*e}indexForTime(e){return Math.max(0,Math.min(this.values.length-1,Math.round(this.values.length*(e/this.totalTime))-1))}valueForTime(e){let t=this.indexForTime(e);return this.values[t]}setFrom(e){this.animator.setFrom(e),this.preCalculate()}setTo(e){this.animator.setTo(e),this.preCalculate()}isReady(){return this.values!==void 0&&this.values.length>0&&this.totalTime>0}next(e){return this.currentTime+=e,this.valueForTime(this.currentTime)}isFinished(){return this.totalTime===0||this.currentTime>=this.totalTime}get endValue(){this.preCalculate();let e=this.valueForTime(this.totalTime);return this.values.length>0?e:this.animator.next(0)}},wx={addEventListener:()=>{},removeEventListener:()=>{},dispatchEvent:()=>!1,ResizeObserver:void 0,onpointerdown:!1,onpointermove:!1,onpointerup:!1,ontouchstart:!1,ontouchmove:!1,ontouchend:!1,onmousedown:!1,onmousemove:!1,onmouseup:!1,devicePixelRatio:1,scrollX:0,scrollY:0,location:{href:""},setTimeout:()=>0,clearTimeout:()=>{},setInterval:()=>0,clearInterval:()=>{},requestAnimationFrame:()=>0,cancelAnimationFrame:()=>{},getSelection:()=>null,matchMedia:e=>({matches:!1,media:e,onchange:()=>{},addEventListener:()=>{},removeEventListener:()=>{},addListener:()=>{},removeListener:()=>{},dispatchEvent:()=>!1}),innerHeight:0,SVGSVGElement:{}},se=typeof W>"u"?wx:W,Cx=e=>{setTimeout(e,1/60)},Tx=se.requestAnimationFrame||Cx,Sf=e=>Tx(e),Ex=be(m0(),1),{EventEmitter:kx}=Ex.default,Rx=class{constructor(){this._emitter=new kx}eventNames(){return this._emitter.eventNames()}eventListeners(){let e={};for(let t of this._emitter.eventNames())e[t]=this._emitter.listeners(t);return e}on(e,t){this.addEventListener(e,t,!1,!1,this)}off(e,t){this.removeEventListeners(e,t)}once(e,t){this.addEventListener(e,t,!0,!1,this)}unique(e,t){this.addEventListener(e,t,!1,!0,this)}addEventListener(e,t,r,n,i){if(n){for(let o of this._emitter.eventNames())if(t===this._emitter.listeners(o))return}r===!0?this._emitter.once(e,t,i):this._emitter.addListener(e,t,i)}removeEventListeners(e,t){e?this._emitter.removeListener(e,t):this.removeAllEventListeners()}removeAllEventListeners(){this._emitter.removeAllListeners()}countEventListeners(e,t){if(e)return this._emitter.listeners(e).length;{let r=0;for(let n of this._emitter.eventNames())r+=this._emitter.listeners(n).length;return r}}emit(e,...t){this._emitter.emit(e,...t)}},pn=1/60,_x=class extends Rx{constructor(e=!1){super(),this._started=!1,this._frame=0,this._frameTasks=[],this.tick=()=>{this._started&&(Sf(this.tick),this.emit("update",this._frame,pn),this.emit("render",this._frame,pn),this._processFrameTasks(),this._frame++)},e&&this.start()}addFrameTask(e){this._frameTasks.push(e)}_processFrameTasks(){var e;let t=this._frameTasks,r=t.length;if(r!==0){for(let n=0;n<r;n++)(e=t[n])==null||e.call(t);t.length=0}}static set TimeStep(e){pn=e}static get TimeStep(){return pn}start(){return this._started?this:(this._frame=0,this._started=!0,Sf(this.tick),this)}stop(){return this._started=!1,this}get frame(){return this._frame}get time(){return this._frame*pn}},bn=new _x,Q=(e=>(e.canvas="CANVAS",e.export="EXPORT",e.thumbnail="THUMBNAIL",e.preview="PREVIEW",e))(Q||{}),Or={imageBaseURL:"",target:"PREVIEW",zoom:1};(e=>{function t(){return Or.target}e.current=t;function r(){let n=Or.target;return n==="CANVAS"||n==="EXPORT"}e.hasRestrictions=r})(Q||(Q={}));var Px=class{constructor(e,t,r){this.animator=e,this.updateCallback=t,this.finishedCallback=r,this.update=(n,i)=>{if(this.animator.isFinished())this.finish();else{let o=this.animator.next(i);this.updateCallback(o)}},this.animator.isReady()||console.warn("AnimationDriver initialized with animator that isn't ready")}finish(){this.finishedCallback&&this.finishedCallback(this.animator.isFinished())}isFinished(){return this.animator.isFinished()}},Ix=class extends Px{play(){if(Or.target!=="PREVIEW"){this.finishedCallback&&this.finishedCallback(!1);return}bn.on("update",this.update)}cancel(){bn.off("update",this.update)}finish(){bn.off("update",this.update),super.finish()}},Fx={precalculate:!1,colorModel:"husl"},La=class{constructor(e,t,r,n,i,o=Ix){this.playStateSource="idle",this.readyPromise=Promise.resolve(),this.resetFinishedPromise();let a={...Fx},s={};i&&(Object.assign(a,i),Object.assign(s,i));let l;a.customInterpolation?l=a.customInterpolation:l=new Hd(i);let c;n?c=new n(s,l):c=new Vd({},l),a.precalculate&&(c=new Sx({animator:c})),c.setFrom(t),c.setTo(r);let u=d=>{La.driverCallbackHandler(e,d)},f=d=>{d&&(La.driverCallbackHandler(e,r),this.playStateSource==="running"&&(this.playStateValue="finished"))};this.driver=new o(c,u,f)}static driverCallbackHandler(e,t){if(Ae(e)||Qe(e))e.set(t);else{let r=e;de.transaction(n=>{for(let i in r){let o=r[i];Ae(o)?n(o,t[i]):r[i]=t[i]}})}}get playStateValue(){return this.playStateSource}set playStateValue(e){if(e!==this.playStateSource){let t=e;switch(this.playStateSource=e,e){case"idle":t==="running"&&this.oncancel&&this.oncancel(),this.readyResolve&&this.readyResolve(),this.resetReadyPromise();break;case"finished":if(t==="idle"){console.warn("Bad state transition");break}this.onfinish&&this.onfinish(),this.finishedResolve&&this.finishedResolve();break;case"running":this.resetReadyPromise();break}t==="finished"&&this.resetFinishedPromise(),e==="finished"&&(this.playStateValue="idle")}}get playState(){return this.playStateValue}resetReadyPromise(){this.readyResolve=null,this.readyPromise=new Promise((e,t)=>{this.readyResolve=e})}get ready(){return this.readyPromise}resetFinishedPromise(){this.finishedResolve=null,this.finishedReject=null,this.finishedPromise=new Promise((e,t)=>{this.finishedResolve=e,this.finishedReject=t}),this.finishedPromise.catch(e=>{})}get finished(){return this.finishedPromise}play(){this.playStateValue="running",this.driver.play()}cancel(){if(this.playStateValue==="running"){if(this.driver.cancel(),this.playState!=="idle"){let e="AbortError";this.finishedReject&&this.finishedReject(e)}this.playStateValue="idle"}}finish(){this.playStateSource==="running"&&(this.playStateValue="finished",this.driver.finish())}isFinished(){return this.playStateValue==="finished"}};function Mx(e,t,r,n){Od("animate()","2.0.0","the new animation API (https://www.framer.com/api/animation/)");let i=e,o;Ae(e)||Qe(e)?o=e.get():o=de.objectToValues(e);let a=new La(i,o,t,r,n);return a.play(),a}var t_=(()=>{function e(t,r,n,i){return Ae(t)?Mx(t,r,n,i):mi(t,r,n)}return e.spring=(t,r,n)=>e(t,r,ix,n),e.bezier=(t,r,n)=>e(t,r,Vd,n),e.linear=(t,r,n)=>e.bezier(t,r,{...n,curve:"linear"}),e.ease=(t,r,n)=>e.bezier(t,r,{...n,curve:"ease"}),e.easeIn=(t,r,n)=>e.bezier(t,r,{...n,curve:"ease-in"}),e.easeOut=(t,r,n)=>e.bezier(t,r,{...n,curve:"ease-out"}),e.easeInOut=(t,r,n)=>e.bezier(t,r,{...n,curve:"ease-in-out"}),e})(),xi=e=>({correct:(t,{delta:r,treeScale:n})=>{if(typeof t=="string"&&(t=parseFloat(t)),t===0)return"0px";let i=t;return r&&n&&(i=Math.round(t/r[e].scale/n[e]),i=Math.max(i,1)),i+"px"}});Mo({borderTopWidth:xi("y"),borderLeftWidth:xi("x"),borderRightWidth:xi("x"),borderBottomWidth:xi("y")});function Ce(e,...t){var r,n;if(e)return;let i=Error("Assertion Error"+(t.length>0?": "+t.join(" "):""));if(i.stack)try{let o=i.stack.split(`
`);(r=o[1])!=null&&r.includes("assert")?(o.splice(1,1),i.stack=o.join(`
`)):(n=o[0])!=null&&n.includes("assert")&&(o.splice(0,1),i.stack=o.join(`
`))}catch{}throw i}function Oa(e,t){throw t||new Error(e?`Unexpected value: ${e}`:"Application entered invalid state")}var Ar=h.createContext({getLayoutId:e=>null,persistLayoutIdCache:()=>{},top:!1,enabled:!0});function Lx({children:e}){if(M(Ar).top)return h.createElement(h.Fragment,null,e);let r=O({byId:{},byName:{},byLastId:{},byPossibleId:{},byLastName:{},byLayoutId:{},count:{byId:{},byName:{}}}),n=O({byId:{},byName:{},byLastId:{},byPossibleId:{},byLastName:{},byLayoutId:{}}),i=O(new Set).current,o=oe(({id:l,name:c,duplicatedFrom:u})=>{if(!l)return null;let f=c?"byName":"byId",d=r.current[f][l];if(d)return d;let m=c||l;if(!u&&!i.has(m)&&(!r.current.byLayoutId[m]||r.current.byLayoutId[m]===m))return r.current.count[f][m]===void 0&&(r.current.count[f][m]=0,r.current.byLayoutId[m]=m,n.current[f][l]=m),i.add(m),m;let p;if(u?.length)for(let w=u.length-1;w>=0;w--){let T=u[w];Ce(!!T,"duplicatedId must be defined");let E=r.current[f][T],R=r.current.byLastId[T];if(R&&!p){let B=r.current.byLayoutId[R],L=!B||B===c;R&&!i.has(R)&&(!c||L)&&(p=[R,T])}let _=r.current.byLayoutId[E],F=!_||_===c;if(E&&!i.has(E)&&(!c||F))return n.current[f][l]=E,n.current.byLastId[T]=E,i.add(E),E}let g=r.current.byLastId[l];if(g&&!i.has(g))return i.add(g),n.current.byId[l]=g,g;if(p){let[w,T]=p;return n.current[f][l]=w,n.current.byLastId[T]=w,i.add(w),w}let x=r.current.byPossibleId[l];if(x&&!i.has(x))return i.add(x),n.current.byId[l]=x,x;let v=u?.[0],b=c||v||l,y=r.current.count[f][b]+1||0,{layoutId:S,value:C}=Ox(b,y,i);if(r.current.count[f][b]=C,n.current[f][l]=S,u?.length&&!c){let w=u[u.length-1];if(w&&(n.current.byLastId[w]=S),u.length>1)for(let T=0;T<u.length-1;T++){let E=u[T];E!==void 0&&(n.current.byPossibleId[E]||(n.current.byPossibleId[E]=S))}}return n.current.byLayoutId[S]=m,i.add(S),S},[]),a=oe(()=>{r.current={byId:{...r.current.byId,...n.current.byId},byLastId:{...r.current.byLastId,...n.current.byLastId},byPossibleId:{...r.current.byPossibleId,...n.current.byPossibleId},byName:{...r.current.byName,...n.current.byName},byLastName:{...r.current.byLastName,...n.current.byLastName},byLayoutId:{...r.current.byLayoutId,...n.current.byLayoutId},count:{...r.current.count,byName:{}}},n.current={byId:{},byName:{},byLastId:{},byPossibleId:{},byLastName:{},byLayoutId:{}},i.clear()},[]),s=O({getLayoutId:o,persistLayoutIdCache:a,top:!0,enabled:!0}).current;return h.createElement(Ar.Provider,{value:s},e)}function Ox(e,t,r){let n=t,i=n?`${e}-${n}`:e;for(;r.has(i);)n++,i=`${e}-${n}`;return{layoutId:i,value:n}}function Ax({enabled:e=!0,...t}){let r=M(Ar),n=ne(()=>({...r,enabled:e}),[e]);return h.createElement(Ar.Provider,{...t,value:n})}function vt(e){let t=O(null);return t.current===null&&(t.current=e()),t.current}var Vx={background:void 0,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",lineHeight:"1.4em",textOverflow:"ellipsis",overflow:"hidden",minHeight:0,width:"100%",height:"100%"},Dx={...Vx,border:"1px solid rgba(149, 149, 149, 0.15)",borderRadius:6,fontSize:"12px",backgroundColor:"rgba(149, 149, 149, 0.1)",color:"#a5a5a5"},Nd={overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",maxWidth:"100%",flexShrink:0,padding:"0 10px"},Bx={...Nd,fontWeight:500},zx={...Nd,whiteSpace:"pre",maxHeight:"calc(50% - calc(20px * var(--framerInternalCanvas-canvasPlaceholderContentScaleFactor, 1)))",WebkitMaskImage:"linear-gradient(to bottom, black 80%, transparent 100%)"};function s_(e){let{error:t,file:r}=e,n=r?`Error in ${Hx(r)}`:"Error",i=t instanceof Error?t.message:""+t;return h.createElement("div",{style:Dx},h.createElement("div",{className:"text",style:Bx},n),i&&h.createElement("div",{className:"text",style:zx},i))}function Hx(e){return e.startsWith("./")?e.replace("./",""):e}var l_=992-5;function D(e){return typeof e=="number"&&isFinite(e)}function Nx(e){return!e||!Object.keys(e).length&&e.constructor===Object}function Fr(e){return typeof e!="string"&&typeof e!="number"}function Mr(e){return e!==null&&typeof e<"u"&&typeof e!="boolean"&&!Nx(e)}var Aa;(e=>{function t(i,o){return i===o?!0:!i||!o?!1:i.x===o.x&&i.y===o.y&&i.width===o.width&&i.height===o.height}e.equals=t,e.atOrigin=i=>({...i,x:0,y:0}),e.fromTwoPoints=(i,o)=>({x:Math.min(i.x,o.x),y:Math.min(i.y,o.y),width:Math.abs(i.x-o.x),height:Math.abs(i.y-o.y)}),e.fromRect=i=>({x:i.left,y:i.top,width:i.right-i.left,height:i.bottom-i.top}),e.multiply=(i,o)=>({x:i.x*o,y:i.y*o,width:i.width*o,height:i.height*o}),e.divide=(i,o)=>(0,e.multiply)(i,1/o),e.offset=(i,o)=>{let a=typeof o.x=="number"?o.x:0,s=typeof o.y=="number"?o.y:0;return{...i,x:i.x+a,y:i.y+s}};function r(i,o){if(o===0)return i;let a=2*o;return{x:i.x-o,y:i.y-o,width:i.width+a,height:i.height+a}}e.inflate=r,e.pixelAligned=i=>{let o=Math.round(i.x),a=Math.round(i.y),s=Math.round(i.x+i.width),l=Math.round(i.y+i.height),c=Math.max(s-o,0),u=Math.max(l-a,0);return{x:o,y:a,width:c,height:u}},e.halfPixelAligned=i=>{let o=Math.round(i.x*2)/2,a=Math.round(i.y*2)/2,s=Math.round((i.x+i.width)*2)/2,l=Math.round((i.y+i.height)*2)/2,c=Math.max(s-o,1),u=Math.max(l-a,1);return{x:o,y:a,width:c,height:u}},e.round=(i,o=0)=>{let a=gi(i.x,o),s=gi(i.y,o),l=gi(i.width,o),c=gi(i.height,o);return{x:a,y:s,width:l,height:c}},e.roundToOutside=i=>{let o=Math.floor(i.x),a=Math.floor(i.y),s=Math.ceil(i.x+i.width),l=Math.ceil(i.y+i.height),c=Math.max(s-o,0),u=Math.max(l-a,0);return{x:o,y:a,width:c,height:u}},e.minX=i=>i.x,e.maxX=i=>i.x+i.width,e.minY=i=>i.y,e.maxY=i=>i.y+i.height,e.positions=i=>({minX:i.x,midX:i.x+i.width/2,maxX:(0,e.maxX)(i),minY:i.y,midY:i.y+i.height/2,maxY:(0,e.maxY)(i)}),e.center=i=>({x:i.x+i.width/2,y:i.y+i.height/2}),e.fromPoints=i=>{let o=i.map(f=>f.x),a=i.map(f=>f.y),s=Math.min(...o),l=Math.min(...a),c=Math.max(...o)-s,u=Math.max(...a)-l;return{x:s,y:l,width:c,height:u}},e.merge=(...i)=>{let o={x:Math.min(...i.map(e.minX)),y:Math.min(...i.map(e.minY))},a={x:Math.max(...i.map(e.maxX)),y:Math.max(...i.map(e.maxY))};return(0,e.fromTwoPoints)(o,a)},e.intersection=(i,o)=>{let a=Math.max(i.x,o.x),s=Math.min(i.x+i.width,o.x+o.width),l=Math.max(i.y,o.y),c=Math.min(i.y+i.height,o.y+o.height);return{x:a,y:l,width:s-a,height:c-l}},e.points=i=>[{x:(0,e.minX)(i),y:(0,e.minY)(i)},{x:(0,e.minX)(i),y:(0,e.maxY)(i)},{x:(0,e.maxX)(i),y:(0,e.minY)(i)},{x:(0,e.maxX)(i),y:(0,e.maxY)(i)}],e.transform=(i,o)=>{let{x:a,y:s}=o.transformPoint({x:i.x,y:i.y}),{x:l,y:c}=o.transformPoint({x:i.x+i.width,y:i.y}),{x:u,y:f}=o.transformPoint({x:i.x+i.width,y:i.y+i.height}),{x:d,y:m}=o.transformPoint({x:i.x,y:i.y+i.height}),p=Math.min(a,l,u,d),g=Math.max(a,l,u,d)-p,x=Math.min(s,c,f,m),v=Math.max(s,c,f,m)-x;return{x:p,y:x,width:g,height:v}},e.containsPoint=(i,o)=>!(o.x<(0,e.minX)(i)||o.x>(0,e.maxX)(i)||o.y<(0,e.minY)(i)||o.y>(0,e.maxY)(i)||isNaN(i.x)||isNaN(i.y)),e.containsRect=(i,o)=>{for(let a of(0,e.points)(o))if(!(0,e.containsPoint)(i,a))return!1;return!0},e.toCSS=i=>({display:"block",transform:`translate(${i.x}px, ${i.y}px)`,width:`${i.width}px`,height:`${i.height}px`}),e.inset=(i,o)=>({x:i.x+o,y:i.y+o,width:Math.max(0,i.width-2*o),height:Math.max(0,i.height-2*o)}),e.intersects=(i,o)=>!(o.x>=(0,e.maxX)(i)||(0,e.maxX)(o)<=i.x||o.y>=(0,e.maxY)(i)||(0,e.maxY)(o)<=i.y),e.overlapHorizontally=(i,o)=>{let a=e.maxX(i),s=e.maxX(o);return a>o.x&&s>i.x},e.overlapVertically=(i,o)=>{let a=e.maxY(i),s=e.maxY(o);return a>o.y&&s>i.y},e.doesNotIntersect=(i,o)=>o.find(a=>e.intersects(a,i))===void 0,e.isEqual=(i,o)=>{if(i&&o){let{x:a,y:s,width:l,height:c}=i;return o.x===a&&o.y===s&&o.width===l&&o.height===c}else return i===o},e.cornerPoints=i=>{let o=i.x,a=i.x+i.width,s=i.y,l=i.y+i.height;return[{x:o,y:s},{x:a,y:s},{x:a,y:l},{x:o,y:l}]},e.midPoints=i=>{let o=i.x,a=i.x+i.width/2,s=i.x+i.width,l=i.y,c=i.y+i.height/2,u=i.y+i.height;return[{x:a,y:l},{x:s,y:c},{x:a,y:u},{x:o,y:c}]},e.pointDistance=(i,o)=>{let a=0,s=0;return o.x<i.x?a=i.x-o.x:o.x>e.maxX(i)&&(a=o.x-e.maxX(i)),o.y<i.y?s=i.y-o.y:o.y>e.maxY(i)&&(s=o.y-e.maxY(i)),Re.distance({x:a,y:s},{x:0,y:0})};let n={x:0,y:0,width:0,height:0};e.fromAny=(i,o=n)=>({x:i.x||o.x,y:i.y||o.y,width:i.width||o.width,height:i.height||o.height})})(Aa||(Aa={}));var _i;(e=>{e.quickfix=t=>((t.widthType===2||t.heightType===2)&&(t.aspectRatio=null),D(t.aspectRatio)&&(t.left&&t.right&&(t.widthType=0),t.top&&t.bottom&&(t.heightType=0),t.left&&t.right&&t.top&&t.bottom&&(t.bottom=!1),t.widthType!==0&&t.heightType!==0&&(t.heightType=0)),t.left&&t.right&&((t.fixedSize||t.widthType===2||D(t.maxWidth))&&(t.right=!1),t.widthType=0),t.top&&t.bottom&&((t.fixedSize||t.heightType===2||D(t.maxHeight))&&(t.bottom=!1),t.heightType=0),t)})(_i||(_i={}));function Pi(e){if(typeof e=="string"){let t=e.trim();if(t==="auto")return 2;if(t.endsWith("fr"))return 3;if(t.endsWith("%"))return 1;if(t.endsWith("vw")||t.endsWith("vh"))return 4}return 0}var Va;(e=>{e.fromProperties=t=>{let{left:r,right:n,top:i,bottom:o,width:a,height:s,centerX:l,centerY:c,aspectRatio:u,autoSize:f}=t,d=_i.quickfix({left:D(r)||Ae(r),right:D(n)||Ae(n),top:D(i)||Ae(i),bottom:D(o)||Ae(o),widthType:Pi(a),heightType:Pi(s),aspectRatio:u||null,fixedSize:f===!0}),m=null,p=null,g=0,x=0;if(d.widthType!==0&&typeof a=="string"){let y=parseFloat(a);a.endsWith("fr")?(g=3,m=y):a==="auto"?g=2:(g=1,m=y/100)}else a!==void 0&&typeof a!="string"&&(m=de.getNumber(a));if(d.heightType!==0&&typeof s=="string"){let y=parseFloat(s);s.endsWith("fr")?(x=3,p=y):s==="auto"?x=2:(x=1,p=parseFloat(s)/100)}else s!==void 0&&typeof s!="string"&&(p=de.getNumber(s));let v=.5,b=.5;return l&&(v=parseFloat(l)/100),c&&(b=parseFloat(c)/100),{left:d.left?de.getNumber(r):null,right:d.right?de.getNumber(n):null,top:d.top?de.getNumber(i):null,bottom:d.bottom?de.getNumber(o):null,widthType:g,heightType:x,width:m,height:p,aspectRatio:d.aspectRatio||null,centerAnchorX:v,centerAnchorY:b}},e.toSize=(t,r,n,i)=>{let o=null,a=null,s=r?.sizing?de.getNumber(r?.sizing.width):null,l=r?.sizing?de.getNumber(r?.sizing.height):null,c=wf(t.left,t.right);if(s&&D(c))o=s-c;else if(n&&t.widthType===2)o=n.width;else if(D(t.width))switch(t.widthType){case 0:o=t.width;break;case 3:o=i?i.freeSpaceInParent.width/i.freeSpaceUnitDivisor.width*t.width:null;break;case 1:case 4:s&&(o=s*t.width);break;case 2:break;default:Oa(t.widthType)}let u=wf(t.top,t.bottom);if(l&&D(u))a=l-u;else if(n&&t.heightType===2)a=n.height;else if(D(t.height))switch(t.heightType){case 0:a=t.height;break;case 3:a=i?i.freeSpaceInParent.height/i.freeSpaceUnitDivisor.height*t.height:null;break;case 1:case 4:l&&(a=l*t.height);break;case 2:break;default:Oa(t.heightType)}return Gx(o,a,t,{height:l??0,width:s??0},r?.viewport)},e.toRect=(t,r=null,n=null,i=!1,o=null)=>{var a;let s=t.left||0,l=t.top||0,{width:c,height:u}=e.toSize(t,r,n,o),f=(a=r?.positioning)!=null?a:null,d=f?de.getNumber(f.width):null,m=f?de.getNumber(f.height):null;t.left!==null?s=t.left:d&&t.right!==null?s=d-t.right-c:d&&(s=t.centerAnchorX*d-c/2),t.top!==null?l=t.top:m&&t.bottom!==null?l=m-t.bottom-u:m&&(l=t.centerAnchorY*m-u/2);let p={x:s,y:l,width:c,height:u};return i?Aa.pixelAligned(p):p}})(Va||(Va={}));var $x=200,Ux=200;function Ii(e,t,r,n){if(typeof t=="string"){if(t.endsWith("%")&&r)switch(e){case"maxWidth":case"minWidth":return parseFloat(t)/100*r.width;case"maxHeight":case"minHeight":return parseFloat(t)/100*r.height;default:break}if(t.endsWith("vh")&&n)switch(e){case"maxWidth":case"minWidth":return parseFloat(t)/100*n.width;case"maxHeight":case"minHeight":return parseFloat(t)/100*n.height;default:break}return parseFloat(t)}return t}function Wx(e,t,r,n){return t.minHeight&&(e=Math.max(Ii("minHeight",t.minHeight,r,n),e)),t.maxHeight&&(e=Math.min(Ii("maxHeight",t.maxHeight,r,n),e)),e}function jx(e,t,r,n){return t.minWidth&&(e=Math.max(Ii("minWidth",t.minWidth,r,n),e)),t.maxWidth&&(e=Math.min(Ii("maxWidth",t.maxWidth,r,n),e)),e}function Gx(e,t,r,n,i){let o=jx(D(e)?e:$x,r,n,i),a=Wx(D(t)?t:Ux,r,n,i);return D(r.aspectRatio)&&r.aspectRatio>0&&(D(r.left)&&D(r.right)?a=o/r.aspectRatio:D(r.top)&&D(r.bottom)?o=a*r.aspectRatio:r.widthType!==0?a=o/r.aspectRatio:o=a*r.aspectRatio),{width:o,height:a}}function wf(e,t){return!D(e)||!D(t)?null:e+t}function Xx(e){return typeof e.right=="string"||typeof e.bottom=="string"||typeof e.left=="string"&&(!e.center||e.center==="y")||typeof e.top=="string"&&(!e.center||e.center==="x")}function Dr(e){return!e._constraints||Xx(e)?!1:e._constraints.enabled}function Yx(e){let{size:t}=e,{width:r,height:n}=e;return D(t)&&(r===void 0&&(r=t),n===void 0&&(n=t)),D(r)&&D(n)?{width:r,height:n}:null}function Kx(e){let t=Yx(e);if(t===null)return null;let{left:r,top:n}=e;return D(r)&&D(n)?{x:r,y:n,...t}:null}function Vr(e,t,r=!0){if(e.positionFixed||e.positionAbsolute)return null;let n=t===1||t===2;if(!Dr(e)||n)return Kx(e);let i=qx(e),o=Zx(t),a=o?{sizing:o,positioning:o,viewport:null}:null;return Va.toRect(i,a,null,r,null)}function qx(e){let{left:t,right:r,top:n,bottom:i,center:o,_constraints:a,size:s}=e,{width:l,height:c}=e;l===void 0&&(l=s),c===void 0&&(c=s);let{aspectRatio:u,autoSize:f}=a,d=_i.quickfix({left:D(t),right:D(r),top:D(n),bottom:D(i),widthType:Pi(l),heightType:Pi(c),aspectRatio:u||null,fixedSize:f===!0}),m=null,p=null,g=0,x=0;if(d.widthType!==0&&typeof l=="string"){let y=parseFloat(l);l.endsWith("fr")?(g=3,m=y):l==="auto"?g=2:(g=1,m=y/100)}else l!==void 0&&typeof l!="string"&&(m=l);if(d.heightType!==0&&typeof c=="string"){let y=parseFloat(c);c.endsWith("fr")?(x=3,p=y):c==="auto"?x=2:(x=1,p=parseFloat(c)/100)}else c!==void 0&&typeof c!="string"&&(p=c);let v=.5,b=.5;return(o===!0||o==="x")&&(d.left=!1,typeof t=="string"&&(v=parseFloat(t)/100)),(o===!0||o==="y")&&(d.top=!1,typeof n=="string"&&(b=parseFloat(n)/100)),{left:d.left?t:null,right:d.right?r:null,top:d.top?n:null,bottom:d.bottom?i:null,widthType:g,heightType:x,width:m,height:p,aspectRatio:d.aspectRatio||null,centerAnchorX:v,centerAnchorY:b,minHeight:e.minHeight,maxHeight:e.maxHeight,minWidth:e.minWidth,maxWidth:e.maxWidth}}var es=h.createContext({parentSize:0});function Zx(e){return e===0||e===1||e===2?null:e}function Br(){return h.useContext(es).parentSize}function $d(e){return typeof e=="object"}var Jx=e=>{let t=Br(),{parentSize:r,children:n}=e,i=h.useMemo(()=>({parentSize:r}),[Qx(r),eb(r)]);return t===1?n?h.createElement(h.Fragment,null,n):null:h.createElement(es.Provider,{value:i},n)};function Qx(e){return $d(e)?e.width:e}function eb(e){return $d(e)?e.height:e}var f_=es.Consumer;function Ud(e,t){return h.createElement(Jx,{parentSize:t},e)}function tb(e){let t=Br();return Vr(e,t,!0)}var rb=(e=>(e.Boolean="boolean",e.Number="number",e.String="string",e.RichText="richtext",e.FusedNumber="fusednumber",e.Enum="enum",e.SegmentedEnum="segmentedenum",e.Color="color",e.Image="image",e.ResponsiveImage="responsiveimage",e.File="file",e.ComponentInstance="componentinstance",e.Array="array",e.EventHandler="eventhandler",e.Transition="transition",e.Link="link",e.Date="date",e.Object="object",e.Font="font",e.PageScope="pagescope",e))(rb||{}),pa;function nb(){if(pa!==void 0)return pa;let e=document.createElement("div");Object.assign(e.style,{position:"absolute",display:"flex",flexDirection:"column",rowGap:"1px"}),e.appendChild(document.createElement("div")),e.appendChild(document.createElement("div")),document.body.appendChild(e);let t=e.scrollHeight===1;return e.parentNode&&e.parentNode.removeChild(e),pa=t,t}var ar="flexbox-gap-not-supported",Cf=!1;function d_(){Cf||(Cf=!0,!nb()&&document.body.classList.add(ar))}var ib=`
[data-framer-component-type="DeprecatedRichText"] p,
[data-framer-component-type="DeprecatedRichText"] div,
[data-framer-component-type="DeprecatedRichText"] h1,
[data-framer-component-type="DeprecatedRichText"] h2,
[data-framer-component-type="DeprecatedRichText"] h3,
[data-framer-component-type="DeprecatedRichText"] h4,
[data-framer-component-type="DeprecatedRichText"] h5,
[data-framer-component-type="DeprecatedRichText"] h6,
[data-framer-component-type="DeprecatedRichText"] li,
[data-framer-component-type="DeprecatedRichText"] ol,
[data-framer-component-type="DeprecatedRichText"] ul,
[data-framer-component-type="DeprecatedRichText"] span:not([data-text-fill]) {
    font-family: var(--framer-font-family, Inter, Inter Placeholder, sans-serif);
    font-style: var(--framer-font-style, normal);
    font-weight: var(--framer-font-weight, 400);
    color: var(--framer-text-color, #000);
    font-size: var(--framer-font-size, 16px);
    letter-spacing: var(--framer-letter-spacing, 0);
    text-transform: var(--framer-text-transform, none);
    text-decoration: var(--framer-text-decoration, none);
    line-height: var(--framer-line-height, 1.2em);
    text-align: var(--framer-text-alignment, start);
}
`,ob=`
[data-framer-component-type="DeprecatedRichText"] p:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] div:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h1:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h2:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h3:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h4:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h5:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] h6:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] ol:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] ul:not(:first-child),
[data-framer-component-type="DeprecatedRichText"] .framer-image:not(:first-child) {
    margin-top: var(--framer-paragraph-spacing, 0);
}
`,ab=`
[data-framer-component-type="DeprecatedRichText"] span[data-text-fill] {
    display: inline-block;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
`,sb=`
[data-framer-component-type="DeprecatedRichText"] a,
[data-framer-component-type="DeprecatedRichText"] a span:not([data-text-fill]) {
    font-family: var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
    font-style: var(--framer-link-font-style, var(--framer-font-style, normal));
    font-weight: var(--framer-link-font-weight, var(--framer-font-weight, 400));
    color: var(--framer-link-text-color, var(--framer-text-color, #000));
    font-size: var(--framer-link-font-size, var(--framer-font-size, 16px));
    text-transform: var(--framer-link-text-transform, var(--framer-text-transform, none));
    text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none));
}
`,lb=`
[data-framer-component-type="DeprecatedRichText"] a:hover,
[data-framer-component-type="DeprecatedRichText"] a:hover span:not([data-text-fill]) {
    font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
    font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
    font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
    color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
    font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
    text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
    text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
}
`,cb=`
a[data-framer-page-link-current],
a[data-framer-page-link-current] span:not([data-text-fill]) {
    font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
    font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
    font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
    color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
    font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
    text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
    text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
}
`,ub=`
a[data-framer-page-link-current]:hover,
a[data-framer-page-link-current]:hover span:not([data-text-fill]) {
    font-family: var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));
    font-style: var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));
    font-weight: var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));
    color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));
    font-size: var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))));
    text-transform: var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));
    text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))));
}
`,fb=`
[data-framer-component-type="DeprecatedRichText"] strong {
    font-weight: bolder;
}
`,db=`
[data-framer-component-type="DeprecatedRichText"] em {
    font-style: italic;
}
`,hb=`
[data-framer-component-type="DeprecatedRichText"] .framer-image {
    display: block;
    max-width: 100%;
    height: auto;
}
`,mb=`
[data-framer-component-type="DeprecatedRichText"] p,
[data-framer-component-type="DeprecatedRichText"] div,
[data-framer-component-type="DeprecatedRichText"] h1,
[data-framer-component-type="DeprecatedRichText"] h2,
[data-framer-component-type="DeprecatedRichText"] h3,
[data-framer-component-type="DeprecatedRichText"] h4,
[data-framer-component-type="DeprecatedRichText"] h5,
[data-framer-component-type="DeprecatedRichText"] h6 {
    margin: 0;
    padding: 0;
}
`,pb=`
[data-framer-component-type="DeprecatedRichText"] .text-styles-preset-reset {
    --framer-font-family: Inter, Inter Placeholder, sans-serif;
    --framer-font-style: normal;
    --framer-font-weight: 500;
    --framer-text-color: #000;
    --framer-font-size: 16px;
    --framer-letter-spacing: 0;
    --framer-text-transform: none;
    --framer-text-decoration: none;
    --framer-line-height: 1.2em;
    --framer-text-alignment: start;
}
`,vb=`
[data-framer-component-type="DeprecatedRichText"] ul,
[data-framer-component-type="DeprecatedRichText"] ol {
    display: table;
    width: 100%;
    padding-left: 0;
    margin: 0;
}
`,gb=`
[data-framer-component-type="DeprecatedRichText"] li {
    display: table-row;
    counter-increment: list-item;
    list-style: none;
}
`,yb=`
[data-framer-component-type="DeprecatedRichText"] ol > li::before {
    display: table-cell;
    width: 2.25ch;
    box-sizing: border-box;
    padding-right: 0.75ch;
    content: counter(list-item) ".";
    white-space: nowrap;
}
`,xb=`
[data-framer-component-type="DeprecatedRichText"] ul > li::before {
    display: table-cell;
    width: 2.25ch;
    box-sizing: border-box;
    padding-right: 0.75ch;
    content: "\u2022";
}
`,bb=['[data-framer-component-type="DeprecatedRichText"] { cursor: inherit; }',pb,mb,ib,ob,ab,sb,lb,cb,ub,fb,db,hb,vb,gb,yb,xb],Sb=[`
        p.framer-text,
        div.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        ol.framer-text,
        ul.framer-text {
            margin: 0;
            padding: 0;
        }
    `,`
        p.framer-text,
        div.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        li.framer-text,
        ol.framer-text,
        ul.framer-text,
        span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-font-family, Inter, Inter Placeholder, sans-serif);
            font-style: var(--framer-font-style, normal);
            font-weight: var(--framer-font-weight, 400);
            color: var(--framer-text-color, #000);
            font-size: calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));
            letter-spacing: var(--framer-letter-spacing, 0);
            text-transform: var(--framer-text-transform, none);
            text-decoration: var(--framer-text-decoration, none);
            line-height: var(--framer-line-height, 1.2em);
            text-align: var(--framer-text-alignment, start);
        }
    `,`
        strong.framer-text {
            font-weight: bolder;
        }
    `,`
        em.framer-text {
            font-style: italic;
        }
    `,`
        p.framer-text:not(:first-child),
        div.framer-text:not(:first-child),
        h1.framer-text:not(:first-child),
        h2.framer-text:not(:first-child),
        h3.framer-text:not(:first-child),
        h4.framer-text:not(:first-child),
        h5.framer-text:not(:first-child),
        h6.framer-text:not(:first-child),
        ol.framer-text:not(:first-child),
        ul.framer-text:not(:first-child),
        .framer-image.framer-text:not(:first-child) {
            margin-top: var(--framer-paragraph-spacing, 0);
        }
    `,`
        li.framer-text > ul.framer-text:nth-child(2),
        li.framer-text > ol.framer-text:nth-child(2) {
            margin-top: 0;
        }
    `,`
        .framer-text[data-text-fill] {
            display: inline-block;
            background-clip: text;
            -webkit-background-clip: text;
            /* make this a transparent color if you want to visualise the clipping  */
            -webkit-text-fill-color: transparent;
            padding: max(0em, calc(calc(1.3em - var(--framer-line-height, 1.3em)) / 2));
            margin: min(0em, calc(calc(1.3em - var(--framer-line-height, 1.3em)) / -2));
        }
    `,`
        code.framer-text,
        code.framer-text span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-code-text-color, var(--framer-text-color, #000));
            font-size: calc(var(--framer-font-size, 16px) * var(--framer-font-size-scale, 1));
            letter-spacing: var(--framer-letter-spacing, 0);
            line-height: var(--framer-line-height, 1.2em);
        }
    `,`
        a.framer-text,
        a.framer-text span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-link-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-link-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-text-color, var(--framer-text-color, #000));
            font-size: calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-text-transform, var(--framer-text-transform, none));
            text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none));
            /* Cursor inherit to overwrite the user agent stylesheet on rich text links. */
            cursor: var(--framer-custom-cursors, pointer);
        }
    `,`
        code.framer-text a.framer-text,
        code.framer-text a.framer-text span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-link-font-size, var(--framer-font-size, 16px)) * var(--framer-font-size-scale, 1));
        }
    `,`
        a.framer-text:hover,
        a.framer-text:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
        }
    `,`
        code.framer-text a.framer-text:hover,
        code.framer-text a.framer-text:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));
        }
    `,`
        a.framer-text[data-framer-page-link-current],
        a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
        }
    `,`
        code.framer-text a.framer-text[data-framer-page-link-current],
        code.framer-text a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))) * var(--framer-font-size-scale, 1));
        }
    `,`
        a.framer-text[data-framer-page-link-current]:hover,
        a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));
            font-style: var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));
            color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))) * var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));
            text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))));
        }
    `,`
        code.framer-text a.framer-text[data-framer-page-link-current]:hover,
        code.framer-text a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))) * var(--framer-font-size-scale, 1));
        }
    `,`
        .framer-image.framer-text {
            display: block;
            max-width: 100%;
            height: auto;
        }
    `,`
        .text-styles-preset-reset.framer-text {
            --framer-font-family: Inter, Inter Placeholder, sans-serif;
            --framer-font-style: normal;
            --framer-font-weight: 500;
            --framer-text-color: #000;
            --framer-font-size: 16px;
            --framer-letter-spacing: 0;
            --framer-text-transform: none;
            --framer-text-decoration: none;
            --framer-line-height: 1.2em;
            --framer-text-alignment: start;
        }
    `,`
        ul.framer-text,
        ol.framer-text {
            display: table;
            width: 100%;
        }
    `,`
        li.framer-text {
            display: table-row;
            counter-increment: list-item;
            list-style: none;
        }
    `,`
        ol.framer-text > li.framer-text::before {
            display: table-cell;
            width: 2.25ch;
            box-sizing: border-box;
            padding-right: 0.75ch;
            content: counter(list-item) ".";
            white-space: nowrap;
        }
    `,`
        ul.framer-text > li.framer-text::before {
            display: table-cell;
            width: 2.25ch;
            box-sizing: border-box;
            padding-right: 0.75ch;
            content: "\u2022";
        }
    `,`
        .framer-text-module[style*="aspect-ratio"] > :first-child {
            width: 100%;
        }
    `,`
        @supports not (aspect-ratio: 1) {
            .framer-text-module[style*="aspect-ratio"] {
                position: relative;
            }
        }
    `,`
        @supports not (aspect-ratio: 1) {
            .framer-text-module[style*="aspect-ratio"]::before {
                content: "";
                display: block;
                padding-bottom: calc(100% / calc(var(--aspect-ratio)));
            }
        }
    `,`
        @supports not (aspect-ratio: 1) {
            .framer-text-module[style*="aspect-ratio"] > :first-child {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
            }
        }
    `],wb=new Set,va;function Wd(e,t,r=wb){if(!(!e||r.has(e)||typeof document>"u")){if(r.add(e),!t){if(!va){let n=document.createElement("style");if(n.setAttribute("type","text/css"),n.setAttribute("data-framer-css","true"),!document.head){console.warn("not injecting CSS: the document is missing a <head> element");return}if(document.head.appendChild(n),n.sheet)va=n.sheet;else{console.warn("not injecting CSS: injected <style> element does not have a sheet",n);return}}t=va}try{t.insertRule(e,t.cssRules.length)}catch{}}}var Cb=["[data-framer-component-type] { position: absolute; }"],Tb=`
[data-framer-component-type="Text"] > * {
    text-align: var(--framer-text-alignment, start);
}`,Eb=`
[data-framer-component-type="Text"] span span,
[data-framer-component-type="Text"] p span,
[data-framer-component-type="Text"] h1 span,
[data-framer-component-type="Text"] h2 span,
[data-framer-component-type="Text"] h3 span,
[data-framer-component-type="Text"] h4 span,
[data-framer-component-type="Text"] h5 span,
[data-framer-component-type="Text"] h6 span {
    display: block;
}`,kb=`
[data-framer-component-type="Text"] span span span,
[data-framer-component-type="Text"] p span span,
[data-framer-component-type="Text"] h1 span span,
[data-framer-component-type="Text"] h2 span span,
[data-framer-component-type="Text"] h3 span span,
[data-framer-component-type="Text"] h4 span span,
[data-framer-component-type="Text"] h5 span span,
[data-framer-component-type="Text"] h6 span span {
    display: unset;
}`,Rb=`
[data-framer-component-type="Text"] div div span,
[data-framer-component-type="Text"] a div span,
[data-framer-component-type="Text"] span span span,
[data-framer-component-type="Text"] p span span,
[data-framer-component-type="Text"] h1 span span,
[data-framer-component-type="Text"] h2 span span,
[data-framer-component-type="Text"] h3 span span,
[data-framer-component-type="Text"] h4 span span,
[data-framer-component-type="Text"] h5 span span,
[data-framer-component-type="Text"] h6 span span,
[data-framer-component-type="Text"] a {
    font-family: var(--font-family);
    font-style: var(--font-style);
    font-weight: min(calc(var(--framer-font-weight-increase, 0) + var(--font-weight, 400)), 900);
    color: var(--text-color);
    letter-spacing: var(--letter-spacing);
    font-size: var(--font-size);
    text-transform: var(--text-transform);
    text-decoration: var(--text-decoration);
    line-height: var(--line-height);
}`,_b=`
[data-framer-component-type="Text"] div div span,
[data-framer-component-type="Text"] a div span,
[data-framer-component-type="Text"] span span span,
[data-framer-component-type="Text"] p span span,
[data-framer-component-type="Text"] h1 span span,
[data-framer-component-type="Text"] h2 span span,
[data-framer-component-type="Text"] h3 span span,
[data-framer-component-type="Text"] h4 span span,
[data-framer-component-type="Text"] h5 span span,
[data-framer-component-type="Text"] h6 span span,
[data-framer-component-type="Text"] a {
    --font-family: var(--framer-font-family);
    --font-style: var(--framer-font-style);
    --font-weight: var(--framer-font-weight);
    --text-color: var(--framer-text-color);
    --letter-spacing: var(--framer-letter-spacing);
    --font-size: var(--framer-font-size);
    --text-transform: var(--framer-text-transform);
    --text-decoration: var(--framer-text-decoration);
    --line-height: var(--framer-line-height);
}`,Pb=`
[data-framer-component-type="Text"] a,
[data-framer-component-type="Text"] a div span,
[data-framer-component-type="Text"] a span span span,
[data-framer-component-type="Text"] a p span span,
[data-framer-component-type="Text"] a h1 span span,
[data-framer-component-type="Text"] a h2 span span,
[data-framer-component-type="Text"] a h3 span span,
[data-framer-component-type="Text"] a h4 span span,
[data-framer-component-type="Text"] a h5 span span,
[data-framer-component-type="Text"] a h6 span span {
    --font-family: var(--framer-link-font-family, var(--framer-font-family));
    --font-style: var(--framer-link-font-style, var(--framer-font-style));
    --font-weight: var(--framer-link-font-weight, var(--framer-font-weight));
    --text-color: var(--framer-link-text-color, var(--framer-text-color));
    --font-size: var(--framer-link-font-size, var(--framer-font-size));
    --text-transform: var(--framer-link-text-transform, var(--framer-text-transform));
    --text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration));
}`,Ib=`
[data-framer-component-type="Text"] a:hover,
[data-framer-component-type="Text"] a div span:hover,
[data-framer-component-type="Text"] a span span span:hover,
[data-framer-component-type="Text"] a p span span:hover,
[data-framer-component-type="Text"] a h1 span span:hover,
[data-framer-component-type="Text"] a h2 span span:hover,
[data-framer-component-type="Text"] a h3 span span:hover,
[data-framer-component-type="Text"] a h4 span span:hover,
[data-framer-component-type="Text"] a h5 span span:hover,
[data-framer-component-type="Text"] a h6 span span:hover {
    --font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family)));
    --font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style)));
    --font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
    --text-color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color)));
    --font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size)));
    --text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));
    --text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration)));
}`,Fb=`
[data-framer-component-type="Text"].isCurrent a,
[data-framer-component-type="Text"].isCurrent a div span,
[data-framer-component-type="Text"].isCurrent a span span span,
[data-framer-component-type="Text"].isCurrent a p span span,
[data-framer-component-type="Text"].isCurrent a h1 span span,
[data-framer-component-type="Text"].isCurrent a h2 span span,
[data-framer-component-type="Text"].isCurrent a h3 span span,
[data-framer-component-type="Text"].isCurrent a h4 span span,
[data-framer-component-type="Text"].isCurrent a h5 span span,
[data-framer-component-type="Text"].isCurrent a h6 span span {
    --font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family)));
    --font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style)));
    --font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
    --text-color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color)));
    --font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size)));
    --text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));
    --text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration)));
}`,Mb=['[data-framer-component-type="Text"] { cursor: inherit; }',"[data-framer-component-text-autosized] * { white-space: pre; }",Tb,Eb,kb,Rb,_b,Pb,Ib,Fb],Lb=`
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > *,
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > [data-framer-component-type],
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > [data-framer-legacy-stack-gap-enabled] > *,
:not([data-framer-generated]) > [data-framer-stack-content-wrapper] > [data-framer-legacy-stack-gap-enabled] > [data-framer-component-type] {
    position: relative;
}`,Ob=[`[data-framer-stack-content-wrapper][data-framer-stack-gap-enabled="true"] {
        row-gap: var(--stack-native-row-gap);
        column-gap: var(--stack-native-column-gap);
    }`,`.${ar} [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled="true"] {
        row-gap: unset;
        column-gap: unset;
    }`],Ab=`
.${ar} [data-framer-legacy-stack-gap-enabled="true"] > *, [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"] {
    margin-top: calc(var(--stack-gap-y) / 2);
    margin-bottom: calc(var(--stack-gap-y) / 2);
    margin-right: calc(var(--stack-gap-x) / 2);
    margin-left: calc(var(--stack-gap-x) / 2);
}
`,Vb=`
.${ar}
[data-framer-stack-direction-reverse="false"]
[data-framer-legacy-stack-gap-enabled="true"]
> *:first-child,
[data-framer-stack-direction-reverse="false"]
[data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
> *:first-child,
.${ar}
[data-framer-stack-direction-reverse="true"]
[data-framer-legacy-stack-gap-enabled="true"]
> *:last-child,
[data-framer-stack-direction-reverse="true"]
[data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
> *:last-child {
    margin-top: 0;
    margin-left: 0;
}`,Db=`
.${ar}
[data-framer-stack-direction-reverse="false"]
[data-framer-legacy-stack-gap-enabled="true"]
> *:last-child,
[data-framer-stack-direction-reverse="false"]
[data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
> *:last-child,
.${ar}
[data-framer-stack-direction-reverse="true"]
[data-framer-legacy-stack-gap-enabled="true"]
> *:first-child,
[data-framer-stack-direction-reverse="true"]
[data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
> *:first-child {
    margin-right: 0;
    margin-bottom: 0;
}`,Bb=[Lb,Ab,...Ob,Vb,Db],zb=[`
NavigationContainer
[data-framer-component-type="NavigationContainer"] > *,
[data-framer-component-type="NavigationContainer"] > [data-framer-component-type] {
    position: relative;
}`],Hb=['[data-framer-component-type="Scroll"]::-webkit-scrollbar { display: none; }','[data-framer-component-type="ScrollContentWrapper"] > * { position: relative; }'],Nb=['[data-framer-component-type="NativeScroll"] { -webkit-overflow-scrolling: touch; }','[data-framer-component-type="NativeScroll"] > * { position: relative; }','[data-framer-component-type="NativeScroll"].direction-both { overflow-x: scroll; overflow-y: scroll; }','[data-framer-component-type="NativeScroll"].direction-vertical { overflow-x: hidden; overflow-y: scroll; }','[data-framer-component-type="NativeScroll"].direction-horizontal { overflow-x: scroll; overflow-y: hidden; }','[data-framer-component-type="NativeScroll"].direction-vertical > * { width: 100% !important; }','[data-framer-component-type="NativeScroll"].direction-horizontal > * { height: 100% !important; }','[data-framer-component-type="NativeScroll"].scrollbar-hidden::-webkit-scrollbar { display: none; }'],$b=['[data-framer-component-type="DeviceComponent"].no-device > * { width: 100% !important; height: 100% !important; }'],Ub=['[data-framer-component-type="PageContentWrapper"] > *, [data-framer-component-type="PageContentWrapper"] > [data-framer-component-type] { position: relative; }'],Wb=['[data-is-present="false"], [data-is-present="false"] * { pointer-events: none !important; }'],jb=['[data-framer-cursor="pointer"] { cursor: pointer; }','[data-framer-cursor="grab"] { cursor: grab; }','[data-framer-cursor="grab"]:active { cursor: grabbing; }'],Gb=['[data-framer-component-type="Frame"] *, [data-framer-component-type="Stack"] * { pointer-events: auto; }',"[data-framer-generated] * { pointer-events: unset }"],Xb=[`[data-reset="button"] {
        border-width: 0;
        padding: 0;
        background: none;
}`],Yb=['[data-hide-scrollbars="true"]::-webkit-scrollbar { width: 0px; height: 0px; }','[data-hide-scrollbars="true"]::-webkit-scrollbar-thumb { background: transparent; }'],Kb=e=>e?Gb:[],qb=[".svgContainer svg { display: block; }"],jd=e=>[...Cb,...Mb,...Sb,...bb,...Bb,...zb,...Hb,...Nb,...Ub,...$b,...Wb,...jb,...Kb(e),...qb,...Xb,...Yb],Zb=jd(!1),Jb=jd(!0),Tf=!1;function $t(){if(Tf)return;Tf=!0;let e=Q.current()==="PREVIEW"?Jb:Zb;for(let t of e)Wd(t,void 0,void 0)}function Gd(e){return typeof e=="function"}function Qb(e){return typeof e=="boolean"}function te(e){return typeof e=="string"}function Se(e){return typeof e=="number"&&Number.isFinite(e)}function ts(e){return Array.isArray(e)}function qe(e){return e!==null&&typeof e=="object"&&!ts(e)}function wn(e){return typeof e>"u"}function mt(e){return e instanceof Date&&!isNaN(e.getTime())}function eS(e){return qe(e)||Gd(e)}var Ef="optional";function tS(e){return!!e&&Ef in e&&e[Ef]===!0}function rS(e){try{switch(e.type){case"string":case"color":case"date":case"link":return te(e.defaultValue)?e.defaultValue:void 0;case"boolean":return Qb(e.defaultValue)?e.defaultValue:void 0;case"enum":return wn(e.defaultValue)?void 0:e.options.includes(e.defaultValue)?e.defaultValue:void 0;case"fusednumber":case"number":return Se(e.defaultValue)?e.defaultValue:void 0;case"transition":return qe(e.defaultValue)?e.defaultValue:void 0;case"font":return{};case"object":{let t=qe(e.defaultValue)?e.defaultValue:{};return qe(e.controls)&&Xd(t,e.controls),t}case"array":return ts(e.defaultValue)?e.defaultValue:void 0;case"file":case"image":case"richtext":case"pagescope":case"eventhandler":case"segmentedenum":case"responsiveimage":case"componentinstance":return;default:return}}catch{return}}function Xd(e,t){for(let r in t){let n=t[r];if(!n)continue;let i=e[r];if(!wn(i)||tS(n))continue;let o=rS(n);wn(o)||(e[r]=o)}}function nS(e){if(qe(e.defaultProps))return e.defaultProps;let t={};return e.defaultProps=t,t}function iS(e,t){if(!eS(e))return;let r=nS(e);Xd(r,t)}function Yd(e,t){Object.assign(e,{propertyControls:t}),iS(e,t)}var ft={iPhonePro:{screenRadius:0,clayBezelLeft:21,clayBezelRight:21,clayBezelTop:21,clayBezelBottom:21,clayBezelRadius:38+21},iPhone8:{screenRadius:0,clayBezelLeft:24,clayBezelRight:24,clayBezelTop:96,clayBezelBottom:96,clayBezelRadius:38*1.5},iPadPro:{screenRadius:25,clayBezelLeft:38,clayBezelRight:38,clayBezelTop:38,clayBezelBottom:38,clayBezelRadius:25+38},desktop:{clayBezelLeft:20,clayBezelRight:20,clayBezelTop:20,clayBezelBottom:20,clayBezelRadius:20}},oS=[{id:"iphone-12",title:"iPhone 12",screenRadius:0,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:390,screenHeight:844,externalClay:{width:500,height:974,screenOffsetTop:65,screenOffsetLeft:55},screenMask:'<g style="transform: scale(0.5);"><path d="M171.2 0c2.3 0 4 .5 5.4 1.3 1.6 1 2.8 2.2 3.7 3.8.8 1.6 1.2 2.3 1.2 4.9 0 12 2.2 19 6.2 26.5s9.8 13.3 17.3 17.4c7.5 4 15.8 6.1 30.6 6.1h311.5c14.3 0 22.5-2.2 29.9-6.1 7.5-4 13.3-10 17.3-17.4 4-7.5 6.2-14.5 6.2-26.5 0-2.6.4-3.2 1.1-4.9.8-1.6 2-2.9 3.4-3.8 1.4-.8 3.2-1.3 5.4-1.3h54.2c40.1 0 54.7 4.2 69.4 12a81.8 81.8 0 0134 34c7.8 14.7 12 29.3 12 69.4v1457.2c0 40.1-4.2 54.7-12 69.4a81.8 81.8 0 01-34 34c-14.7 7.8-29.3 12-69.4 12H115.4c-40.1 0-54.7-4.2-69.4-12a81.8 81.8 0 01-34-34c-7.8-14.7-12-29.3-12-69.4V115.4C0 75.3 4.2 60.7 12 46a81.8 81.8 0 0134-34C60.7 4.2 75.3 0 115.4 0h55.4z" fill="#000" fill-rule="evenodd"/></g>',realisticImage:{width:490,height:944,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"black",title:"Black",colorValue:"#2E2C36"},{id:"white",title:"White",colorValue:"#F7F3F0"},{id:"blue",title:"Blue",colorValue:"#14496D"},{id:"green",title:"Green",colorValue:"#DAF0D9"},{id:"red",title:"Red",colorValue:"#DB4141"}],handOffset:{left:29,right:29,bottom:29}}},{id:"iphone-12-mini",title:"iPhone 12 Mini",screenRadius:0,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:360,screenHeight:780,externalClay:{width:450,height:890,screenOffsetTop:55,screenOffsetLeft:45},screenMask:'<g style="transform: scale(0.5);"><path d="M142 18c0 19 14 47 43 48h349c31 0 44-29 44-48 0-12 4-18 14-18h18c38 0 52 4 66 11 14 8 25 19 33 33v1c7 14 11 28 11 65v1340c0 38-4 52-11 66-8 14-19 25-33 33h-1c-14 7-28 11-65 11H110c-38 0-52-4-66-11-14-8-25-19-33-33v-1c-7-13-11-27-11-64V110c0-38 4-52 11-66 8-14 19-25 33-33h1C58 4 72 0 109 0h16c11 0 17 6 17 18z" fill="#000" fill-rule="evenodd"/></g>',realisticImage:{width:460,height:880,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"black",title:"Black",colorValue:"#2E2C36"},{id:"white",title:"White",colorValue:"#F7F3F0"},{id:"blue",title:"Blue",colorValue:"#14496D"},{id:"green",title:"Green",colorValue:"#DAF0D9"},{id:"red",title:"Red",colorValue:"#DB4141"}],handOffset:{left:31.5,right:30.5,bottom:30}}},{id:"iphone-12-pro",title:"iPhone 12 Pro",screenRadius:0,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:390,screenHeight:844,externalClay:{width:494,height:968,screenOffsetTop:62,screenOffsetLeft:52},screenMask:'<g style="transform: scale(0.5);"><path d="M171.2 0c2.3 0 4 .5 5.4 1.3 1.6 1 2.8 2.2 3.7 3.8.8 1.6 1.2 2.3 1.2 4.9 0 12 2.2 19 6.2 26.5s9.8 13.3 17.3 17.4c7.5 4 15.8 6.1 30.6 6.1h311.5c14.3 0 22.5-2.2 29.9-6.1 7.5-4 13.3-10 17.3-17.4 4-7.5 6.2-14.5 6.2-26.5 0-2.6.4-3.2 1.1-4.9.8-1.6 2-2.9 3.4-3.8 1.4-.8 3.2-1.3 5.4-1.3h54.2c40.1 0 54.7 4.2 69.4 12a81.8 81.8 0 0134 34c7.8 14.7 12 29.3 12 69.4v1457.2c0 40.1-4.2 54.7-12 69.4a81.8 81.8 0 01-34 34c-14.7 7.8-29.3 12-69.4 12H115.4c-40.1 0-54.7-4.2-69.4-12a81.8 81.8 0 01-34-34c-7.8-14.7-12-29.3-12-69.4V115.4C0 75.3 4.2 60.7 12 46a81.8 81.8 0 0134-34C60.7 4.2 75.3 0 115.4 0h55.4z" fill="#000" fill-rule="evenodd"/></g>',realisticImage:{width:490,height:944,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"graphite",title:"Graphite",colorValue:"#585753"},{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"pacific-blue",title:"Pacific Blue",colorValue:"#415D6C"},{id:"gold",title:"Gold",colorValue:"#FCECD5"}],handOffset:{left:29,right:29,bottom:29}}},{id:"iphone-12-pro-max",title:"iPhone 12 Pro Max",screenRadius:50,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:428,screenHeight:926,externalClay:{width:532,height:1050,screenOffsetTop:62,screenOffsetLeft:52},screenMask:'<path d="M102 0c6 0 7 3 7 9 0 10 7 23 24 23h164c13 0 22-12 22-23 0-6 1-9 7-9h34c24 0 32 2 41 7s15 11 20 20 7 17 7 41v790c0 24-2 32-7 41s-11 15-20 20-17 7-41 7H68c-24 0-32-2-41-7s-15-11-20-20-7-17-7-41V68c0-24 2-32 7-41S18 12 27 7s17-7 41-7h34z" fill="#000" fill-rule="evenodd"/>',realisticImage:{width:528,height:1026,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"graphite",title:"Graphite",colorValue:"#585753"},{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"pacific-blue",title:"Pacific Blue",colorValue:"#415D6C"},{id:"gold",title:"Gold",colorValue:"#FCECD5"}],handOffset:{left:28.5,right:28,bottom:29}}},{id:"iphone-11",title:"iPhone 11",screenRadius:0,clayBezelLeft:35.5,clayBezelRight:35.5,clayBezelTop:35.5,clayBezelBottom:35.5,clayBezelRadius:77,screenWidth:414,screenHeight:896,externalClay:{width:524,height:1026,screenOffsetTop:65,screenOffsetLeft:55},screenMask:'<path d="M85.5 0C89.1 0 92 3 92 6.5c.3 6 1.5 10 3.4 13.5 2.2 4.1 5.5 7.4 9.6 9.6 4.2 2.2 8.9 3.4 17 3.4h170c8.1 0 12.8-1.2 17-3.4 4.1-2.2 7.4-5.5 9.6-9.6A31 31 0 00322 6.5c0-3.6 3-6.5 6.5-6.5h32.3c18.5 0 25.2 2 32 5.5 6.7 3.7 12 9 15.7 15.7 3.6 6.8 5.5 13.5 5.5 32v789.6c0 18.5-2 25.2-5.5 32-3.7 6.7-9 12-15.7 15.7-6.8 3.6-13.5 5.5-32 5.5H53.2c-18.5 0-25.2-2-32-5.5-6.7-3.7-12-9-15.7-15.7C2 868 0 861.3 0 842.8V53.2c0-18.5 2-25.2 5.5-32 3.7-6.7 9-12 15.7-15.7C28 2 34.7 0 53.2 0h32.3z" fill="#000" fill-rule="nonzero"/>',realisticImage:{width:514,height:996,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"black",title:"Black",colorValue:"#202120"},{id:"white",title:"White",colorValue:"#F9F6EF"},{id:"purple",title:"Purple",colorValue:"#D1CDDB"},{id:"green",title:"Green",colorValue:"#ADE0CD"},{id:"red",title:"Red",colorValue:"#B90D2E"},{id:"yellow",title:"Yellow",colorValue:"#FFE680"}],handOffset:{left:14.5,right:14.5,bottom:14.5}}},{id:"iphone-11-pro",title:"iPhone 11 Pro",...ft.iPhonePro,screenWidth:375,screenHeight:812,externalClay:{width:485,height:942,screenOffsetTop:65,screenOffsetLeft:55},screenMask:'<path d="M292 8.668V9c0 9.266-7.07 21-23.332 21h-162C90.402 30 83.332 18.266 83.332 9v-.332c0-4.285 0-8.668-7.664-8.668H43.332C16.312 0 0 16.313 0 43.332v725.336C0 795.688 16.313 812 43.332 812h288.336c27.02 0 43.332-16.313 43.332-43.332V43.332C375 16.312 358.687 0 331.668 0h-32C292 0 292 4.383 292 8.668zm0 0"/>',realisticImage:{width:475,height:912,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#52514F"},{id:"silver",title:"Silver",colorValue:"#EBEBE3"},{id:"gold",title:"Gold",colorValue:"#FBD7BD"},{id:"midnight-green",title:"Midnight Green",colorValue:"#4F5850"}],handOffset:{left:24.5,right:24.5,bottom:23.5}}},{id:"iphone-11-pro-max",title:"iPhone 11 Pro Max",...ft.iPhonePro,screenWidth:414,screenHeight:896,externalClay:{width:524,height:1026,screenOffsetTop:65,screenOffsetLeft:55},screenMask:'<path d="M96 0c3.313 0 5.91 2.688 6 6 .18 6.645 1.191 10.148 2.938 13.41 1.917 3.586 4.73 6.402 8.316 8.317 3.586 1.918 7.441 2.941 15.445 2.941h156.602c8.004 0 11.86-1.023 15.445-2.941 3.586-1.915 6.399-4.73 8.317-8.317 1.746-3.265 2.746-6.758 2.937-13.41.094-3.313 2.688-6 6-6h46.004c17.387 0 23.687 1.809 30.043 5.21 6.355 3.4 11.344 8.388 14.742 14.743C412.191 26.31 414 32.61 414 49.996v796.008c0 17.387-1.809 23.687-5.21 30.043-3.4 6.355-8.388 11.344-14.743 14.742-6.356 3.402-12.656 5.211-30.043 5.211H49.996c-17.387 0-23.687-1.809-30.043-5.21-6.355-3.4-11.344-8.388-14.742-14.743C1.809 869.69 0 863.39 0 846.004V49.996C0 32.61 1.809 26.31 5.21 19.953c3.4-6.355 8.388-11.344 14.743-14.742C26.31 1.809 32.61 0 49.996 0zm0 0"/>',realisticImage:{width:514,height:996,screenOffsetLeft:50,screenOffsetTop:50,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#52514F"},{id:"silver",title:"Silver",colorValue:"#EBEBE3"},{id:"gold",title:"Gold",colorValue:"#FBD7BD"},{id:"midnight-green",title:"Midnight Green",colorValue:"#4F5850"}],handOffset:{left:23.5,right:24.5,bottom:24}}},{id:"iphone-8",title:"iPhone 8",...ft.iPhone8,screenWidth:375,screenHeight:667,externalClay:{width:491,height:971,screenOffsetLeft:58,screenOffsetTop:152},realisticImage:{width:475,height:927,screenOffsetLeft:50,screenOffsetTop:130,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#28282A"},{id:"silver",title:"Silver",colorValue:"#DFE1E2"},{id:"gold",title:"Gold",colorValue:"#F6E6DB"}],handOffset:{left:22,right:22,bottom:18.5}}},{id:"iphone-8-plus",title:"iPhone 8 Plus",...ft.iPhone8,screenWidth:414,screenHeight:736,externalClay:{width:530,height:1064,screenOffsetLeft:58,screenOffsetTop:164},realisticImage:{width:514,height:996,screenOffsetLeft:50,screenOffsetTop:130,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#28282A"},{id:"silver",title:"Silver",colorValue:"#DFE1E2"},{id:"gold",title:"Gold",colorValue:"#F6E6DB"}],handOffset:{left:21,right:20.5,bottom:19}}},{id:"iphone-se",title:"iPhone SE",screenWidth:320,screenHeight:568,screenRadius:0,clayBezelLeft:20,clayBezelRight:20,clayBezelTop:112,clayBezelBottom:112,clayBezelRadius:38*1.5,externalClay:{width:436,height:872,screenOffsetLeft:58,screenOffsetTop:152},realisticImage:{width:420,height:828,screenOffsetLeft:50,screenOffsetTop:130,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"},{id:"gold",title:"Gold",colorValue:"#EFD8BD"},{id:"rose-gold",title:"Rose Gold",colorValue:"#F7CFCA"}],handOffset:{left:22,right:22,bottom:26.5}}},{id:"samsung-galaxy-s7",title:"Samsung Galaxy S7",screenRadius:0,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:360,screenHeight:640,externalClay:{width:454,height:880,screenOffsetTop:120,screenOffsetLeft:47},realisticImage:{width:440,height:860,screenOffsetLeft:40,screenOffsetTop:110,availableColors:[{id:"black",title:"Black",colorValue:"#2E2C36"},{id:"white",title:"White",colorValue:"#F7F3F0"},{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"gold",title:"Gold",colorValue:"#FCECD5"}],handOffset:{left:26,right:25.5,bottom:32}}},{id:"samsung-note-10",title:"Samsung Note 10",screenWidth:360,screenHeight:760,screenRadius:10,clayBezelLeft:7,clayBezelRight:7,clayBezelTop:15,clayBezelBottom:15,clayBezelRadius:15},{id:"pixel-5",title:"Google Pixel 5",screenRadius:31,clayBezelLeft:22,clayBezelRight:22,clayBezelTop:22,clayBezelBottom:22,clayBezelRadius:66,screenWidth:360,screenHeight:780,externalClay:{width:460,height:900,screenOffsetTop:60,screenOffsetLeft:50},realisticImage:{width:920/2,height:1760/2,screenOffsetLeft:100/2,screenOffsetTop:100/2,availableColors:[{id:"just-black",title:"Just Black",colorValue:"#2E2C36"},{id:"sorta-sage",title:"Sorta Sage",colorValue:"#B7C9C0"}],handOffset:{left:31.5,right:31,bottom:31}}},{id:"pixel-4",title:"Google Pixel 4",screenWidth:360,screenHeight:760,screenRadius:34,clayBezelLeft:10,clayBezelRight:10,clayBezelTop:50,clayBezelBottom:25,clayBezelRadius:50,externalClay:{width:460,height:938,screenOffsetLeft:50,screenOffsetTop:89},realisticImage:{width:460,height:920,screenOffsetLeft:50,screenOffsetTop:80,availableColors:[{id:"clearly-white",title:"Clearly White",colorValue:"#EAEDF2"},{id:"just-black",title:"Just Black",colorValue:"#1A1A1A"},{id:"oh-so-orange",title:"Oh So Orange",colorValue:"#FF7A68"}],handOffset:{left:35.5,right:35.5,bottom:57}}},{id:"macbook-air",title:"MacBook Air",screenWidth:1440,screenHeight:900,disableRotation:!0,externalClay:{width:1890,height:1125,screenOffsetLeft:225,screenOffsetTop:98},realisticImage:{width:3848/2,height:2240/2,screenOffsetLeft:484/2,screenOffsetTop:196/2,availableColors:[{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"space-grey",title:"Space Grey",colorValue:"#B1B5B7"},{id:"gold",title:"Gold",colorValue:"#FCECD5"}]}},{id:"macbook-pro-13",title:'MacBook Pro 13"',screenWidth:1440,screenHeight:900,disableRotation:!0,externalClay:{width:1914,height:1169,screenOffsetLeft:236,screenOffsetTop:109},realisticImage:{width:3916/2,height:2330/2,screenOffsetLeft:518/2,screenOffsetTop:218/2,availableColors:[{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"space-grey",title:"Space Grey",colorValue:"#B1B5B7"}]}},{id:"macbook-pro-16",title:'MacBook Pro 16"',screenWidth:1536,screenHeight:960,disableRotation:!0,externalClay:{width:1984,height:1179,screenOffsetLeft:225,screenOffsetTop:78},realisticImage:{width:4032/2,height:2348/2,screenOffsetLeft:480/2,screenOffsetTop:148/2,availableColors:[{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"space-grey",title:"Space Grey",colorValue:"#B1B5B7"}]}},{id:"imac-21-5",title:'iMac 21.5"',screenWidth:2048,screenHeight:1152,disableRotation:!0,externalClay:{width:2288,height:1892,screenOffsetLeft:120,screenOffsetTop:120},realisticImage:{width:4562/2,height:3796/2,screenOffsetLeft:232/2,screenOffsetTop:244/2}},{id:"imac-27",title:'iMac 27"',screenWidth:2560,screenHeight:1440,disableRotation:!0,externalClay:{width:2848,height:2351,screenOffsetLeft:144,screenOffsetTop:151},realisticImage:{width:5676/2,height:4720/2,screenOffsetLeft:278/2,screenOffsetTop:292/2,availableColors:[{id:"silver",title:"Silver",colorValue:"#E5E6E1"},{id:"pro",title:"Pro",colorValue:"#5F5E63"}]}},{id:"pro-display-xdr",title:"Pro Display XDR",screenWidth:3008,screenHeight:1692,disableRotation:!0,externalClay:{width:3148,height:2325,screenOffsetLeft:70,screenOffsetTop:60},realisticImage:{width:6276/2,height:4695/2,screenOffsetLeft:130/2,screenOffsetTop:130/2}},{id:"dell-xps",title:"Dell XPS",screenWidth:1920,screenHeight:1080,disableRotation:!0,externalClay:{width:2624,height:1381,screenOffsetLeft:352,screenOffsetTop:57},realisticImage:{width:5412/2,height:2746/2,screenOffsetLeft:786/2,screenOffsetTop:108/2}},{id:"surface-book",title:"Microsoft Surface Book",screenWidth:1500,screenHeight:1e3,disableRotation:!0,externalClay:{width:2089,height:1234,screenOffsetLeft:296,screenOffsetTop:93},realisticImage:{width:4200/2,height:2508/2,screenOffsetLeft:600/2,screenOffsetTop:210/2}},{id:"ipad",title:"iPad",screenRadius:0,screenWidth:810,screenHeight:1080,clayBezelLeft:30,clayBezelRight:30,clayBezelTop:95,clayBezelBottom:95,clayBezelRadius:0,externalClay:{width:966,height:1378,screenOffsetLeft:78,screenOffsetTop:149},realisticImage:{width:1920/2,height:2720/2,screenOffsetLeft:75,screenOffsetTop:140,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"},{id:"gold",title:"Gold",colorValue:"#EFD8BD"}]}},{id:"ipad-mini",title:"iPad Mini",screenRadius:0,clayBezelLeft:49,clayBezelRight:49,clayBezelTop:49,clayBezelBottom:49,clayBezelRadius:49,screenWidth:768,screenHeight:1024,externalClay:{width:924,height:1384,screenOffsetLeft:78,screenOffsetTop:180},realisticImage:{width:1856/2,height:2728/2,screenOffsetLeft:160/2,screenOffsetTop:340/2,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"},{id:"gold",title:"Gold",colorValue:"#EFD8BD"}]}},{id:"ipad-air",title:"iPad Air",screenRadius:18,clayBezelLeft:49,clayBezelRight:49,clayBezelTop:49,clayBezelBottom:49,clayBezelRadius:49,screenWidth:820,screenHeight:1180,externalClay:{width:994,height:1374,screenOffsetLeft:87,screenOffsetTop:97},realisticImage:{width:1960/2,height:2680/2,screenOffsetLeft:160/2,screenOffsetTop:160/2,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"},{id:"rose-gold",title:"Rose Gold",colorValue:"#ECCBC4"},{id:"blue",title:"Blue",colorValue:"#CBDAE6"},{id:"green",title:"Green",colorValue:"#DAF0D9"}]}},{id:"ipad-pro-11",title:"iPad Pro 11\u2033",screenRadius:17,clayBezelLeft:49,clayBezelRight:49,clayBezelTop:49,clayBezelBottom:49,clayBezelRadius:49,screenWidth:834,screenHeight:1194,externalClay:{width:990,height:1370,screenOffsetLeft:78,screenOffsetTop:88},realisticImage:{width:1968/2,height:2688/2,screenOffsetLeft:75,screenOffsetTop:75,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"}]}},{id:"ipad-pro-12-9",title:"iPad Pro 12.9\u2033",...ft.iPadPro,screenRadius:17,screenWidth:1024,screenHeight:1366,externalClay:{width:1180,height:1542,screenOffsetLeft:78,screenOffsetTop:88},realisticImage:{width:2348/2,height:3032/2,screenOffsetLeft:75,screenOffsetTop:75,availableColors:[{id:"space-grey",title:"Space Grey",colorValue:"#C3C4C8"},{id:"silver",title:"Silver",colorValue:"#E1E2E4"}]}},{id:"surface-3",title:"Microsoft Surface 3",screenRadius:0,clayBezelLeft:49,clayBezelRight:49,clayBezelTop:49,clayBezelBottom:49,clayBezelRadius:49,screenWidth:960,screenHeight:640,externalClay:{width:1184,height:864,screenOffsetLeft:112,screenOffsetTop:112},realisticImage:{width:2280/2,height:1580/2,screenOffsetLeft:180/2,screenOffsetTop:150/2}},{id:"surface-pro-4",title:"Microsoft Surface Pro 4",screenRadius:0,clayBezelLeft:49,clayBezelRight:49,clayBezelTop:49,clayBezelBottom:49,clayBezelRadius:49,screenWidth:1368,screenHeight:912,externalClay:{width:1592,height:1136,screenOffsetLeft:112,screenOffsetTop:112},realisticImage:{width:3176/2,height:2224/2,screenOffsetLeft:220/2,screenOffsetTop:200/2}},{id:"apple-watch-44",title:"Apple Watch 44mm",screenRadius:33,screenWidth:184,screenHeight:224,disableRotation:!0,externalClay:{width:298,height:502,screenOffsetLeft:57,screenOffsetTop:129},realisticImage:{width:548/2,height:908/2,screenOffsetLeft:90/2,screenOffsetTop:230/2,availableColors:[{id:"black",title:"Black",colorValue:"#2E2C36"},{id:"white",title:"White",colorValue:"#F7F3F0"},{id:"yellow",title:"Yellow",colorValue:"#FDDC6C"},{id:"orange",title:"Orange",colorValue:"#F35C56"}]}},{id:"apple-watch-40",title:"Apple Watch 40mm",screenRadius:27,screenWidth:162,screenHeight:197,disableRotation:!0,externalClay:{width:280,height:463,screenOffsetLeft:59,screenOffsetTop:124},realisticImage:{width:504/2,height:854/2,screenOffsetLeft:90/2,screenOffsetTop:230/2,availableColors:[{id:"black",title:"Black",colorValue:"#2E2C36"},{id:"white",title:"White",colorValue:"#F7F3F0"},{id:"yellow",title:"Yellow",colorValue:"#FDDC6C"},{id:"orange",title:"Orange",colorValue:"#F35C56"}]}},{id:"tv-full-hd",title:"Full HD",screenRadius:0,screenWidth:1920,screenHeight:1080,externalClay:{width:1968,height:1168,screenOffsetLeft:24,screenOffsetTop:12},realisticImage:{width:4040/2,height:2360/2,screenOffsetLeft:100/2,screenOffsetTop:100/2}},{id:"tv-4k",title:"4K",screenRadius:0,screenWidth:3840,screenHeight:2160,externalClay:{width:3908,height:2308,screenOffsetLeft:34,screenOffsetTop:24},realisticImage:{width:7960/2,height:4600/2,screenOffsetLeft:140/2,screenOffsetTop:140/2}},{id:"720p",title:"720p",...ft.desktop,screenWidth:720,screenHeight:1280},{id:"900p",title:"900p",...ft.desktop,screenWidth:900,screenHeight:1440},{id:"1080p",title:"1080p",...ft.desktop,screenWidth:1080,screenHeight:1920},{id:"1440p",title:"1440p",...ft.desktop,screenWidth:1440,screenHeight:2560},{id:"4k",title:"4K",...ft.desktop,screenWidth:2160,screenHeight:3840}];var h_=oS.reduce((e,t)=>(e[t.id]=t,e),{});var aS=be(gt(),1);function sS(e){let t=Object.create(null);return r=>(t[r]===void 0&&(t[r]=e(r)),t[r])}var lS=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,cS=sS(e=>lS.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91),Tt=typeof document<"u"?Wr:N,kf=e=>()=>{Za(e)},uS={useImageSource(e){var t;return(t=e.src)!=null?t:""},useImageElement(e,t,r){let n=new Image;return n.src=sr.useImageSource(e,t,r),e.srcSet&&(n.srcset=e.srcSet),n}},fS=!1,dS={get(e,t,r){return Reflect.has(e,t)?Reflect.get(e,t,r):kf(fS?`${String(t)} is not available in this version of Framer.`:`${String(t)} is only available inside of Framer. https://www.framer.com/`)}},sr=new Proxy(uS,dS);function hS(e,t,r=1){var n,i,o,a;let{width:s,height:l}=t,c=(i=(n=e.pixelWidth)!=null?n:e.intrinsicWidth)!=null?i:0,u=(a=(o=e.pixelHeight)!=null?o:e.intrinsicHeight)!=null?a:0;if(s<1||l<1||c<1||u<1)return;s*=r,l*=r;let f=s/l,d=c/u;switch(e.fit){case"fill":return d>f?u/l:c/s;case"fit":case"stretch":return Math.max(c/s,u/l)}}function Rf(e,t){return t&&Math.max(1,e)>t?"pixelated":"auto"}var _f={position:"absolute",pointerEvents:"none",userSelect:"none",borderRadius:"inherit",top:0,right:0,bottom:0,left:0},mS={backgroundSize:"16px 16px",backgroundImage:"repeating-linear-gradient(45deg, rgba(180, 180, 180, 0.8) 0, rgba(180, 180, 180, 0.8) 1px, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.2) 50%)",border:"1px solid #c4c4c4"};function pS(e){switch(e){case"fit":return"contain";case"stretch":return"fill";default:return"cover"}}function vS(e,t){if(!t)return"auto";let r=Q.current()==="CANVAS"?se.devicePixelRatio:1,n=hS(e,t,r);return Q.current()==="CANVAS"?Rf(1,n):Rf(Or.zoom,n)}function gS(e,t){return{pointerEvents:"none",userSelect:"none",display:"block",width:"100%",height:"100%",borderRadius:"inherit",objectPosition:"center",objectFit:pS(e.fit),imageRendering:vS(e,t)}}function yS({image:e,containerSize:t,nodeId:r,alt:n}){let i=h.useRef(null),o=Q.current()!=="CANVAS",a=sr.useImageSource(e,t,r),s=gS(e,t);if(!o){let l=sr.useImageElement(e,t,r);Tt(()=>{let c=i.current;if(c!==null)return c.appendChild(l),()=>{c.removeChild(l)}},[l]),Object.assign(l.style,s)}return h.createElement("div",{ref:i,style:{display:"contents",borderRadius:"inherit",pointerEvents:"none"}},o?h.createElement("img",{src:a,alt:n??e.alt,srcSet:e.srcSet,sizes:e.sizes,style:s,loading:e.loading}):null)}function Kd({layoutId:e,image:t,...r}){e&&(e=e+"-background");let n=te(t.src),i=!n;return h.createElement(ke.div,{layoutId:e,style:i?{..._f,...mS}:_f,"data-framer-background-image-wrapper":!0},n&&h.createElement(yS,{image:t,...r}))}var xS="src",Et;(e=>{e.isImageObject=function(t){return!t||typeof t=="string"?!1:xS in t}})(Et||(Et={}));function bS(e,t){let{_forwardedOverrideId:r,_forwardedOverrides:n,id:i}=t,o=r??i,a=n&&o?n[o]:void 0;return a&&typeof a=="string"&&(e={...e,src:a}),e}function SS(e){let{background:t,image:r}=e;if(r!==void 0&&t&&!Et.isImageObject(t))return;let n=null;if(te(r)?n={alt:"",src:r}:n=de.get(t,null),!!Et.isImageObject(n))return bS(n,e)}function wS(e,t,r=!0){let{borderWidth:n,borderStyle:i,borderColor:o}=e;if(!n)return;let a,s,l,c;if(typeof n=="number"?a=s=l=c=n:(a=n.top||0,s=n.bottom||0,l=n.left||0,c=n.right||0),!(a===0&&s===0&&l===0&&c===0)){if(r&&a===s&&a===l&&a===c){t.border=`${a}px ${i} ${o}`;return}t.borderStyle=e.borderStyle,t.borderColor=e.borderColor,t.borderTopWidth=`${a}px`,t.borderBottomWidth=`${s}px`,t.borderLeftWidth=`${l}px`,t.borderRightWidth=`${c}px`}}function CS(e){let t=e.layoutId?`${e.layoutId}-border`:void 0;if(!e.borderWidth)return null;let r={position:"absolute",left:0,right:0,top:0,bottom:0,borderRadius:"inherit",pointerEvents:"none"};return e.border?(r.border=e.border,h.createElement(ke.div,{style:r})):(wS(e,r,!1),h.createElement(ke.div,{"data-frame-border":!0,style:r,layoutId:t}))}var TS=be(gt(),1),me=typeof Hr<"u"?Hr:void 0,kt=()=>typeof document=="object";var ES=()=>{let e=-1,r=me&&/Version\/([\d.]+)/.exec(me.userAgent);return r&&r[1]&&(e=parseFloat(r[1])),e},kS=()=>me&&/Chrome/.test(me.userAgent)&&/Google Inc/.test(me.vendor)&&!_S(),rs=()=>me&&/Safari/.test(me.userAgent)&&/Apple Computer/.test(me.vendor);var RS=()=>me&&/FramerX/.test(me.userAgent),_S=()=>me&&/Edg\//.test(me.userAgent),PS=()=>me&&/(android)/i.test(me.userAgent),IS=()=>me&&/(iPhone|iPod|iPad)/i.test(me.platform),FS=()=>me&&/Mac/.test(me.platform),MS=()=>me&&/Win/.test(me.platform),LS=()=>se.ontouchstart===null&&se.ontouchmove===null&&se.ontouchend===null;var OS=()=>TS.default.env.NODE_ENV==="test";var AS=()=>{if(FS())return"macos";if(IS())return"ios";if(PS())return"android";if(MS())return"windows"},VS=e=>{e||(e=AS());let t={apple:"-apple-system, BlinkMacSystemFont, SF Pro Text, SF UI Text, Helvetica Neue",google:"Roboto, Helvetica Neue",microsoft:"Segoe UI, Helvetica Neue"};return e==="macos"||e==="ios"?t.apple:e==="android"?t.google:e==="windows"?t.microsoft:t.apple};var DS=kS();function ns(e){let t={};return!DS||Q.current()!=="CANVAS"||((e===!0||e==="x")&&(t["data-framer-layout-hint-center-x"]=!0),(e===!0||e==="y")&&(t["data-framer-layout-hint-center-y"]=!0)),t}function is(e){return e.replace(/^id_/,"").replace(/\\/g,"")}function qd(e,t){if(!t&&(t=e.children,!t))return{props:e,children:t};let r=e._forwardedOverrides,n=e._overrideForwardingDescription;if(n){r=void 0;for(let i in n){let o=n[i];e[o]!==void 0&&(r||(r={},e={...e}),r[i]=e[o],delete e[o])}}return r?(t=h.Children.map(t,i=>h.isValidElement(i)?h.cloneElement(i,{_forwardedOverrides:r}):i),{props:e,children:t}):{props:e,children:t}}var BS=e=>Boolean(e&&typeof e=="object"&&e.mix&&e.toValue),zS=e=>e==="background"||e.endsWith("color")||e.endsWith("Color"),Pf=(e,t)=>t&&typeof t=="object"?(Ee(BS(t),"Motion styles must be numbers, strings, or an instance with a `toValue` and `mix` methods."),t.toValue()):zS(e)&&typeof t=="string"&&z.isColor(t)?z(t).toValue():t,HS=(e,t)=>{if(Array.isArray(t)){let r=t.length,n=[];for(let i=0;i<r;i++)n.push(Pf(e,t[i]));return n}else return Pf(e,t)},NS={size:{set:(e,t,r)=>{e.height===void 0&&(t.height=r),e.width===void 0&&(t.width=r)},type:H},radius:{set:(e,t,r)=>{t.borderRadius=r},type:H},shadow:{set:(e,t,r)=>{t.boxShadow=r},type:at}},$S=e=>{let t={};for(let r in e){let n=HS(r,e[r]),i=NS[r];if(i){let a=i.type&&typeof e[r]=="number"?i.type.transform(e[r]):e[r];i.set(e,t,a)}else t[r]=n}return t};function Pn(e){return(t,r)=>e===!0?`translate(-50%, -50%) ${r}`:e==="x"?`translateX(-50%) ${r}`:e==="y"?`translateY(-50%) ${r}`:r||"none"}function Rt(e,{specificLayoutId:t,postfix:r}={}){let{name:n,layoutIdKey:i,duplicatedFrom:o,__fromCodeComponentNode:a=!1,drag:s}=e,{getLayoutId:l,enabled:c}=M(Ar);return ne(()=>{if(!c)return e.layoutId;let u=t||e.layoutId;if(!u&&(s||!i||a))return;let f=u||l({id:i,name:n,duplicatedFrom:o});if(f)return r?`${f}-${r}`:f},[c])}var ur=h.createContext(!1),nr=[],US=function(){return nr.some(function(e){return e.activeTargets.length>0})},WS=function(){return nr.some(function(e){return e.skippedTargets.length>0})},If="ResizeObserver loop completed with undelivered notifications.",jS=function(){var e;typeof ErrorEvent=="function"?e=new ErrorEvent("error",{message:If}):(e=document.createEvent("Event"),e.initEvent("error",!1,!1),e.message=If),W.dispatchEvent(e)},Cn;(function(e){e.BORDER_BOX="border-box",e.CONTENT_BOX="content-box",e.DEVICE_PIXEL_CONTENT_BOX="device-pixel-content-box"})(Cn||(Cn={}));var ir=function(e){return Object.freeze(e)},GS=function(){function e(t,r){this.inlineSize=t,this.blockSize=r,ir(this)}return e}(),Zd=function(){function e(t,r,n,i){return this.x=t,this.y=r,this.width=n,this.height=i,this.top=this.y,this.left=this.x,this.bottom=this.top+this.height,this.right=this.left+this.width,ir(this)}return e.prototype.toJSON=function(){var t=this,r=t.x,n=t.y,i=t.top,o=t.right,a=t.bottom,s=t.left,l=t.width,c=t.height;return{x:r,y:n,top:i,right:o,bottom:a,left:s,width:l,height:c}},e.fromRect=function(t){return new e(t.x,t.y,t.width,t.height)},e}(),os=function(e){return e instanceof SVGElement&&"getBBox"in e},Jd=function(e){if(os(e)){var t=e.getBBox(),r=t.width,n=t.height;return!r&&!n}var i=e,o=i.offsetWidth,a=i.offsetHeight;return!(o||a||e.getClientRects().length)},Ff=function(e){var t,r;if(e instanceof Element)return!0;var n=(r=(t=e)===null||t===void 0?void 0:t.ownerDocument)===null||r===void 0?void 0:r.defaultView;return!!(n&&e instanceof n.Element)},XS=function(e){switch(e.tagName){case"INPUT":if(e.type!=="image")break;case"VIDEO":case"AUDIO":case"EMBED":case"OBJECT":case"CANVAS":case"IFRAME":case"IMG":return!0}return!1},Sn=typeof W<"u"?W:{},bi=new WeakMap,Mf=/auto|scroll/,YS=/^tb|vertical/,KS=/msie|trident/i.test(Sn.navigator&&Sn.navigator.userAgent),dt=function(e){return parseFloat(e||"0")},Lr=function(e,t,r){return e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=!1),new GS((r?t:e)||0,(r?e:t)||0)},Lf=ir({devicePixelContentBoxSize:Lr(),borderBoxSize:Lr(),contentBoxSize:Lr(),contentRect:new Zd(0,0,0,0)}),Qd=function(e,t){if(t===void 0&&(t=!1),bi.has(e)&&!t)return bi.get(e);if(Jd(e))return bi.set(e,Lf),Lf;var r=getComputedStyle(e),n=os(e)&&e.ownerSVGElement&&e.getBBox(),i=!KS&&r.boxSizing==="border-box",o=YS.test(r.writingMode||""),a=!n&&Mf.test(r.overflowY||""),s=!n&&Mf.test(r.overflowX||""),l=n?0:dt(r.paddingTop),c=n?0:dt(r.paddingRight),u=n?0:dt(r.paddingBottom),f=n?0:dt(r.paddingLeft),d=n?0:dt(r.borderTopWidth),m=n?0:dt(r.borderRightWidth),p=n?0:dt(r.borderBottomWidth),g=n?0:dt(r.borderLeftWidth),x=f+c,v=l+u,b=g+m,y=d+p,S=s?e.offsetHeight-y-e.clientHeight:0,C=a?e.offsetWidth-b-e.clientWidth:0,w=i?x+b:0,T=i?v+y:0,E=n?n.width:dt(r.width)-w-C,R=n?n.height:dt(r.height)-T-S,_=E+x+C+b,F=R+v+S+y,B=ir({devicePixelContentBoxSize:Lr(Math.round(E*devicePixelRatio),Math.round(R*devicePixelRatio),o),borderBoxSize:Lr(_,F,o),contentBoxSize:Lr(E,R,o),contentRect:new Zd(f,l,E,R)});return bi.set(e,B),B},eh=function(e,t,r){var n=Qd(e,r),i=n.borderBoxSize,o=n.contentBoxSize,a=n.devicePixelContentBoxSize;switch(t){case Cn.DEVICE_PIXEL_CONTENT_BOX:return a;case Cn.BORDER_BOX:return i;default:return o}},qS=function(){function e(t){var r=Qd(t);this.target=t,this.contentRect=r.contentRect,this.borderBoxSize=ir([r.borderBoxSize]),this.contentBoxSize=ir([r.contentBoxSize]),this.devicePixelContentBoxSize=ir([r.devicePixelContentBoxSize])}return e}(),th=function(e){if(Jd(e))return 1/0;for(var t=0,r=e.parentNode;r;)t+=1,r=r.parentNode;return t},ZS=function(){var e=1/0,t=[];nr.forEach(function(a){if(a.activeTargets.length!==0){var s=[];a.activeTargets.forEach(function(c){var u=new qS(c.target),f=th(c.target);s.push(u),c.lastReportedSize=eh(c.target,c.observedBox),f<e&&(e=f)}),t.push(function(){a.callback.call(a.observer,s,a.observer)}),a.activeTargets.splice(0,a.activeTargets.length)}});for(var r=0,n=t;r<n.length;r++){var i=n[r];i()}return e},Of=function(e){nr.forEach(function(r){r.activeTargets.splice(0,r.activeTargets.length),r.skippedTargets.splice(0,r.skippedTargets.length),r.observationTargets.forEach(function(i){i.isActive()&&(th(i.target)>e?r.activeTargets.push(i):r.skippedTargets.push(i))})})},JS=function(){var e=0;for(Of(e);US();)e=ZS(),Of(e);return WS()&&jS(),e>0},ga,rh=[],QS=function(){return rh.splice(0).forEach(function(e){return e()})},e1=function(e){if(!ga){var t=0,r=document.createTextNode(""),n={characterData:!0};new MutationObserver(function(){return QS()}).observe(r,n),ga=function(){r.textContent=""+(t?t--:t++)}}rh.push(e),ga()},t1=function(e){e1(function(){requestAnimationFrame(e)})},ki=0,r1=function(){return!!ki},n1=250,i1={attributes:!0,characterData:!0,childList:!0,subtree:!0},Af=["resize","load","transitionend","animationend","animationstart","animationiteration","keyup","keydown","mouseup","mousedown","mouseover","mouseout","blur","focus"],Vf=function(e){return e===void 0&&(e=0),Date.now()+e},ya=!1,o1=function(){function e(){var t=this;this.stopped=!0,this.listener=function(){return t.schedule()}}return e.prototype.run=function(t){var r=this;if(t===void 0&&(t=n1),!ya){ya=!0;var n=Vf(t);t1(function(){var i=!1;try{i=JS()}finally{if(ya=!1,t=n-Vf(),!r1())return;i?r.run(1e3):t>0?r.run(t):r.start()}})}},e.prototype.schedule=function(){this.stop(),this.run()},e.prototype.observe=function(){var t=this,r=function(){return t.observer&&t.observer.observe(document.body,i1)};document.body?r():Sn.addEventListener("DOMContentLoaded",r)},e.prototype.start=function(){var t=this;this.stopped&&(this.stopped=!1,this.observer=new MutationObserver(this.listener),this.observe(),Af.forEach(function(r){return Sn.addEventListener(r,t.listener,!0)}))},e.prototype.stop=function(){var t=this;this.stopped||(this.observer&&this.observer.disconnect(),Af.forEach(function(r){return Sn.removeEventListener(r,t.listener,!0)}),this.stopped=!0)},e}(),Da=new o1,Df=function(e){!ki&&e>0&&Da.start(),ki+=e,!ki&&Da.stop()},a1=function(e){return!os(e)&&!XS(e)&&getComputedStyle(e).display==="inline"},s1=function(){function e(t,r){this.target=t,this.observedBox=r||Cn.CONTENT_BOX,this.lastReportedSize={inlineSize:0,blockSize:0}}return e.prototype.isActive=function(){var t=eh(this.target,this.observedBox,!0);return a1(this.target)&&(this.lastReportedSize=t),this.lastReportedSize.inlineSize!==t.inlineSize||this.lastReportedSize.blockSize!==t.blockSize},e}(),l1=function(){function e(t,r){this.activeTargets=[],this.skippedTargets=[],this.observationTargets=[],this.observer=t,this.callback=r}return e}(),Si=new WeakMap,Bf=function(e,t){for(var r=0;r<e.length;r+=1)if(e[r].target===t)return r;return-1},wi=function(){function e(){}return e.connect=function(t,r){var n=new l1(t,r);Si.set(t,n)},e.observe=function(t,r,n){var i=Si.get(t),o=i.observationTargets.length===0;Bf(i.observationTargets,r)<0&&(o&&nr.push(i),i.observationTargets.push(new s1(r,n&&n.box)),Df(1),Da.schedule())},e.unobserve=function(t,r){var n=Si.get(t),i=Bf(n.observationTargets,r),o=n.observationTargets.length===1;i>=0&&(o&&nr.splice(nr.indexOf(n),1),n.observationTargets.splice(i,1),Df(-1))},e.disconnect=function(t){var r=this,n=Si.get(t);n.observationTargets.slice().forEach(function(i){return r.unobserve(t,i.target)}),n.activeTargets.splice(0,n.activeTargets.length)},e}(),c1=function(){function e(t){if(arguments.length===0)throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");if(typeof t!="function")throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");wi.connect(this,t)}return e.prototype.observe=function(t,r){if(arguments.length===0)throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!Ff(t))throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");wi.observe(this,t,r)},e.prototype.unobserve=function(t){if(arguments.length===0)throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!Ff(t))throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");wi.unobserve(this,t)},e.prototype.disconnect=function(){wi.disconnect(this)},e.toString=function(){return"function ResizeObserver () { [polyfill code] }"},e}();function as(){let[e,t]=h.useState(0);return h.useCallback(()=>t(r=>r+1),[])}var vn,gn,u1=class{constructor(){on(this,vn,void 0),on(this,gn,new WeakMap);var e;let t=(e=se.ResizeObserver)!=null?e:c1;_o(this,vn,new t(this.updateResizedElements.bind(this)))}updateResizedElements(e){for(let t of e){let r=He(this,gn).get(t.target);r&&r(t.contentRect)}}observeElementWithCallback(e,t){He(this,vn).observe(e),He(this,gn).set(e,t)}unobserve(e){He(this,vn).unobserve(e),He(this,gn).delete(e)}};vn=new WeakMap;gn=new WeakMap;var Ci=kt()?new u1:void 0;function f1(e){let t=as();N(()=>{let r=e?.current;if(r)return Ci?.observeElementWithCallback(e.current,t),()=>{Ci?.unobserve(r)}},[e,t])}var d1="data-framer-size-compatibility-wrapper";function h1(e){return[...e.firstElementChild&&e.firstElementChild.hasAttribute(d1)?e.firstElementChild.children:e.children].filter(nh).map(ih)}function nh(e){return e instanceof HTMLBaseElement||e instanceof HTMLHeadElement||e instanceof HTMLLinkElement||e instanceof HTMLMetaElement||e instanceof HTMLScriptElement||e instanceof HTMLStyleElement||e instanceof HTMLTitleElement?!1:e instanceof HTMLElement||e instanceof SVGElement}function ih(e){if(!(e instanceof HTMLElement)||e.children.length===0||e.style.display!=="contents")return e;let t=[...e.children].find(nh);return t?ih(t):e}function In(e,t,r=()=>[],n={}){let{id:i,visible:o,_needsMeasure:a}=e,{skipHook:s=!1}=n,l=Boolean(M(ur)),c=Q.current()==="CANVAS";Tt(()=>{!c||l||s||t.current&&i&&o&&a&&sr.queueMeasureRequest(is(i),t.current,r(t.current))})}function oh(e){let t=e.closest("[data-framer-component-container]");t&&sr.queueMeasureRequest(is(t.id),t,h1(t))}function Ba(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function zf(e){return Ba(e,"equals")?typeof e.equals=="function":!1}function za(e,t,r){let n=Array.isArray,i=Object.keys;if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){let o=n(e),a=n(t),s,l;if(o&&a){if(l=e.length,l!==t.length)return!1;for(s=l;s--!==0;)if(!r&&e[s]!==t[s]||r&&!za(e[s],t[s],!0))return!1;return!0}if(o!==a)return!1;let c=e instanceof Date,u=t instanceof Date;if(c!==u)return!1;if(c&&u)return e.getTime()===t.getTime();let f=e instanceof RegExp,d=t instanceof RegExp;if(f!==d)return!1;if(f&&d)return e.toString()===t.toString();if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let p of e)if(!t.has(p))return!1;return!0}if(zf(e)&&zf(t))return e.equals(t);let m=i(e);if(l=m.length,l!==i(t).length)return!1;for(let p of m){if(!Ba(t,p))return!1;if(!(p==="_owner"&&Ba(e,"$$typeof")&&e.$$typeof)&&(!r&&e[p]!==t[p]||r&&!za(e[p],t[p],!0)))return!1}return!0}return e!==e&&t!==t}function ss(e,t,r=!0){try{return za(e,t,r)}catch(n){if(n instanceof Error&&n.message.match(/stack|recursion/i))return console.warn("Warning: isEqual does not handle circular references.",n.name,n.message),!1;throw n}}var ah="0.000001px",xa=` translateZ(${ah})`,sh=RS()||rs()||OS();function m1(e){e.willChange="transform";let t=Q.current()==="CANVAS";sh&&t&&(e.translateZ=ah)}function Vi(e){e.willChange="transform",p1(e,!0)}function p1(e,t){let r=Q.current()==="CANVAS";if(!sh||!r)return;let n=e.transform||"";t?n.includes(xa)||(e.transform=n+xa):e.transform=n.replace(xa,"")}function lh(e,t,r,n=!0){if(!e)return;let i=r||e.style[t],o=()=>{e.style[t]=i};e.style[t]=null,n?Promise.resolve().then(o):setTimeout(o,0)}var Ht=class extends ue{constructor(){super(...arguments),this.layerElement=null,this.setLayerElement=e=>{this.layerElement=e}}static applyWillChange(e,t,r){e.willChangeTransform&&(r?m1(t):Vi(t))}shouldComponentUpdate(e,t){return e._needsMeasure||this.state!==t||!ss(this.props,e)}componentDidUpdate(e){this.props.clip&&this.props.radius===0&&e.radius!==0&&lh(this.layerElement,"overflow","hidden",!1)}};Ht.defaultProps={};function v1(e,t){if(e.size<t)return;let n=Math.round(Math.random());for(let i of e.keys())(++n&1)!==1&&e.delete(i)}function g1(e,t,r,n){let i=t.get(r);if(i)return i;v1(t,e);let o=n(r);return t.set(r,o),o}var ch=e=>{let t=0,r,n;if(e.length===0)return t;for(r=0;r<e.length;r++)n=e.charCodeAt(r),t=(t<<5)-t+n,t|=0;return t},ls={hueRotate:(e,t)=>z.toHslString(z.hueRotate(z(e),t)),setAlpha:(e,t)=>z.toRgbString(z.alpha(z(e),t)),getAlpha:e=>{let t=Qa(e);return t?t.a:1},multiplyAlpha:(e,t)=>z.toRgbString(z.multiplyAlpha(z(e),t)),toHex:e=>z.toHexString(z(e)).toUpperCase(),toRgb:e=>z.toRgb(z(e)),toRgbString:e=>z.toRgbString(z(e)),toHSV:e=>z.toHsv(z(e)),toHSL:e=>z.toHsl(z(e)),toHslString:e=>z.toHslString(z(e)),toHsvString:e=>z.toHsvString(z(e)),hsvToHSLString:e=>z.toHslString(z(pf(e.h,e.s,e.v,e.a))),hsvToString:e=>pf(e.h,e.s,e.v),rgbaToString:e=>z.toRgbString(z(e)),hslToString:e=>z.toRgbString(z(e)),toColorPickerSquare:e=>z.toRgbString(z({h:e,s:1,l:.5,a:1})),isValid:e=>z(e).isValid!==!1,equals:(e,t)=>(typeof e=="string"&&(e=z(e)),typeof t=="string"&&(t=z(t)),z.equal(e,t)),toHexOrRgbaString:e=>{let t=z(e);return t.a!==1?z.toRgbString(t):z.toHexString(t)}},y1=/var\(.+\)/,x1=new Map;function b1(e,t){let r=[e,t];return y1.test(e)?e:g1(1e3,x1,r,()=>ls.multiplyAlpha(e,t))}function Fn(e,t=1){let r;return"stops"in e?r=e.stops:r=[{value:e.start,position:0},{value:e.end,position:1}],t===1?r:r.map(n=>({...n,value:b1(n.value,t)}))}function uh(e,t){let r=0;return Fn(e,t).forEach(n=>{r^=ch(n.value)^n.position}),r}var S1=["stops"];function fh(e){return e&&S1.every(t=>t in e)}var w1=["start","end"];function dh(e){return e&&w1.every(t=>t in e)}var C1=["angle","alpha"],Tn={isLinearGradient:e=>e&&C1.every(t=>t in e)&&(dh(e)||fh(e)),hash:e=>e.angle^uh(e,e.alpha),toCSS:(e,t)=>{let r=Fn(e,e.alpha),n=t!==void 0?t:e.angle,i=r.map(o=>`${o.value} ${o.position*100}%`);return`linear-gradient(${n}deg, ${i.join(", ")})`}},T1=["widthFactor","heightFactor","centerAnchorX","centerAnchorY","alpha"],En={isRadialGradient:e=>e&&T1.every(t=>t in e)&&(dh(e)||fh(e)),hash:e=>e.centerAnchorX^e.centerAnchorY^e.widthFactor^e.heightFactor^uh(e,e.alpha),toCSS:e=>{let{alpha:t,widthFactor:r,heightFactor:n,centerAnchorX:i,centerAnchorY:o}=e,s=Fn(e,t).map(l=>`${l.value} ${l.position*100}%`);return`radial-gradient(${r*100}% ${n*100}% at ${i*100}% ${o*100}%, ${s.join(", ")})`}};function E1({background:e,backgroundColor:t},r){t?typeof t=="string"||Qe(t)?r.backgroundColor=t:z.isColorObject(e)&&(r.backgroundColor=e.initialValue||z.toRgbString(e)):e&&(e=de.get(e,null),typeof e=="string"||Qe(e)?r.background=e:Tn.isLinearGradient(e)?r.background=Tn.toCSS(e):En.isRadialGradient(e)?r.background=En.toCSS(e):z.isColorObject(e)&&(r.backgroundColor=e.initialValue||z.toRgbString(e)))}function J(e,t,r,n){if(n===void 0&&(n=t),e[t]!==void 0){r[n]=e[t];return}}function k1(e){return e?e.left!==void 0&&e.right!==void 0:!1}function R1(e){return e?e.top!==void 0&&e.bottom!==void 0:!1}function _1(e){if(!e)return{};let t={};return e.preserve3d===!0?t.transformStyle="preserve-3d":e.preserve3d===!1&&(t.transformStyle="flat"),e.backfaceVisible===!0?t.backfaceVisibility="visible":e.backfaceVisible===!1&&(t.backfaceVisibility="hidden"),t.backfaceVisibility&&(t.WebkitBackfaceVisibility=t.backfaceVisibility),e.perspective!==void 0&&(t.perspective=t.WebkitPerspective=e.perspective),e.__fromCanvasComponent||(e.center===!0?(t.left="50%",t.top="50%"):e.center==="x"?t.left="50%":e.center==="y"&&(t.top="50%")),J(e,"size",t),J(e,"width",t),J(e,"height",t),J(e,"minWidth",t),J(e,"minHeight",t),J(e,"top",t),J(e,"right",t),J(e,"bottom",t),J(e,"left",t),J(e,"position",t),J(e,"overflow",t),J(e,"opacity",t),(!e._border||!e._border.borderWidth)&&J(e,"border",t),J(e,"borderRadius",t),J(e,"radius",t,"borderRadius"),J(e,"color",t),J(e,"shadow",t,"boxShadow"),J(e,"x",t),J(e,"y",t),J(e,"z",t),J(e,"rotate",t),J(e,"rotateX",t),J(e,"rotateY",t),J(e,"rotateZ",t),J(e,"scale",t),J(e,"scaleX",t),J(e,"scaleY",t),J(e,"skew",t),J(e,"skewX",t),J(e,"skewY",t),J(e,"originX",t),J(e,"originY",t),J(e,"originZ",t),E1(e,t),t}function P1(e){for(let t in e)if(t==="drag"||t.startsWith("while")||typeof e[t]=="function"&&t.startsWith("on")&&!t.includes("Animation"))return!0;return!1}var Hf=["onClick","onDoubleClick","onMouse","onMouseDown","onMouseUp","onTapDown","onTap","onTapUp","onPointer","onPointerDown","onPointerUp","onTouch","onTouchDown","onTouchUp"],I1=new Set([...Hf,...Hf.map(e=>`${e}Capture`)]);function F1(e){if(e.drag)return"grab";for(let t in e)if(I1.has(t))return"pointer"}var ba="overflow";function M1(e){return Nf(e)?!0:e.style?!!Nf(e.style):!1}function Nf(e){return ba in e&&(e[ba]==="scroll"||e[ba]==="auto")}function cs(e){let{left:t,top:r,bottom:n,right:i,width:o,height:a,center:s,_constraints:l,size:c,widthType:u,heightType:f,positionFixed:d,positionAbsolute:m}=e,p=Me(e.minWidth),g=Me(e.minHeight),x=Me(e.maxWidth),v=Me(e.maxHeight);return{top:Me(r),left:Me(t),bottom:Me(n),right:Me(i),width:Me(o),height:Me(a),size:Me(c),center:s,_constraints:l,widthType:u,heightType:f,positionFixed:d,positionAbsolute:m,minWidth:p,minHeight:g,maxWidth:x,maxHeight:v}}var $f={x:0,y:0,width:200,height:200};function L1(e){var t,r;$t();let n=Boolean(M(ur)),{style:i,_initialStyle:o,__fromCanvasComponent:a,size:s}=e,l=cs(e),c=tb(l),u={display:"block",flexShrink:0,userSelect:Q.current()!=="PREVIEW"?"none":void 0};e.__fromCanvasComponent||(u.backgroundColor=e.background===void 0?"rgba(0, 170, 255, 0.3)":void 0);let f=!P1(e)&&!e.__fromCanvasComponent&&!M1(e),d=e.style?!("pointerEvents"in e.style):!0;f&&d&&(u.pointerEvents="none");let p=h.Children.count(e.children)>0&&h.Children.toArray(e.children).every(y=>typeof y=="string"||typeof y=="number")&&{display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"},g=_1(e);s===void 0&&!a&&(k1(g)||(u.width=$f.width),R1(g)||(u.height=$f.height)),l.minWidth!==void 0&&(u.minWidth=l.minWidth),l.minHeight!==void 0&&(u.minHeight=l.minHeight);let x={};Dr(l)&&c&&!hh(e)&&(x={left:c.x,top:c.y,width:c.width,height:c.height,right:void 0,bottom:void 0}),Object.assign(u,p,o,g,x,i),Object.assign(u,{overflowX:(t=u.overflowX)!=null?t:u.overflow,overflowY:(r=u.overflowY)!=null?r:u.overflow,overflow:void 0}),Ht.applyWillChange(e,u,!0);let v=u;u.transform||(v={x:0,y:0,...u});let b=Q.current()==="CANVAS";return e.positionSticky?(!b||n)&&(v.position="sticky",v.willChange="transform",v.zIndex=1,v.top=e.positionStickyTop,v.right=e.positionStickyRight,v.bottom=e.positionStickyBottom,v.left=e.positionStickyLeft):b&&(e.positionFixed||e.positionAbsolute)&&(v.position="absolute"),"rotate"in v&&v.rotate===void 0&&delete v.rotate,[v,c]}var O1=new Set(["width","height","opacity","overflow","radius","background","color","x","y","z","rotate","rotateX","rotateY","rotateZ","scale","scaleX","scaleY","skew","skewX","skewY","originX","originY","originZ"]);function A1(e){let t={};for(let r in e)(br(r)||cS(r))&&!O1.has(r)?t[r]=e[r]:(r==="positionTransition"||r==="layoutTransition")&&(t.layout=!0,typeof e[r]!="boolean"&&!e.transition&&(t.transition=e[r]));return t}function V1(e){return"data-framer-name"in e}var D1=Ie(function(t,r){var n,i;let{name:o,center:a,border:s,_border:l,__portal:c}=t,{props:u,children:f}=qd(t),d=A1(u),m=Rt(t),p=F1(t),g=O(null),x=r??g,v={"data-framer-component-type":"Frame","data-framer-cursor":p,"data-framer-highlight":p==="pointer"?!0:void 0,"data-layoutid":m};!V1(t)&&o&&(v["data-framer-name"]=o);let[b,y]=L1(u),S=cs(u),C=hh(S);a&&!(y&&!C&&Dr(S))?(d.transformTemplate||(d.transformTemplate=Pn(a)),Object.assign(v,ns(a))):d.transformTemplate||(d.transformTemplate=void 0),In(t,x);let w=SS(t),T=Boolean(M(ur)),E=B1(u,S,y,T),R=Ud(h.createElement(h.Fragment,null,w?h.createElement(Kd,{alt:(n=t.alt)!=null?n:"",image:w,containerSize:y??void 0,nodeId:t.id&&is(t.id),layoutId:m}):null,f,h.createElement(CS,{...l,border:s,layoutId:m})),E),_=ke[(i=t.as)!=null?i:"div"];return h.createElement(_,{...v,...d,layoutId:m,style:b,ref:x,transformValues:$S},R,c)}),pt=Ie(function(t,r){aS.default.env.NODE_ENV!=="production"&&se.perf&&se.perf.nodeRender();let{visible:n=!0}=t;return n?h.createElement(D1,{...t,ref:r}):null});function B1(e,t,r,n){if(n)return r?{width:r.width,height:r.height}:1;let{_usesDOMRect:i}=e,{widthType:o=0,heightType:a=0,width:s,height:l}=t;return r&&!i?r:o===0&&a===0&&typeof s=="number"&&typeof l=="number"?{width:s,height:l}:i||e.positionFixed||e.positionAbsolute?2:0}function hh({width:e,height:t}){return e==="auto"||e==="min-content"||t==="auto"||t==="min-content"}function mh({title:e="",description:t="Click and drag the connector to any frame on the canvas \u2192",children:r,size:n,hide:i,insideUserCodeComponent:o=!1}){let{target:a}=Or,s=h.Children.count(r);return o&&s===0?h.createElement(pt,{...n,"data-name":"placeholder"}):a!=="CANVAS"||i||s!==0?null:h.createElement(pt,{key:"empty-state",className:"framerInternalUI-canvasPlaceholder",top:0,left:0,bottom:0,right:0,style:{position:"absolute"}},h.createElement("div",{style:{display:"flex",alignItems:"center",lineHeight:"1.4",height:"100%",width:"100%"}},h.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",width:"100%",WebkitMaskImage:`linear-gradient(90deg, black, black calc(100% - 12px * ${z1}), transparent)`}},h.createElement(H1,null,e),h.createElement(N1,null,t))))}var z1="var(--framerInternalCanvas-canvasPlaceholderContentScaleFactor, 1)";function H1({children:e}){return h.createElement("span",{style:{display:"flex",flexDirection:"column",textAlign:"center",flexGrow:1,flexShrink:0,fontWeight:600,marginBottom:"5px"}},e)}function N1({children:e}){return h.createElement("span",{style:{display:"flex",flexDirection:"column",textAlign:"center",flexGrow:1,flexShrink:0,fontWeight:400,maxWidth:"200px"}},e)}var I_=be(Li(),1);var $1=class{constructor(){this.warning=()=>{Za("The Navigator API is only available inside of Framer: https://www.framer.com/")},this.goBack=()=>this.warning(),this.instant=()=>this.warning(),this.fade=()=>this.warning(),this.push=()=>this.warning(),this.modal=()=>this.warning(),this.overlay=()=>this.warning(),this.flip=()=>this.warning(),this.customTransition=()=>this.warning(),this.magicMotion=()=>this.warning()}},U1=new $1,ph=fe(U1);var O_=be(Li(),1);var V_=be(Li(),1),Uf=(e,t,r)=>{let n=Math.min(t,r),i=Math.max(t,r);return e<n&&(e=n),e>i&&(e=i),e},B_=h.createContext({dragging:!1});var W1={onMouseEnter:"mouseenter",onMouseLeave:"mouseleave"},z_=Object.keys(W1);var H_=be(gt(),1),Wf=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),Sa=(()=>{function e(t={},r=!1,n=!0){let i={};i[Ct]={makeAnimatables:r,observeAnimatables:n,observers:new Ad,reset(){for(let a in o)Wf(o,a)&&(o[a]=Wf(t,a)?t[a]:void 0)},transactions:new Set};let o=new Proxy(i,G1);return Object.assign(o,t),o}return e.resetObject=t=>t[Ct].reset(),e.addObserver=(t,r)=>t[Ct].observers.add(r),e})(),j1=class{constructor(){this.set=(e,t,r,n)=>{if(t===Ct)return!1;let i=e[Ct],o,a;if(Ae(r)?(o=r,a=o.get()):a=r,i.makeAnimatables&&typeof r!="function"&&typeof r!="object"&&!o&&(o=de(r)),i.observeAnimatables&&o){let c=i.transactions;o.onUpdate({update:(u,f)=>{f&&c.add(f),i.observers.notify({value:n},f)},finish:u=>{c.delete(u)&&i.observers.finishTransaction(u)}})}let s=!1,l=!0;if(e[t]!==void 0){Ae(e[t])?(l=e[t].get()!==a,e[t].set(a)):(l=e[t]!==a,e[t]=a);let c=a!==null&&typeof a=="object";(Array.isArray(a)||c)&&(l=!0),s=!0}else o&&(r=o),s=Reflect.set(e,t,r);return l&&i.observers.notify({value:n}),s},this.get=(e,t,r)=>{if(t===Ct)return e[t];let n=Reflect.get(e,t,r);return typeof n=="function"?n.bind(r):n}}deleteProperty(e,t){let r=Reflect.deleteProperty(e,t);return e[Ct].observers.notify({value:e}),r}ownKeys(e){let t=Reflect.ownKeys(e),r=t.indexOf(Ct);return r!==-1&&t.splice(r,1),t}getOwnPropertyDescriptor(e,t){if(t!==Ct)return Reflect.getOwnPropertyDescriptor(e,t)}},G1=new j1,Ct=Symbol("private");var X1="opacity";function Y1(e){return X1 in e}function K1(e,t){if(!Y1(e))return;let r=de.getNumber(e.opacity);r!==1&&(t.opacity=r)}function q1(e){let t=[];if(e&&e.length){let r=e.map(n=>`drop-shadow(${n.x}px ${n.y}px ${n.blur}px ${n.color})`);t.push(...r)}return t}function us(e,t){if(!e.shadows||e.shadows.length===0)return;let r=e.shadows.map(n=>`${n.x}px ${n.y}px ${n.blur}px ${n.color}`).join(", ");r&&(t.textShadow=r)}function Z1(e,t){let r=[];D(e.brightness)&&r.push(`brightness(${e.brightness/100})`),D(e.contrast)&&r.push(`contrast(${e.contrast/100})`),D(e.grayscale)&&r.push(`grayscale(${e.grayscale/100})`),D(e.hueRotate)&&r.push(`hue-rotate(${e.hueRotate}deg)`),D(e.invert)&&r.push(`invert(${e.invert/100})`),D(e.saturate)&&r.push(`saturate(${e.saturate/100})`),D(e.sepia)&&r.push(`sepia(${e.sepia/100})`),D(e.blur)&&r.push(`blur(${e.blur}px)`),e.dropShadows&&r.push(...q1(e.dropShadows)),r.length!==0&&(t.filter=t.WebkitFilter=r.join(" "))}function J1(e,t){D(e.backgroundBlur)&&(t.backdropFilter=t.WebkitBackdropFilter=`blur(${e.backgroundBlur}px)`)}function Di(e,t){J1(e,t),Z1(e,t)}var{getNumber:W_}=de;var fs="__LAYOUT_TREE_ROOT",vh=h.createContext({schedulePromoteTree:()=>{},scheduleProjectionDidUpdate:()=>{},initLead:()=>{}}),Q1=class extends ue{constructor(){super(...arguments),this.shouldAnimate=!1,this.scheduledPromotion=!1,this.scheduledDidUpdate=!1,this.scheduleProjectionDidUpdate=()=>{this.scheduledDidUpdate=!0},this.schedulePromoteTree=(e,t,r)=>{this.follow=this.lead,this.shouldAnimate=r,this.lead=e,this.transition=t,this.scheduledPromotion=!0},this.initLead=(e,t)=>{this.follow=this.lead,this.lead=e,this.follow&&t&&(this.follow.layoutMaybeMutated=!0)},this.sharedLayoutContext={schedulePromoteTree:this.schedulePromoteTree,scheduleProjectionDidUpdate:this.scheduleProjectionDidUpdate,initLead:this.initLead}}getSnapshotBeforeUpdate(){var e;if(!this.scheduledPromotion||!this.lead||!this.follow)return null;let t=!!((e=this.lead)!=null&&e.layoutMaybeMutated)&&!this.shouldAnimate;return this.lead.projectionNodes.forEach(r=>{var n;r?.promote({needsReset:t,transition:this.shouldAnimate?this.transition:void 0,preserveFollowOpacity:r.options.layoutId===fs&&!((n=this.follow)!=null&&n.isExiting)})}),this.shouldAnimate?this.follow.layoutMaybeMutated=!0:this.scheduleProjectionDidUpdate(),this.lead.layoutMaybeMutated=!1,this.transition=void 0,this.scheduledPromotion=!1,null}componentDidUpdate(){var e,t;if(!this.lead)return null;this.scheduledDidUpdate&&((t=(e=this.lead.rootProjectionNode)==null?void 0:e.root)==null||t.didUpdate(),this.scheduledDidUpdate=!1)}render(){return h.createElement(vh.Provider,{value:this.sharedLayoutContext},this.props.children)}},ew={width:"100%",height:"100%",backgroundColor:"none"};function tw(e){return h.createElement(ke.div,{layoutId:fs,style:ew},e.children)}var Bt,yn,rw=class{constructor(e){on(this,Bt,void 0),on(this,yn,new WeakMap),document&&_o(this,Bt,new IntersectionObserver(this.resizeObserverCallback.bind(this),e))}resizeObserverCallback(e,t){for(let r of e){let n=He(this,yn).get(r.target);n&&n([r],t)}}observeElementWithCallback(e,t){He(this,Bt)&&(He(this,Bt).observe(e),He(this,yn).set(e,t))}unobserve(e){He(this,Bt)&&(He(this,Bt).unobserve(e),He(this,yn).delete(e))}get root(){var e;return(e=He(this,Bt))==null?void 0:e.root}};Bt=new WeakMap;yn=new WeakMap;var nw=h.createContext(new Map);function iw(e,t,r){let n=vt(()=>`${r.rootMargin}`),i=h.useContext(nw);h.useEffect(()=>{var o;if(typeof IntersectionObserver>"u")return;let a=e.current;if(!a)return;let s=i.get(n);if(!s||s.root!==((o=r.root)==null?void 0:o.current)){let{root:l,...c}=r;s=new rw({...c,root:l?.current}),i.set(n,s)}return s.observeElementWithCallback(a,t),()=>s?.unobserve(a)},[])}var ow=new Array(100).fill(void 0).map((e,t)=>t*.01),aw=h.createContext(null);function sw(e,t,r){let n=h.useRef({isInView:!1,hasAnimatedOnce:!1}),{animateOnce:i,threshold:o,rootMargin:a="0px 0px 0px 0px"}=t,s=h.useCallback(([l])=>{var c;if(!l)return;let{isInView:u,hasAnimatedOnce:f}=n.current,d=cw(l,(c=o?.y)!=null?c:0);if(d&&!u){if(i&&f)return;n.current.hasAnimatedOnce=!0,n.current.isInView=!0,r(!0);return}if(!d&&u){if(n.current.isInView=!1,i)return;r(!1);return}},[i,o?.y,r]);iw(e,s,{threshold:ow,rootMargin:a})}function lw(e,t){return t.height===0?0:e.height/Math.min(t.height,se.innerHeight)}function cw({boundingClientRect:e,intersectionRect:t,isIntersecting:r},n){return e.height===0?r:r&&lw(t,e)>=n}var uw=class extends ue{constructor(){super(...arguments),this.projectionNodes=new Map,this.shouldPreserveFollowOpacity=e=>e.options.layoutId===fs&&!this.props.isExiting,this.switchLayoutGroupContext={register:e=>this.addChild(e),deregister:e=>this.removeChild(e),transition:this.props.isLead!==void 0&&this.props.animatesLayout?this.props.transition:void 0,shouldPreserveFollowOpacity:this.shouldPreserveFollowOpacity}}componentDidMount(){this.props.isLead&&this.props.sharedLayoutContext.initLead(this,!!this.props.animatesLayout)}shouldComponentUpdate(e){let{isLead:t,isExiting:r,isOverlayed:n,animatesLayout:i,transition:o,sharedLayoutContext:a}=e;if(this.isExiting=r,t===void 0)return!0;let s=!this.props.isLead&&!!t,l=this.props.isExiting&&!r,c=s||l,u=!!this.props.isLead&&!t,f=this.props.isOverlayed!==n;return(c||u)&&this.projectionNodes.forEach(d=>d?.willUpdate()),c?a.schedulePromoteTree(this,o,!!i):f&&a.scheduleProjectionDidUpdate(),!!c&&!!i}addChild(e){let t=e.options.layoutId;t&&(this.projectionNodes.set(t,e),this.setRootChild(e))}setRootChild(e){if(!this.rootProjectionNode)return this.rootProjectionNode=e;this.rootProjectionNode=this.rootProjectionNode.depth<e.depth?this.rootProjectionNode:e}removeChild(e){let t=e.options.layoutId;t&&this.projectionNodes.delete(t)}render(){return h.createElement(ti.Provider,{value:this.switchLayoutGroupContext},this.props.children)}},fw=e=>{let t=h.useContext(vh);return h.createElement(uw,{...e,sharedLayoutContext:t})},gh=h.createContext(!0);function yh(){return M(gh)}function dw(){return new Map}function hw(){return vt(dw)}var xh=fe({register:()=>{},deregister:()=>{}}),mw=({isCurrent:e,isOverlayed:t,children:r})=>{let n=hw(),i=oe(s=>{if(n.has(s)){console.warn("NavigationTargetWrapper: already registered");return}n.set(s,void 0)},[n]),o=oe(s=>{let l=n.get(s);l?.(),n.delete(s)},[n]),a=O({register:i,deregister:o}).current;return N(()=>(n.forEach((s,l)=>{let c=l(e,t);n.set(l,Gd(c)?c:void 0)}),()=>{n.forEach((s,l)=>{s&&(s(),n.set(l,void 0))})}),[e,t,n]),h.createElement(xh.Provider,{value:a},r)};function pw(e,t=[]){let{register:r,deregister:n}=M(xh);N(()=>{if(e)return r(e),()=>n(e)},[r,n,...t])}var wa=h.memo(function({isLayeredContainer:t,isCurrent:r,isPrevious:n,isOverlayed:i=!1,visible:o,transitionProps:a,children:s,backdropColor:l,onTapBackdrop:c,backfaceVisible:u,exitBackfaceVisible:f,animation:d,exitAnimation:m,instant:p,initialProps:g,exitProps:x,position:v={top:0,right:0,bottom:0,left:0},withMagicMotion:b,index:y,areMagicMotionLayersPresent:S,id:C,isInitial:w}){let T=Gu(),E=M(Sr),{persistLayoutIdCache:R}=M(Ar),_=O({wasCurrent:void 0,wasPrevious:!1,wasBeingRemoved:!1,wasReset:!0,origins:jf({},g,a)}),F=O(null),B=E!==null&&!E.isPresent;r&&_.current.wasCurrent===void 0&&R(),N(()=>{if(t||!T)return;if(B){_.current={..._.current,wasBeingRemoved:B};return}let{wasPrevious:Y,wasCurrent:$}=_.current,ie=r&&!$||!B&&_.current.wasBeingRemoved&&r,Te=n&&!Y,q=jf(_.current.origins,g,a),le=_.current.wasReset;ie||Te?(T.stop(),T.start({zIndex:y,...q,...a}),le=!1):le===!1&&(T.stop(),T.set({zIndex:y,...rr,opacity:0}),le=!0),_.current={wasCurrent:!!r,wasPrevious:!!n,wasBeingRemoved:!1,wasReset:le,origins:q}},[r,n,B]);let L=p?{type:!1}:"velocity"in d?{...d,velocity:0}:d,P=p?{type:!1}:m||d,I={...v};(I.left===void 0||I.right===void 0)&&(I.width="auto"),(I.top===void 0||I.bottom===void 0)&&(I.height="auto");let k=(Gf(a)||Gf(g))&&(t||r||n)?1200:void 0,A={...rr,..._.current.origins},X=t?{initial:{...A,...g},animate:{...A,...a,transition:L},exit:{...A,...x,transition:d}}:{animate:T,exit:{...A,...x,transition:P}},j=!(B||S===!1),V=!!r&&j,Z=r&&w;return h.createElement(pt,{"data-framer-component-type":"NavigationContainerWrapper",width:"100%",height:"100%",style:{position:"absolute",transformStyle:"flat",backgroundColor:"transparent",overflow:"hidden",zIndex:t||B||r&&b?y:void 0,pointerEvents:void 0,visibility:o?"visible":"hidden",perspective:k}},t&&h.createElement(pt,{width:"100%",height:"100%","data-framer-component-type":"NavigationContainerBackdrop",transition:d,initial:{opacity:p&&o?1:0},animate:{opacity:1},exit:{opacity:0},backgroundColor:l||"transparent",onTap:B?void 0:c}),h.createElement(pt,{...I,...X,transition:{default:L,originX:{type:!1},originY:{type:!1},originZ:{type:!1}},backgroundColor:"transparent",backfaceVisible:B?f:u,"data-framer-component-type":"NavigationContainer","data-framer-is-current-navigation-target":!!r,style:{pointerEvents:void 0,opacity:Z||t||r&&b?1:0},"data-is-present":j?void 0:!1,ref:F},h.createElement(aw.Provider,{value:F},h.createElement(gh.Provider,{value:V},h.createElement(mw,{isCurrent:V,isOverlayed:i},h.createElement(fw,{isLead:r,animatesLayout:!!b,transition:L,isExiting:!j,isOverlayed:i,id:C},s))))))},vw);function vw(e,t){return!(t.isCurrent===void 0||e.isCurrent!==t.isCurrent||e.isPrevious!==t.isPrevious||t.isCurrent&&e.isOverlayed!==t.isOverlayed)}function jf(e,t,r){let n={...e};return t&&(D(t.originX)&&(n.originX=t.originX),D(t.originY)&&(n.originY=t.originY),D(t.originZ)&&(n.originZ=t.originZ)),r&&(D(r.originX)&&(n.originX=r.originX),D(r.originY)&&(n.originY=r.originY),D(r.originZ)&&(n.originZ=r.originZ)),n}function Gf(e){var t,r,n;if(!e||!("rotateX"in e||"rotateY"in e||"z"in e))return!1;let o=e.rotateX!==0||e.rotateY!==0||e.z!==0,a=((t=e?.transition)==null?void 0:t.rotateX.from)!==0||((r=e?.transition)==null?void 0:r.rotateY.from)!==0||((n=e?.transition)==null?void 0:n.z.from)!==0;return o||a}var rr={x:0,y:0,z:0,rotate:0,rotateX:0,rotateY:0,rotateZ:0,scale:1,scaleX:1,scaleY:1,scaleZ:1,skew:0,skewX:0,skewY:0,originX:.5,originY:.5,originZ:0,opacity:1},we={Fade:{exit:{opacity:0},enter:{opacity:0}},PushLeft:{exit:{x:"-30%"},enter:{x:"100%"}},PushRight:{exit:{x:"30%"},enter:{x:"-100%"}},PushUp:{exit:{y:"-30%"},enter:{y:"100%"}},PushDown:{exit:{y:"30%"},enter:{y:"-100%"}},Instant:{animation:{type:!1},enter:{opacity:0}},Modal:{overCurrentContext:!0,goBackOnTapOutside:!0,position:{center:!0},enter:{opacity:0,scale:1.2}},OverlayLeft:{overCurrentContext:!0,goBackOnTapOutside:!0,position:{right:0,top:0,bottom:0},enter:{x:"100%"}},OverlayRight:{overCurrentContext:!0,goBackOnTapOutside:!0,position:{left:0,top:0,bottom:0},enter:{x:"-100%"}},OverlayUp:{overCurrentContext:!0,goBackOnTapOutside:!0,position:{bottom:0,left:0,right:0},enter:{y:"100%"}},OverlayDown:{overCurrentContext:!0,goBackOnTapOutside:!0,position:{top:0,left:0,right:0},enter:{y:"-100%"}},FlipLeft:{backfaceVisible:!1,exit:{rotateY:-180},enter:{rotateY:180}},FlipRight:{backfaceVisible:!1,exit:{rotateY:180},enter:{rotateY:-180}},FlipUp:{backfaceVisible:!1,exit:{rotateX:180},enter:{rotateX:-180}},FlipDown:{backfaceVisible:!1,exit:{rotateX:-180},enter:{rotateX:180}},MagicMotion:{withMagicMotion:!0}};function gw(e){switch(e&&e.appearsFrom?e.appearsFrom:"right"){case"right":return we.PushLeft;case"left":return we.PushRight;case"bottom":return we.PushUp;case"top":return we.PushDown}}function yw(e){switch(e&&e.appearsFrom?e.appearsFrom:"bottom"){case"right":return we.OverlayLeft;case"left":return we.OverlayRight;case"bottom":return we.OverlayUp;case"top":return we.OverlayDown}}function xw(e){switch(e&&e.appearsFrom?e.appearsFrom:"bottom"){case"right":return we.FlipLeft;case"left":return we.FlipRight;case"bottom":return we.FlipUp;case"top":return we.FlipDown}}var bw=()=>({current:-1,previous:-1,currentOverlay:-1,previousOverlay:-1,visualIndex:0,overlayItemId:0,historyItemId:0,history:[],overlayStack:[],containers:{},containerIndex:{},containerVisualIndex:{},containerIsRemoved:{},transitionForContainer:{},previousTransition:null});function Xf(e,t){switch(t.type){case"addOverlay":return ww(e,t.transition,t.component);case"removeOverlay":return Cw(e);case"add":return bh(e,t.key,t.transition,t.component);case"remove":return Sh(e);case"update":return Sw(e,t.key,t.component);case"back":return Tw(e);case"forward":return Ew(e);default:return}}function Sw(e,t,r){return{...e,containers:{...e.containers,[t]:r}}}function ww(e,t,r){let n=e.overlayStack[e.currentOverlay];if(n&&n.component===r)return;let i=e.overlayItemId+1,o=[...e.overlayStack,{key:`stack-${i}`,component:r,transition:t}];return{...e,overlayStack:o,overlayItemId:i,currentOverlay:Math.max(0,Math.min(e.currentOverlay+1,o.length-1)),previousOverlay:e.currentOverlay}}function Cw(e){return{...e,overlayStack:[],currentOverlay:-1,previousOverlay:e.currentOverlay}}function bh(e,t,r,n){e.containers[t]||(e.containers[t]=n),e.history=e.history.slice(0,e.current+1),e.visualIndex=Math.max(e.history.length,0);let i=e.history[e.history.length-1],o=i&&i.key===t;if(e.overlayStack=[],o&&e.currentOverlay>-1)return{...e,currentOverlay:-1,previousOverlay:e.currentOverlay};if(o)return;let a=e.containerVisualIndex[t],s=e.containerIsRemoved[t],l=i?.key&&r.withMagicMotion?Pw(t,a,s,e.history):!0;e.history.push({key:t,transition:r,visualIndex:l?Math.max(e.visualIndex,0):e.containerVisualIndex[t]});let c=e.current+1,u=e.current;for(let p in e.containerIndex)e.containerIndex[p]===c&&(e.containerIndex[p]=_w(p,e.history));e.containerIndex[t]=c;let{containerVisualIndex:f,containerIsRemoved:d}=kw(e,t,l),m=wh(c,u,e.history,e.containerIndex,e.transitionForContainer);return{...e,current:c,previous:u,containerVisualIndex:f,containerIsRemoved:d,transitionForContainer:m,previousTransition:null,currentOverlay:-1,historyItemId:e.historyItemId+1,previousOverlay:e.currentOverlay}}function Tw(e){let t={...e.containers},r=Sh(e);if(r)return r.containers=t,r}function Ew(e){let t=e.history[e.current+1];if(!t)return;let{key:r,transition:n,component:i}=t,o=[...e.history],a=bh(e,r,n,i);if(a)return a.history=o,a}function Sh(e){let t=[...e.history.slice(0,e.current+1)];if(t.length===1)return;let r=t.pop();if(!r)return;let n=t[t.length-1];Ce(n,"The navigation history must have at least one component"),e.containerIndex[n.key]=t.length-1,t.every(d=>d.key!==r.key)&&delete e.containers[r.key];let o=e.current-1,a=e.current,{containerIsRemoved:s,containerVisualIndex:l,previousTransition:c,visualIndex:u}=Rw(e,n,r),f=wh(o,a,e.history,e.containerIndex,e.transitionForContainer);return{...e,current:o,previous:a,containerIsRemoved:s,containerVisualIndex:l,previousTransition:c,visualIndex:u,transitionForContainer:f}}function kw(e,t,r){let n={containerVisualIndex:{...e.containerVisualIndex},containerIsRemoved:{...e.containerIsRemoved}};if(r)n.containerVisualIndex[t]=e.history.length-1,n.containerIsRemoved[t]=!1;else{let i=e.containerVisualIndex[t];for(let[o,a]of Object.entries(e.containerVisualIndex))i!==void 0&&a>i&&(n.containerIsRemoved[o]=!0)}return n}function Rw(e,t,r){let n=[t.key,r.key],i=e.history[e.history.length-2],o=e.previousTransition===null?null:{...e.previousTransition},a={containerIsRemoved:{...e.containerIsRemoved},containerVisualIndex:{...e.containerVisualIndex},previousTransition:o,visualIndex:e.visualIndex};i&&n.push(i.key);let s=e.containerVisualIndex[t.key],l=e.containerVisualIndex[r.key],c=s!==void 0&&l!==void 0&&s<=l||t.visualIndex!==void 0&&t.visualIndex<e.history.length-1,u=t.visualIndex;return c?(a.containerIsRemoved[r.key]=!0,a.containerVisualIndex[t.key]=u!==void 0?u:e.history.length-1):(a.visualIndex=e.visualIndex+1,a.containerVisualIndex[t.key]=e.visualIndex+1),r.transition.withMagicMotion&&(a.previousTransition=r.transition||null),e.containerIsRemoved[t.key]=!1,a}function _w(e,t){var r;for(let n=t.length;n>t.length;n--)if(((r=t[n])==null?void 0:r.key)===e)return n;return-1}function wh(e,t,r,n,i){let o={...i};for(let[a,s]of Object.entries(n)){let l=Iw(s,{current:e,previous:t,history:r});l&&(o[a]=l)}return o}function Pw(e,t,r,n){return r||t===void 0?!0:t===0?!1:n.slice(t,n.length).findIndex(a=>a.key===e)>-1?!0:!(n.slice(0,t-1).findIndex(a=>a.key===e)>-1)}function Iw(e,t){let{current:r,previous:n,history:i}=t;if(!(e!==r&&e!==n)){if(e===r&&r>n){let o=i[e];return Ti("enter",o?.transition.enter,o?.transition.animation)}if(e===n&&r>n){let o=i[e+1];return Ti("exit",o?.transition.exit,o?.transition.animation)}if(e===r&&r<n){let o=i[e+1];return Ti("enter",o?.transition.exit,o?.transition.animation)}if(e===n&&r<n){let o=i[e];return Ti("exit",o?.transition.enter,o?.transition.animation)}}}var Fw=Object.keys(rr);function Ti(e,t,r){let n={},i={};return Fw.forEach(o=>{n[o]=rr[o],i[o]={...r,from:rr[o]}}),t&&Object.keys(t).forEach(o=>{if(t[o]===void 0)return;let a=t[o],s=typeof t[o]=="string"?`${rr[o]}%`:rr[o];n[o]=e==="enter"?s:a,i[o]={...r,from:e==="enter"?a:s,velocity:0}}),{...n,transition:{...i}}}var Q_=ph.Consumer,Ch=h.createContext(void 0),eP=Ch.Provider,Th=h.createContext(void 0),ds=class extends ue{constructor(e){var t;super(e),this.lastEventTimeStamp=null,this.state=bw(),this.navigationAction=s=>{if(!this.props.enabled&&this.state.history.length>0)return;let l=Xf(this.state,s);if(!l)return;let{skipLayoutAnimation:c}=this.props,u=l.history[l.current],f=s.type==="add"&&s.transition.withMagicMotion||s.type==="forward"&&u?.transition.withMagicMotion||s.type==="remove"&&!!l.previousTransition,d=()=>{var m;this.setState(l),u?.key&&((m=this.context)==null||m.call(this,u.key))};c&&!f?c(d):d()},this.goBack=()=>{var s;if(!this.isSameEventTransition())return this.lastEventTimeStamp=((s=globalThis.event)==null?void 0:s.timeStamp)||null,this.state.currentOverlay!==-1?this.navigationAction({type:"removeOverlay"}):this.navigationAction({type:"remove"})};let r=this.props.children;if(!r||!Mr(r)||!Fr(r))return;let n={...we.Instant},o={type:"add",key:((t=r.key)==null?void 0:t.toString())||`stack-${this.state.historyItemId+1}`,transition:n,component:r},a=Xf(this.state,o);a&&(this.state=a)}componentDidMount(){var e;$t();let t=this.state.history[this.state.current];t&&((e=this.context)==null||e.call(this,t.key))}UNSAFE_componentWillReceiveProps(e){var t;let r=e.children;if(!Mr(r)||!Fr(r))return;let n=(t=r.key)==null?void 0:t.toString();n&&(this.state.history.length===0?this.transition(r,we.Instant):this.navigationAction({type:"update",key:n,component:r}))}componentWillUnmount(){var e,t;(t=(e=this.props).resetProjection)==null||t.call(e)}getStackState(e){let{current:t,previous:r,currentOverlay:n,previousOverlay:i}=this.state;return e.overCurrentContext?{current:n,previous:i,history:this.state.overlayStack}:{current:t,previous:r,history:this.state.history}}isSameEventTransition(){return globalThis.event?this.lastEventTimeStamp===globalThis.event.timeStamp:!1}transition(e,t,r){var n,i;if(this.isSameEventTransition()||(this.lastEventTimeStamp=((n=globalThis.event)==null?void 0:n.timeStamp)||null,!e||!Mr(e)||!Fr(e)))return;let o={...t,...r};if(!!o.overCurrentContext)return this.navigationAction({type:"addOverlay",transition:o,component:e});let s=((i=e.key)==null?void 0:i.toString())||`stack-${this.state.historyItemId+1}`;this.navigationAction({type:"add",key:s,transition:o,component:e})}instant(e){this.transition(e,we.Instant,void 0)}fade(e,t){this.transition(e,we.Fade,t)}push(e,t){this.transition(e,gw(t),t)}modal(e,t){this.transition(e,we.Modal,t)}overlay(e,t){this.transition(e,yw(t),t)}flip(e,t){this.transition(e,xw(t),t)}magicMotion(e,t){this.transition(e,we.MagicMotion,t)}customTransition(e,t){this.transition(e,t)}render(){var e,t,r,n,i;let o=this.getStackState({overCurrentContext:!1}),a=this.getStackState({overCurrentContext:!0}),s=Mw(a),l=a.current>-1,c=this.state.history.length===1,u=[];for(let[d,m]of Object.entries(this.state.containers)){let p=this.state.containerIndex[d];Ce(p!==void 0,"Container's index must be registered");let g=this.state.containerVisualIndex[d];Ce(g!==void 0,"Container's visual index must be registered");let x=this.state.containerIsRemoved[d],v=this.state.history[p],b=this.state.transitionForContainer[d],y=p===this.state.current,S=p===this.state.previous,C=y?!1:x,w=((e=v?.transition)==null?void 0:e.withMagicMotion)||y&&!!this.state.previousTransition;u.push(h.createElement(wa,{key:d,id:d,index:g,isInitial:c,isCurrent:y,isPrevious:S,isOverlayed:l,visible:y||S,position:(t=v?.transition)==null?void 0:t.position,instant:Kf(p,o),transitionProps:b,animation:Yf(p,o),backfaceVisible:zw(p,o),exitAnimation:(r=v?.transition)==null?void 0:r.animation,exitBackfaceVisible:(n=v?.transition)==null?void 0:n.backfaceVisible,exitProps:(i=v?.transition)==null?void 0:i.enter,withMagicMotion:w,areMagicMotionLayersPresent:C?!1:void 0},h.createElement(tw,null,qf({component:m,transition:v?.transition}))))}let f=this.state.overlayStack.map((d,m)=>h.createElement(wa,{isLayeredContainer:!0,key:d.key,isCurrent:m===this.state.currentOverlay,position:d.transition.position,initialProps:Bw(m,a),transitionProps:Hw(m,a),instant:Kf(m,a,!0),animation:Yf(m,a),exitProps:d.transition.enter,visible:Nw(m,a),backdropColor:Vw(d.transition),backfaceVisible:Dw(m,a),onTapBackdrop:$w(d.transition,this.goBack),index:this.state.current+1+m},qf({component:d.component,transition:d.transition})));return h.createElement(pt,{"data-framer-component-type":"NavigationRoot",top:0,left:0,width:"100%",height:"100%",position:"relative",style:{overflow:"hidden",backgroundColor:"unset",pointerEvents:void 0,...this.props.style}},h.createElement(ph.Provider,{value:this},h.createElement(Th.Provider,{value:c},h.createElement(wa,{isLayeredContainer:!0,position:void 0,initialProps:{},instant:!1,transitionProps:Lw(s),animation:Ow(s),backfaceVisible:Aw(s),visible:!0,backdropColor:void 0,onTapBackdrop:void 0,index:0},h.createElement(Lx,null,h.createElement(Q1,null,h.createElement(aa,{presenceAffectsLayout:!1},u)))),h.createElement(aa,null,f))))}};ds.defaultProps={enabled:!0};ds.contextType=Ch;var Eh={stiffness:500,damping:50,restDelta:1,type:"spring"};function Mw(e){let t,r;return e.current!==-1?t=e.history[e.current]:r=e.history[e.previous],{currentOverlayItem:t,previousOverlayItem:r}}function Lw({currentOverlayItem:e}){return e&&e.transition.exit}function Ow({currentOverlayItem:e,previousOverlayItem:t}){return e&&e.transition.animation?e.transition.animation:t&&t.transition.animation?t.transition.animation:Eh}function Aw({currentOverlayItem:e,previousOverlayItem:t}){return e?e.transition.backfaceVisible:t&&t.transition.backfaceVisible}function Vw(e){if(e.backdropColor)return e.backdropColor;if(e.overCurrentContext)return"rgba(4,4,15,.4)"}function Dw(e,t){let{current:r,history:n}=t;if(e===r){let i=n[e];return i&&i.transition?i.transition.backfaceVisible:!0}else if(e<r){let i=n[e+1];return i&&i.transition?i.transition.backfaceVisible:!0}else{let i=n[e];return i&&i.transition?i.transition.backfaceVisible:!0}}function Bw(e,t){let r=t.history[e];if(r)return r.transition.enter}function zw(e,t){var r,n,i,o;let{current:a,previous:s,history:l}=t;return e===s&&a>s||e===a&&a<s?(n=(r=l[e+1])==null?void 0:r.transition)==null?void 0:n.backfaceVisible:(o=(i=l[e])==null?void 0:i.transition)==null?void 0:o.backfaceVisible}function Hw(e,t){let{current:r,history:n}=t;if(e!==r)if(e<r){let i=n[e+1];if(i&&i.transition)return i.transition.exit}else{let i=n[e];if(i&&i.transition)return i.transition.enter}}function Yf(e,t){let{current:r,previous:n,history:i}=t,o=n>r?n:r;if(e<o){let a=i[e+1];if(a&&a.transition.animation)return a.transition.animation}else if(e!==o){let a=i[e];if(a&&a.transition.animation)return a.transition.animation}else{let a=i[e];if(a?.transition.animation)return a.transition.animation}return Eh}function Kf(e,t,r){let{current:n,previous:i,history:o}=t;return!!(r&&o.length>1||e!==i&&e!==n||n===i)}function Nw(e,t){let{current:r,previous:n}=t;return e>r&&e>n?!1:e===r}function qf(e){return h.Children.map(e.component,r=>{var n,i;if(!Mr(r)||!Fr(r)||!r.props)return r;let o={style:(n=r.props.style)!=null?n:{}},a=(i=e?.transition)==null?void 0:i.position,s=!a||a.left!==void 0&&a.right!==void 0,l=!a||a.top!==void 0&&a.bottom!==void 0,c="style"in r.props?qe(r.props.style):!0;return s&&("width"in r.props&&(o.width="100%"),c&&(o.style.width="100%")),l&&("height"in r.props&&(o.height="100%"),c&&(o.style.height="100%")),h.cloneElement(r,o)})}function $w(e,t){if(e.goBackOnTapOutside!==!1)return t}function Uw(e){let t=Ju(),r=ca();return h.createElement(ds,{...e,resetProjection:t,skipLayoutAnimation:r},e.children)}var tP=be(gt(),1);function Ww(e,t){let r,n=(...o)=>{se.clearTimeout(r),r=se.setTimeout(e,t,...o)},i=()=>{se.clearTimeout(r)};return n.cancel=i,n}function jw(e,{enabled:t,initial:r,prev:n,direction:i,constraints:o,offsetX:a,offsetY:s,onScrollStart:l,onScroll:c,onScrollEnd:u}){let f=O(!1),d=oe(()=>{let p=Gw(a,s),g={point:p,velocity:{x:a.getVelocity(),y:s.getVelocity()},offset:{x:p.x-r.x,y:p.y-r.y},delta:{x:p.x-n.x,y:p.y-n.y}};return n.x=p.x,n.y=p.y,g},[]),m;if(t){let p=function(y){return o.current===null?y:Uf(y,o.current.left,o.current.right)},g=function(y){return o.current===null?y:Uf(y,o.current.top,o.current.bottom)},x=function(y){a.stop(),a.set(p(a.get()-y))},v=function(y){s.stop(),s.set(g(s.get()-y))},b=Ww(()=>{u&&u(d()),f.current=!1},200);m=y=>{if(y.preventDefault(),!f.current){let S=a.get(),C=s.get();r.x=S,r.y=C,n.x=S,n.y=C,l&&l(d()),f.current=!0}switch(i){case"horizontal":x(y.deltaX);break;case"vertical":v(y.deltaY);break;default:x(y.deltaX),v(y.deltaY)}c&&c(d()),b()}}Ku(e,"wheel",m,{passive:!1})}function Gw(e,t){return{x:e.get(),y:t.get()}}function kh(e){let{paddingPerSide:t,paddingTop:r,paddingBottom:n,paddingLeft:i,paddingRight:o}=e;return t!==!1&&(r!==void 0||n!==void 0||i!==void 0||o!==void 0)}function Xw(e){let{padding:t=0,paddingTop:r,paddingBottom:n,paddingLeft:i,paddingRight:o}=e;return kh(e)?{top:r!==void 0?r:t,bottom:n!==void 0?n:t,left:i!==void 0?i:t,right:o!==void 0?o:t}:{top:t,bottom:t,left:t,right:t}}function Yw({top:e,left:t,bottom:r,right:n}){return`${e}px ${n}px ${r}px ${t}px`}function Zf(e,t){e&&(e.style.display="none",e.offsetHeight,e.style.display=t)}var Kw=Boolean(rs()&&ES()<15.4);function qw(e,t,r){if(!Kw)return;let n=h.useRef(!0),i=h.useRef(!1),o=h.useRef(e);return i.current=!1,h.useLayoutEffect(()=>{if(o.current=e,n.current){n.current=!1;return}i.current||(Zf(t.current,r),i.current=!0)},[e,t,o,r]),h.useCallback(()=>{o.current!==e&&(i.current||Zf(t.current,r),i.current=!0)},[e,t])}var sP=(()=>{let e=h.memo(h.forwardRef(function(r,n){var i,o;let{as:a="div",direction:s="vertical",distribution:l="start",alignment:c="center",gap:u=10,wrap:f=!1,useFlexboxGap:d=!0,children:m,style:p,className:g,willChangeTransform:x,__fromCodeComponentNode:v,parentSize:b,__contentWrapperStyle:y,...S}=r,C=Boolean(d||f),w=h.useRef(null),T=qw(u,w,"flex"),E=Qw(s),R=tC(E),_=Qf(l),F=kh(S)||S.padding?Yw(Xw(S)):void 0,B={...p};Ht.applyWillChange({willChangeTransform:x},B,!0),v&&!Dr(cs(S))&&(S.width="100%",S.height="100%",S._constraints={enabled:!0});let L=Rt(r),{children:P,props:I}=qd(S,m),U=(i=S.widthType)!=null?i:S.width==="auto"?2:0,k=(o=S.heightType)!=null?o:S.height==="auto"?2:0,A=Zw(P,s,U,k),X=Jw(A,u,E,_,C,f),j=Ud(X,b??1),V={"data-framer-component-type":"Stack"},Z=S.__fromCanvasComponent;Z&&(V["data-framer-generated"]=!0);let Y=Qf(c),$={display:"flex",flexDirection:E,flexWrap:f?"wrap":"nowrap",justifyContent:_,alignItems:Y,alignContent:Y,padding:F,...y},ie=_h(u,_,f);if(C&&ie){let Te=Rh(_);(Te||s!=="horizontal")&&($["--stack-native-column-gap"]=`${u}px`),(Te||s!=="vertical")&&($["--stack-native-row-gap"]=`${u}px`)}return $.width===void 0&&($.width=U===2?"min-content":"100%"),$.height===void 0&&($.height=k===2?"min-content":"100%"),Z&&(p?.width&&($.width=p?.width),p?.height&&($.height=p?.height)),h.createElement(pt,{as:a,background:Z?void 0:"none",...I,layoutId:L,ref:rC(n,w),...V,style:B,className:g,layoutScroll:!0},h.createElement(ke.div,{"data-framer-stack-content-wrapper":!0,"data-framer-stack-direction-reverse":R,"data-framer-stack-gap-enabled":ie,style:$,onBeforeLayoutMeasure:T},j))}));return e.defaultProps={distribution:"start"},e.displayName="Stack",Yd(e,{direction:{type:"segmentedenum",options:["horizontal","vertical"],title:"Direction",defaultValue:"vertical"},distribution:{type:"enum",options:["start","center","end","space-between","space-around","space-evenly"],optionTitles:["Start","Center","End","Space Between","Space Around","Space Evenly"],title:"Distribute",defaultValue:"space-around"},alignment:{type:"segmentedenum",options:["start","center","end"],title:"Align",defaultValue:"center"},gap:{type:"number",min:0,title:"Gap",hidden:t=>t.distribution!==void 0&&["space-between","space-around","space-evenly"].includes(t.distribution),defaultValue:10},padding:{type:"fusednumber",toggleKey:"paddingPerSide",toggleTitles:["Padding","Padding per side"],valueKeys:["paddingTop","paddingRight","paddingBottom","paddingLeft"],valueLabels:["t","r","b","l"],min:0,title:"Padding",defaultValue:0}}),e})();function Jf(e){return typeof e=="string"&&e.endsWith("fr")}function Ei(e){let t=parseFloat(e);return D(t)?t:0}function Zw(e,t,r,n){return h.Children.map(e,i=>{if(!Mr(i)||!Fr(i))return;let o=t==="vertical",a={},s=!1,{style:l,size:c}=i.props,{width:u,height:f}=i.props;c!==void 0&&(u===void 0&&(u=c),f===void 0&&(f=c));let d=u,m=f;if(Jf(u)&&(s=!0,s=!0,o?r===2?(a.alignSelf="stretch",d="auto"):d=`${Ei(u)*100}%`:(d=1,a.flexGrow=Ei(u),a.flexBasis=0),a.width=d),Jf(f)&&(s=!0,o?(m=1,a.flexGrow=Ei(f),a.flexBasis=0):n===2?(a.alignSelf="stretch",m="auto"):m=`${Ei(f)*100}%`,a.height=m),!s)return i;let p={...l,...a};return h.cloneElement(i,{width:d,height:m,style:p})})}function Rh(e){return e?!["space-between","space-around","space-evenly","stretch"].includes(e):!1}function _h(e,t,r){return!(!e||!r&&!Rh(t))}function Jw(e,t,r,n,i,o){let a={display:"contents"},s=_h(t,n,o);if(s){let l=eC(r);a["--stack-gap-x"]=`${l?0:t}px`,a["--stack-gap-y"]=`${l?t:0}px`}return h.createElement("div",{"data-framer-legacy-stack-gap-enabled":s,"data-framer-stack-flexbox-gap":i,style:a},e)}function Qw(e){switch(e){case"vertical":return"column";case"horizontal":return"row";default:return e}}function eC(e){return e==="column"||e==="column-reverse"}function tC(e){switch(e){case"column-reverse":case"row-reverse":return!0;default:return!1}}function Qf(e){switch(e){case"start":return"flex-start";case"end":return"flex-end";default:return e}}function rC(e,t){return r=>{t.current=r,typeof e=="function"?e(r):e&&(e.current=r)}}var nC={horizontal:"x",vertical:"y",both:!0};function iC(e){return e&&nC[e]}var oC=({dragDirection:e,children:t,fromCanvasComponent:r})=>ne(()=>h.Children.map(t,n=>{if(n===null||typeof n!="object"||typeof n.type=="string")return n;let i={};switch(e){case"vertical":i.width="100%";break;case"horizontal":i.height="100%";break;default:return n}let o=r?{style:Object.assign({},n.props.style,i)}:i;return h.cloneElement(n,o)}),[e,t]),ed=e=>typeof e=="number"?e:e.get(),aC=h.forwardRef(function(t,r){let{direction:n="vertical",directionLock:i=!1,dragEnabled:o=!0,dragElastic:a,dragMomentum:s,dragTransition:l,wheelEnabled:c=!0,contentOffsetX:u=0,contentOffsetY:f=0,contentWidth:d,contentHeight:m,onScrollStart:p,onScroll:g,onScrollEnd:x,onDragStart:v,onDrag:b,onDragEnd:y,onUpdate:S,onDirectionLock:C,style:w,children:T,scrollAnimate:E,resetOffset:R,overdragEnabled:_=!0,layoutId:F,native:B,...L}=t,P=Rt(t,{specificLayoutId:F,postfix:"scroll"}),I=ct(typeof u=="number"?u:0),U=ct(typeof f=="number"?f:0),k=Qe(u)?u:I,A=Qe(f)?f:U,X=O(null),j=Yu(),V=yh(),Z=O(!0);$t();function Y(G){return G=sC(G),d!==void 0&&(G.left=-d),m!==void 0&&(G.top=-m),X.current=G}let{initial:$,prev:ie}=O({initial:{x:0,y:0},prev:{x:0,y:0}}).current,Te=Q.current()==="PREVIEW",q=O(null),le=r||q,ge=O(null),Be=O(null);function Le(G){let ee=G&&Z.current===!1;return R&&ee}function Xe(){if(!ge.current||!le.current)return;let G=Le(V);Z.current=V;let ee=Be.current;if(ee===null&&u===void 0&&f===void 0)return;let Pe=ee===null||!Qe(u)&&u!==ee.offsetX||!Qe(f)&&f!==ee.offsetY,xt=ge.current.offsetWidth-le.current.offsetWidth,$e=ge.current.offsetHeight-le.current.offsetHeight,bt=xt!==ee?.maxXOffset||$e!==ee?.maxYOffset,rm=ee?.x!==k.get()||ee?.y!==A.get();if(G||Pe||bt&&!rm){let xs=n!=="vertical"?ed(u):0,bs=n!=="horizontal"?ed(f):0,Ss=-Math.min(xs,xt),ws=-Math.min(bs,$e);k.set(Ss),A.set(ws),Be.current={maxXOffset:xt,maxYOffset:$e,offsetX:xs,offsetY:bs,x:Ss,y:ws}}}Tt(()=>{Q.current()==="CANVAS"&&Xe()}),Tt(()=>{Q.current()!=="CANVAS"&&Xe()},[]),h.useEffect(()=>{Le(V)&&Xe(),V===!1&&(Z.current=!1)},[V]);let Ye=()=>({x:k.get(),y:A.get()}),zr=oe(()=>{let G=Ye();$.x=G.x,$.y=G.y,ie.x=G.x,ie.y=G.y},[]),fr=oe(()=>{let G=Ye(),ee={point:G,velocity:{x:k.getVelocity(),y:A.getVelocity()},offset:{x:G.x-$.x,y:G.y-$.y},delta:{x:G.x-ie.x,y:G.y-ie.y}};return ie.x=G.x,ie.y=G.y,ee},[k,A]),et=oe(()=>{S&&S({x:k.get(),y:A.get()}),g&&g(fr())},[g,S,fr,k,A]),yt=oe(()=>{K.update(et,!1,!0)},[et]),Hi=(G,ee)=>{zr(),v&&v(G,ee),p&&p(ee)},Ni=()=>x&&x(fr());jw(le,{enabled:c,initial:$,prev:ie,direction:n,offsetX:k,offsetY:A,onScrollStart:G=>{p?.(G)},onScroll:g,onScrollEnd:x,constraints:X});let dr=ct(0),Ze=ct(0);Tt(()=>{let G=Pe=>{let xt=le.current;if(!(xt instanceof HTMLDivElement))return;xt.scrollLeft=-Pe;let $e=X.current;if($e&&_){let bt=0;Pe>$e.right&&(bt=Pe),Pe<$e.left&&(bt=Pe-$e.left),dr.set(bt)}yt()},ee=k.get();return ee!==0&&G(ee),k.on("change",G)},[k,dr,yt,_]),Tt(()=>{let G=Pe=>{let xt=le.current;if(!(xt instanceof HTMLDivElement))return;xt.scrollTop=-Pe;let $e=X.current;if($e&&_){let bt=0;Pe>$e.bottom&&(bt=Pe),Pe<$e.top&&(bt=Pe-$e.top),Ze.set(bt)}yt()},ee=A.get();return ee!==0&&G(ee),A.on("change",G)},[A,Ze,yt,_]);let On=h.useCallback(()=>{let G=le.current;if(!(G instanceof HTMLDivElement))return;let ee=Math.abs(k.get()+G.scrollLeft),Pe=Math.abs(A.get()+G.scrollTop);ee>1&&k.set(-G.scrollLeft),Pe>1&&A.set(-G.scrollTop)},[k,A]),An=h.Children.count(T)===0,$i=n!=="vertical"&&!An?"auto":"100%",_e=n!=="horizontal"&&!An?"auto":"100%",Vn=L.__fromCanvasComponent?{}:{width:L.__fromCodeComponentNode?"100%":L.width,height:L.__fromCodeComponentNode?"100%":L.height};return h.createElement(pt,{"data-framer-component-type":"Scroll",background:"none",...L,...Vn,style:{...w,willChange:Te?"transform":void 0,overflow:"hidden"},onScroll:On,preserve3d:L.preserve3d,ref:le,layoutId:P,layoutScroll:!0,onBeforeLayoutMeasure:Xe},h.createElement(pt,{"data-framer-component-type":"ScrollContentWrapper",animate:E,drag:o&&iC(n),dragDirectionLock:i,dragElastic:a,dragMomentum:s,dragTransition:l,dragConstraints:le,dragControls:j,onDragStart:Hi,onDrag:b,onDragEnd:y,onDragTransitionEnd:Ni,onDirectionLock:C,onMeasureDragConstraints:Y,width:$i,height:_e,_dragX:k,_dragY:A,position:"relative",x:_?dr:void 0,y:_?Ze:void 0,ref:ge,style:{display:An?"block":"inline-block",willChange:Te?"transform":void 0,backgroundColor:"transparent",overflow:"visible",minWidth:"100%",minHeight:"100%"},preserve3d:L.preserve3d},h.createElement(mh,{children:T,size:{width:D(L.width)?L.width:"100%",height:D(L.height)?L.height:"100%"},insideUserCodeComponent:!L.__fromCodeComponentNode,title:"Scroll",description:"Click and drag the connector to any frame on the canvas \u2192"}),oC({dragDirection:n,children:T,fromCanvasComponent:L.__fromCanvasComponent})))});function sC({top:e,left:t,right:r,bottom:n}){let i=r-t;return{top:-(n-e),left:-i,right:0,bottom:0}}function Mn(...e){return e.filter(Boolean).join(" ")}function Ca(...e){let t=!1,r=!1;return e.forEach(n=>{t=t||n===1,r=r||n===3}),t&&!r}function lC(...e){return e.every(t=>t===0||t===2)}var td=400;function cC({from:e,velocity:t,onUpdate:r,onComplete:n,onStop:i}){let o=e,a=0,s=0,l=[],c=()=>{Ca(a,s)&&r(o)},u=()=>{lC(a,s)&&n()};return t.x&&(a=1,l.push(Ot({keyframes:[e.x],velocity:-t.x,timeConstant:td,onUpdate:f=>{o.x=f,K.update(c,!1,!0)},onComplete:()=>{if(a!==1)throw Error("animation x should be running when completing");a=2,u()}}))),t.y&&(s=1,l.push(Ot({keyframes:[e.y],velocity:-t.y,timeConstant:td,onUpdate:f=>{o.y=f,K.update(c,!1,!0)},onComplete:()=>{if(s!==1)throw Error("animation y should be running when completing");s=2,u()}}))),Ca(a,s)||u(),{stop:()=>{Ca(a,s)&&(l.forEach(f=>f.stop()),a=a===1?3:a,s=s===1?3:s,i())}}}var rd=3,uC=LS(),fC=rs();function nd(e){return e instanceof HTMLElement?e.style.touchAction:null}function dC(e,t){switch(t){case"horizontal":return nd(e)==="pan-x";case"vertical":return nd(e)==="pan-y";default:return!1}}function hC(e){let t=e.tagName.toLowerCase();return t==="input"||t==="text"||t==="textarea"}function mC(e,t){return!(!(e instanceof Element)||hC(e)||e.hasAttribute("draggable")&&!dC(e,t))}function id(e){return{x:e.pageX,y:e.pageY}}function od(e,t,r){e?.style&&(e.style[t]=r)}function pC(e,t){var r;return(r=e?.style)==null?void 0:r[t]}var Ha={};Object.freeze(Ha);function vC(e,t,r){if(uC||Q.current()!=="PREVIEW")return Ha;let n=h.useRef(null);return N(()=>{if(!e.current)return;let i=e.current,o=0,a=null,s=null,l=null,c=[];function u(p){var g;switch(o){case 0:case 4:case 3:return}if(p.metaKey)return;let x=id(p);if(!s)return;let v=Re.subtract(x,s);if(o===1||o===5){let b=Math.abs(v.x),y=Math.abs(v.y);if((b>rd||y>rd)&&b!==y){let S=b>y?"horizontal":"vertical";if(t==="horizontal"&&S==="vertical"||t==="vertical"&&S==="horizontal"){o=3;return}o=2,a?.forEach(([w])=>od(w,"pointerEvents","none"))}}fC&&p.preventDefault(),o===2&&((g=se.getSelection())==null||g.empty(),c=Ph([...c,p]),l&&(t!=="vertical"&&(i.scrollLeft=l.x-v.x),t!=="horizontal"&&(i.scrollTop=l.y-v.y)))}function f(p){se.removeEventListener("mousemove",u,!1),se.removeEventListener("mouseup",f),o===2&&a&&a.forEach(([x,v])=>od(x,"pointerEvents",v||"auto")),a=null;let g=yC({mouseMoveEvents:c,mouseUpEvent:p});if(s=null,o===2){let x=t!=="horizontal"&&g.y!==0,v=t!=="vertical"&&g.x!==0;if(!x&&!v){o=0;return}o=4,n.current=cC({from:{x:i.scrollLeft,y:i.scrollTop},velocity:{x:v?g.x:0,y:x?g.y:0},onUpdate:b=>{v&&(i.scrollLeft=b.x),x&&(i.scrollTop=b.y)},onStop:()=>{o!==5&&(o=0),n.current=null},onComplete:()=>{if(o!==4)throw Error("On animation completion we should still be in the animation phase");o=0,n.current=null}})}else o=0}function d(){var p;(p=n.current)==null||p.stop()}function m(p){var g;if(!r||p.metaKey)return;if(!mC(p.target,t)){o===4&&(o=0,(g=n.current)==null||g.stop());return}let x=o;if(o=x===4?5:1,s=id(p),a=document.elementsFromPoint(s.x,s.y).filter(v=>v instanceof HTMLElement||v instanceof SVGElement).map(v=>[v,pC(v,"pointerEvents")]),l={x:i.scrollLeft,y:i.scrollTop},c=[],n.current){if(x!==4)throw Error("When stopping a drag animation we need to be animating");n.current.stop()}se.addEventListener("mousemove",u),se.addEventListener("mouseup",f),i.addEventListener("mousewheel",d)}return i.addEventListener("mousedown",m),()=>{var p;i.removeEventListener("mousedown",m),i.removeEventListener("mousewheel",d),se.removeEventListener("mousemove",u),se.removeEventListener("mouseup",f),o=5,(p=n.current)==null||p.stop()}},[e,t,r]),h.useMemo(()=>({cancelEmulatedTouchScrollAnimation:()=>{var i;(i=n.current)==null||i.stop()}}),[])}var gC=4/60*1e3;function Ph(e){let r=new CustomEvent("getTime").timeStamp-gC;return e.filter(n=>n.timeStamp>r)}var ad={x:0,y:0};function yC({mouseMoveEvents:e,mouseUpEvent:t}){let n=Ph(e)[0];if(!n)return ad;let i=t.clientX-n.clientX,o=t.clientY-n.clientY,a=t.timeStamp-n.timeStamp;return a===0?ad:{x:i/a*1e3,y:o/a*1e3}}function sd(e,t,r,n){Tt(()=>{if(Qe(r)){let i=()=>{n?.();let o=e.current;o&&(o[t]=Math.abs(r.get()))};return i(),r.on("change",i)}else if(D(r)){let i=e.current;if(!i)return;n?.(),i[t]=Math.abs(r)}},[r])}var xC=h.forwardRef(function(t,r){let{direction:n="vertical",scrollBarVisible:i=!1,dragEnabled:o=!0,contentOffsetX:a=0,contentOffsetY:s=0,contentWidth:l,contentHeight:c,children:u,resetOffset:f,onScroll:d,className:m,directionLock:p=!1,wheelEnabled:g=!0,scrollAnimate:x,dragTransition:v,dragMomentum:b,dragElastic:y,overdragEnabled:S=!0,onScrollStart:C,onScrollEnd:w,onDragStart:T,onDrag:E,onDragEnd:R,onUpdate:_,onDirectionLock:F,layoutId:B,native:L,...P}=t,I=Rt(t,{specificLayoutId:B,postfix:"scroll"}),U=h.useRef(null),k=r||U,{cancelEmulatedTouchScrollAnimation:A}=vC(k,n,o);$t();let X=yh(),j=h.useRef(X),V=()=>{if(!f)return;let Y=j.current;if(j.current=X,!(X&&!Y))return;let ie=k.current;ie&&(n!=="vertical"&&(A?.(),ie.scrollLeft=Math.abs(Qe(a)?a.get():a)),n!=="horizontal"&&(A?.(),ie.scrollTop=Math.abs(Qe(s)?s.get():s)))};Tt(V,[X]),sd(k,"scrollLeft",a,A),sd(k,"scrollTop",s,A);let Z=P.__fromCanvasComponent?{}:{width:P.__fromCodeComponentNode?"100%":P.width,height:P.__fromCodeComponentNode?"100%":P.height};return h.createElement(pt,{ref:k,"data-framer-component-type":"NativeScroll",background:"none",...P,...Z,onScroll:d,layoutId:I,onBeforeLayoutMeasure:V,layoutScroll:!0,className:Mn(m,`direction-${n}`,!i&&"scrollbar-hidden")},h.createElement(mh,{children:u,size:{width:D(P.width)?P.width:"100%",height:D(P.height)?P.height:"100%"},insideUserCodeComponent:!P.__fromCodeComponentNode,title:"Scroll",description:"Click and drag the connector to any frame on the canvas \u2192"}),u)}),gP=(()=>{let e=h.forwardRef(function(r,n){return r.native?h.createElement(xC,{ref:n,...r}):h.createElement(aC,{ref:n,...r})});return e.defaultProps={directionLock:!1},Yd(e,{native:{type:"boolean",defaultValue:!1},direction:{type:"segmentedenum",title:"Direction",options:["vertical","horizontal","both"],defaultValue:"vertical"},contentOffsetX:{type:"number",title:"Offset X",defaultValue:0,min:0,step:10,displayStepper:!0,hidden:({direction:t})=>t==="vertical"},contentOffsetY:{type:"number",title:"Offset Y",defaultValue:0,min:0,step:10,displayStepper:!0,hidden:({direction:t})=>t==="horizontal"},directionLock:{type:"boolean",title:"Lock",enabledTitle:"1 Axis",disabledTitle:"Off",defaultValue:!0,hidden:({native:t})=>t===!0},dragEnabled:{type:"boolean",title:"Drag",enabledTitle:"On",disabledTitle:"Off",defaultValue:!0},overdragEnabled:{type:"boolean",title:"Overdrag",enabledTitle:"On",disabledTitle:"Off",defaultValue:!0,hidden:({native:t})=>t===!0},wheelEnabled:{type:"boolean",title:"Wheel",enabledTitle:"On",disabledTitle:"Off",defaultValue:!0,hidden:({native:t})=>t===!0},scrollBarVisible:{type:"boolean",title:"Scroll Bar",enabledTitle:"Visible",disabledTitle:"Hidden",defaultValue:!1,hidden:({native:t})=>t===!1},resetOffset:{type:"boolean",title:"Reset",defaultValue:!1}}),e.supportsConstraints=!0,e})();var bC=(()=>{function e(t={}){let r=Sa(t,!1,!1);return e.addData(r),r}return e._stores=[],e.addData=t=>{e._stores.push(t)},e.reset=()=>{e._stores.forEach(t=>Sa.resetObject(t))},e.addObserver=(t,r)=>Sa.addObserver(t,r),e})(),Ta=bC;var SC={update:0},wC=h.createContext({update:NaN});var CC=class extends ue{constructor(){super(...arguments),this.observers=[],this.state=SC,this.taskAdded=!1,this.frameTask=()=>{this.setState({update:this.state.update+1}),this.taskAdded=!1},this.observer=()=>{this.taskAdded||(this.taskAdded=!0,bn.addFrameTask(this.frameTask))}}componentWillUnmount(){this.observers.map(e=>e()),Ta.reset()}render(){let{children:e}=this.props;return this.observers.map(t=>t()),this.observers=[],Ta._stores.forEach(t=>{let r=Ta.addObserver(t,this.observer);this.observers.push(r)}),h.createElement(wC.Provider,{value:{...this.state}},e)}},Ih="__framer__",TC=Ih.length;function EC(e){if(e.startsWith(Ih))return e.substr(TC)}var Nt=["opacity","x","y","scale","rotate","rotateX","rotateY","transformPerspective"],kn=e=>{var t,r,n,i,o,a,s,l;return{x:ve((t=e?.x)!=null?t:0),y:ve((r=e?.y)!=null?r:0),opacity:ve((n=e?.opacity)!=null?n:1),scale:ve((i=e?.scale)!=null?i:1),rotate:ve((o=e?.rotate)!=null?o:0),rotateX:ve((a=e?.rotateX)!=null?a:0),rotateY:ve((s=e?.rotateY)!=null?s:0),transformPerspective:ve((l=e?.transformPerspective)!=null?l:0)}},Ne={x:0,y:0,scale:1,opacity:1,transformPerspective:0,rotate:0,rotateX:0,rotateY:0},kC=new Set(["loopEffectEnabled","loopTransition","loop","loopRepeatType","loopRepeatDelay"]),RC=()=>{let e=O();return N(()=>()=>{clearTimeout(e.current)},[]),async t=>new Promise(r=>{e.current=setTimeout(()=>{r(!0)},t*1e3)})};function _C({loopEffectEnabled:e,loopRepeatDelay:t,loopTransition:r,loopRepeatType:n,loop:i}){let o=mn(),a=vt(()=>kn()),s=h.useRef(!1),l=RC(),c=async()=>{if(!i)return;let d=r||void 0,m=s.current&&n==="mirror",p=m?Ne:i,g=m?i:Ne;return s.current=!s.current,Promise.all(Nt.map(x=>{var v;if(!(o&&x!=="opacity"))return a[x].set((v=g[x])!=null?v:Ne[x]),new Promise(b=>{var y;mi(a[x],(y=p[x])!=null?y:g[x],{...d,onComplete:()=>b()})})}))},u=async()=>{e&&(await c(),await l(t??0),await u())},f=oe(()=>{Nt.forEach(d=>{a[d].stop()}),Nt.forEach(d=>{a[d].set(Ne[d])}),s.current=!1},[a]);return h.useEffect(()=>(e&&i?u():f(),()=>f()),[e]),{values:a}}function PC(e,t,r,n,i){let o=r/100-1,a=i?(t-n)*o:0,s=-e*o;return a+s}var IC=new Set(["speed","adjustPosition","offset","parallaxTransformEnabled"]);function FC(e,t,r){let{speed:n=100,offset:i=0,adjustPosition:o=!1,parallaxTransformEnabled:a}=e,s=h.useRef(null),l=mn(),c=h.useCallback(p=>s.current===null||n===100?0:PC(p,s.current,n,i,o),[s,n,i,o]);h.useLayoutEffect(()=>{K.read(()=>{var p,g,x;s.current=(x=(g=(p=t.current)==null?void 0:p.getBoundingClientRect())==null?void 0:g.top)!=null?x:0}),K.update(()=>{f.set(c(u.get())),o&&d.set(r??"initial")})},[t,s,o]);let{scrollY:u}=Bu(),f=Je(u,c),d=ct(o&&s.current===null?"hidden":r),m=ct(0);return{values:{y:l||!a?m:f},style:{visibility:d}}}function MC(e,t){let r=0,n=e;for(;n&&n!==t&&n instanceof HTMLElement;)r+=n.offsetTop,n=n.offsetParent;return r}var LC=1;function Fh(e,t=0,r){var n,i,o;let a=[],s=[];for(let l=e.length;l>=0;l--){let{ref:c,offset:u}=(n=e[l])!=null?n:{};if(!c||!c.current)continue;let d=MC(c.current,document.documentElement)-LC-(u??0)-t,m=(o=(i=c.current)==null?void 0:i.clientHeight)!=null?o:0,p=a[a.length-1],g=Math.max(d+m,0);a.push(d),s.unshift(Math.max(d,0),p===void 0?g:Math.min(g,Math.max(p-1,0))),r?.(l)}return s}var OC=new Set(["threshold","animateOnce","opacity","targetOpacity","x","y","scale","transition","rotate","rotateX","rotateY","perspective","enter","exit","animate","styleAppearEffectEnabled","targets"]),AC=["animate","animate"];function VC(e,t,r){let n=Fh(e,t),i=[...AC],o=n[0];if(Ce(typeof o=="number",`Invalid inputRange: ${n}`),o>1&&(n.unshift(0,o-1),i.unshift("initial","initial")),r){let a=n.length-1,s=n[a];Ce(typeof s=="number",`Invalid inputRange: ${n}`),n.push(s+1),i.push("exit")}return{inputRange:n,outputRange:i}}function Ea(e){var t,r,n,i,o,a,s,l,c;return{x:(t=e?.x)!=null?t:Ne.x,y:(r=e?.y)!=null?r:Ne.y,scale:(n=e?.scale)!=null?n:Ne.scale,opacity:(i=e?.opacity)!=null?i:Ne.opacity,transformPerspective:(o=e?.transformPerspective)!=null?o:Ne.transformPerspective,rotate:(a=e?.rotate)!=null?a:Ne.rotate,rotateX:(s=e?.rotateX)!=null?s:Ne.rotateX,rotateY:(l=e?.rotateY)!=null?l:Ne.rotateY,transition:(c=e?.transition)!=null?c:void 0}}function DC({opacity:e,targetOpacity:t,perspective:r,enter:n,exit:i,animate:o,...a}){return h.useMemo(()=>{var s;return{initial:n??Ea({...a,opacity:(s=e??t)!=null?s:1,transformPerspective:r}),animate:o??Ea({opacity:t}),exit:i??Ea()}},[o,a,n,i,e,t,r])}function BC(e,t){let r=mn(),n=DC(e),i=vt(()=>kn(e.styleAppearEffectEnabled?n.initial:n.animate)),o=h.useRef({isPlaying:!1,scheduledAppearState:void 0,lastAppearState:!e.styleAppearEffectEnabled}),a=h.useRef(),s=h.useCallback(async({transition:c,...u},f)=>{var d;let m=(d=c??n.animate.transition)!=null?d:e.transition;await a.current,a.current=Promise.all(Nt.map(p=>{var g,x;f&&i[p].set((g=n.initial[p])!=null?g:Ne[p]);let v=(x=u[p])!=null?x:Ne[p],b=Qt.get(t.current);return b&&typeof v!="object"&&b.setBaseTarget(p,v),new Promise(y=>{r&&p!=="opacity"?y():mi(i[p],v,{restDelta:p==="scale"?.001:void 0,...m,onComplete:()=>y()})})}))},[]),l={animateOnce:!!e.animateOnce,threshold:{y:e.threshold}};return sw(t,l,c=>{if(e.targets||!e.styleAppearEffectEnabled)return;let{isPlaying:u,lastAppearState:f}=o.current;if(!(e.animateOnce&&o.current.lastAppearState===!0)){if(u){o.current.scheduledAppearState=c;return}o.current.scheduledAppearState=void 0,o.current.lastAppearState=c,f!==c&&s(c?n.animate:n.exit,c)}}),h.useEffect(()=>{if(!e.targets||!e.styleAppearEffectEnabled)return;let c={initial:!0},u="initial";return hn(({y:f})=>{var d;let{targets:m}=e;if(!m||!m[0]||m[0].ref&&!m[0].ref.current)return;let{inputRange:p,outputRange:g}=VC(m,((d=e.threshold)!=null?d:0)*f.containerLength,!!e.exit);if(p.length===0)return;Ce(p.length===g.length,`Style ranges must have the same number of entries. Input: ${p}, Output: ${g}`);let x=dn(f.current,p,g);e.animateOnce&&c[x]||(c[x]=!0,u!==x&&(u=x,s(n[x])))})},[]),{values:i}}var zC=new Set(["transformViewportThreshold","styleTransformEffectEnabled","transformTargets","spring","transformTrigger"]),HC=(e,t)=>{var r,n;let i=(r=e?.[0])==null?void 0:r.target;return kn(t?{opacity:(n=i?.opacity)!=null?n:1}:i)},Mh=()=>({opacity:[],x:[],y:[],scale:[],rotate:[],rotateX:[],rotateY:[],transformPerspective:[]});function NC(e,t){let r=h.useRef({});h.useEffect(()=>{if(t!==void 0)for(let n in e){let i=e[n];i.attach((o,a)=>{let s=r.current[n];if(s&&s.stop(),r.current[n]=Ot({keyframes:[i.get(),o],velocity:i.getVelocity(),...t,restDelta:.001,onUpdate:a}),!he.isProcessing){let l=performance.now()-he.timestamp;l<40&&(r.current[n].time=l/1e3)}return i.get()})}},[JSON.stringify(t)])}function $C(e,t){let r=Mh();return{inputRange:Fh(e,t,i=>{var o,a,s,l,c;let u=(o=e[i-1])==null?void 0:o.target,f=(a=e[i])==null?void 0:a.target;for(let d of Nt)(c=r[d])==null||c.unshift((s=u?.[d])!=null?s:0,(l=f?.[d])!=null?l:0)}),effectKeyOutputRange:r}}function UC(e){var t;let r=Mh();for(let{target:n}of e)for(let i of Nt)(t=r[i])==null||t.push(n[i]);return r}var ka=[0,1];function WC({transformTrigger:e,styleTransformEffectEnabled:t,transformTargets:r,spring:n,transformViewportThreshold:i=0},o){let a=mn(),s=vt(()=>HC(r,a));return h.useLayoutEffect(()=>{var l;if(!(t!==!0||!r))if(e!=="onScrollTarget"){let c=UC(r);return hn(({y:u})=>{for(let f of Nt)a&&f!=="opacity"||(Ce(ka.length===c[f].length,`Transform ranges must have the same number of entries. Input: ${ka}, Output: ${c[f]}`),s[f].set(dn(u.progress,ka,c[f])))},e==="onInView"?{target:(l=o.current)!=null?l:void 0,offset:["start end","end end"]}:void 0)}else return hn(({y:c})=>{if(!r[0]||r[0].ref&&!r[0].ref.current)return;let{inputRange:u,effectKeyOutputRange:f}=$C(r,i*c.containerLength);if(u.length!==0)for(let d of Nt)a&&d!=="opacity"||(Ce(u.length===f[d].length,`Transform ranges must have the same number of entries. Input: ${u}, Output: ${f[d]}`),s[d].set(dn(c.current,u,f[d])))})},[a,e,o,i,t,s,r]),NC(s,n),{values:s}}var Lh={parallax:IC,styleAppear:OC,styleTransform:zC,loop:kC},jC=Object.keys(Lh);function ld(e,t){return!(e in t)||t[e]===!0}function GC(e){let t={parallax:{},styleAppear:{},styleTransform:{},loop:{},forwardedProps:{}};for(let r in e){let n=EC(r);if(n){for(let i of jC)if(Lh[i].has(n)){t[i][n]=e[r];break}}else t.forwardedProps[r]=e[r]}return t.parallax.parallaxTransformEnabled=ld("parallaxTransformEnabled",t.parallax),t.styleAppear.styleAppearEffectEnabled=ld("styleAppearEffectEnabled",t.styleAppear),t}var Er=e=>e.reduce((t,r)=>t+=r,0),cd=e=>e.reduce((t,r)=>t=t*r,1),XC="current";function YC(e){return qe(e)&&XC in e}var Oh=e=>h.forwardRef((t,r)=>{var n;if(t.__withFX)return h.createElement(e,{...t,ref:r});let i=h.useRef(null),o=r??i,{parallax:a={},styleAppear:s={},styleTransform:l={},loop:c={},forwardedProps:u}=GC(t),f=vt(()=>{var I;let U={};if(!qe(u.initial))return kn();for(let k in u.initial){let A=(I=u.initial)==null?void 0:I[k];xe(A)&&(A=A.get()),Se(A)&&(U[k]=A)}return kn(U)}),d=(n=t.__targetOpacity)!=null?n:1,m=ct(d),{values:p,style:g}=FC(a,o),{values:x}=WC(l,o),{values:v}=BC(s,o),{values:b}=_C(c),y=h.useMemo(()=>({scale:[f.scale,x.scale,v.scale,b.scale],opacity:[f.opacity,m,x.opacity,v.opacity,b.opacity],x:[f.x,x.x,v.x,b.x],y:[f.y,x.y,v.y,b.y,p.y],rotate:[f.rotate,x.rotate,v.rotate,b.rotate],rotateX:[f.rotateX,x.rotateX,v.rotateX,b.rotateX],rotateY:[f.rotateY,x.rotateY,v.rotateY,b.rotateY],transformPerspective:[f.transformPerspective,x.transformPerspective,v.transformPerspective]}),[m,f,x,p,v,b]),S=Je(y.scale,cd),C=Je(y.opacity,cd),w=Je(y.x,Er),T=Je(y.y,Er),E=Je(y.rotate,Er),R=Je(y.rotateX,Er),_=Je(y.rotateY,Er),F=Je(y.transformPerspective,Er),{drag:B,dragConstraints:L}=u;f1(B&&YC(L)?L:void 0);let P={opacity:C,scale:S,x:w,y:T,rotate:E,rotateX:R,rotateY:_};return wn(t.__perspectiveFX)&&(P.transformPerspective=F),h.createElement(e,{...u,__withFX:!0,style:{...t.style,...g,...P},values:f,ref:o})}),kP=Oh,RP=Oh,_P=be(Li(),1);var KC=e=>h.forwardRef((t,r)=>{let n=Rt(t);return h.createElement(e,{layoutId:n,...t,layoutIdKey:void 0,duplicatedFrom:void 0,ref:r})}),qC=h.forwardRef(({children:e,layoutId:t,...r},n)=>{let i=vt(()=>t?`${t}-container`:void 0);return h.createElement(ke.div,{layoutId:i,...r,ref:n},h.createElement(ur.Provider,{value:!0},h.createElement(Ax,{enabled:!1},h.createElement(Au,{id:t??"",inherit:"id"},h.Children.map(e,o=>h.isValidElement(o)?h.cloneElement(o,{layoutId:t}):o)))))}),LP=KC(qC),ud="element",ZC="collection",JC="collectionItemId",QC="pathVariables",Ah="framer/page-link,";function Vh(e){return te(e)&&e.startsWith(`data:${Ah}`)}function hs(e){if(Vh(e))try{let t=new URL(e),r=t.pathname.substring(Ah.length),n=t.searchParams,i=n.has(ud)?n.get(ud):void 0,o,a=n.get(ZC),s=n.get(JC),l=n.get(QC);if(a&&s&&l){let c=Object.fromEntries(new URLSearchParams(l).entries());o={collection:a,collectionItemId:s,pathVariables:c}}return{target:r==="none"?null:r,element:i==="none"?void 0:i,collectionItem:o}}catch{return}}function Dh(e,t,r){var n,i,o;let a=t.getAttribute("data-framer-page-link-target"),s,l;if(a){s=(n=t.getAttribute("data-framer-page-link-element"))!=null?n:void 0;let u=t.getAttribute("data-framer-page-link-path-variables");u&&(l=Object.fromEntries(new URLSearchParams(u).entries()))}else{let u=t.getAttribute("href");if(!u)return!1;let f=hs(u);if(!f||!f.target)return!1;a=f.target,s=(i=f.element)!=null?i:void 0,l=(o=f.collectionItem)==null?void 0:o.pathVariables}let c=s?t.dataset.framerSmoothScroll!==void 0:void 0;return e(a,s,Object.assign({},r,l),c),!0}var eT=h.createContext(void 0);function Bi(e,t){return e instanceof HTMLAnchorElement?e:e instanceof Element?e===t?null:Bi(e.parentElement,t):null}var tT="webPageId";function Fi(e){return Boolean(e&&typeof e=="object"&&tT in e)}function rT(e){if(!e)return;let t={};for(let r in e.pathVariables){let n=e.pathVariables[r];n&&(t[r]=n)}return t}function zi(e){if(!Vh(e))return e;let t=hs(e);if(!t)return;let{target:r,element:n,collectionItem:i}=t;if(r)return{webPageId:r,hash:n??void 0,pathVariables:rT(i)}}var Bh=/:([a-zA-Z][a-zA-Z0-9_]*)/g,zh=h.createContext(void 0);function ms(){var e;let t=h.useContext(zh),r=(e=cr())==null?void 0:e.pathVariables;return t||r}function ps(e,{webPageId:t,hash:r,pathVariables:n},i){if(t!==e.id||r)return!1;if(e.path&&e.pathVariables){let o=Object.assign({},i,n);for(let[,a]of e.path.matchAll(Bh))if(!a||e.pathVariables[a]!==o[a])return!1}return!0}function nT(e){let t=cr(),r=h.useContext(zh);if(!t)return!1;let n=te(e)?zi(e):e;return Fi(n)?ps(t,n,r):!1}function Hh(e){return e===void 0?!1:!!(e.startsWith("#")||e.startsWith("/")||e.startsWith("."))}function iT(e,t){try{let r=new URL(e);return Boolean(r.protocol)}catch{}return t}function vs(e,t){return e!==void 0?e?"_blank":void 0:t?void 0:"_blank"}function Na(e,t=void 0){let r=Hh(e),n=vs(t,r);return{href:iT(e,r)?e:`https://${e}`,target:n,rel:r?void 0:"noopener"}}function Nh(e,t,r,n,i){return o=>{var a;if(o.metaKey)return;let s=Bi(o.target);!s||s.getAttribute("target")==="_blank"||(o.preventDefault(),(a=e.navigate)==null||a.call(e,t,r,n,i))}}function oT(e,t,r,n,i,o){let a=Hh(e);if(!r.routes||!r.getRoute||!n||!a)return Na(e,t);try{let[s,l]=e.split("#",2);Ce(s!==void 0,"A href must have a defined pathname.");let{routeId:c,pathVariables:u}=_0(r.routes,s),f=r.getRoute(c);if(f){qa(f.page);let d=Object.assign({},i,u),m=Ai(f,{currentRoutePath:n.path,hash:l||void 0,pathVariables:d}),p=vs(t,!0);return{href:m,target:p,onClick:Nh(r,c,l||void 0,d,o)}}}catch{}return Na(e,t)}var VP=h.forwardRef(({children:e,href:t,openInNewTab:r,smoothScroll:n,...i},o)=>{let a=lr(),s=cr(),l=ms(),c=h.useMemo(()=>{var f;if(!t)return{};let d=Fi(t)?t:zi(t);if(!d)return{};if(te(d))return oT(d,r,a,s,l,n);let{webPageId:m,hash:p,pathVariables:g,hashVariables:x}=d,v=(f=a.getRoute)==null?void 0:f.call(a,m);v&&qa(v.page);let b=Object.assign({},l,g),y=Object.assign({},l,x),S=vs(r,!0),C=Ai(v,{currentRoutePath:s?.path,hash:p,pathVariables:b,hashVariables:y}),w=C.split("#",2)[1];return{href:C,target:S,onClick:Nh(a,m,w,b,n),"data-framer-page-link-current":s&&ps(s,d,l)||void 0}},[s,t,r,l,a,n]);if(!e)return null;let u=h.Children.only(e);return h.isValidElement(u)?h.cloneElement(u,{...i,...c,ref:o??i.ref}):null});function DP(e,t,r){let n=Fi(e)?e:zi(e);if(!Fi(n))return te(e)?Na(e).href:void 0;if(!t.getRoute||!t.currentRouteId)return;let i=t.getRoute(t.currentRouteId),{webPageId:o,hash:a,pathVariables:s,hashVariables:l}=n,c=t.getRoute(o),u=Object.assign({},t.currentPathVariables,r,s),f=Object.assign({},t.currentPathVariables,r,l);return Ai(c,{currentRoutePath:i?.path,hash:a,pathVariables:u,hashVariables:f,relative:!1})}var aT=class{constructor(){this.entries=new Map}set(e,t,r,n){let i=this.entries.get(e);switch(t){case"transformTemplate":{Ce(typeof r=="string",`transformTemplate must be a string, received: ${r}`),i?i.transformTemplate=r:this.entries.set(e,{transformTemplate:r});break}case"initial":case"animate":{Ce(typeof r=="object",`${t} must be a valid object, received: ${r}`),i?(i[t]=r,i.variantHash||(i.variantHash=n)):this.entries.set(e,{[t]:r,variantHash:n});break}default:break}}clear(){this.entries.clear()}toObject(){return Object.fromEntries(this.entries)}},$h=new aT,BP=(e,t,r,n)=>(kt()||$h.set(t,e,r,n),r),sT="__Appear_Animation_Transform__",zP=(e,t)=>{if(!kt()){let r=t?.({},sT);if(r===void 0)return t;$h.set(e,"transformTemplate",r)}return t},HP="data-framer-appear-id",NP="data-framer-appear-animation";function UP({RootComponent:e,isWebsite:t,routeId:r,pathVariables:n,routes:i,notFoundPage:o,isReducedMotion:a=!1,includeDataObserver:s=!1}){if(h.useEffect(()=>{t||bn.start()},[]),t)return h.createElement(Mu,{reducedMotion:a?"user":"never"},h.createElement(B0,{initialRoute:r,initialPathVariables:n,routes:i,notFoundPage:o,defaultPageStyle:{minHeight:"100%",width:"auto"}}));{let l=s?CC:h.Fragment;return h.createElement(l,null,h.createElement(A0,{routes:i},h.createElement(Uw,null,h.createElement(e,{key:r}))))}}var lT=h.createContext(void 0);if(kt())for(let e of document.querySelectorAll("style[data-framer-css-ssr]"))document.head.appendChild(e);var cT=(()=>{var e;if(!kt())return new Set;let t=(e=document.querySelector("style[data-framer-css-ssr-minified]"))==null?void 0:e.getAttribute("data-framer-components");return t?new Set(t.split(" ")):new Set})(),Uh={"data-framer-css-ssr":!0},XP=(e,t,r)=>h.forwardRef((n,i)=>{var o;let{sheet:a,cache:s}=(o=h.useContext(lT))!=null?o:{};if(!kt()){let l=Array.isArray(t)?t.join(`
`):t;return h.createElement(h.Fragment,null,h.createElement("style",{...Uh,"data-framer-component":r,dangerouslySetInnerHTML:{__html:l}}),h.createElement(e,{...n,ref:i}))}return h.useInsertionEffect(()=>{if(r&&cT.has(r))return;(Array.isArray(t)?t:t.split(`
`)).forEach(c=>c&&Wd(c,a,s))},[]),h.createElement(e,{...n,ref:i})}),Wh=h.createContext(void 0),$a="ssr-variant";function uT(e,t,r){var n;return"ref"in e?{...t,ref:(n=e.ref)!=null?n:r}:{...t,ref:r}}function Ua(e,t,r){return h.createElement(h.Fragment,null,h.Children.map(e,n=>!n||!Mr(n)||!Fr(n)?null:h.cloneElement(n,uT(n,t,r))))}function fT(e,t,r,n,i,o,a,s){let l=h.Children.toArray(t),c=l[0];if(l.length!==1||!h.isValidElement(c))return console.warn(s+": expected exactly one React element for a child",t),Ua(t,r,n);let u=[],f=[];for(let[p]of Object.entries(i)){if(p===o)continue;let g=e[p];if(!g||!mT(c.props,g)){f.push(p);continue}let x=fd([p],a);x.length&&u.push({variants:x,propOverrides:g})}if(u.length===0)return h.cloneElement(c,{...r,ref:n});let d=[o,...f],m=fd(d,a);return m.length&&u.unshift({variants:m}),h.createElement(h.Fragment,null,!a&&h.createElement("style",{...Uh},`.${$a} { display: contents }`),u.map(({variants:p,propOverrides:g})=>{let x=p.join("+"),v=h.createElement(Wh.Provider,{key:x,value:new Set(p)},h.cloneElement(c,{...r,...g,ref:n})),b=dT(p,a,i);return b.length?(Ce(u.length>1,"Must branch out when there are hiddenClassNames"),v=h.createElement("div",{key:x,className:`${$a} ${b.join(" ")}`},v)):Ce(u.length===1,"Cannot branch out when hiddenClassNames is empty"),v}))}function dT(e,t,r){let n=[];for(let[i,o]of Object.entries(r)){let a=t&&!t.has(i);if(e.includes(i)||a)continue;let s=o.split("-")[2];n.push(`hidden-${s}`)}return n}function fd(e,t){return t?e.filter(r=>t.has(r)):e}function hT(e){switch(e){case"transformTemplate":return!1;default:return!1}}function mT(e,t){for(let r of Object.keys(t))if(!hT(r)&&!ss(e[r],t[r],!0))return!0;return!1}function pT(e,t,r){return!r||!e?t:{...t,...r[e]}}var YP=h.forwardRef(function({breakpoint:t,overrides:r,children:n,...i},o){if(kt())return Ua(n,pT(t,i,r),o);let a=h.useContext(eT);if(!a)return console.warn("PropertyOverrides is missing GeneratedComponentContext"),Ua(n,i,o);let{primaryVariantId:s,variantClassNames:l}=a,c=h.useContext(Wh);return fT(r,n,i,o,l,s,c,"PropertyOverrides")});function Ra(e,t){e.forEach(r=>clearTimeout(r)),e.clear(),t.forEach(r=>r&&r("Callback cancelled by variant change")),t.clear()}function dd(){return new Set}function qP(e){let t=vt(dd),r=vt(dd);return pw(()=>()=>Ra(r,t)),h.useEffect(()=>()=>Ra(r,t),[t,r]),h.useEffect(()=>{Ra(r,t)},[e,t,r]),h.useRef({activeVariantCallback:n=>(...i)=>new Promise((o,a)=>(t.add(a),n(...i).then(o))).catch(()=>{}),delay:async(n,i)=>{await new Promise(o=>r.add(globalThis.setTimeout(()=>o(!0),i))),n()}}).current}function vT(e,t,r){return h.useCallback(n=>{var i,o,a;return r?e?t?Object.assign({},(i=r[e])==null?void 0:i[n],(o=r[t])==null?void 0:o[n]):((a=r[e])==null?void 0:a[n])||{}:{}:{}},[e,t,r])}function jh(e){for(let[t,r]of Object.entries(e))if(se.matchMedia(r).matches)return t}function QP(e,t,r=!0){var n;let i=M(Th),o=O(kt()&&(n=jh(t))!=null?n:e),a=O(r&&i?e:o.current),s=as(),l=Zu(),c=oe(u=>{(u!==o.current||u!==a.current)&&l(()=>{o.current=a.current=u,Ur(()=>{s()})})},[l,s]);return Tt(()=>{!r||i!==!0||c(o.current)},[]),N(()=>{let u=[];for(let[f,d]of Object.entries(t)){let m=se.matchMedia(d),p=g=>{g.matches&&c(f)};gT(m,p),u.push([m,p])}return()=>u.forEach(([f,d])=>yT(f,d))},[t,c]),[o.current,a.current]}function gT(e,t){e.addEventListener?e.addEventListener("change",t):e.addListener(t)}function yT(e,t){e.removeEventListener?e.removeEventListener("change",t):e.removeListener(t)}function eI(e,t,r){var n,i,o,a,s;let l=(n=jh(t))!=null?n:e,c=(i=r[l])==null?void 0:i.split("-")[2];if(c)for(let u of document.querySelectorAll(`.hidden-${c}`))(o=u.parentNode)==null||o.removeChild(u);for(let u of document.querySelectorAll(`.${$a}`))if(u.childElementCount>1){console.warn("SSR variant was expected to have at most one child at this point",u);continue}else u.childElementCount===1?(a=u.parentNode)==null||a.replaceChild(u.firstChild,u):(s=u.parentNode)==null||s.removeChild(u);for(let u of document.querySelectorAll("[data-framer-original-sizes]")){let f=u.getAttribute("data-framer-original-sizes");f===""?u.removeAttribute("sizes"):u.setAttribute("sizes",f),u.removeAttribute("data-framer-original-sizes")}}function rI(e,t){return ne(()=>{if(!Array.isArray(e)||!t)return null;let r=e.find(n=>Object.entries(t).every(([i,o])=>{let a=n[i];return o===void 0||a===void 0||qe(o)||qe(a)?!1:String(o)===String(a)}));return r??null},[e,t])}function gs(){return Q.current()==="CANVAS"}function lI(){let[e,t]=h.useState(!1),r=h.useCallback(n=>{n?document.documentElement.style.setProperty("overflow","hidden"):document.documentElement.style.removeProperty("overflow"),t(n)},[]);return h.useEffect(()=>()=>{document.documentElement.style.removeProperty("overflow")},[]),[e,r]}var ys=class{constructor(e){this.resolver=e}static is(e){return e instanceof ys}preload(){if(this.status){let t=this.status;return t.type!=="pending"?void 0:t.promise}let e=this.resolver().then(t=>{this.status={type:"fulfilled",value:t}},t=>{this.status={type:"rejected",error:t}});return this.status={type:"pending",promise:e},e}read(){let e=this.status;if(!e)throw new Error("Need to call preload() before read()");switch(e.type){case"pending":throw new Error("Need to wait for preload() to resolve");case"fulfilled":return e.value;case"rejected":throw e.error;default:Oa(e)}}},Ln=class{constructor(){this.map1=new WeakMap}get(e,t){let r=this.map1.get(e);return r?.get(t)}set(e,t,r){var n;let i=(n=this.map1.get(e))!=null?n:new WeakMap;return this.map1.set(e,i),i.set(t,r)}},hd=new WeakMap;function xT(e){let t=hd.get(e);if(t)return t;let r=e.map((n,i)=>({...n,index:i}));return hd.set(e,r),r}var md=new Ln;function bT(e,t){if(!t)return e;let r=md.get(e,t);if(r)return r;let n=e.filter(i=>Ve(t,{resolveIdentifier(o){return i[o]}}));return md.set(e,t,n),n}var pd=new Ln;function ST(e,t){if(!t)return e;let r=pd.get(e,t);if(r)return r;let n=[...e].sort((i,o)=>{let a=0;for(let s of t){let l=Ve(s,{resolveIdentifier(u){return i[u]}}),c=Ve(s,{resolveIdentifier(u){return o[u]}});if(Se(l)&&Se(c)&&(a=l-c),te(l)&&te(c)&&(a=l.localeCompare(c,"en")),a!==0)return s.direction==="desc"?-a:a}return Se(i.index)&&Se(o.index)?i.index-o.index:0});return pd.set(e,t,n),n}var vd=new Ln;function wT(e,t){if(!t)return e;let r=vd.get(e,t);if(r)return r;let n=Ve(t,{resolveIdentifier(){throw new Error("Can't resolve identifier")}}),i=Se(n)?e.slice(n):e;return vd.set(e,t,i),i}var gd=new Ln;function CT(e,t){if(!t)return e;let r=gd.get(e,t);if(r)return r;let n=Ve(t,{resolveIdentifier(){throw new Error("Can't resolve identifier")}}),i=Se(n)?e.slice(0,n):e;return gd.set(e,t,i),i}var yd=new Ln;function TT(e,t){let r=yd.get(e,t);if(r)return r;let n=ET(t,e);if(n)throw n;let i=e.map(o=>{var a;let s={};for(let l of t){let c=(a=l.alias)!=null?a:FT(l);s[c]=Ve(l,{resolveIdentifier(u){let f=o[u];return ys.is(f)?f.read():f}})}return s});return yd.set(e,t,i),i}function ET(e,t){let r=[];for(let i of e)ht(r,i);if(r.length===0)return;let n=[];for(let i of t)for(let o of r){let a=i[o];if(!ys.is(a))continue;let s=a.preload();s&&n.push(s)}if(n.length!==0)return Promise.all(n)}function ht(e,t){if(t.type==="Identifier"&&e.push(t.name),t.type==="FunctionCall")for(let r of t.arguments)ht(e,r);if(t.type==="Case"){t.value&&ht(e,t.value);for(let r of t.conditions)ht(e,r.when),ht(e,r.then);t.else&&ht(e,t.else)}t.type==="UnaryOperation"&&ht(e,t.value),t.type==="BinaryOperation"&&(ht(e,t.left),ht(e,t.right)),t.type==="TypeCast"&&ht(e,t.value)}function fI(e){let t=O();t.current&&ss(t.current,e)?e=t.current:t.current=e;let r=e.from.data;return r=xT(r),r=bT(r,e.where),r=ST(r,e.orderBy),r=wT(r,e.offset),r=CT(r,e.limit),r=TT(r,e.select),r}function Ve(e,t){switch(e.type){case"Identifier":return t.resolveIdentifier(e.name);case"LiteralValue":return e.value;case"FunctionCall":return kT(e,t);case"Case":return RT(e,t);case"UnaryOperation":return _T(e,t);case"BinaryOperation":return PT(e,t);case"TypeCast":return IT(e,t);default:throw new Error(`Unsupported expression: ${JSON.stringify(e)}`)}}function kT(e,t){function r(n){let i=e.arguments[n];if(i)return Ve(i,t)}switch(e.functionName){case"CONTAINS":{let n=r(0),i=r(1);return te(n)&&te(i)?n.toLowerCase().includes(i.toLowerCase()):!1}case"STARTS_WITH":{let n=r(0),i=r(1);return te(n)&&te(i)?n.toLowerCase().startsWith(i.toLowerCase()):!1}case"ENDS_WITH":{let n=r(0),i=r(1);return te(n)&&te(i)?n.toLowerCase().endsWith(i.toLowerCase()):!1}default:throw new Error(`Unsupported function: ${e.functionName}`)}}function RT(e,t){let r=e.value&&Ve(e.value,t);for(let n of e.conditions){let i=Ve(n.when,t);if(e.value?Wa(i,r):i)return Ve(n.then,t)}if(e.else)return Ve(e.else,t)}function _T(e,t){let r=Ve(e.value,t);switch(e.operator){case"not":return!r;default:throw new Error(`Unsupported unary operation: ${e.operator}`)}}function PT(e,t){let r=Ve(e.left,t),n=Ve(e.right,t);switch(e.operator){case"and":return Boolean(r&&n);case"or":return Boolean(r||n);case"==":return Wa(r,n);case"!=":return!Wa(r,n);case"<":return Se(r)&&Se(n)||mt(r)&&mt(n)?r<n:!1;case"<=":return Se(r)&&Se(n)||mt(r)&&mt(n)?r<=n:!1;case">":return Se(r)&&Se(n)||mt(r)&&mt(n)?r>n:!1;case">=":return Se(r)&&Se(n)||mt(r)&&mt(n)?r>=n:!1;default:throw new Error(`Unsupported binary operation: ${e.operator}`)}}function IT(e,t){let r=Ve(e.value,t);switch(e.dataType){case"BOOLEAN":return Boolean(r);case"NUMBER":{if(Se(r)&&isFinite(r))return r;if(te(r)){let n=parseFloat(r);if(isFinite(n))return n}return 0}case"DATE":return r instanceof Date?r:!te(r)&&!Se(r)?void 0:new Date(r);case"STRING":return String(r);default:throw new Error(`Unsupported type cast: ${e.dataType}`)}}function FT(e){switch(e.type){case"Identifier":return e.name;default:throw new Error(`Can't stringify expression: ${JSON.stringify(e)}`)}}function Wa(e,t){return e==null&&t==null?!0:te(e)&&te(t)?e.toLowerCase()===t.toLowerCase():mt(e)&&mt(t)?e.getTime()===t.getTime():e===t}function dI(e){let r=Object.entries(e).filter(([,n])=>!(wn(n)||qe(n))).map(([n,i])=>({type:"BinaryOperation",operator:"==",left:{type:"TypeCast",value:{type:"Identifier",name:n},dataType:"STRING"},right:{type:"LiteralValue",value:String(i)}}));return r.length===0?{type:"LiteralValue",value:!1}:r.reduce((n,i)=>({type:"BinaryOperation",operator:"and",left:n,right:i}))}function MT(e,t){return`${e}-${t}`}function LT(e,t){let n=e.indexOf(t)+1;n>=e.length&&(n=0);let i=e[n];return Ce(i!==void 0,"nextVariant should be defined"),i}function OT(e,t){if(e){if(t){let r=e[t];if(r)return r}return e.default}}function xd(e,t,r){let{hover:n,pressed:i}=e||{};if(i&&r)return"pressed";if(n&&t)return"hover"}function AT(e,t){let r=t[e];return r||`framer-v-${e}`}function bd(e,t,r){return e&&r.has(e)?e:t}var VT=Symbol("cycle");function mI({variant:e,defaultVariant:t,transitions:r,enabledGestures:n,cycleOrder:i=[],variantProps:o={},variantClassNames:a={}}){let s=as(),l=vt(()=>new Set(i)),c=h.useRef({isHovered:!1,isPressed:!1,baseVariant:bd(e,t,l),lastVariant:e,gestureVariant:void 0,defaultVariant:t,enabledGestures:n,cycleOrder:i,transitions:r}),u=h.useCallback(S=>{let{isHovered:C,isPressed:w,enabledGestures:T,defaultVariant:E}=c.current,R=bd(S,E,l),_=xd(T?.[R],C,w),F=_?MT(R,_):void 0;return[R,F]},[l]),f=h.useCallback(({isHovered:S,isPressed:C})=>{S!==void 0&&(c.current.isHovered=S),C!==void 0&&(c.current.isPressed=C);let{baseVariant:w,gestureVariant:T,defaultVariant:E}=c.current,[R,_]=u(w);(R!==w||_!==T)&&(c.current.baseVariant=R||E,c.current.gestureVariant=_,s())},[u,s]),d=h.useCallback(S=>{let{defaultVariant:C,cycleOrder:w,baseVariant:T,gestureVariant:E}=c.current,R=S===VT?LT(w||[],T||C):S,[_,F]=u(R);(_!==T||F!==E)&&(c.current.baseVariant=_||C,c.current.gestureVariant=F,s())},[u,s]);if(e!==c.current.lastVariant){let[S,C]=u(e);c.current.lastVariant=S,(S!==c.current.baseVariant||C!==c.current.gestureVariant)&&(c.current.baseVariant=S,c.current.gestureVariant=C)}let{baseVariant:m,gestureVariant:p,defaultVariant:g,enabledGestures:x,isHovered:v,isPressed:b}=c.current,y=vT(c.current.baseVariant,c.current.gestureVariant,o);return h.useMemo(()=>{let S=[];return m!==g&&S.push(m),p&&S.push(p),{variants:S,baseVariant:m,gestureVariant:p,transition:OT(c.current.transitions,m),setVariant:d,setGestureState:f,addVariantProps:y,classNames:Mn(AT(m,a),xd(x?.[m],v,b))}},[m,p,v,b,y,d,g,x,f,a])}var gI=be(gt(),1);var DT=be(gt(),1);function BT(e,t){return{id:`id${t}g${Tn.hash(e)}`,angle:e.angle-90,stops:Fn(e).map(r=>({color:r.value,alpha:ls.getAlpha(r.value)*e.alpha,position:r.position}))}}function zT(e,t){return{id:`id${t}g${En.hash(e)}`,widthFactor:e.widthFactor,heightFactor:e.heightFactor,centerAnchorX:e.centerAnchorX,centerAnchorY:e.centerAnchorY,stops:Fn(e).map(r=>({color:r.value,alpha:ls.getAlpha(r.value)*e.alpha,position:r.position}))}}function HT(e,t,r){var n;if(e=de.get(e,"#09F"),!Et.isImageObject(e)||!e.pixelWidth||!e.pixelHeight)return;let i=e.pixelWidth,o=e.pixelHeight,a,{fit:s}=e;if(s==="fill"||s==="fit"||!s){let c=1,u=1,f=0,d=0,m=i/o,p=t.height*m,g=t.width/m,x=p/t.width,v=g/t.height;(s==="fill"||!s?v>x:v<x)?(u=v,d=(1-v)/2):(c=x,f=(1-x)/2),a=`translate(${f}, ${d}) scale(${c}, ${u})`}return{id:`id${r}g-fillImage`,path:(n=e.src)!=null?n:"",transform:a}}var NT=h.createContext(void 0),$T=()=>h.useContext(NT),UT="framer/asset-reference,";function WT(e){return e.startsWith(`data:${UT}`)}function jT(e,t){var r;if(/^\w+:/.test(e)&&!WT(e))return e;typeof t!="number"?t=void 0:t<=512?t=512:t<=1024?t=1024:t<=2048?t=2048:t=4096;let n=Q.current()==="EXPORT";return(r=sr.assetResolver(e,{pixelSize:t,isExport:n}))!=null?r:""}var GT=class extends ue{render(){let{id:e,path:t,transform:r}=this.props,n=jT(t);return h.createElement("pattern",{id:e,width:"100%",height:"100%",patternContentUnits:"objectBoundingBox"},h.createElement("image",{key:n,width:1,height:1,xlinkHref:n,preserveAspectRatio:"none",transform:r}))}},Sd=kt(),XT=class{constructor(e,t,r,n,i=0){this.id=e,this.svg=t,this.innerHTML=r,this.viewBox=n,this.count=i}},YT=class{constructor(){this.entries=new Map}debugGetEntries(){return this.entries}subscribe(e,t,r){if(!e||e==="")return"";let n=this.entries.get(e);if(!n){r||(r="svg"+String(ch(e))+"_"+String(e.length));let i=e,o,a=KT(e);a&&(t&&qT(a,r),a.id=r,o=eE(a),i=a.outerHTML),n=this.createDOMElementFor(i,r,o),this.entries.set(e,n)}return n.count+=1,n.innerHTML}getViewBox(e){if(!e||e==="")return;let t=this.entries.get(e);return t?.viewBox}unsubscribe(e){if(!e||e==="")return;let t=this.entries.get(e);t&&(t.count-=1,!(t.count>0)&&setTimeout(()=>this.maybeRemoveEntry(e),5e3))}maybeRemoveEntry(e){let t=this.entries.get(e);t&&(t.count>0||(this.entries.delete(e),this.removeDOMElement(t)))}removeDOMElement(e){let t="container_"+e.id;if(Sd){let r=document?.querySelector("#"+t);r?.remove()}}createDOMElementFor(e,t,r){let n="container_"+t;if(Sd){let s=document.querySelector("#svg-templates");if(s||(s=document.createElement("div"),s.id="svg-templates",s.style.position="absolute",s.style.top="0",s.style.left="0",s.style.width="0",s.style.height="0",s.style.overflow="hidden",document.body.appendChild(s)),!document.querySelector("#"+n)){let l=document.createElement("div");l.id=n,l.innerHTML=e,l.firstElementChild&&(l.firstElementChild.id=t),s.appendChild(l)}}let i=r?`0 0 ${r.width} ${r.height}`:void 0,a=`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100%; height: 100%"${i?` viewBox="${i}"`:""}><use href="#${t}"></use></svg>`;return new XT(t,e,a,i)}clear(){this.entries.clear()}generateTemplates(){let e=[],t="position: absolute; overflow: hidden; top: 0; left: 0; width: 0; height: 0";e.push(`<div id="svg-templates" style="${t}">`);for(let r of this.entries.values()){let n="container_"+r.id;e.push(`  <div id="${n}">`),e.push(`    ${r.svg}`),e.push("  </div>")}return e.push("</div>"),e.join(`
`)}},xn=new YT;function KT(e){if(typeof DOMParser>"u"){console.warn("unable to find DOMParser");return}try{let n=new DOMParser().parseFromString(e,"text/html").getElementsByTagName("svg")[0];if(!n)throw Error("no svg element found");return n}catch{return}}function qT(e,t){let r=ZT(t);Gh(e,r)}function ZT(e){return e.replace(/[^a-z0-9\-_:.]|^[^a-z]+/gi,"")}function Gh(e,t){JT(e,t),Array.from(e.children).forEach(n=>{Gh(n,t)})}function JT(e,t){e.getAttributeNames().forEach(n=>{let i=e.getAttribute(n);if(!i)return;if(n==="id"&&e.setAttribute(n,`${t}_${i}`),n==="href"||n==="xlink:href"){let[a,s]=i.split("#");if(a)return;e.setAttribute(n,`#${t}_${s}`);return}let o="url(#";if(i.includes(o)){let a=i.replace(o,`${o}${t}_`);e.setAttribute(n,a)}})}var QT={cm:96/2.54,mm:96/2.54/10,Q:96/2.54/40,in:96,pc:96/6,pt:96/72,px:1,em:16,ex:8,ch:8,rem:16};function wd(e){var t;if(!e)return;let r=/(-?[0-9.]+)([a-z%]*)/.exec(e);if(!(r?.[1]===void 0||r?.[2]===void 0)&&!((t=r[2])!=null&&t.startsWith("%")))return Math.round(parseFloat(r[1])*(QT[r[2]]||1))}function eE(e){let t=wd(e.getAttribute("width")),r=wd(e.getAttribute("height"));if(!(typeof t!="number"||typeof r!="number")&&!(t<=0||r<=0))return{width:t,height:r}}function wI(e){let t=Br(),r="svg"+h.useId(),n=Rt(e),i=h.useRef(null),o=$T();return In(e,i),h.createElement(oE,{...e,innerRef:i,parentSize:t,ariaId:r,layoutId:n,providedWindow:o})}var tE=5e4;function rE(e){return e.indexOf("image")>=0}function nE(e){return e.indexOf("var(--")>=0}function iE(e){return!!(e.borderRadius||e.borderBottomLeftRadius||e.borderBottomRightRadius||e.borderTopLeftRadius||e.borderTopRightRadius)}function Cd(e,t){var r,n,i;let o=e.current;if(!o)return;let a=(r=t.providedWindow)!=null?r:se,s=o.firstElementChild;if(!s||!(s instanceof a.SVGSVGElement))return;if(!s.getAttribute("viewBox")){let p=xn.getViewBox(t.svg);p&&s.setAttribute("viewBox",p)}let{withExternalLayout:l,parentSize:c}=t;if(!l&&Dr(t)&&c!==1&&c!==2)return;let{intrinsicWidth:f,intrinsicHeight:d,_constraints:m}=t;((n=s.viewBox.baseVal)==null?void 0:n.width)===0&&((i=s.viewBox.baseVal)==null?void 0:i.height)===0&&D(f)&&D(d)&&s.setAttribute("viewBox",`0 0 ${f} ${d}`),m&&m.aspectRatio?s.setAttribute("preserveAspectRatio",""):s.setAttribute("preserveAspectRatio","none"),s.setAttribute("width","100%"),s.setAttribute("height","100%")}var oE=(()=>{var e;return e=class extends Ht{constructor(){super(...arguments),this.container=h.createRef(),this.svgElement=null,this.setSVGElement=t=>{this.svgElement=t,this.setLayerElement(t)},this.previouslyRenderedSVG=""}static frame(t){return Vr(t,t.parentSize||0)}get frame(){return Vr(this.props,this.props.parentSize||0)}componentDidMount(){this.props.svgContentId||Cd(this.container,this.props)}componentWillUnmount(){xn.unsubscribe(this.previouslyRenderedSVG),this.previouslyRenderedSVG=""}componentDidUpdate(t){if(super.componentDidUpdate(t),this.props.svgContentId)return;let{fill:r}=this.props;Et.isImageObject(r)&&Et.isImageObject(t.fill)&&r.src!==t.fill.src&&lh(this.svgElement,"fill",null,!1),Cd(this.container,this.props)}collectLayout(t,r){if(this.props.withExternalLayout){r.width="100%",r.height="100%",r.aspectRatio="inherit";return}let n=this.frame,{rotation:i,intrinsicWidth:o,intrinsicHeight:a,width:s,height:l}=this.props,c=de.getNumber(i);if(t.opacity=D(this.props.opacity)?this.props.opacity:1,Q.hasRestrictions()&&n){Object.assign(t,{transform:`translate(${n.x}px, ${n.y}px) rotate(${c.toFixed(4)}deg)`,width:`${n.width}px`,height:`${n.height}px`}),Dr(this.props)&&(t.position="absolute");let u=n.width/(o||1),f=n.height/(a||1);r.transformOrigin="top left";let{zoom:d,target:m}=Or;if(m==="EXPORT"){let p=d>1?d:1;r.transform=`scale(${u*p}, ${f*p})`,r.zoom=1/p}else r.transform=`scale(${u}, ${f})`;o&&a&&(r.width=o,r.height=a)}else{let{left:u,right:f,top:d,bottom:m}=this.props;Object.assign(t,{left:u,right:f,top:d,bottom:m,width:s,height:l,rotate:c}),Object.assign(r,{left:0,top:0,bottom:0,right:0,position:"absolute"})}}render(){var t,r;DT.default.env.NODE_ENV!=="production"&&se.perf&&se.perf.nodeRender();let{id:n,visible:i,style:o,fill:a,svg:s,intrinsicHeight:l,intrinsicWidth:c,ariaId:u="",title:f,description:d,layoutId:m,className:p,variants:g,transition:x,withExternalLayout:v,innerRef:b,svgContentId:y}=this.props;if(!v&&(!i||!n))return null;let S=(t=n??m)!=null?t:"svg";$t();let C=this.frame,w=C||{width:c||100,height:l||100},T={...o,imageRendering:"pixelated",flexShrink:0},E={};this.collectLayout(T,E),K1(this.props,T),Di(this.props,T),Ht.applyWillChange(this.props,T,!1);let R=null;if(typeof a=="string"||z.isColorObject(a)){let j=z.isColorObject(a)?a.initialValue||z.toRgbString(a):a;T.fill=j,T.color=j}else if(Tn.isLinearGradient(a)){let j=a,V=`${encodeURI(n||"")}g${Tn.hash(j)}`;T.fill=`url(#${V})`;let Z=BT(j,S);R=h.createElement("svg",{ref:this.setSVGElement,xmlns:"http://www.w3.org/2000/svg",width:"100%",height:"100%",style:{position:"absolute"}},h.createElement("linearGradient",{id:V,gradientTransform:`rotate(${Z.angle}, 0.5, 0.5)`},Z.stops.map((Y,$)=>h.createElement("stop",{key:$,offset:Y.position,stopColor:Y.color,stopOpacity:Y.alpha}))))}else if(En.isRadialGradient(a)){let j=a,V=`${encodeURI(n||"")}g${En.hash(j)}`;T.fill=`url(#${V})`;let Z=zT(j,S);R=h.createElement("svg",{ref:this.setSVGElement,xmlns:"http://www.w3.org/2000/svg",width:"100%",height:"100%",style:{position:"absolute"}},h.createElement("radialGradient",{id:V,cy:j.centerAnchorY,cx:j.centerAnchorX,r:j.widthFactor},Z.stops.map((Y,$)=>h.createElement("stop",{key:$,offset:Y.position,stopColor:Y.color,stopOpacity:Y.alpha}))))}else if(Et.isImageObject(a)){let j=HT(a,w,S);j&&(T.fill=`url(#${j.id})`,R=h.createElement("svg",{ref:this.setSVGElement,xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:"100%",height:"100%",style:{position:"absolute"}},h.createElement("defs",null,h.createElement(GT,{...j}))))}let _={"data-framer-component-type":"SVG"},F=!C;F&&Object.assign(_,ns(this.props.center));let B=!R&&!T.fill&&!T.background&&!T.backgroundImage&&s.length<tE&&!rE(s)&&!nE(s),L=null;if(B)T.backgroundSize="100% 100%",T.backgroundImage=`url('data:image/svg+xml;utf8,${encodeURIComponent(s)}')`,xn.unsubscribe(this.previouslyRenderedSVG),this.previouslyRenderedSVG="";else{let j=y?"svg"+y:null,V=xn.subscribe(s,!y,j);xn.unsubscribe(this.previouslyRenderedSVG),this.previouslyRenderedSVG=s,iE(T)&&(T.overflow="hidden"),L=h.createElement(h.Fragment,null,R,h.createElement("div",{key:Et.isImageObject(a)?a.src:"",className:"svgContainer",style:E,ref:this.container,dangerouslySetInnerHTML:{__html:V}}))}let P=ke[(r=this.props.as)!=null?r:"div"],{href:I,target:U,rel:k,onClick:A}=this.props,X=u+"desc";return h.createElement(P,{..._,layoutId:m,transformTemplate:F?Pn(this.props.center):void 0,id:n,ref:b,style:T,className:p,variants:g,transition:x,tabIndex:this.props.tabIndex,role:f||d?"img":void 0,"aria-label":f,"aria-describedby":d?X:void 0,href:I,target:U,rel:k,onClick:A},L,d&&h.createElement("div",{style:aE,id:X},d))}},e.supportsConstraints=!0,e.defaultSVGProps={left:void 0,right:void 0,top:void 0,bottom:void 0,style:void 0,_constraints:{enabled:!0,aspectRatio:null},parentSize:0,rotation:0,visible:!0,svg:"",shadows:[]},e.defaultProps={...Ht.defaultProps,...e.defaultSVGProps},e})(),aE={clip:"rect(1px, 1px, 1px, 1px)",clipPath:"inset(50%)",height:"1px",width:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute"},sE=be(gt(),1),lE=/[&<>'"]/g,cE=e=>e.replace(lE,t=>({"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"})[t]||t),uE=/(<([a-z]+)(?:\s+(?!href[\s=])[^=\s]+=(?:'[^']*'|"[^"]*"))*)(?:(\s+href\s*=)(?:'([^']*)'|"([^"]*)"))?((?:\s+[^=\s]+=(?:'[^']*'|"[^"]*"))*>)/gi;function Xh(e,t,r,n){return e.replace(uE,(i,o,a,s,l,c,u)=>{var f,d,m;if(a.toLowerCase()!=="a")return i;let p=l||c,g=hs(p.replace(/&amp;/g,"&"));if(!g||!g.target)return i;let x=t(g.target);if(!lf(x)||!lf(r))return i;let v=x.path,b=r.path;if(!v||!b)return i;let y=` data-framer-page-link-target="${g.target}"`,S=Oi(x,(f=g.element)!=null?f:void 0);S&&(y+=` data-framer-page-link-element="${g.element}"`);let C=zi(p);if(!C||te(C))return i;ps(r,C,n)&&(y+=" data-framer-page-link-current");let w=v,T=Object.assign({},n,(d=g.collectionItem)==null?void 0:d.pathVariables);if(Object.keys(T).length>0&&(w=w.replace(Bh,(E,R)=>""+T[R])),(m=g.collectionItem)!=null&&m.pathVariables){let E=new URLSearchParams(g.collectionItem.pathVariables);y+=` data-framer-page-link-path-variables="${E}"`}return w=Id(b,w),o+s+`"${cE(w+(S?`#${S}`:""))}"`+y+u})}var fE=be(gt(),1);var dE=["sans-serif","serif","monospace","display","handwriting"],_r="CUSTOM;";function hE(e,t){if(!t)return e.substring(0,e.lastIndexOf("."));let r=t.font.preferredFamily===""?t.font.fontFamily:t.font.preferredFamily,n=t.font.preferredSubFamily===""?t.font.fontSubFamily:t.font.preferredSubFamily;return`${r} ${n}`}var mE=class{constructor(){this.name="custom",this.typefaces=[],this.byFamily=new Map,this.assetsByFamily=new Map}importFonts(e){this.typefaces.length=0,this.byFamily.clear(),this.assetsByFamily.clear();let t=[];return e.forEach(r=>{var n;if(!this.isValidCustomFontAsset(r))return;let i=hE(r.name,r.properties),o=this.createTypeface(i),a={typeface:o,selector:`${_r}${i}`,variant:this.inferVariantName(i),postscriptName:(n=r.properties)==null?void 0:n.font.postscriptName,file:r.url};o.fonts.push(a),o.owner=r.ownerType==="team"?"team":"project",this.assetsByFamily.set(i,r),t.push(...o.fonts)}),t}isValidCustomFontAsset(e){var t;return!e.mimeType.startsWith("font/")||((t=e.properties)==null?void 0:t.kind)!=="font"||!e.properties.font?!1:"fontFamily"in e.properties.font}inferVariantName(e){let t=["thin","ultra light","extra light","light","normal","medium","semi bold","bold","extra bold","black"],r=[...t.map(a=>`${a} italic`),...t],n=e.toLowerCase(),i=[...n.split(" "),...n.split("-"),...n.split("_")],o=r.find(a=>i.includes(a)||i.includes(a.replace(/\s+/g,"")));return o?o.replace(/(^\w|\s\w)/g,a=>a.toUpperCase()):"Regular"}createTypeface(e){let t=this.byFamily.get(e);if(t)return t;let r={source:this.name,family:e,fonts:[]};return this.addTypeface(r),r}addTypeface(e){this.typefaces.push(e),this.byFamily.set(e.family,e)}parseSelector(e){if(!e.startsWith(_r))return null;let t=e.split(_r);return t[1]===void 0?null:{source:"custom",family:t[1]}}getFontBySelector(e,t=!0){let r=this.parseSelector(e);if(r&&!(!t&&!this.byFamily.get(r.family)))return this.getTypefaceByFamily(r.family).fonts[0]}getTypefaceByFamily(e){let t=this.byFamily.get(e);if(t)return t;let r={source:"custom",family:e,fonts:[]};return r.fonts.push({selector:`${_r}${e}`,variant:this.inferVariantName(e),typeface:r}),r}};function Yh(e){if(e==="regular")return{style:"normal",weight:400};let t=/([0-9]*)([a-z]*)/.exec(e);if(!t)return null;let r=parseInt(t[1]||"400"),n=t[2]||"normal";return{weight:r,style:n}}var ja="GF;",pE=class{constructor(){this.name="google",this.typefaces=[],this.byFamily=new Map}getTypefaceByFamily(e){var t;return(t=this.byFamily.get(e))!=null?t:null}parseSelector(e){if(!e.startsWith(ja))return null;let t=e.split("-");if(t.length!==2)return null;let[r,n]=t;return!r||!n?null:{family:r.replace(ja,""),variant:n,source:this.name}}createTypeface(e){let t={family:e,fonts:[],source:this.name};return this.addTypeface(t),t}addTypeface(e){this.typefaces.push(e),this.byFamily.set(e.family,e)}importFonts(e){this.typefaces.length=0,this.byFamily.clear();let t=[];return e.forEach(r=>r.variants.forEach(n=>{var i;let o=r.family,a=this.getTypefaceByFamily(o);a||(a=this.createTypeface(o));let s=Yh(n)||{},{weight:l,style:c}=s,u=`GF;${o}-${n}`,f={typeface:a,variant:n,selector:u,weight:l,style:c,category:vE(r.category),file:(i=r.files[n])==null?void 0:i.replace("http://","https://")};a.fonts.push(f),t.push(f)})),t}};function vE(e){if(dE.includes(e))return e}var gE=be(g0(),1),Td=5e3,yE=3,Kh=class extends Error{constructor(e){super(e),this.name="FontLoadingError"}},_a=new Map,Pa=new Map,xE=(e,t)=>qh(e,t);async function qh(e,t,r=0){let{family:n,url:i,stretch:o,unicodeRange:a}=e,s=e.weight||500,l=e.style||"normal",c=`${n}-${l}-${s}-${i}`;if(!_a.has(c)||r>0){let u=new FontFace(n,`url(${i})`,{weight:te(s)?s:s?.toString(),style:l,stretch:o,unicodeRange:a}),f=u.load().then(()=>(t.fonts.add(u),Zh(n,l,s))).catch(d=>{if(d.name!=="NetworkError")throw d;if(r<yE)return qh(e,t,r+1);throw new Kh(`Font loading failed after ${r} retries due to network error: ${JSON.stringify({family:n,style:l,weight:s,url:i,stretch:o,unicodeRange:a})}`)});_a.set(c,f)}await _a.get(c)}async function Zh(e,t,r){let n=`${e}-${t}-${r}`;if(!Pa.has(n)){let o=new gE.default(e,{style:t,weight:r}).load(null,Td);Pa.set(n,o)}try{await Pa.get(n)}catch{throw new Kh(`Failed to check if font is ready (${Td}ms timeout exceeded): ${JSON.stringify({family:e,style:t,weight:r})}`)}}var bE={Arial:{Regular:{selector:"Arial",weight:void 0},Black:{selector:"Arial-Black",weight:void 0},Narrow:{selector:"Arial Narrow",weight:void 0},"Rounded Bold":{selector:"Arial Rounded MT Bold",weight:void 0}},Avenir:{Book:{selector:"Avenir",weight:void 0},Light:{selector:"Avenir-Light",weight:void 0},Medium:{selector:"Avenir-Medium",weight:void 0},Heavy:{selector:"Avenir-Heavy",weight:void 0},Black:{selector:"Avenir-Black",weight:void 0}},"Avenir Next":{Regular:{selector:"Avenir Next",weight:void 0},"Ultra Light":{selector:"AvenirNext-UltraLight",weight:void 0},Medium:{selector:"AvenirNext-Medium",weight:void 0},"Demi Bold":{selector:"AvenirNext-DemiBold",weight:void 0},Heavy:{selector:"AvenirNext-Heavy",weight:void 0}},"Avenir Next Condensed":{Regular:{selector:"Avenir Next Condensed",weight:void 0},"Ultra Light":{selector:"AvenirNextCondensed-UltraLight",weight:void 0},Medium:{selector:"AvenirNextCondensed-Medium",weight:void 0},"Demi Bold":{selector:"AvenirNextCondensed-DemiBold",weight:void 0},Heavy:{selector:"AvenirNextCondensed-Heavy",weight:void 0}},Baskerville:{Regular:{selector:"Baskerville",weight:void 0},"Semi Bold":{selector:"Baskerville-SemiBold",weight:void 0}},"Bodoni 72":{Book:{selector:"Bodoni 72",weight:void 0},Oldstyle:{selector:"Bodoni 72 Oldstyle",weight:void 0},Smallcaps:{selector:"Bodoni 72 Smallcaps",weight:void 0}},Courier:{Regular:{selector:"Courier",weight:void 0}},"Courier New":{Regular:{selector:"Courier New",weight:void 0}},Futura:{Medium:{selector:"Futura",weight:void 0},Condensed:{selector:"Futura-CondensedMedium",weight:void 0},"Condensed ExtraBold":{selector:"Futura-CondensedExtraBold",weight:void 0}},Georgia:{Regular:{selector:"Georgia",weight:void 0}},"Gill Sans":{Regular:{selector:"Gill Sans",weight:void 0},Light:{selector:"GillSans-Light",weight:void 0},SemiBold:{selector:"GillSans-SemiBold",weight:void 0},UltraBold:{selector:"GillSans-UltraBold",weight:void 0}},Helvetica:{Regular:{selector:"Helvetica",weight:void 0},Light:{selector:"Helvetica-Light",weight:void 0},Bold:{selector:"Helvetica-Bold",weight:void 0},Oblique:{selector:"Helvetica-Oblique",weight:void 0},"Light Oblique":{selector:"Helvetica-LightOblique",weight:void 0},"Bold Oblique":{selector:"Helvetica-BoldOblique",weight:void 0}},"Helvetica Neue":{Regular:{selector:"Helvetica Neue",weight:void 0},UltraLight:{selector:"HelveticaNeue-UltraLight",weight:void 0},Thin:{selector:"HelveticaNeue-Thin",weight:void 0},Light:{selector:"HelveticaNeue-Light",weight:void 0},Medium:{selector:"HelveticaNeue-Medium",weight:void 0},Bold:{selector:"HelveticaNeue-Bold",weight:void 0},Italic:{selector:"HelveticaNeue-Italic",weight:void 0},"UltraLight Italic":{selector:"HelveticaNeue-UltraLightItalic",weight:void 0},"Thin Italic":{selector:"HelveticaNeue-ThinItalic",weight:void 0},"Light Italic":{selector:"HelveticaNeue-LightItalic",weight:void 0},"Medium Italic":{selector:"HelveticaNeue-MediumItalic",weight:void 0},"Bold Italic":{selector:"HelveticaNeue-BoldItalic",weight:void 0},"Condensed Bold":{selector:"HelveticaNeue-CondensedBold",weight:void 0},"Condensed Black":{selector:"HelveticaNeue-CondensedBlack",weight:void 0}},"Hoefler Text":{Regular:{selector:"Hoefler Text",weight:void 0}},Impact:{Regular:{selector:"Impact",weight:void 0}},"Lucida Grande":{Regular:{selector:"Lucida Grande",weight:void 0}},Menlo:{Regular:{selector:"Menlo",weight:void 0}},Monaco:{Regular:{selector:"Monaco",weight:void 0}},Optima:{Regular:{selector:"Optima",weight:void 0},ExtraBlack:{selector:"Optima-ExtraBlack",weight:void 0}},Palatino:{Regular:{selector:"Palatino",weight:void 0}},"SF Pro Display":{Regular:{selector:"__SF-UI-Display-Regular__",weight:400},Ultralight:{selector:"__SF-UI-Display-Ultralight__",weight:100},Thin:{selector:"__SF-UI-Display-Thin__",weight:200},Light:{selector:"__SF-UI-Display-Light__",weight:300},Medium:{selector:"__SF-UI-Display-Medium__",weight:500},Semibold:{selector:"__SF-UI-Display-Semibold__",weight:600},Bold:{selector:"__SF-UI-Display-Bold__",weight:700},Heavy:{selector:"__SF-UI-Display-Heavy__",weight:800},Black:{selector:"__SF-UI-Display-Black__",weight:900},Italic:{selector:"__SF-UI-Display-Italic__",weight:400},"Ultralight Italic":{selector:"__SF-UI-Display-Ultralight-Italic__",weight:100},"Thin Italic":{selector:"__SF-UI-Display-Thin-Italic__",weight:200},"Light Italic":{selector:"__SF-UI-Display-Light-Italic__",weight:300},"Medium Italic":{selector:"__SF-UI-Display-Medium-Italic__",weight:500},"Semibold Italic":{selector:"__SF-UI-Display-Semibold-Italic__",weight:600},"Bold Italic":{selector:"__SF-UI-Display-Bold-Italic__",weight:700},"Heavy Italic":{selector:"__SF-UI-Display-Heavy-Italic__",weight:800},"Black Italic":{selector:"__SF-UI-Display-Black-Italic__",weight:900}},"SF Pro Display Condensed":{Regular:{selector:"__SF-UI-Display-Condensed-Regular__",weight:400},Ultralight:{selector:"__SF-UI-Display-Condensed-Ultralight__",weight:100},Thin:{selector:"__SF-UI-Display-Condensed-Thin__",weight:200},Light:{selector:"__SF-UI-Display-Condensed-Light__",weight:300},Medium:{selector:"__SF-UI-Display-Condensed-Medium__",weight:500},Semibold:{selector:"__SF-UI-Display-Condensed-Semibold__",weight:600},Bold:{selector:"__SF-UI-Display-Condensed-Bold__",weight:700},Heavy:{selector:"__SF-UI-Display-Condensed-Heavy__",weight:800},Black:{selector:"__SF-UI-Display-Condensed-Black__",weight:900}},"SF Pro Text":{Regular:{selector:"__SF-UI-Text-Regular__",weight:400},Light:{selector:"__SF-UI-Text-Light__",weight:200},Medium:{selector:"__SF-UI-Text-Medium__",weight:500},Semibold:{selector:"__SF-UI-Text-Semibold__",weight:600},Bold:{selector:"__SF-UI-Text-Bold__",weight:700},Heavy:{selector:"__SF-UI-Text-Heavy__",weight:800},Italic:{selector:"__SF-UI-Text-Italic__",weight:400},"Light Italic":{selector:"__SF-UI-Text-Light-Italic__",weight:200},"Medium Italic":{selector:"__SF-UI-Text-Medium-Italic__",weight:500},"Semibold Italic":{selector:"__SF-UI-Text-Semibold-Italic__",weight:600},"Bold Italic":{selector:"__SF-UI-Text-Bold-Italic__",weight:700},"Heavy Italic":{selector:"__SF-UI-Text-Heavy-Italic__",weight:800}},"SF Pro Text Condensed":{Regular:{selector:"__SF-UI-Text-Condensed-Regular__",weight:400},Light:{selector:"__SF-UI-Text-Condensed-Light__",weight:200},Medium:{selector:"__SF-UI-Text-Condensed-Medium__",weight:500},Semibold:{selector:"__SF-UI-Text-Condensed-Semibold__",weight:600},Bold:{selector:"__SF-UI-Text-Condensed-Bold__",weight:700},Heavy:{selector:"__SF-UI-Text-Condensed-Heavy__",weight:800}},Tahoma:{Regular:{selector:"Tahoma",weight:void 0}},Times:{Regular:{selector:"Times",weight:void 0}},"Times New Roman":{Regular:{selector:"Times New Roman",weight:void 0}},Trebuchet:{Regular:{selector:"Trebuchet MS",weight:void 0}},Verdana:{Regular:{selector:"Verdana",weight:void 0}}},SE={"__SF-Compact-Display-Regular__":"SFCompactDisplay-Regular|.SFCompactDisplay-Regular","__SF-Compact-Display-Ultralight__":"SFCompactDisplay-Ultralight|.SFCompactDisplay-Ultralight","__SF-Compact-Display-Thin__":"SFCompactDisplay-Thin|.SFCompactDisplay-Thin","__SF-Compact-Display-Light__":"SFCompactDisplay-Light|.SFCompactDisplay-Light","__SF-Compact-Display-Medium__":"SFCompactDisplay-Medium|.SFCompactDisplay-Medium","__SF-Compact-Display-Semibold__":"SFCompactDisplay-Semibold|.SFCompactDisplay-Semibold","__SF-Compact-Display-Heavy__":"SFCompactDisplay-Heavy|.SFCompactDisplay-Heavy","__SF-Compact-Display-Black__":"SFCompactDisplay-Black|.SFCompactDisplay-Black","__SF-Compact-Display-Bold__":"SFCompactDisplay-Bold|.SFCompactDisplay-Bold","__SF-UI-Text-Regular__":".SFNSText|SFProText-Regular|SFUIText-Regular|.SFUIText","__SF-UI-Text-Light__":".SFNSText-Light|SFProText-Light|SFUIText-Light|.SFUIText-Light","__SF-UI-Text-Medium__":".SFNSText-Medium|SFProText-Medium|SFUIText-Medium|.SFUIText-Medium","__SF-UI-Text-Semibold__":".SFNSText-Semibold|SFProText-Semibold|SFUIText-Semibold|.SFUIText-Semibold","__SF-UI-Text-Bold__":".SFNSText-Bold|SFProText-Bold|SFUIText-Bold|.SFUIText-Bold","__SF-UI-Text-Heavy__":".SFNSText-Heavy|SFProText-Heavy|.SFUIText-Heavy","__SF-UI-Text-Italic__":".SFNSText-Italic|SFProText-Italic|SFUIText-Italic|.SFUIText-Italic","__SF-UI-Text-Light-Italic__":".SFNSText-LightItalic|SFProText-LightItalic|SFUIText-LightItalic|.SFUIText-LightItalic","__SF-UI-Text-Medium-Italic__":".SFNSText-MediumItalic|SFProText-MediumItalic|SFUIText-MediumItalic|.SFUIText-MediumItalic","__SF-UI-Text-Semibold-Italic__":".SFNSText-SemiboldItalic|SFProText-SemiboldItalic|SFUIText-SemiboldItalic|.SFUIText-SemiboldItalic","__SF-UI-Text-Bold-Italic__":".SFNSText-BoldItalic|SFProText-BoldItalic|SFUIText-BoldItalic|.SFUIText-BoldItalic","__SF-UI-Text-Heavy-Italic__":".SFNSText-HeavyItalic|SFProText-HeavyItalic|.SFUIText-HeavyItalic","__SF-Compact-Text-Regular__":"SFCompactText-Regular|.SFCompactText-Regular","__SF-Compact-Text-Light__":"SFCompactText-Light|.SFCompactText-Light","__SF-Compact-Text-Medium__":"SFCompactText-Medium|.SFCompactText-Medium","__SF-Compact-Text-Semibold__":"SFCompactText-Semibold|.SFCompactText-Semibold","__SF-Compact-Text-Bold__":"SFCompactText-Bold|.SFCompactText-Bold","__SF-Compact-Text-Heavy__":"SFCompactText-Heavy|.SFCompactText-Heavy","__SF-Compact-Text-Italic__":"SFCompactText-Italic|.SFCompactText-Italic","__SF-Compact-Text-Light-Italic__":"SFCompactText-LightItalic|.SFCompactText-LightItalic","__SF-Compact-Text-Medium-Italic__":"SFCompactText-MediumItalic|.SFCompactText-MediumItalic","__SF-Compact-Text-Semibold-Italic__":"SFCompactText-SemiboldItalic|.SFCompactText-SemiboldItalic","__SF-Compact-Text-Bold-Italic__":"SFCompactText-BoldItalic|.SFCompactText-BoldItalic","__SF-Compact-Text-Heavy-Italic__":"SFCompactText-HeavyItalic|.SFCompactText-HeavyItalic","__SF-UI-Display-Condensed-Regular__":".SFNSDisplayCondensed-Regular|SFUIDisplayCondensed-Regular|.SFUIDisplayCondensed-Regular","__SF-UI-Display-Condensed-Ultralight__":".SFNSDisplayCondensed-Ultralight|SFUIDisplayCondensed-Ultralight|.SFUIDisplayCondensed-Ultralight","__SF-UI-Display-Condensed-Thin__":".SFNSDisplayCondensed-Thin|SFUIDisplayCondensed-Thin|.SFUIDisplayCondensed-Thin","__SF-UI-Display-Condensed-Light__":".SFNSDisplayCondensed-Light|SFUIDisplayCondensed-Light|.SFUIDisplayCondensed-Light","__SF-UI-Display-Condensed-Medium__":".SFNSDisplayCondensed-Medium|SFUIDisplayCondensed-Medium|.SFUIDisplayCondensed-Medium","__SF-UI-Display-Condensed-Semibold__":".SFNSDisplayCondensed-Semibold|SFUIDisplayCondensed-Semibold|.SFUIDisplayCondensed-Semibold","__SF-UI-Display-Condensed-Bold__":".SFNSDisplayCondensed-Bold|SFUIDisplayCondensed-Bold|.SFUIDisplayCondensed-Bold","__SF-UI-Display-Condensed-Heavy__":".SFNSDisplayCondensed-Heavy|SFUIDisplayCondensed-Heavy|.SFUIDisplayCondensed-Heavy","__SF-UI-Display-Condensed-Black__":".SFNSDisplayCondensed-Black|.SFUIDisplayCondensed-Black","__SF-UI-Display-Regular__":".SFNSDisplay|SFProDisplay-Regular|SFUIDisplay-Regular|.SFUIDisplay","__SF-UI-Display-Ultralight__":".SFNSDisplay-Ultralight|SFProDisplay-Ultralight|SFUIDisplay-Ultralight|.SFUIDisplay-Ultralight","__SF-UI-Display-Thin__":".SFNSDisplay-Thin|SFProDisplay-Thin|SFUIDisplay-Thin|.SFUIDisplay-Thin","__SF-UI-Display-Light__":".SFNSDisplay-Light|SFProDisplay-Light|SFUIDisplay-Light|.SFUIDisplay-Light","__SF-UI-Display-Medium__":".SFNSDisplay-Medium|SFProDisplay-Medium|SFUIDisplay-Medium|.SFUIDisplay-Medium","__SF-UI-Display-Semibold__":".SFNSDisplay-Semibold|SFProDisplay-Semibold|SFUIDisplay-Semibold|.SFUIDisplay-Semibold","__SF-UI-Display-Bold__":".SFNSDisplay-Bold|SFProDisplay-Bold|SFUIDisplay-Bold|.SFUIDisplay-Bold","__SF-UI-Display-Heavy__":".SFNSDisplay-Heavy|SFProDisplay-Heavy|SFUIDisplay-Heavy|.SFUIDisplay-Heavy","__SF-UI-Display-Black__":".SFNSDisplay-Black|SFProDisplay-Black|.SFUIDisplay-Black","__SF-UI-Display-Italic__":".SFNSDisplay-Italic|SFProDisplay-Italic|SFUIDisplay-Italic","__SF-UI-Display-Ultralight-Italic__":".SFNSDisplay-UltralightItalic|SFProDisplay-UltralightItalic|SFUIDisplay-UltralightItalic|.SFUIDisplay-UltralightItalic","__SF-UI-Display-Thin-Italic__":".SFNSDisplay-ThinItalic|SFProDisplay-ThinItalic|SFUIDisplay-ThinItalic|.SFUIDisplay-ThinItalic","__SF-UI-Display-Light-Italic__":".SFNSDisplay-LightItalic|SFProDisplay-LightItalic|SFUIDisplay-LightItalic|.SFUIDisplay-LightItalic","__SF-UI-Display-Medium-Italic__":".SFNSDisplay-MediumItalic|SFProDisplay-MediumItalic|SFUIDisplay-MediumItalic|.SFUIDisplay-MediumItalic","__SF-UI-Display-Semibold-Italic__":".SFNSDisplay-SemiboldItalic|SFProDisplay-SemiboldItalic|SFUIDisplay-SemiboldItalic|.SFUIDisplay-SemiboldItalic","__SF-UI-Display-Bold-Italic__":".SFNSDisplay-BoldItalic|SFProDisplay-BoldItalic|SFUIDisplay-BoldItalic|.SFUIDisplay-BoldItalic","__SF-UI-Display-Heavy-Italic__":".SFNSDisplay-HeavyItalic|SFProDisplay-HeavyItalic|SFUIDisplay-HeavyItalic|.SFUIDisplay-HeavyItalic","__SF-UI-Display-Black-Italic__":".SFNSDisplay-BlackItalic|SFProDisplay-BlackItalic|.SFUIDisplay-BlackItalic","__SF-UI-Text-Condensed-Regular__":".SFNSTextCondensed-Regular|SFUITextCondensed-Regular|.SFUITextCondensed-Regular","__SF-UI-Text-Condensed-Light__":".SFNSTextCondensed-Light|SFUITextCondensed-Light|.SFUITextCondensed-Light","__SF-UI-Text-Condensed-Medium__":".SFNSTextCondensed-Medium|SFUITextCondensed-Medium|.SFUITextCondensed-Medium","__SF-UI-Text-Condensed-Semibold__":".SFNSTextCondensed-Semibold|SFUITextCondensed-Semibold|.SFUITextCondensed-Semibold","__SF-UI-Text-Condensed-Bold__":".SFNSTextCondensed-Bold|SFUITextCondensed-Bold|.SFUITextCondensed-Bold","__SF-UI-Text-Condensed-Heavy__":".SFNSTextCondensed-Heavy|.SFUITextCondensed-Heavy","__SF-Compact-Rounded-Regular__":"SFCompactRounded-Regular|.SFCompactRounded-Regular","__SF-Compact-Rounded-Ultralight__":"SFCompactRounded-Ultralight|.SFCompactRounded-Ultralight","__SF-Compact-Rounded-Thin__":"SFCompactRounded-Thin|.SFCompactRounded-Thin","__SF-Compact-Rounded-Light__":"SFCompactRounded-Light|.SFCompactRounded-Light","__SF-Compact-Rounded-Medium__":"SFCompactRounded-Medium|.SFCompactRounded-Medium","__SF-Compact-Rounded-Semibold__":"SFCompactRounded-Semibold|.SFCompactRounded-Semibold","__SF-Compact-Rounded-Bold__":"SFCompactRounded-Bold|.SFCompactRounded-Bold","__SF-Compact-Rounded-Heavy__":"SFCompactRounded-Heavy|.SFCompactRounded-Heavy","__SF-Compact-Rounded-Black__":"SFCompactRounded-Black|.SFCompactRounded-Black"},Ed=bE,wE="System Default",CE=class{constructor(){this.name="local",this.typefaces=[],this.byFamily=new Map,this.typefaceAliasBySelector=new Map,this.typefaceAliases=new Map,this.interTypefaceSelectors=new Set}getTypefaceByFamily(e){var t;return(t=this.byFamily.get(e))!=null?t:null}createTypeface(e){let t={family:e,fonts:[],source:this.name};return this.addTypeface(t),t}addTypeface(e){this.typefaces.push(e),this.byFamily.set(e.family,e)}importFonts(){let e=[];for(let i of Object.keys(Ed)){let o=Ed[i];if(!o)continue;let a=this.createTypeface(i);for(let s of Object.keys(o)){let l=o[s];if(!l)continue;let{selector:c,weight:u}=l,f={variant:s,selector:c,weight:u,typeface:a,status:"loaded"};a.fonts.push(f)}e.push(...a.fonts)}for(let[i,o]of Object.entries(SE))this.addTypefaceAlias(i,o);let{typeface:t,aliases:r}=this.getSystemTypeface();this.addTypeface(t);for(let[i,o]of r)this.addTypefaceAlias(i,o);e.push(...t.fonts);let n=this.importInterTypeface();return e.push(...n.fonts),e}importInterTypeface(){let e=[["Regular","Inter",400],["Thin","Inter-Thin",100],["Extra Light","Inter-ExtraLight",200],["Light","Inter-Light",300],["Medium","Inter-Medium",500],["Semibold","Inter-SemiBold",600],["Bold","Inter-Bold",700],["Extra Bold","Inter-ExtraBold",800],["Black","Inter-Black",900],["Thin Italic","Inter-ThinItalic",100],["Extra Light Italic","Inter-ExtraLightItalic",200],["Light Italic","Inter-LightItalic",300],["Italic","Inter-Italic",400],["Medium Italic","Inter-MediumItalic",500],["Semibold Italic","Inter-SemiBoldItalic",600],["Bold Italic","Inter-BoldItalic",700],["Extra Bold Italic","Inter-ExtraBoldItalic",800],["Black Italic","Inter-BlackItalic",900]],t=this.createTypeface("Inter");for(let r of e){let[n,i,o]=r,a={variant:n,selector:i,weight:o,typeface:t,style:/italic/i.test(i)?"italic":"normal"};t.fonts.push(a)}return t.fonts.forEach(r=>this.interTypefaceSelectors.add(r.selector)),t}addTypefaceAlias(e,t){this.typefaceAliases.set(e,t),this.typefaceAliasBySelector.set(t,e)}getSystemTypeface(){let e=this.workaroundChrome81and82("system-ui|-apple-system|BlinkMacSystemFont|Segoe UI|Roboto|Oxygen|Ubuntu|Cantarell|Fira Sans|Droid Sans|Helvetica Neue|sans-serif"),t={family:wE,fonts:[],source:this.name},r=new Map,n=[400,100,200,300,500,600,700,800,900],i=["normal","italic"];for(let o of i)for(let a of n){let s=TE(a,o),l=`__SystemDefault-${a}-${o}__`,c={variant:s,selector:l,style:o,weight:a,typeface:t,status:"loaded"};t.fonts.push(c),r.set(l,e)}return{typeface:t,aliases:r}}getTypefaceAliasBySelector(e){return this.typefaceAliasBySelector.get(e)||null}getTypefaceSelectorByAlias(e){return this.typefaceAliases.get(e)||null}isTypefaceAlias(e){return!!(e&&e.match(/^__.*__$/))}workaroundChrome81and82(e){if(me){let t=me.userAgent;if(!t.includes("Mac OS X 10_15")||!t.includes("Chrome/81")&&!t.includes("Chrome/82"))return e}return`Inter|${e}`}},kd={100:"Thin",200:"Extra Light",300:"Light",400:"Normal",500:"Medium",600:"Semi Bold",700:"Bold",800:"Extra Bold",900:"Black"};function TE(e,t){let r=t==="normal"?"Regular":"Italic";return e===400?r:t!=="normal"?`${kd[e]} ${r}`:`${kd[e]}`}var EE=class{constructor(){this.enabled=!1,this.bySelector=new Map,this.loadedSelectors=new Set,this.local=new CE,this.google=new pE,this.custom=new mE,this.bySelector=new Map,this.importLocalFonts();let e=this.getFontBySelector("Inter");Ce(e,"Can\u2019t find Inter font"),this.defaultFont=e}addFont(e){this.bySelector.set(e.selector,e)}getAvailableFonts(){return Array.from(this.bySelector.values())}importLocalFonts(){this.local.importFonts().forEach(e=>{this.addFont(e),this.local.interTypefaceSelectors.has(e.selector)||this.loadFont(e)})}async importGoogleFonts(){if(!this.getGoogleFontsListPromise){this.getGoogleFontsListPromise=sr.fetchGoogleFontsList();let e=await this.getGoogleFontsListPromise;this.google.importFonts(e).forEach(t=>{this.addFont(t)})}return this.getGoogleFontsListPromise}importCustomFonts(e){this.bySelector.forEach((t,r)=>{r.startsWith(_r)&&this.bySelector.delete(r)}),this.custom.importFonts(e).forEach(t=>this.addFont(t))}getTypeface(e){return this[e.source].getTypefaceByFamily(e.family)}getFontBySelector(e,t=!0){return e.startsWith(_r)?this.custom.getFontBySelector(e,t):this.bySelector.get(e)}getDraftPropertiesBySelector(e){let t=this.getFontBySelector(e);if(t)return{style:t.style,weight:t.weight,variant:t.variant,family:t.typeface.family,source:t.typeface.source,category:t.category};let r=this.google.parseSelector(e);if(r){let n=Yh(r.variant);if(n)return{style:n.style,weight:n.weight,variant:r.variant,family:r.family,source:"google",category:void 0}}return null}isSelectorLoaded(e){return this.loadedSelectors.has(e)}async loadFont(e){return this.isSelectorLoaded(e.selector)?0:e.typeface.source==="local"?(this.local.interTypefaceSelectors.has(e.selector)&&fE.default.env.NODE_ENV!=="test"&&await Zh(e.typeface.family,e.style,e.weight),this.loadedSelectors.add(e.selector),1):e.file?(await xE({family:e.typeface.family,url:e.file,weight:e.weight,style:e.style},document),this.loadedSelectors.add(e.selector),1):Promise.reject(`Unable to load font: ${e.selector}`)}async loadWebFontsFromSelectors(e){if(!this.enabled)return[];e.some(r=>r.startsWith(ja))&&await this.importGoogleFonts();let t=e.map(r=>this.bySelector.get(r)).filter(r=>!!r);return Promise.allSettled(t.map(r=>this.loadFont(r)))}async loadMissingFonts(e,t){let r=e.filter(i=>!or.isSelectorLoaded(i));if(r.length===0)return;await or.loadWebFontsFromSelectors(r),r.every(i=>or.isSelectorLoaded(i))&&t&&t()}},or=new EE;Promise.allSettled=Promise.allSettled||(e=>Promise.all(e.map(t=>t.then(r=>({status:"fulfilled",value:r})).catch(r=>({status:"rejected",reason:r})))));function kE(e=[],t=5e3){let r=e.filter(o=>!or.isSelectorLoaded(o)),[n,i]=h.useState(r.length?"loading":"done");return h.useEffect(()=>{if(!r.length)return;i("loading");let o=setTimeout(()=>{i("timeout")},t);or.loadWebFontsFromSelectors(r).then(()=>{clearTimeout(o),i("done")})},[e.join(", "),r.join(", ")]),n}function Jh(e,t){return e.length===t.length&&e.every((r,n)=>r===t[n])}var EI=h.forwardRef(function(t,r){var n,i;let o=Br(),a=Rt(t),s=O(null),l=r??s,{navigate:c,getRoute:u}=lr(),f=cr();Ka((n=t.preload)!=null?n:[]);let d=gs(),m=nT(t.__link),p=kE(t.fonts);In(t,l);let{fonts:g,__fromCanvasComponent:x}=t,v=O([]),b=!Jh((i=v.current)!=null?i:[],g??[]);v.current=g,N(()=>{!b||!g||or.loadWebFontsFromSelectors(g).then(w=>{!x||!l.current||Q.current()!=="CANVAS"||!w.some(E=>E.status==="fulfilled"&&E.value===1)||oh(l.current)})},[g]);let y=ms(),S=h.useCallback(w=>{let T=Bi(w.target,l.current);if(w.metaKey||!c||!T)return;Dh(c,T,y)&&w.preventDefault()},[c,y]);N(()=>{var w;(w=l.current)==null||w.addEventListener("click",S);let T=l.current;return()=>T?.removeEventListener("click",S)},[S]);let C=h.useMemo(()=>!t.rawHTML||d||!u||!f?t.rawHTML:Xh(t.rawHTML,u,f,y),[t.rawHTML,u,d,f,y]);return h.createElement(RE,{...t,innerRef:l,layoutId:a,parentSize:o,fontLoadStatus:p,rawHTML:C,matchesCurrentRoute:m})}),RE=(()=>{var e;return e=class extends Ht{constructor(){super(...arguments),this.setElement=t=>{this.props.innerRef&&(this.props.innerRef.current=t),this.setLayerElement(t)},this.renderMain=t=>{sE.default.env.NODE_ENV!=="production"&&se.perf&&se.perf.nodeRender();let{font:r,visible:n,alignment:i,willChangeTransform:o,opacity:a,id:s,layoutId:l,className:c,transition:u,variants:f,name:d,__fromCanvasComponent:m,_initialStyle:p,widthType:g,heightType:x,_usesDOMRect:v,autoSize:b,style:y,fontLoadStatus:S,matchesCurrentRoute:C,preload:w,tabIndex:T,...E}=this.props;if(!n)return null;$t();let R=this.props.isEditable&&this.props.environment()==="CANVAS",F={outline:"none",display:"flex",flexDirection:"column",justifyContent:BE(this.props.verticalAlignment),opacity:R?0:a,flexShrink:0};if(p)for(let U in p)U.startsWith("--framer")&&(F[U]=p[U]);let B={"data-framer-component-type":"Text","data-framer-name":d};b&&(B["data-framer-component-text-autosized"]="true"),this.collectLayout(F,t),Di(this.props,F),us(this.props,F),(F.opacity===1||F.opacity===void 0)&&delete F.opacity,o&&Vi(F);let L=this.props.rawHTML,P=this.getOverrideText()||this.props.text;te(P)&&(L?L=DE(L,P):L=`<p style="font: ${r}">${P}</p>`),this.props.style&&Object.assign(F,this.props.style);let I=this.transformTemplate;if(I&&Object.assign(B,ns(this.props.center)),L){F.lineHeight="1px",F.fontSize="0px",Q.current()==="CANVAS"&&S==="loading"&&(F.visibility="hidden"),Q.current()==="CANVAS"&&S==="timeout"&&(F.backgroundColor="rgba(255, 0, 0, 0.3)"),Object.assign(F,y),i&&(F["--framer-text-alignment"]=i);let U=zE(T);return h.createElement(ke.div,{layoutId:l,id:s,...U,...B,...E,style:F,transformTemplate:I,dangerouslySetInnerHTML:{__html:L},"data-center":this.props.center,className:Mn(c,C&&"isCurrent"),transition:u,variants:f,ref:this.setElement})}}}get frame(){return Vr(this.props,this.props.parentSize||0,!1)}getOverrideText(){let{_forwardedOverrideId:t,_forwardedOverrides:r,id:n}=this.props,i=t??n;if(i&&r){let o=r[i];if(te(o))return o}}render(){return h.createElement(ur.Consumer,null,this.renderMain)}collectLayout(t,r){if(this.props.withExternalLayout)return;let n=this.frame,{rotation:i,autoSize:o,positionSticky:a,positionStickyTop:s,positionStickyRight:l,positionStickyBottom:c,positionStickyLeft:u,width:f,height:d,_usesDOMRect:m,positionFixed:p,positionAbsolute:g}=this.props,x=de.getNumber(i);if(n&&!(m&&(f==="auto"||d==="auto"))&&Q.hasRestrictions())Object.assign(t,{transform:`translate(${n.x}px, ${n.y}px) rotate(${x.toFixed(4)}deg)`,width:o?"auto":`${n.width}px`,minWidth:`${n.width}px`,height:`${n.height}px`});else{let{left:y,right:S,top:C,bottom:w}=this.props,T,E;o?(T="auto",E="auto"):((!D(y)||!D(S))&&(T=f),(!D(C)||!D(w))&&(E=d)),Object.assign(t,{left:y,right:S,top:C,bottom:w,width:T,height:E,rotate:x})}let b=Q.current()==="CANVAS";a?(!b||r)&&(t.position="sticky",t.willChange="transform",t.zIndex=1,t.top=s,t.right=l,t.bottom=c,t.left=u):b&&(p||g)&&(t.position="absolute")}get transformTemplate(){let{_usesDOMRect:t,widthType:r,heightType:n,__fromCanvasComponent:i}=this.props;if(this.props.transformTemplate)return this.props.transformTemplate;let o=this.frame,a=t&&(r===2||n===2);if(!o||!Q.hasRestrictions()||i||a)return Pn(this.props.center)}},e.supportsConstraints=!0,e.defaultTextProps={opacity:void 0,left:void 0,right:void 0,top:void 0,bottom:void 0,_constraints:{enabled:!0,aspectRatio:null},rotation:0,visible:!0,alignment:void 0,verticalAlignment:"top",shadows:[],font:"16px "+VS()},e.defaultProps={...Ht.defaultProps,...e.defaultTextProps,isEditable:!1,environment:Q.current,withExternalLayout:!1,fontLoadStatus:"loading"},e})(),_E="(?:<a[^>]*>)?",PE="(?:</a>)?",IE="<[^>]+>",FE="</[^>]+>",ME="<(?:div|span)[^>]*>",LE="</(?:div|span)>",OE="<[^>]+>",AE="</[^>]+>",VE=new RegExp(`^(${_E}${IE}${ME}${OE}).*?(${AE}).*?(${LE}${FE}${PE})$`,"s");function DE(e,t){return e.replace(VE,(r,n,i,o)=>n+t+i+"<br>"+o)}function BE(e){switch(e){case"top":return"flex-start";case"center":return"center";case"bottom":return"flex-end"}}function zE(e){return e===void 0?{}:{tabIndex:e}}var kI=be(gt(),1);var Mi=class{constructor(e){this.__class="PathSegment",this.x=0,this.y=0,this.handleMirroring="straight",this.handleOutX=0,this.handleOutY=0,this.handleInX=0,this.handleInY=0,this.radius=0,e&&Object.assign(this,e)}merge(e){return Object.assign(Object.create(Object.getPrototypeOf(this)),this,e)}};Mi.displayName="WithClassDiscriminatorMixin(PathSegment)";Mi.prototype.__class="PathSegment";(e=>{e.point=t=>({x:t.x,y:t.y}),e.handleOut=t=>({x:t.handleOutX,y:t.handleOutY}),e.handleIn=t=>({x:t.handleInX,y:t.handleInY}),e.calculatedHandleOut=t=>{switch(t.handleMirroring){case"symmetric":case"disconnected":case"asymmetric":return Re.add((0,e.point)(t),(0,e.handleOut)(t));default:return{x:t.x,y:t.y}}},e.calculatedHandleIn=t=>{switch(t.handleMirroring){case"symmetric":return Re.subtract((0,e.point)(t),(0,e.handleOut)(t));case"disconnected":case"asymmetric":return Re.add((0,e.point)(t),(0,e.handleIn)(t));default:return(0,e.point)(t)}},e.curveDefault=(t,r)=>{if(t.length>2){let n,i;r===0?n=t[t.length-1]:n=t[r-1],r===t.length-1?i=t[0]:i=t[r+1],Ce(n,"pointBefore should be defined"),Ce(i,"pointAfter should be defined");let o=Re.subtract((0,e.point)(i),(0,e.point)(n));return{x:o.x/4,y:o.y/4}}return{x:10,y:10}}})(Mi||(Mi={}));var II=be(gt(),1);var kr=class{constructor(){this.canvas={children:[]},this.listeners=[],this.ids=[]}static shared(e){if(e){let t=new kr;return t.setCanvas(e),t}return kr.__shared||(kr.__shared=new kr),kr.__shared}updateNode(e){let t=e.props.id,r=this.canvas.children;r||(this.canvas.children=r=[]);let n=!1;for(let i=0;i<r.length;i++){let o=r[i];if(o?.props.id===t){n=!0,r[i]=e;break}}n||r.push(e),this.setCanvas(this.canvas)}setCanvas(e){e.children&&(this.canvas=e,this.listeners.forEach((t,r)=>{let n=this.ids[r];if(!n)return;let i=Ga(e,n);t.setState({data:i})}))}registerListener(e,t){return this.listeners.push(e),this.ids.push(t),Ga(this.canvas,t)}removeListener(e){let t=this.listeners.indexOf(e);t!==-1&&(this.listeners.splice(t,1),this.ids.splice(t,1))}},HE=kr;HE.__shared=null;function Rd(e,t){let{name:r,props:n}=t;return n&&n.id===e||r===e}function Ga(e,t){if(!e)return null;if(Rd(t,e))return e;let{children:r}=e;if(!r||!ts(r))return null;for(let n of r)if(Rd(t,n))return n;for(let n of r){let i=Ga(n,t);if(i)return i}return null}var NE=h.createContext(null),MI=NE.Provider,OI=h.forwardRef(function(t,r){var n;let{background:i,children:o,alt:a,...s}=t,l={...s.style};i&&delete l.background;let c=ke[(n=t.as)!=null?n:"div"];return h.createElement(c,{...s,style:l,ref:r},i&&h.createElement(Kd,{image:i,alt:a}),o)});var $E={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Qh=/[&<>"']/g,UE=RegExp(Qh.source);function _d(e){return e&&UE.test(e)?e.replace(Qh,t=>$E[t]):e||""}var Pd="{{ text-placeholder }}",WE="rich-text-wrapper",jE=Ie(function(t,r){var n,i;let{id:o,name:a,html:s,htmlFromDesign:l,text:c,textFromDesign:u,fonts:f=[],width:d,height:m,left:p,right:g,top:x,bottom:v,center:b,className:y,stylesPresetsClassName:S,visible:C=!0,opacity:w,rotation:T=0,verticalAlignment:E="top",isEditable:R=!1,willChangeTransform:_,environment:F=Q.current,withExternalLayout:B=!1,positionSticky:L,positionStickyTop:P,positionStickyRight:I,positionStickyBottom:U,positionStickyLeft:k,__htmlStructure:A,__fromCanvasComponent:X=!1,_forwardedOverrideId:j,_forwardedOverrides:V,_usesDOMRect:Z,children:Y,...$}=t,ie=Br(),Te=Rt(t),q=O(null),le=r??q,{navigate:ge,getRoute:Be}=lr(),Le=cr();Ka((n=t.preload)!=null?n:[]),In(t,le);let Xe=M(ur),Ye=gs(),zr=c,fr=j??o;if(fr&&V){let _e=V[fr];typeof _e=="string"&&(zr=_e)}let et="";if(zr){let _e=_d(zr);et=A?A.replace(Pd,_e):`<p>${_e}</p>`}else if(s)et=s;else if(u){let _e=_d(u);et=A?A.replace(Pd,_e):`<p>${_e}</p>`}else l&&(et=l);let yt=ms(),Hi=ne(()=>Ye||!Be||!Le?et:Xh(et,Be,Le,yt),[Ye,et,Be,Le,yt]);if(N(()=>{let _e=le.current;if(_e===null)return;function Vn(G){let ee=Bi(G.target,le.current);if(G.metaKey||!ge||!ee||ee.getAttribute("target")==="_blank")return;Dh(ge,ee,yt)&&G.preventDefault()}return _e.addEventListener("click",Vn),()=>{_e.removeEventListener("click",Vn)}},[ge,yt]),tm(f,X,le),!C)return null;$t();let Ni=R&&F()==="CANVAS",ce={outline:"none",display:"flex",flexDirection:"column",justifyContent:em(E),opacity:Ni?0:w,flexShrink:0},dr=Q.hasRestrictions(),Ze=Vr(t,ie||0,!1),On=Z&&(d==="auto"||m==="auto"),$i=!!t.transformTemplate||!Ze||!dr||X||On?(i=t.transformTemplate)!=null?i:Pn(b):void 0;if(!B){if(Ze&&dr&&!On){let _e=de.getNumber(T).toFixed(4);ce.transform=`translate(${Ze.x}px, ${Ze.y}px) rotate(${_e}deg)`,ce.width=Ze.width,ce.minWidth=Ze.width,ce.height=Ze.height}else ce.left=p,ce.right=g,ce.top=x,ce.bottom=v,ce.width=d,ce.height=m,ce.rotate=T;L?(!Ye||Xe)&&(ce.position="sticky",ce.willChange="transform",ce.zIndex=1,ce.top=P,ce.right=I,ce.bottom=U,ce.left=k):Ye&&(t.positionFixed||t.positionAbsolute)&&(ce.position="absolute")}return Di(t,ce),us(t,ce),_&&Vi(ce),Object.assign(ce,t.style),re(ke.div,{id:o,ref:le,...$,style:ce,layoutId:Te,"data-framer-name":a,"data-framer-component-type":"DeprecatedRichText","data-center":b,className:Mn(y,S,WE),transformTemplate:$i,dangerouslySetInnerHTML:{__html:Hi}})});function em(e){switch(e){case"top":return"flex-start";case"center":return"center";case"bottom":return"flex-end"}}function tm(e,t,r){let n=O([]);Jh(n.current,e)||(n.current=e,or.loadWebFontsFromSelectors(e).then(i=>{if(!t||!r.current||Q.current()!=="CANVAS")return;i.some(a=>a.status==="fulfilled"&&a.value===1)&&oh(r.current)}))}var GE=Ie((e,t)=>{var r;let{__fromCanvasComponent:n=!1,_forwardedOverrideId:i,_forwardedOverrides:o,_usesDOMRect:a,bottom:s,center:l,children:c,environment:u=Q.current,fonts:f=[],height:d,isEditable:m=!1,left:p,name:g,opacity:x,positionSticky:v,positionStickyBottom:b,positionStickyLeft:y,positionStickyRight:S,positionStickyTop:C,right:w,rotation:T=0,style:E,stylesPresetsClassNames:R,text:_,top:F,verticalAlignment:B="top",visible:L=!0,width:P,willChangeTransform:I,withExternalLayout:U=!1,viewBox:k,viewBoxScale:A=1,...X}=e,j=Br(),V=gs(),Z=M(ur),Y=Rt(e),$=O(null),ie=t??$;if(In(e,ie),tm(f,n,ie),!L)return null;$t();let Te=m&&u()==="CANVAS",q={outline:"none",display:"flex",flexDirection:"column",justifyContent:em(B),opacity:Te?0:x,flexShrink:0},le=Q.hasRestrictions(),ge=Vr(e,j||0,!1),Be=a&&(P==="auto"||d==="auto"),Xe=!!e.transformTemplate||!ge||!le||n||Be?(r=e.transformTemplate)!=null?r:Pn(l):void 0;if(!U){if(ge&&le&&!Be){let Ye=de.getNumber(T).toFixed(4);q.transform=`translate(${ge.x}px, ${ge.y}px) rotate(${Ye}deg)`,q.width=ge.width,q.minWidth=ge.width,q.height=ge.height}else q.left=p,q.right=w,q.top=F,q.bottom=s,q.width=P,q.height=d,q.rotate=T;v?(!V||Z)&&(q.position="sticky",q.willChange="transform",q.zIndex=1,q.top=C,q.right=S,q.bottom=b,q.left=y):V&&(e.positionFixed||e.positionAbsolute)&&(q.position="absolute")}return Di(e,q),us(e,q),I&&Vi(q),Object.assign(q,E),Y&&(X.layout="preserve-aspect"),te(e.viewBox)?h.createElement(ke.svg,{...X,ref:ie,style:q,layoutId:Y,transformTemplate:Xe,"data-framer-name":g,"data-framer-component-type":"RichTextContainer",viewBox:k},h.createElement(ke.foreignObject,{width:"100%",height:"100%",transform:`scale(${A})`,style:{overflow:"visible",transformOrigin:"center center"}},c&&Xa(c,R,_))):h.createElement(ke.div,{...X,ref:ie,style:q,layoutId:Y,transformTemplate:Xe,"data-framer-name":g,"data-framer-component-type":"RichTextContainer"},c&&Xa(c,R,_))});function Xa(e,t,r){let n=$r.toArray(e.props.children);te(r)&&(n=n.slice(0,1)),n=n.map(a=>mr(a)?Xa(a,t,r):te(r)?r:a);let{["data-preset-tag"]:i,...o}=e.props;if(te(e.type)||la(e.type)){let a=i||qu(e.type)||e.type,s=te(a)?t?.[a]:void 0;o.className=Mn("framer-text",o.className,s)}return Ut(e,o,...n)}var DI=Ie(({children:e,html:t,htmlFromDesign:r,...n},i)=>{let o=t||e||r;if(te(o)){!n.stylesPresetsClassName&&qe(n.stylesPresetsClassNames)&&(n.stylesPresetsClassName=Object.values(n.stylesPresetsClassNames).join(" "));let a={[te(t)?"html":"htmlFromDesign"]:o};return h.createElement(jE,{...n,...a,ref:i})}if(!n.stylesPresetsClassNames&&te(n.stylesPresetsClassName)){let[a,s,l,c,u]=n.stylesPresetsClassName.split(" ");a===void 0||s===void 0||l===void 0||c===void 0||u===void 0?console.warn(`Encountered invalid stylesPresetsClassNames: ${n.stylesPresetsClassNames}`):n.stylesPresetsClassNames={h1:a,h2:s,h3:l,p:c,a:u}}return h.createElement(GE,{...n,ref:i},mr(o)?o:void 0)});function BI(e,t){Object.assign(e,{fonts:t})}function zI(e){return e.fonts||[]}var XE={name:"framer",version:"2.3.0",main:"build/index.js",type:"module",exports:{".":"./build/index.js","./package.json":"./package.json","./*":"./build/*"},files:["build","CHANGELOG.md","README.md","LICENSE.md","postinstall.cjs"],types:"./build/index.d.ts",author:"Framer",license:"MIT",scripts:{prepublishOnly:"make build",coverage:"yarn :jest --coverage",lint:"yarn :eslint ./src --ext .ts,.tsx --format codeframe --quiet","lint:fix":"yarn lint --fix",test:"yarn :jest",watch:"yarn :jest --watch",postinstall:"node postinstall.cjs"},dependencies:{"@juggle/resize-observer":"^3.3.1",eventemitter3:"^3.1.0",fontfaceobserver:"^2.1.0","hoist-non-react-statics":"^3.3.2",hsluv:"^0.0.3"},devDependencies:{"@framerjs/router":"workspace:src/router","@testing-library/dom":"^8.19.1","@testing-library/jest-dom":"^5.16.5","@testing-library/react":"^13.4.0","@testing-library/user-event":"^14.4.3","@types/google.fonts":"1.0.3","@types/hsluv":"https://github.com/framer/typed_hsluv#bump","@types/node":"^18.11.18","@types/react":"^18.0.26","@types/react-dom":"^18.0.10","@types/yargs":"^17.0.19","@typescript-eslint/eslint-plugin":"^5.48.0","@typescript-eslint/parser":"^5.48.0",chalk:"^4.1.2",eslint:"^8.31.0",immutable:"^3.8.2","jest-diff":"^29.3.1","jest-junit":"^15.0.0",react:"^18.2.0","react-dom":"^18.2.0",rollup:"^3.17.2","rollup-plugin-dts":"^5.1.0",semver:"^7.3.8",typescript:"^4.9.5",yargs:"^17.6.2"},peerDependencies:{"framer-motion":"10.11.4",react:"^18.2.0","react-dom":"^18.2.0"},tsdoc:{tsdocFlavor:"AEDoc"},framer:{components:[{name:"Scroll",children:!0,properties:[{key:"direction",title:"Direction",kind:"enum",options:["horizontal","vertical","both"]}]},{name:"Page"}]}},{version:NI}=XE;fi.prototype.addChild=function({transformer:e=t=>t}){let t=ve(e(this.get()));return this.onChange(r=>t.set(e(r))),t};/**
 * @license Emotion v11.0.0
 * MIT License
 *
 * Copyright (c) Emotion team and other contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *//*! Bundled license information:

react-is/cjs/react-is.production.min.js:
  (** @license React v16.13.1
   * react-is.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/export{h as a,$r as b,Hn as c,Ut as d,fe as e,re as f,Ie as g,km as h,Ur as i,oe as j,N as k,Wt as l,Wr as m,ne as n,O as o,Ue as p,We as q,fi as r,ve as s,ke as t,aa as u,Au as v,ct as w,Je as x,zu as y,mi as z,NR as A,S0 as B,_0 as C,lr as D,YR as E,JR as F,e_ as G,Q as H,t_ as I,s_ as J,rb as K,d_ as L,Jb as M,Yd as N,yh as O,sP as P,Mn as Q,gP as R,Oh as S,kP as T,RP as U,KC as V,LP as W,eT as X,zh as Y,VP as Z,DP as _,$h as $,BP as aa,sT as ba,zP as ca,HP as da,NP as ea,UP as fa,XP as ga,YP as ha,qP as ia,vT as ja,QP as ka,eI as la,rI as ma,lI as na,fI as oa,dI as pa,mI as qa,xn as ra,wI as sa,or as ta,EI as ua,OI as va,DI as wa,BI as xa,zI as ya};
//# sourceMappingURL=chunk-5F276QAW.mjs.map
