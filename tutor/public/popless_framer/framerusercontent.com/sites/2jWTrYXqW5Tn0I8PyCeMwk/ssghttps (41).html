import siteMetadata from "https://framerusercontent.com/modules/W4hVCnkMB6ND8hXTkGlk/p3Q71W5Klx6mT88rlrNR/siteMetadata.js";
const metadata = params => ({
  breakpoints: [{
    hash: "1cvqqu5",
    mediaQuery: "(min-width: 1920px)"
  }, {
    hash: "1b951nk",
    mediaQuery: "(min-width: 1280px) and (max-width: 1919px)"
  }, {
    hash: "vh5fxv",
    mediaQuery: "(min-width: 950px) and (max-width: 1279px)"
  }, {
    hash: "6pjiis",
    mediaQuery: "(min-width: 810px) and (max-width: 949px)"
  }, {
    hash: "1ks6mtz",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: siteMetadata(params).title || "Blog",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};