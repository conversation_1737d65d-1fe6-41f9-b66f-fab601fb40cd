// @ts-ignore
import { Storage } from "https://framerusercontent.com/modules/3Xi2AslpcDRhfyCVPmx3/d0Oobr5BHnVqZJQyMdGn/storage.js";
export const hashCode = s => s.split("").reduce((a, b) => {
  a = (a << 5) - a + b.charCodeAt(0);
  return a & a;
}, 0);
export function corsProxy(url) {
  return `https://cors-anywhere.herokuapp.com/${url}`;
}
export async function cachedResponse(url, cache = new Storage("cache")) {
  const cacheKey = url;
  const data = await cache.get(cacheKey);
  if (data) {
    return data;
  } else {
    var req = new XMLHttpRequest();
    req.open("GET", url, true);
    req.responseType = "blob";
    return new Promise((resolve, reject) => {
      req.onload = async function () {
        if (this.status === 200) {
          await cache.set(url, this.response);
          resolve(this.response);
        } else {
          reject(new Error(`Response status ${this.status} ${this.statusText}`));
        }
      };
      req.onerror = function (error) {
        reject(error);
      };
      req.send();
    });
  }
}
export async function checkForCachedData(url, cache = new Storage("cache")) {
  const cacheKey = url;
  const data = await cache.get(cacheKey);
  if (data) {
    return data;
  } else {
    return null;
  }
}
export const __FramerMetadata__ = {
  "exports": {
    "hashCode": {
      "type": "variable"
    },
    "checkForCachedData": {
      "type": "function"
    },
    "corsProxy": {
      "type": "function"
    },
    "cachedResponse": {
      "type": "function"
    }
  }
};