// Generated by Framer (2bdc57c)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Image, PathVariablesContext, PropertyOverrides, removeHiddenBreakpointLayers, RichText, useActiveVariantCallback, useHydratedBreakpointVariants, useQueryData, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import Intercom from "https://framerusercontent.com/modules/UIhUTcd796YH7Ndybys8/totj55n8qE3VYpdXhshW/Intercom.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import Updates from "https://framerusercontent.com/modules/irEVrtVIL4DC0EMBxrVm/2E8CqjxyFsrfqZcwb3ob/B1Di2VPAG.js";
import * as sharedStyle4 from "https://framerusercontent.com/modules/1EpHnr2nicQOm2hJS1GZ/fX3rD1mDGtZf0YfpuTmU/COkUUCscg.js";
import * as sharedStyle2 from "https://framerusercontent.com/modules/pvMhEO5Dzo3Z6Vedl1bY/4dqFwoBEkiDOaWdDgioe/EciIwMErV.js";
import * as sharedStyle1 from "https://framerusercontent.com/modules/23Jb6FVdyxtYcAce3GeP/r8KACoWwJP1mo24LYwGW/QE4Vr6ldo.js";
import * as sharedStyle from "https://framerusercontent.com/modules/yjPEtrSg4SBLUDhnOWiU/OHJkwjsINSCrOIeYbYWF/stylesPresetHeading1.js";
import * as sharedStyle3 from "https://framerusercontent.com/modules/wyZs1JsaYdpuznv4eH4t/xaFQHlOum3gr4elZTJsL/stylesPresetParagraph.js";
import metadataProvider from "https://framerusercontent.com/modules/ZJy6Dg6AFK1RtSEM9SZF/zSfxeFoe1ZQU2yrYRnxa/qFQZWHHvr.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const IntercomFonts = getFonts(Intercom);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["HMV4eVM_q", "rUUH1MPgf", "jga45LiAa"];
const breakpoints = {
  HMV4eVM_q: "(min-width: 1280px)",
  jga45LiAa: "(max-width: 809px)",
  rUUH1MPgf: "(min-width: 810px) and (max-width: 1279px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  HMV4eVM_q: "framer-v-xokaa8",
  jga45LiAa: "framer-v-2i6wx8",
  rUUH1MPgf: "framer-v-f7he9s"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("HMV4eVM_q", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  Desktop: "HMV4eVM_q",
  Phone: "jga45LiAa",
  Tablet: "rUUH1MPgf"
};
const transitions = {
  default: {
    duration: 0
  }
};
const toResponsiveImage = value => {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? {
    src: value
  } : undefined;
};
const QueryData = ({
  query,
  children
}) => {
  const data = useQueryData(query);
  return children(data);
};
const metadata = metadataProvider();
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style,
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "HMV4eVM_q",
  X6lZdtyhcju6AvrJS2,
  i9b_wiCx7ju6AvrJS2,
  ZfRzI34v5ju6AvrJS2,
  eQK4KKIbWju6AvrJS2,
  vrVpNVVBYju6AvrJS2,
  aGtle5Xwmju6AvrJS2,
  idju6AvrJS2,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata1 = metadataProvider();
    document.title = metadata1.title || "";
    if (metadata1.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata1.viewport);
    }
    if (metadata1.bodyClassName) {
      Array.from(document.body.classList).filter(c => c.startsWith("framer-body-")).map(c => document.body.classList.remove(c));
      document.body.classList.add(metadata1.bodyClassName);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const poplessLink1xqqrqe = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noopener");
  });
  const twitterLinkzdpqk6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noopener");
  });
  const isDisplayed = () => {
    if (baseVariant === "rUUH1MPgf") return true;
    return !isBrowser();
  };
  const isDisplayed1 = () => {
    if (["rUUH1MPgf", "jga45LiAa"].includes(baseVariant)) return !isBrowser();
    return true;
  };
  const isDisplayed2 = () => {
    if (baseVariant === "jga45LiAa") return true;
    return !isBrowser();
  };
  const defaultLayoutId = React.useId();
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "HMV4eVM_q",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        className: cx("framer-fBFOC", sharedStyle.className, sharedStyle1.className, sharedStyle2.className, sharedStyle3.className, sharedStyle4.className),
        style: {
          display: "contents"
        },
        children: [/*#__PURE__*/_jsxs(motion.div, {
          ...restProps,
          className: cx("framer-xokaa8", className),
          ref: ref,
          style: {
            ...style
          },
          children: [/*#__PURE__*/_jsx(Container, {
            className: "framer-qah2z1-container",
            children: /*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                jga45LiAa: {
                  variant: "aZMkidfTG"
                },
                rUUH1MPgf: {
                  variant: "aZMkidfTG"
                }
              },
              children: /*#__PURE__*/_jsx(HeaderNavigation, {
                height: "100%",
                id: "R1RwBnZUN",
                layoutId: "R1RwBnZUN",
                style: {
                  width: "100%"
                },
                variant: "Dg2JgAbsI",
                width: "100%"
              })
            })
          }), /*#__PURE__*/_jsxs(motion.main, {
            className: "framer-8434wd",
            "data-framer-name": "Main",
            name: "Main",
            children: [/*#__PURE__*/_jsxs(motion.header, {
              className: "framer-1skgn6l",
              "data-framer-name": "Stack",
              name: "Stack",
              children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  jga45LiAa: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("h1", {
                        style: {
                          "--framer-text-alignment": "center"
                        },
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNzAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "36px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-2px",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Product updates"
                        })
                      })
                    })
                  },
                  rUUH1MPgf: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("h1", {
                        style: {
                          "--framer-line-height": "1em",
                          "--framer-text-alignment": "center"
                        },
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNzAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "54px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-2px",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Product updates"
                        })
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("h1", {
                      style: {
                        "--framer-line-height": "1em",
                        "--framer-text-alignment": "center"
                      },
                      children: /*#__PURE__*/_jsx("span", {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItNzAw",
                          "--framer-font-family": '"Inter", sans-serif',
                          "--framer-font-size": "61px",
                          "--framer-font-style": "normal",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-2px",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "Product updates"
                      })
                    })
                  }),
                  className: "framer-1hwsd0t",
                  fonts: ["GF;Inter-700"],
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              }), isDisplayed() && /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs("h1", {
                    style: {
                      "--framer-line-height": "1em",
                      "--framer-text-alignment": "center"
                    },
                    children: [/*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNzAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "54px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: "New features, fixes &"
                    }), /*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNzAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "58px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: /*#__PURE__*/_jsx("br", {})
                    }), /*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNzAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "54px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: "improvements."
                    })]
                  })
                }),
                className: "framer-19rftrs hidden-xokaa8 hidden-2i6wx8",
                fonts: ["GF;Inter-700"],
                verticalAlignment: "top",
                withExternalLayout: true
              }), isDisplayed1() && /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs("h1", {
                    style: {
                      "--framer-line-height": "1em",
                      "--framer-text-alignment": "center"
                    },
                    children: [/*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNjAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "61px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "600",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: "New features, fixes & "
                    }), /*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNzAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "64px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: /*#__PURE__*/_jsx("br", {})
                    }), /*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNjAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "61px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "600",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: "improvements."
                    })]
                  })
                }),
                className: "framer-km5qcw hidden-f7he9s hidden-2i6wx8",
                fonts: ["GF;Inter-600", "GF;Inter-700"],
                verticalAlignment: "top",
                withExternalLayout: true
              }), isDisplayed2() && /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx("h1", {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "center"
                    },
                    children: /*#__PURE__*/_jsxs("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNzAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "36px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-2px",
                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                      },
                      children: ["New features, fixes ", /*#__PURE__*/_jsx("br", {}), "& improvements."]
                    })
                  })
                }),
                className: "framer-1oklxvj hidden-xokaa8 hidden-f7he9s",
                fonts: ["GF;Inter-700"],
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Container, {
                className: "framer-1xw9eit-container",
                children: /*#__PURE__*/_jsx(Intercom, {
                  appId: "lsvujawt",
                  height: "100%",
                  id: "nzAFN2RL0",
                  layoutId: "nzAFN2RL0",
                  style: {
                    height: "100%",
                    width: "100%"
                  },
                  width: "100%"
                })
              })]
            }), /*#__PURE__*/_jsx(motion.header, {
              className: "framer-shsuqq",
              "data-framer-name": "Stack",
              name: "Stack",
              children: /*#__PURE__*/_jsx(motion.div, {
                className: "framer-2g6s1j",
                "data-framer-name": "Collection",
                name: "Collection",
                children: /*#__PURE__*/_jsx(QueryData, {
                  query: {
                    from: {
                      data: Updates,
                      type: "Collection"
                    },
                    select: [{
                      name: "X6lZdtyhc",
                      type: "Identifier"
                    }, {
                      name: "i9b_wiCx7",
                      type: "Identifier"
                    }, {
                      name: "ZfRzI34v5",
                      type: "Identifier"
                    }, {
                      name: "eQK4KKIbW",
                      type: "Identifier"
                    }, {
                      name: "vrVpNVVBY",
                      type: "Identifier"
                    }, {
                      name: "aGtle5Xwm",
                      type: "Identifier"
                    }, {
                      name: "id",
                      type: "Identifier"
                    }]
                  },
                  children: collection => collection.map(({
                    X6lZdtyhc: X6lZdtyhcju6AvrJS2,
                    i9b_wiCx7: i9b_wiCx7ju6AvrJS2,
                    ZfRzI34v5: ZfRzI34v5ju6AvrJS2,
                    eQK4KKIbW: eQK4KKIbWju6AvrJS2,
                    vrVpNVVBY: vrVpNVVBYju6AvrJS2,
                    aGtle5Xwm: aGtle5Xwmju6AvrJS2,
                    id: idju6AvrJS2
                  }, i) => /*#__PURE__*/_jsx(LayoutGroup, {
                    id: `ju6AvrJS2-${idju6AvrJS2}`,
                    children: /*#__PURE__*/_jsx(PathVariablesContext.Provider, {
                      value: {
                        aGtle5Xwm: aGtle5Xwmju6AvrJS2
                      },
                      children: /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-capxy7",
                        "data-framer-name": "Post",
                        name: "Post",
                        children: [/*#__PURE__*/_jsx(motion.div, {
                          className: "framer-1tdmbgj",
                          children: /*#__PURE__*/_jsxs(motion.div, {
                            className: "framer-pd2wkk",
                            children: [/*#__PURE__*/_jsx(RichText, {
                              __fromCanvasComponent: true,
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-font-size": "14px",
                                    "--framer-line-height": "22px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItNTAw",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "14px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "500",
                                      "--framer-text-color": "rgb(217, 48, 37)"
                                    },
                                    children: "Guest experience"
                                  })
                                })
                              }),
                              className: "framer-129b0sc",
                              fonts: ["GF;Inter-500"],
                              text: X6lZdtyhcju6AvrJS2,
                              verticalAlignment: "top",
                              withExternalLayout: true
                            }), /*#__PURE__*/_jsxs(motion.div, {
                              className: "framer-1yubdqr",
                              children: [/*#__PURE__*/_jsx(RichText, {
                                __fromCanvasComponent: true,
                                children: /*#__PURE__*/_jsx(React.Fragment, {
                                  children: /*#__PURE__*/_jsx("p", {
                                    style: {
                                      "--framer-line-height": "30px",
                                      "--framer-text-alignment": "left"
                                    },
                                    children: /*#__PURE__*/_jsx("span", {
                                      style: {
                                        "--font-selector": "R0Y7SW50ZXItNzAw",
                                        "--framer-font-family": '"Inter", sans-serif',
                                        "--framer-font-size": "24px",
                                        "--framer-font-style": "normal",
                                        "--framer-font-weight": "700",
                                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                      },
                                      children: "Title"
                                    })
                                  })
                                }),
                                className: "framer-1nbtsi0",
                                fonts: ["GF;Inter-700"],
                                text: i9b_wiCx7ju6AvrJS2,
                                verticalAlignment: "top",
                                withExternalLayout: true
                              }), /*#__PURE__*/_jsx(RichText, {
                                __fromCanvasComponent: true,
                                children: /*#__PURE__*/_jsx(React.Fragment, {
                                  children: /*#__PURE__*/_jsx("p", {
                                    style: {
                                      "--framer-line-height": "23px",
                                      "--framer-text-alignment": "left"
                                    },
                                    children: /*#__PURE__*/_jsx("span", {
                                      style: {
                                        "--font-selector": "R0Y7SW50ZXItNzAw",
                                        "--framer-font-family": '"Inter", sans-serif',
                                        "--framer-font-size": "22px",
                                        "--framer-font-style": "normal",
                                        "--framer-font-weight": "700",
                                        "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                                      },
                                      children: "Date"
                                    })
                                  })
                                }),
                                className: "framer-e19c2r",
                                fonts: ["GF;Inter-700"],
                                text: ZfRzI34v5ju6AvrJS2,
                                verticalAlignment: "top",
                                withExternalLayout: true
                              })]
                            })]
                          })
                        }), /*#__PURE__*/_jsx(motion.div, {
                          className: "framer-1k6eaes",
                          children: /*#__PURE__*/_jsx(motion.div, {
                            className: "framer-ym8bbr",
                            children: /*#__PURE__*/_jsxs(motion.div, {
                              className: "framer-bent21",
                              children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                                breakpoint: baseVariant,
                                overrides: {
                                  jga45LiAa: {
                                    background: {
                                      alt: "",
                                      fit: "fill",
                                      sizes: "calc(min(100vw, 1200px) - 48px)",
                                      ...toResponsiveImage(eQK4KKIbWju6AvrJS2)
                                    }
                                  },
                                  rUUH1MPgf: {
                                    background: {
                                      alt: "",
                                      fit: "fill",
                                      sizes: "calc(min(100vw, 1200px) - 80px)",
                                      ...toResponsiveImage(eQK4KKIbWju6AvrJS2)
                                    }
                                  }
                                },
                                children: /*#__PURE__*/_jsx(Image, {
                                  background: {
                                    alt: "",
                                    fit: "fill",
                                    sizes: "800px",
                                    ...toResponsiveImage(eQK4KKIbWju6AvrJS2)
                                  },
                                  className: "framer-1rg52bt"
                                })
                              }), /*#__PURE__*/_jsx(RichText, {
                                __fromCanvasComponent: true,
                                children: vrVpNVVBYju6AvrJS2,
                                className: "framer-g4omnx",
                                fonts: ["GF;Inter-regular"],
                                stylesPresetsClassNames: {
                                  a: "framer-styles-preset-1utq9v6",
                                  h1: "framer-styles-preset-o3e5h0",
                                  h2: "framer-styles-preset-b6w6oh",
                                  h3: "framer-styles-preset-24blvi",
                                  p: "framer-styles-preset-16bzrdu"
                                },
                                verticalAlignment: "top",
                                withExternalLayout: true
                              })]
                            })
                          })
                        })]
                      })
                    })
                  }, idju6AvrJS2))
                })
              })
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-llnaf4-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  jga45LiAa: {
                    variant: "Zz_9kWOfb"
                  }
                },
                children: /*#__PURE__*/_jsx(FooterNew, {
                  height: "100%",
                  id: "iAyu_FVu_",
                  layoutId: "iAyu_FVu_",
                  poplessLink: poplessLink1xqqrqe,
                  style: {
                    width: "100%"
                  },
                  twitterLink: twitterLinkzdpqk6,
                  variant: "zyTRmFlly",
                  width: "100%"
                })
              })
            })]
          })]
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-fBFOC [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", `.${metadata.bodyClassName} { background: white; }`, ".framer-fBFOC .framer-1r13iia { display: block; }", ".framer-fBFOC .framer-xokaa8 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }", ".framer-fBFOC .framer-qah2z1-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-fBFOC .framer-8434wd { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-fBFOC .framer-1skgn6l { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 3px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 175px 120px 97px 120px; position: relative; width: 1200px; }", ".framer-fBFOC .framer-1hwsd0t, .framer-fBFOC .framer-19rftrs, .framer-fBFOC .framer-km5qcw { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 720px; word-break: break-word; word-wrap: break-word; }", ".framer-fBFOC .framer-1oklxvj { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-fBFOC .framer-1xw9eit-container { flex: none; height: 1px; position: relative; width: 1px; }", ".framer-fBFOC .framer-shsuqq { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 100px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 80px 0px 150px 0px; position: relative; width: 1200px; }", ".framer-fBFOC .framer-2g6s1j { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 163px; height: min-content; justify-content: flex-start; padding: 0px 0px 0px 0px; position: relative; width: 1200px; }", ".framer-fBFOC .framer-capxy7 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-fBFOC .framer-1tdmbgj { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 50px; height: min-content; justify-content: flex-start; left: 0px; overflow: hidden; padding: 0px 0px 0px 0px; position: sticky; top: 160px; width: 400px; will-change: transform; z-index: 1; }", ".framer-fBFOC .framer-pd2wkk { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-fBFOC .framer-129b0sc, .framer-fBFOC .framer-g4omnx { --framer-link-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0); --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-fBFOC .framer-1yubdqr { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-fBFOC .framer-1nbtsi0, .framer-fBFOC .framer-e19c2r { --framer-link-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0); --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 400px; word-break: break-word; word-wrap: break-word; }", ".framer-fBFOC .framer-1k6eaes { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 50px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 800px; }", ".framer-fBFOC .framer-ym8bbr { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 50px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-fBFOC .framer-bent21 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-fBFOC .framer-1rg52bt { border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; border-top-left-radius: 10px; border-top-right-radius: 10px; flex: none; height: 450px; position: relative; width: 100%; }", ".framer-fBFOC .framer-llnaf4-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-fBFOC .framer-xokaa8, .framer-fBFOC .framer-8434wd, .framer-fBFOC .framer-1skgn6l, .framer-fBFOC .framer-shsuqq, .framer-fBFOC .framer-2g6s1j, .framer-fBFOC .framer-capxy7, .framer-fBFOC .framer-1tdmbgj, .framer-fBFOC .framer-pd2wkk, .framer-fBFOC .framer-1yubdqr, .framer-fBFOC .framer-1k6eaes, .framer-fBFOC .framer-ym8bbr, .framer-fBFOC .framer-bent21 { gap: 0px; } .framer-fBFOC .framer-xokaa8 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-fBFOC .framer-xokaa8 > :first-child, .framer-fBFOC .framer-8434wd > :first-child, .framer-fBFOC .framer-1skgn6l > :first-child, .framer-fBFOC .framer-shsuqq > :first-child, .framer-fBFOC .framer-2g6s1j > :first-child, .framer-fBFOC .framer-1tdmbgj > :first-child, .framer-fBFOC .framer-pd2wkk > :first-child, .framer-fBFOC .framer-1yubdqr > :first-child, .framer-fBFOC .framer-1k6eaes > :first-child, .framer-fBFOC .framer-ym8bbr > :first-child, .framer-fBFOC .framer-bent21 > :first-child { margin-top: 0px; } .framer-fBFOC .framer-xokaa8 > :last-child, .framer-fBFOC .framer-8434wd > :last-child, .framer-fBFOC .framer-1skgn6l > :last-child, .framer-fBFOC .framer-shsuqq > :last-child, .framer-fBFOC .framer-2g6s1j > :last-child, .framer-fBFOC .framer-1tdmbgj > :last-child, .framer-fBFOC .framer-pd2wkk > :last-child, .framer-fBFOC .framer-1yubdqr > :last-child, .framer-fBFOC .framer-1k6eaes > :last-child, .framer-fBFOC .framer-ym8bbr > :last-child, .framer-fBFOC .framer-bent21 > :last-child { margin-bottom: 0px; } .framer-fBFOC .framer-8434wd > *, .framer-fBFOC .framer-pd2wkk > *, .framer-fBFOC .framer-1yubdqr > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-fBFOC .framer-1skgn6l > * { margin: 0px; margin-bottom: calc(3px / 2); margin-top: calc(3px / 2); } .framer-fBFOC .framer-shsuqq > * { margin: 0px; margin-bottom: calc(100px / 2); margin-top: calc(100px / 2); } .framer-fBFOC .framer-2g6s1j > * { margin: 0px; margin-bottom: calc(163px / 2); margin-top: calc(163px / 2); } .framer-fBFOC .framer-capxy7 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-fBFOC .framer-capxy7 > :first-child { margin-left: 0px; } .framer-fBFOC .framer-capxy7 > :last-child { margin-right: 0px; } .framer-fBFOC .framer-1tdmbgj > *, .framer-fBFOC .framer-1k6eaes > *, .framer-fBFOC .framer-ym8bbr > * { margin: 0px; margin-bottom: calc(50px / 2); margin-top: calc(50px / 2); } .framer-fBFOC .framer-bent21 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } }", "@media (min-width: 1280px) { .framer-fBFOC .hidden-xokaa8 { display: none !important; } }", `@media (min-width: 810px) and (max-width: 1279px) { .framer-fBFOC .hidden-f7he9s { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-fBFOC .framer-xokaa8 { width: 810px; } .framer-fBFOC .framer-1skgn6l { gap: 7px; max-width: unset; padding: 114px 24px 0px 24px; width: 100%; } .framer-fBFOC .framer-shsuqq { padding: 60px 40px 150px 40px; width: 100%; } .framer-fBFOC .framer-2g6s1j, .framer-fBFOC .framer-1yubdqr, .framer-fBFOC .framer-1nbtsi0, .framer-fBFOC .framer-e19c2r, .framer-fBFOC .framer-1k6eaes { width: 100%; } .framer-fBFOC .framer-capxy7 { flex-direction: column; gap: 30px; } .framer-fBFOC .framer-1tdmbgj { left: unset; position: relative; top: unset; width: 100%; } .framer-fBFOC .framer-bent21 { gap: 16px; } .framer-fBFOC .framer-1rg52bt { aspect-ratio: 1.6222222222222222 / 1; height: var(--framer-aspect-ratio-supported, 450px); } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-fBFOC .framer-1skgn6l, .framer-fBFOC .framer-capxy7, .framer-fBFOC .framer-bent21 { gap: 0px; } .framer-fBFOC .framer-1skgn6l > * { margin: 0px; margin-bottom: calc(7px / 2); margin-top: calc(7px / 2); } .framer-fBFOC .framer-1skgn6l > :first-child, .framer-fBFOC .framer-capxy7 > :first-child, .framer-fBFOC .framer-bent21 > :first-child { margin-top: 0px; } .framer-fBFOC .framer-1skgn6l > :last-child, .framer-fBFOC .framer-capxy7 > :last-child, .framer-fBFOC .framer-bent21 > :last-child { margin-bottom: 0px; } .framer-fBFOC .framer-capxy7 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-fBFOC .framer-bent21 > * { margin: 0px; margin-bottom: calc(16px / 2); margin-top: calc(16px / 2); } }}`, `@media (max-width: 809px) { .framer-fBFOC .hidden-2i6wx8 { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-fBFOC .framer-xokaa8 { width: 390px; } .framer-fBFOC .framer-1skgn6l { gap: 9px; max-width: unset; padding: 109px 24px 80px 24px; width: 100%; } .framer-fBFOC .framer-1hwsd0t, .framer-fBFOC .framer-2g6s1j, .framer-fBFOC .framer-1yubdqr, .framer-fBFOC .framer-1nbtsi0, .framer-fBFOC .framer-e19c2r, .framer-fBFOC .framer-1k6eaes { width: 100%; } .framer-fBFOC .framer-shsuqq { padding: 0px 24px 150px 24px; width: 100%; } .framer-fBFOC .framer-capxy7 { flex-direction: column; gap: 30px; } .framer-fBFOC .framer-1tdmbgj { left: unset; position: relative; top: unset; width: 100%; } .framer-fBFOC .framer-bent21 { gap: 15px; } .framer-fBFOC .framer-1rg52bt { aspect-ratio: 1.55 / 1; height: var(--framer-aspect-ratio-supported, 221px); } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-fBFOC .framer-1skgn6l, .framer-fBFOC .framer-capxy7, .framer-fBFOC .framer-bent21 { gap: 0px; } .framer-fBFOC .framer-1skgn6l > * { margin: 0px; margin-bottom: calc(9px / 2); margin-top: calc(9px / 2); } .framer-fBFOC .framer-1skgn6l > :first-child, .framer-fBFOC .framer-capxy7 > :first-child, .framer-fBFOC .framer-bent21 > :first-child { margin-top: 0px; } .framer-fBFOC .framer-1skgn6l > :last-child, .framer-fBFOC .framer-capxy7 > :last-child, .framer-fBFOC .framer-bent21 > :last-child { margin-bottom: 0px; } .framer-fBFOC .framer-capxy7 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-fBFOC .framer-bent21 > * { margin: 0px; margin-bottom: calc(15px / 2); margin-top: calc(15px / 2); } }}`, ...sharedStyle.css, ...sharedStyle1.css, ...sharedStyle2.css, ...sharedStyle3.css, ...sharedStyle4.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerIntrinsicHeight 22691
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerIntrinsicWidth 1280
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"rUUH1MPgf":{"layout":["fixed","auto"]},"jga45LiAa":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         */
const FramerqFQZWHHvr = withCSS(Component, css, "framer-fBFOC");
export default FramerqFQZWHHvr;
FramerqFQZWHHvr.displayName = "Updates";
FramerqFQZWHHvr.defaultProps = {
  height: 22691,
  width: 1280
};
addFonts(FramerqFQZWHHvr, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/qFQZWHHvr:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/qFQZWHHvr:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
  weight: "600"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/qFQZWHHvr:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/qFQZWHHvr:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...HeaderNavigationFonts, ...IntercomFonts, ...FooterNewFonts, ...sharedStyle.fonts, ...sharedStyle1.fonts, ...sharedStyle2.fonts, ...sharedStyle3.fonts, ...sharedStyle4.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerqFQZWHHvr",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "1280",
        "framerIntrinsicHeight": "22691",
        "framerResponsiveScreen": "",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"rUUH1MPgf\":{\"layout\":[\"fixed\",\"auto\"]},\"jga45LiAa\":{\"layout\":[\"fixed\",\"auto\"]}}}"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};