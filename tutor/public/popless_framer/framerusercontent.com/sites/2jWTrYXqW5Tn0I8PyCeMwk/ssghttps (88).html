// Welcome to Code in Framer
// Get Started: https://www.framer.com/docs/guides/
/**
 * These annotations control how your component sizes
 * Learn more: https://www.framer.com/docs/guides/auto-sizing
 *
 * @framerSupportedLayoutWidth auto
 * @framerSupportedLayoutHeight auto
 */import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
const css = `
    .p-login-link{
        color: #202124;
        font-size: 16px;
        font-family: "Inter";
        font-weight: 500;
        text-decoration: none;
        line-height: 2.4em;
    }
`;
export default function LoginLink(props) {
  return /*#__PURE__*/_jsxs(_Fragment, {
    children: [/*#__PURE__*/_jsx("a", {
      className: "p-login-link",
      href: "/login",
      target: "_blank",
      children: "Login"
    }), /*#__PURE__*/_jsx("style", {
      children: css
    })]
  });
}
; // Styles are written in object syntax
// Learn more: https://reactjs.org/docs/dom-elements.html#style

export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "LoginLink",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./LoginLinkLight.map