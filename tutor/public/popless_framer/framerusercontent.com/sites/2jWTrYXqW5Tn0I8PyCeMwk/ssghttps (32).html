const metadata = params => ({
  breakpoints: [{
    hash: "106qfxg",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "13a9zok",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "cpi9b8",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | Discord",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};