import{b as c}from"./chunk-VWWF2A63.mjs";import{K as t,g as l,n as i}from"./chunk-5F276QAW.mjs";var y={width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"};var v={...y,borderRadius:6,background:"rgba(149, 149, 149, 0.1)",border:"1px dashed rgba(149, 149, 149, 0.15)",color:"#a5a5a5",flexDirection:"column"},b=l((r,n)=>c("div",{style:v,ref:n}));var S={onClick:{type:t.<PERSON>Handler},onMouseDown:{type:t.<PERSON>Handler},onMouseUp:{type:t.<PERSON>Handler},onMouseEnter:{type:t.EventHandler},onMouseLeave:{type:t.<PERSON>Handler}},x=(r,n)=>r.find(e=>e.toLowerCase().includes(n));function H(r,n,e="",a,u){let p=i(()=>{if(e==null||e?.length===0)return null;let s=e.toLowerCase().replace(/-|\s/g,"");var o;return(o=u[s])!==null&&o!==void 0?o:x(r,s)},[a,e]);return n?a:p}export{b as a,S as b,H as c};
//# sourceMappingURL=chunk-AXDY7YJP.mjs.map
