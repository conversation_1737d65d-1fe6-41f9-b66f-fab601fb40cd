import { jsx as _jsx } from "react/jsx-runtime"; // Generated by <PERSON><PERSON><PERSON> (6906a2c)
import * as React from "react";
import { motion, LayoutGroup } from "framer-motion";
import { addFonts, withCSS, addPropertyControls, ControlType, cx, useAddVariantProps, useVariantState, Text, Stack } from "framer";
import { useRandomID } from "https://framer.com/m/framer/randomID.js@^2.0.0";
const cycleOrder = ["XAG5E5Jgd"];
const variantClassNames = {
  "XAG5E5Jgd": "framer-v-yfw3in"
};
const humanReadableVariantMap = {};
const transitions = {
  "default": {
    "type": "spring",
    "ease": [0.44, 0, 0.56, 1],
    "duration": 0.3,
    "delay": 0,
    "stiffness": 500,
    "damping": 60,
    "mass": 1
  }
};
const Component = /*#__PURE__*/React.forwardRef(function ({
  style,
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "XAG5E5Jgd",
  title: vsL9WVh4y = "Privacy Policy",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    variants,
    baseVariant,
    gestureVariant,
    classNames,
    transition,
    setVariant,
    setGestureState
  } = useVariantState({
    defaultVariant: "XAG5E5Jgd",
    variant,
    transitions,
    variantClassNames,
    cycleOrder
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const variantProps = React.useMemo(() => ({}), []);
  const addVariantProps = useAddVariantProps(baseVariant, gestureVariant, variantProps);
  const defaultLayoutId = useRandomID();
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-XHiXU", classNames),
      style: {
        "display": "contents"
      },
      children: /*#__PURE__*/_jsx(Stack, {
        ...restProps,
        layoutId: "XAG5E5Jgd",
        className: cx("framer-yfw3in", className),
        style: {
          ...style
        },
        background: null,
        direction: "horizontal",
        distribution: "start",
        alignment: "center",
        gap: 10,
        __fromCanvasComponent: true,
        __contentWrapperStyle: {
          "width": "100%",
          "height": "auto",
          "padding": "0px 0px 0px 0px"
        },
        center: false,
        "data-framer-name": "Variant 1",
        transition: transition,
        layoutDependency: layoutDependency,
        ref: ref,
        ...addVariantProps("XAG5E5Jgd"),
        children: /*#__PURE__*/_jsx(Text, {
          style: {
            "--framer-font-family": "\"Inter\", sans-serif",
            "--framer-font-style": "normal",
            "--framer-font-weight": 600,
            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
            "--framer-font-size": "32px",
            "--framer-letter-spacing": "-1px",
            "--framer-text-transform": "none",
            "--framer-text-decoration": "none",
            "--framer-line-height": "1.4em",
            "--framer-text-alignment": "left"
          },
          withExternalLayout: true,
          verticalAlignment: "top",
          __fromCanvasComponent: true,
          alignment: "left",
          fonts: ["GF;Inter-600"],
          layoutId: "NDch71v_1",
          className: "framer-1tahg5s",
          rawHTML: "<h1 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Privacy Policy</span><br></span></h1>",
          text: vsL9WVh4y,
          transition: transition,
          layoutDependency: layoutDependency,
          ...addVariantProps("NDch71v_1")
        })
      })
    })
  });
});
const css = [".framer-XHiXU [data-border=\"true\"]::after { content: \"\"; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}", "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-XHiXU * { box-sizing: border-box; }", ".framer-XHiXU .framer-yfw3in { position: relative; overflow: visible; width: 203px; height: min-content; }", ".framer-XHiXU .framer-1tahg5s { position: relative; overflow: hidden; width: 1px; height: auto; flex: 1 0 0px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerIntrinsicHeight 45
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerIntrinsicWidth 203
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerCanvasComponentVariantDetails {"propertyName": "variant", "data": {"default": {"layout": ["fixed", "auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerVariables {"vsL9WVh4y": "title"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  */
const FramerBlyUUTgI8 = withCSS(Component, css);
export default FramerBlyUUTgI8;
FramerBlyUUTgI8.displayName = "Terms/H1";
FramerBlyUUTgI8.defaultProps = {
  "width": 203,
  "height": 45
};
addPropertyControls(FramerBlyUUTgI8, {
  "vsL9WVh4y": {
    "type": ControlType.String,
    "title": "Title",
    "defaultValue": "Privacy Policy",
    "displayTextArea": false
  }
});
addFonts(FramerBlyUUTgI8, [{
  "url": "https://fonts.gstatic.com/s/inter/v8/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
  "family": "Inter",
  "style": "normal",
  "weight": "600",
  "moduleAsset": {
    "url": "https://fonts.gstatic.com/s/inter/v8/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
    "localModuleIdentifier": "local-module:canvasComponent/BlyUUTgI8:default"
  }
}]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerBlyUUTgI8",
      "slots": [],
      "annotations": {
        "framerIntrinsicWidth": "203",
        "framerCanvasComponentVariantDetails": "{\"propertyName\": \"variant\", \"data\": {\"default\": {\"layout\": [\"fixed\", \"auto\"]}}}",
        "framerIntrinsicHeight": "45",
        "framerVariables": "{\"vsL9WVh4y\": \"title\"}",
        "framerContractVersion": "1"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./BlyUUTgI8.map