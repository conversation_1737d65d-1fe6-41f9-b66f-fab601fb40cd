const metadata = params => ({
  customHTMLBodyStart: '<!-- Google Tag Manager (noscript) -->\n<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MKJC2T5" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>\n<!-- End Google Tag Manager (noscript) -->',
  customHTMLHeadStart: "<!-- Google Optimize -->\n<script async=\"\" src=\"https://www.googleoptimize.com/optimize.js?id=OPT-WLVG3BP\"></script>\n\n<!-- Google tag (gtag.js) (Google Analytics) \n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-9CHTJBNSSD\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n  gtag('config', 'G-9CHTJBNSSD');\n</script>\n-->\n\n<!-- Google Tag Manager -->\n<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\nnew Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\nj=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n})(window,document,'script','dataLayer','GTM-MKJC2T5');</script>\n\n\n<!-- Hotjar Tracking Code for Popless - Prod -->\n<script>\n    (function(h,o,t,j,a,r){\n        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n        h._hjSettings={hjid:2688343,hjsv:6};\n        a=o.getElementsByTagName('head')[0];\n        r=o.createElement('script');r.async=1;\n        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n        a.appendChild(r);\n    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n</script>\n\n<!-- OpenReplay Tracking Code -->\n<!-- OpenReplay Tracking Code for Popless - Prd -->\n<script>\n  var  projectKey = window.location.host==='fe.dev.popless.com' ? 'aDdqPaN8EUCpRu3nr1AA' : \"k8xXUcLSdYaBbzENYuXN\";\n  var initOpts = {\n    projectKey: projectKey,\n    defaultInputMode: 0,\n    obscureTextNumbers: false,\n    obscureTextEmails: true,\n  };\n  var startOpts = { userID: \"\" };\n  (function(A,s,a,y,e,r){\n    r=window.OpenReplay=[e,r,y,[s-1, e]];\n    s=document.createElement('script');s.src=A;s.async=!a;\n    document.getElementsByTagName('head')[0].appendChild(s);\n    r.start=function(v){r.push([0])};\n    r.stop=function(v){r.push([1])};\n    r.setUserID=function(id){r.push([2,id])};\n    r.setUserAnonymousID=function(id){r.push([3,id])};\n    r.setMetadata=function(k,v){r.push([4,k,v])};\n    r.event=function(k,p,i){r.push([5,k,p,i])};\n    r.issue=function(k,p){r.push([6,k,p])};\n    r.isActive=function(){return false};\n    r.getSessionToken=function(){};\n  })(\"//static.openreplay.com/latest/openreplay.js\",1,0,initOpts,startOpts);\n</script>",
  description: "Popless is the best tutoring platform to manage and grow your tutoring business. Power your teaching and students from an all-in-one dashboard. Spend more time teaching and engaging students.",
  favicon: new URL("https://framerusercontent.com/images/AnCV8qCUo5cbDyJncVhFY1rMqIo.png").href,
  socialImage: new URL("https://framerusercontent.com/images/FsysPmdg07zDIFM8LnIlXvGY3x0.jpg").href,
  title: "Popless | The all-in-one tutoring platform for private tutors and group classes"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};