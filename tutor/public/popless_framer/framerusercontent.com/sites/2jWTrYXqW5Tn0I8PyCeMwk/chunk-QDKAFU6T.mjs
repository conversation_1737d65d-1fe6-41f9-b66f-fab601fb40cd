import{b as P,c as E}from"./chunk-BI4OMGMN.mjs";import{b as x,c as Q}from"./chunk-VWWF2A63.mjs";import{H as b,K,N as J,e as q,f as H,j as m,n as _,o as A,t as G}from"./chunk-5F276QAW.mjs";import{c as i}from"./chunk-OIST4OYN.mjs";function y(){return y=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},y.apply(this,arguments)}function ce(t,e){if(t==null)return{};var r={},n=Object.keys(t),c,f;for(f=0;f<n.length;f++)c=n[f],!(e.indexOf(c)>=0)&&(r[c]=t[c]);return r}var u=function(e,r){if(!1){var n;switch(e){case"info":case"warn":case"error":default:}}},ue=function(e){return Object.keys(e).length===0&&e.constructor===Object},O=typeof i>"u",me=function t(e){return Object.keys(e).forEach(function(r){e[r]&&typeof e[r]=="object"?t(e[r]):e[r]===void 0&&delete e[r]}),e},s=function(e){if(!O&&i.Intercom){for(var r=arguments.length,n=new Array(r>1?r-1:0),c=1;c<r;c++)n[c-1]=arguments[c];return i.Intercom.apply(null,[e].concat(n))}else u("error",e+" Intercom instance is not initalized yet")},l="Please wrap your component with `IntercomProvider`.",le=q({boot:function(){return u("error",l)},shutdown:function(){return u("error",l)},hardShutdown:function(){return u("error",l)},update:function(){return u("error",l)},hide:function(){return u("error",l)},show:function(){return u("error",l)},showMessages:function(){return u("error",l)},showNewMessages:function(){return u("error",l)},getVisitorId:function(){return u("error",l),""},startTour:function(){return u("error",l)},trackEvent:function(){return u("error",l)}}),pe=function(e,r){r===void 0&&(r=0);var n=i,c=n.Intercom;if(typeof c=="function")c("reattach_activator"),c("update",n.intercomSettings);else{var f=document,d=function v(){v.c(arguments)};d.q=[],d.c=function(v){d.q.push(v)},n.Intercom=d;var C=function(){setTimeout(function(){var h=f.createElement("script");h.type="text/javascript",h.async=!0,h.src="https://widget.intercom.io/widget/"+e;var w=f.getElementsByTagName("script")[0];w.parentNode.insertBefore(h,w)},r)};document.readyState==="complete"?C():n.attachEvent?n.attachEvent("onload",C):n.addEventListener("load",C,!1)}},fe=function(e){return{custom_launcher_selector:e.customLauncherSelector,alignment:e.alignment,vertical_padding:e.verticalPadding,horizontal_padding:e.horizontalPadding,hide_default_launcher:e.hideDefaultLauncher,session_duration:e.sessionDuration,action_color:e.actionColor,background_color:e.backgroundColor}},X=function(e){return y({company_id:e.companyId,name:e.name,created_at:e.createdAt,plan:e.plan,monthly_spend:e.monthlySpend,user_count:e.userCount,size:e.size,website:e.website,industry:e.industry},e.customAttributes)},de=function(e){return{type:e.type,image_url:e.imageUrl}},ve=function(e){var r;return y({email:e.email,user_id:e.userId,created_at:e.createdAt,name:e.name,phone:e.phone,last_request_at:e.lastRequestAt,unsubscribed_from_emails:e.unsubscribedFromEmails,language_override:e.languageOverride,utm_campaign:e.utmCampaign,utm_content:e.utmContent,utm_medium:e.utmMedium,utm_source:e.utmSource,utm_term:e.utmTerm,avatar:e.avatar&&de(e.avatar),user_hash:e.userHash,company:e.company&&X(e.company),companies:(r=e.companies)==null?void 0:r.map(X)},e.customAttributes)},Y=function(e){return me(y({},fe(e),ve(e)))},Z=function(e){var r=e.appId,n=e.autoBoot,c=n===void 0?!1:n,f=e.autoBootProps,d=e.children,C=e.onHide,v=e.onShow,h=e.onUnreadCountChange,w=e.shouldInitialize,I=w===void 0?!O:w,M=e.apiBase,oe=e.initializeDelay,ae=ce(e,["appId","autoBoot","autoBootProps","children","onHide","onShow","onUnreadCountChange","shouldInitialize","apiBase","initializeDelay"]),g=A(!1),R=A(!1);ue(ae);var T=m(function(o){if(!i.Intercom&&!I){u("warn","Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`");return}if(!g.current){var p=y({app_id:r},M&&{api_base:M},o&&Y(o));i.intercomSettings=p,s("boot",p),g.current=!0}},[M,r,I]);!O&&I&&!R.current&&(pe(r,oe),C&&s("onHide",C),v&&s("onShow",v),h&&s("onUnreadCountChange",h),c&&T(f),R.current=!0);var a=m(function(o,p){if(o===void 0&&(o="A function"),!i.Intercom&&!I){u("warn","Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`");return}if(!g.current){u("warn",["'"+o+"' was called but Intercom has not booted yet. ","Please call 'boot' before calling '"+o+"' or ","set 'autoBoot' to true in the IntercomProvider."].join(""));return}return p()},[I]),k=m(function(){g.current&&(s("shutdown"),g.current=!1)},[]),z=m(function(){g.current&&(s("shutdown"),delete i.Intercom,delete i.intercomSettings,g.current=!1)},[]),D=m(function(){a("update",function(){var o=new Date().getTime();s("update",{last_requested_at:o})})},[a]),F=m(function(o){a("update",function(){if(!o){D();return}var p=Y(o);i.intercomSettings=y({},i.intercomSettings,p),s("update",p)})},[a,D]),N=m(function(){a("hide",function(){s("hide")})},[a]),B=m(function(){a("show",function(){return s("show")})},[a]),j=m(function(){a("showMessages",function(){s("showMessages")})},[a]),$=m(function(o){a("showNewMessage",function(){o?s("showNewMessage",o):s("showNewMessage")})},[a]),U=m(function(){return a("getVisitorId",function(){return s("getVisitorId")})},[a]),L=m(function(o){a("startTour",function(){s("startTour",o)})},[a]),W=m(function(o,p){a("trackEvent",function(){p?s("trackEvent",o,p):s("trackEvent",o)})},[a]),ie=_(function(){return{boot:T,shutdown:k,hardShutdown:z,update:F,hide:N,show:B,showMessages:j,showNewMessages:$,getVisitorId:U,startTour:L,trackEvent:W}},[T,k,z,F,N,B,j,$,U,L,W]),se=_(function(){return d},[d]);return H(le.Provider,{value:ie},se)};var ge={...P,...E,textAlign:"center",padding:15,width:200,height:100,overflow:"hidden"},ee={...ge,color:"#09f",background:"rgb(0, 153, 255, 0.1)",borderColor:"#09f"},te={fontSize:12,fontWeight:600,margin:0},re={fontSize:12,maxWidth:200,lineHeight:1.4,margin:"5px 0 0 0"};function ne({appId:t,style:e,...r}){return b.current()===b.canvas?Q(G.div,{style:{...ee,...e},children:[x("h1",{style:te,children:"Intercom"}),x("p",{style:re,children:"Drop this component into a Screen to add Intercom."})]}):x(Z,{autoBoot:!0,appId:t})}J(ne,{appId:{title:"ID",type:K.String,description:"Create an [Intercom](https://www.intercom.com/) account and copy your workspace ID. [Learn more\u2026](https://www.framer.com/sites/integrations/intercom/)"}});export{ne as a};
//# sourceMappingURL=chunk-QDKAFU6T.mjs.map
