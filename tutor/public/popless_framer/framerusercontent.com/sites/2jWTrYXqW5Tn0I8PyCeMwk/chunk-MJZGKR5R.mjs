import{ta as r}from"./chunk-5F276QAW.mjs";r.loadWebFontsFromSelectors(["Inter-Bold"]);var f=[],m=['.framer-4lUIJ .framer-styles-preset-b6w6oh:not(.rich-text-wrapper), .framer-4lUIJ .framer-styles-preset-b6w6oh.rich-text-wrapper h2 { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 24px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 32px; --framer-text-alignment: start; --framer-paragraph-spacing: 0px; }','@media (max-width: 1919px) and (min-width: 1440px) { .framer-4lUIJ .framer-styles-preset-b6w6oh:not(.rich-text-wrapper), .framer-4lUIJ .framer-styles-preset-b6w6oh.rich-text-wrapper h2 { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 24px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 32px; --framer-text-alignment: start; --framer-paragraph-spacing: 0px; } }','@media (max-width: 1439px) and (min-width: 810px) { .framer-4lUIJ .framer-styles-preset-b6w6oh:not(.rich-text-wrapper), .framer-4lUIJ .framer-styles-preset-b6w6oh.rich-text-wrapper h2 { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 24px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 32px; --framer-text-alignment: start; --framer-paragraph-spacing: 0px; } }','@media (max-width: 809px) and (min-width: 0px) { .framer-4lUIJ .framer-styles-preset-b6w6oh:not(.rich-text-wrapper), .framer-4lUIJ .framer-styles-preset-b6w6oh.rich-text-wrapper h2 { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 18px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 24px; --framer-text-alignment: start; --framer-paragraph-spacing: 0px; } }'],o="framer-4lUIJ";export{f as a,m as b,o as c};
//# sourceMappingURL=chunk-MJZGKR5R.mjs.map
