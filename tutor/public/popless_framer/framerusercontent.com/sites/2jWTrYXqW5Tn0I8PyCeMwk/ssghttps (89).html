// Generated by Fr<PERSON>r (4b4156c)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, Link, RichText, SVG, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import LoginLink from "https://framerusercontent.com/modules/BRFWru5BTDAhogWKAt7P/Jmfmctrb8ZwFoD7TPNxK/LoginLinkLight.js";
const LoginLinkFonts = getFonts(LoginLink);
const enabledGestures = {
  a0XfeqIn2: {
    hover: true
  },
  L1hq47Ifw: {
    hover: true
  },
  z29L6C86N: {
    hover: true
  }
};
const cycleOrder = ["z29L6C86N", "a0XfeqIn2", "E623HYzuZ", "CEVXCr7ws", "L1hq47Ifw"];
const variantClassNames = {
  a0XfeqIn2: "framer-v-1vq2r5t",
  CEVXCr7ws: "framer-v-vurwk9",
  E623HYzuZ: "framer-v-1od3cso",
  L1hq47Ifw: "framer-v-1mccovh",
  z29L6C86N: "framer-v-1b7gv89"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Default - Dropdown": "L1hq47Ifw",
  "Mobile Login": "CEVXCr7ws",
  Default: "z29L6C86N",
  Login: "a0XfeqIn2",
  Mobile: "E623HYzuZ"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "z29L6C86N",
  link: CKHH_NqNO,
  title: I7ENecGG9 = "Hosting",
  tap: obMqikuUN,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "z29L6C86N",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTap1sd3hin = activeVariantCallback(async (...args) => {
    if (obMqikuUN) {
      const res = await obMqikuUN(...args);
      if (res === false) return false;
    }
  });
  const isDisplayed1 = () => {
    if (gestureVariant === "a0XfeqIn2-hover") return false;
    if (["a0XfeqIn2", "CEVXCr7ws"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed2 = () => {
    if (gestureVariant === "L1hq47Ifw-hover") return true;
    if (baseVariant === "L1hq47Ifw") return true;
    return false;
  };
  const isDisplayed3 = () => {
    if (gestureVariant === "L1hq47Ifw-hover") return true;
    if (baseVariant === "L1hq47Ifw") return true;
    return false;
  };
  const isDisplayed4 = () => {
    if (gestureVariant === "a0XfeqIn2-hover") return true;
    if (["a0XfeqIn2", "CEVXCr7ws"].includes(baseVariant)) return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-9JZMP", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: CKHH_NqNO,
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-1b7gv89", className)} framer-14eeasz`,
          "data-framer-name": "Default",
          "data-highlight": true,
          layoutDependency: layoutDependency,
          layoutId: "z29L6C86N",
          onTap: onTap1sd3hin,
          ref: ref,
          style: {
            backgroundColor: "rgb(255, 255, 255)",
            borderBottomLeftRadius: 6,
            borderBottomRightRadius: 6,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6,
            ...style
          },
          transition: transition,
          variants: {
            "a0XfeqIn2-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))"
            },
            "L1hq47Ifw-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))"
            },
            "z29L6C86N-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))"
            },
            CEVXCr7ws: {
              backgroundColor: "rgba(0, 0, 0, 0)"
            },
            E623HYzuZ: {
              backgroundColor: "rgba(0, 0, 0, 0)"
            }
          },
          ...addPropertyOverrides({
            "a0XfeqIn2-hover": {
              "data-framer-name": undefined
            },
            "L1hq47Ifw-hover": {
              "data-framer-name": undefined
            },
            "z29L6C86N-hover": {
              "data-framer-name": undefined
            },
            a0XfeqIn2: {
              "data-framer-name": "Login"
            },
            CEVXCr7ws: {
              "data-framer-name": "Mobile Login"
            },
            E623HYzuZ: {
              "data-framer-name": "Mobile"
            },
            L1hq47Ifw: {
              "data-framer-name": "Default - Dropdown"
            }
          }, baseVariant, gestureVariant),
          children: [isDisplayed1() && /*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--framer-letter-spacing": "-0.2px",
                  "--framer-line-height": "2.4em",
                  "--framer-text-alignment": "center",
                  "--framer-text-color": "var(--extracted-r6o4lv)"
                },
                children: "Hosting"
              })
            }),
            className: "framer-lv43im",
            layoutDependency: layoutDependency,
            layoutId: "LjSz5kYrO",
            style: {
              "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
              "--framer-paragraph-spacing": "0px"
            },
            text: I7ENecGG9,
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true,
            ...addPropertyOverrides({
              E623HYzuZ: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "SW50ZXItTWVkaXVt",
                      "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                      "--framer-font-weight": "500",
                      "--framer-letter-spacing": "-0.2px",
                      "--framer-line-height": "2.4em",
                      "--framer-text-alignment": "left",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Hosting"
                  })
                }),
                fonts: ["Inter-Medium"]
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed2() && /*#__PURE__*/_jsx(motion.div, {
            className: "framer-1h0gjd2",
            layoutDependency: layoutDependency,
            layoutId: "LWsHnXQpl",
            transition: transition,
            children: isDisplayed3() && /*#__PURE__*/_jsx(SVG, {
              className: "framer-1pi75ys",
              "data-framer-name": "Forward",
              fill: "black",
              intrinsicHeight: 15,
              intrinsicWidth: 15,
              layoutDependency: layoutDependency,
              layoutId: "poIl6jluj",
              svg: '<svg width="15" height="15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.834 5.833 7.501 9.167 4.167 5.833" stroke="#202124" stroke-linecap="round" stroke-linejoin="round"/></svg>',
              transition: transition,
              withExternalLayout: true
            })
          }), isDisplayed4() && /*#__PURE__*/_jsx(motion.div, {
            className: "framer-1uwu8gn-container",
            layoutDependency: layoutDependency,
            layoutId: "uT3uuJSsB-container",
            transition: transition,
            children: /*#__PURE__*/_jsx(LoginLink, {
              height: "100%",
              id: "uT3uuJSsB",
              layoutId: "uT3uuJSsB",
              width: "100%"
            })
          })]
        })
      })
    })
  });
});
const css = ['.framer-9JZMP [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-9JZMP * { box-sizing: border-box; }", ".framer-9JZMP .framer-14eeasz { display: block; }", ".framer-9JZMP .framer-1b7gv89 { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px 10px 0px 10px; position: relative; text-decoration: none; width: min-content; }", ".framer-9JZMP .framer-lv43im { flex: none; height: 38px; overflow: hidden; position: relative; white-space: pre; width: auto; }", ".framer-9JZMP .framer-1h0gjd2 { flex: none; height: 16px; overflow: visible; position: relative; width: 16px; }", ".framer-9JZMP .framer-1pi75ys { aspect-ratio: 1 / 1; bottom: -1px; flex: none; height: var(--framer-aspect-ratio-supported, 15px); left: 0px; position: absolute; right: 0px; }", ".framer-9JZMP .framer-1uwu8gn-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-9JZMP .framer-v-1b7gv89 .framer-1b7gv89, .framer-9JZMP .framer-v-1vq2r5t .framer-1b7gv89, .framer-9JZMP .framer-v-1mccovh .framer-1b7gv89 { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-9JZMP .framer-1b7gv89 { gap: 0px; } .framer-9JZMP .framer-1b7gv89 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-9JZMP .framer-1b7gv89 > :first-child { margin-left: 0px; } .framer-9JZMP .framer-1b7gv89 > :last-child { margin-right: 0px; } }", ".framer-9JZMP.framer-v-1od3cso .framer-1b7gv89, .framer-9JZMP.framer-v-vurwk9 .framer-1b7gv89 { justify-content: flex-start; padding: 0px 0px 0px 0px; }", ".framer-9JZMP.framer-v-1mccovh .framer-1b7gv89 { gap: 4px; }", ".framer-9JZMP.framer-v-1mccovh .framer-1h0gjd2 { height: 15px; width: 15px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-9JZMP.framer-v-1mccovh .framer-1b7gv89 { gap: 0px; } .framer-9JZMP.framer-v-1mccovh .framer-1b7gv89 > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-9JZMP.framer-v-1mccovh .framer-1b7gv89 > :first-child { margin-left: 0px; } .framer-9JZMP.framer-v-1mccovh .framer-1b7gv89 > :last-child { margin-right: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicHeight 38
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicWidth 77
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]},"a0XfeqIn2":{"layout":["auto","auto"]},"E623HYzuZ":{"layout":["auto","auto"]},"CEVXCr7ws":{"layout":["auto","auto"]},"L1hq47Ifw":{"layout":["auto","auto"]},"nRRpfEsil":{"layout":["auto","auto"]},"E5qtGaROU":{"layout":["auto","auto"]},"EyDpJUmJQ":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerVariables {"CKHH_NqNO":"link","I7ENecGG9":"title","obMqikuUN":"tap"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                */
const FramermYJ78JUAb = withCSS(Component, css);
export default FramermYJ78JUAb;
FramermYJ78JUAb.displayName = "Menu Item";
FramermYJ78JUAb.defaultProps = {
  height: 38,
  width: 77
};
addPropertyControls(FramermYJ78JUAb, {
  variant: {
    options: ["z29L6C86N", "a0XfeqIn2", "E623HYzuZ", "CEVXCr7ws", "L1hq47Ifw"],
    optionTitles: ["Default", "Login", "Mobile", "Mobile Login", "Default - Dropdown"],
    title: "Variant",
    type: ControlType.Enum
  },
  CKHH_NqNO: {
    title: "Link",
    type: ControlType.Link
  },
  I7ENecGG9: {
    defaultValue: "Hosting",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  obMqikuUN: {
    title: "Tap",
    type: ControlType.EventHandler
  }
});
addFonts(FramermYJ78JUAb, [...LoginLinkFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramermYJ78JUAb",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "38",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]},\"a0XfeqIn2\":{\"layout\":[\"auto\",\"auto\"]},\"E623HYzuZ\":{\"layout\":[\"auto\",\"auto\"]},\"CEVXCr7ws\":{\"layout\":[\"auto\",\"auto\"]},\"L1hq47Ifw\":{\"layout\":[\"auto\",\"auto\"]},\"nRRpfEsil\":{\"layout\":[\"auto\",\"auto\"]},\"E5qtGaROU\":{\"layout\":[\"auto\",\"auto\"]},\"EyDpJUmJQ\":{\"layout\":[\"auto\",\"auto\"]}}}",
        "framerIntrinsicWidth": "77",
        "framerContractVersion": "1",
        "framerVariables": "{\"CKHH_NqNO\":\"link\",\"I7ENecGG9\":\"title\",\"obMqikuUN\":\"tap\"}"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};