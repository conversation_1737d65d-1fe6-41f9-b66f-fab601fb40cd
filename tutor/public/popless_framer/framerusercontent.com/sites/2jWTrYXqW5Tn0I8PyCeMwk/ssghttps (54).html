import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors([]);
export const fonts = [];
export const css = ['.framer-oYeHA .framer-styles-preset-thl1vx:not(.rich-text-wrapper), .framer-oYeHA .framer-styles-preset-thl1vx.rich-text-wrapper a { --framer-link-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0); --framer-link-text-decoration: none; --framer-link-hover-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0) /* {"name":"Blue/Default"} */; --framer-link-hover-text-decoration: underline; }'];
export const className = "framer-oYeHA";
export const __FramerMetadata__ = {
  "exports": {
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};