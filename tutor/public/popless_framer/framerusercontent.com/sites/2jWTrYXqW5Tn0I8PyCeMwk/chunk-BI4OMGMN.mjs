import{H as v,I as k,K as i,O as A,j as S,k as d,n as f,o as m,r as M,s as R}from"./chunk-5F276QAW.mjs";import{b as t}from"./chunk-OIST4OYN.mjs";var z='"Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',E={position:"relative",width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"},L={...E,borderRadius:6,background:"rgba(136, 85, 255, 0.3)",color:"#85F",border:"1px dashed #85F",flexDirection:"column"},O={onClick:{type:i.EventHandler},onMouseEnter:{type:i.EventHandler},onMouseLeave:{type:i.EventHandler}},$={type:i.Number,title:"Font Size",min:2,max:200,step:1,displayStepper:!0},N={font:{type:i.Boolean,title:"Font",defaultValue:!1,disabledTitle:"Default",enabledTitle:"Custom"},fontFamily:{type:i.String,title:"Family",placeholder:"Inter",hidden:({font:e})=>!e},fontWeight:{type:i.Enum,title:"Weight",options:[100,200,300,400,500,600,700,800,900],optionTitles:["Thin","Extra-light","Light","Regular","Medium","Semi-bold","Bold","Extra-bold","Black"],hidden:({font:e})=>!e}};function D(e,a){return T(!0,e,a)}function H(e,a){return T(!1,e,a)}function T(e,a,s=!0){let n=A();d(()=>{s&&n===e&&a()},[n])}var B=()=>{if(typeof t<"u"){let e=t.userAgent.toLowerCase();return(e.indexOf("safari")>-1||e.indexOf("framermobile")>-1||e.indexOf("framerx")>-1)&&e.indexOf("chrome")<0}else return!1},Y=()=>f(()=>B(),[]);var C=e=>e instanceof M;function F(e){let a=m(null);return a.current===null&&(a.current=e()),a.current}function le(e,a){var s;let n=m(a),u=m(),l=m(!1),c=v.current()===v.canvas,h=a?.onChangeDeps?a.onChangeDeps:[],V=S(a?.onChange,[...h]),_=S(p=>!((s=n.current)===null||s===void 0)&&s.transform?n.current.transform(p):p,[]),g=F(()=>C(e)?e:R(_(e)));return d(()=>{if(!C(e)&&l.current){var p,b;let x=_(e);if((p=u.current)===null||p===void 0||p.stop(),V&&V(x,g),!((b=n.current)===null||b===void 0)&&b.animate&&!c){var y;u.current=k(g,x,(y=n.current)===null||y===void 0?void 0:y.transition)}else g.set(x)}l.current=!0},[e,...h]),g}function fe(e){let{borderRadius:a,isMixedBorderRadius:s,topLeftRadius:n,topRightRadius:u,bottomRightRadius:l,bottomLeftRadius:c}=e;return f(()=>s?`${n}px ${u}px ${l}px ${c}px`:`${a}px`,[a,s,n,u,l,c])}var he={borderRadius:{title:"Radius",type:i.FusedNumber,toggleKey:"isMixedBorderRadius",toggleTitles:["Radius","Radius per corner"],valueKeys:["topLeftRadius","topRightRadius","bottomRightRadius","bottomLeftRadius"],valueLabels:["TL","TR","BR","BL"],min:0}};function ge(e){let{padding:a,paddingPerSide:s,paddingTop:n,paddingRight:u,paddingBottom:l,paddingLeft:c}=e;return f(()=>s?`${n}px ${u}px ${l}px ${c}px`:a,[a,s,n,u,l,c])}var Ce={padding:{type:i.FusedNumber,toggleKey:"paddingPerSide",toggleTitles:["Padding","Padding per side"],valueKeys:["paddingTop","paddingRight","paddingBottom","paddingLeft"],valueLabels:["T","R","B","L"],min:0,title:"Padding"}};export{z as a,E as b,L as c,O as d,N as e,D as f,H as g,Y as h,le as i,fe as j,he as k,ge as l,Ce as m};
//# sourceMappingURL=chunk-BI4OMGMN.mjs.map
