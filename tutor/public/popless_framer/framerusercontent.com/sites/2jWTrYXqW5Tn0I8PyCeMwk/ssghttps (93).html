// Generated by <PERSON><PERSON><PERSON> (716dd6f)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, SVG, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  aNF2qN77_: {
    hover: true
  },
  Dg1ZxD_GY: {
    hover: true
  },
  Nwetb9ShE: {
    hover: true
  },
  WOYt30IIU: {
    hover: true
  }
};
const cycleOrder = ["WOYt30IIU", "Dg1ZxD_GY", "Nwetb9ShE", "cR0QzlygM", "aNF2qN77_"];
const variantClassNames = {
  aNF2qN77_: "framer-v-a79dv5",
  cR0QzlygM: "framer-v-aafqwg",
  Dg1ZxD_GY: "framer-v-19y3961",
  Nwetb9ShE: "framer-v-cccw60",
  WOYt30IIU: "framer-v-3s2vxh"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Twitter White": "cR0QzlygM",
  Facebook: "Dg1ZxD_GY",
  Instagram: "aNF2qN77_",
  Twitter: "WOYt30IIU",
  Youtube: "Nwetb9ShE"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "WOYt30IIU",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "WOYt30IIU",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const isDisplayed = () => {
    if (gestureVariant === "Dg1ZxD_GY-hover") return true;
    if (baseVariant === "Dg1ZxD_GY") return true;
    return false;
  };
  const isDisplayed1 = () => {
    if (gestureVariant === "Nwetb9ShE-hover") return true;
    if (baseVariant === "Nwetb9ShE") return true;
    return false;
  };
  const isDisplayed2 = () => {
    if (["Dg1ZxD_GY-hover", "Nwetb9ShE-hover", "aNF2qN77_-hover"].includes(gestureVariant)) return false;
    if (["Dg1ZxD_GY", "Nwetb9ShE", "aNF2qN77_"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed3 = () => {
    if (gestureVariant === "aNF2qN77_-hover") return true;
    if (baseVariant === "aNF2qN77_") return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-jdoxF", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: "https://twitter.com/popless_hq",
        openInNewTab: true,
        ...addPropertyOverrides({
          aNF2qN77_: {
            href: "https://www.instagram.com/popless_hq/"
          },
          Dg1ZxD_GY: {
            href: "https://www.facebook.com/popless.tutoring"
          },
          Nwetb9ShE: {
            href: "https://www.youtube.com/@popless2758"
          }
        }, baseVariant, gestureVariant),
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-3s2vxh", className)} framer-1gpju0`,
          "data-framer-name": "Twitter",
          layoutDependency: layoutDependency,
          layoutId: "WOYt30IIU",
          ref: ref,
          style: {
            ...style
          },
          transition: transition,
          ...addPropertyOverrides({
            "aNF2qN77_-hover": {
              "data-framer-name": undefined
            },
            "Dg1ZxD_GY-hover": {
              "data-framer-name": undefined
            },
            "Nwetb9ShE-hover": {
              "data-framer-name": undefined
            },
            "WOYt30IIU-hover": {
              "data-framer-name": undefined
            },
            aNF2qN77_: {
              "data-framer-name": "Instagram"
            },
            cR0QzlygM: {
              "data-framer-name": "Twitter White"
            },
            Dg1ZxD_GY: {
              "data-framer-name": "Facebook"
            },
            Nwetb9ShE: {
              "data-framer-name": "Youtube"
            }
          }, baseVariant, gestureVariant),
          children: [isDisplayed() && /*#__PURE__*/_jsx(SVG, {
            className: "framer-1hy89en",
            "data-framer-name": "Facebook",
            layout: "position",
            layoutDependency: layoutDependency,
            layoutId: "btxFhk7u3",
            opacity: 1,
            radius: 0,
            svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><path d="M 15.402 21 L 15.402 14.034 L 17.735 14.034 L 18.084 11.326 L 15.402 11.326 L 15.402 9.598 C 15.402 8.814 15.62 8.279 16.744 8.279 L 18.178 8.279 L 18.178 5.857 C 17.484 5.783 16.786 5.748 16.088 5.75 C 14.021 5.75 12.606 7.012 12.606 9.33 L 12.606 11.326 L 10.268 11.326 L 10.268 14.034 L 12.606 14.034 L 12.606 21 L 4 21 C 3.735 21 3.48 20.895 3.293 20.707 C 3.105 20.52 3 20.265 3 20 L 3 4 C 3 3.735 3.105 3.48 3.293 3.293 C 3.48 3.105 3.735 3 4 3 L 20 3 C 20.265 3 20.52 3.105 20.707 3.293 C 20.895 3.48 21 3.735 21 4 L 21 20 C 21 20.265 20.895 20.52 20.707 20.707 C 20.52 20.895 20.265 21 20 21 L 15.402 21 Z" fill="rgb(32,33,36)"></path></svg>',
            svgContentId: 3501114684,
            transition: transition,
            variants: {
              "Dg1ZxD_GY-hover": {
                opacity: .6
              }
            },
            withExternalLayout: true,
            ...addPropertyOverrides({
              "Dg1ZxD_GY-hover": {
                opacity: .6,
                svgContentId: 1057503237
              },
              Dg1ZxD_GY: {
                svgContentId: 3422591038
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed1() && /*#__PURE__*/_jsx(SVG, {
            className: "framer-8azp5k",
            "data-framer-name": "Youtube",
            layout: "position",
            layoutDependency: layoutDependency,
            layoutId: "wylu0W_Rj",
            opacity: 1,
            radius: 0,
            svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><path d="M 22.539 6.966 C 22.415 6.522 22.172 6.119 21.837 5.802 C 21.493 5.475 21.071 5.241 20.611 5.122 C 18.89 4.668 11.995 4.668 11.995 4.668 C 9.12 4.636 6.246 4.78 3.389 5.099 C 2.929 5.227 2.508 5.466 2.163 5.796 C 1.824 6.122 1.578 6.524 1.45 6.965 C 1.142 8.625 0.992 10.311 1.003 12 C 0.992 13.687 1.141 15.373 1.45 17.035 C 1.575 17.474 1.82 17.874 2.16 18.198 C 2.5 18.522 2.923 18.755 3.389 18.879 C 5.133 19.332 11.995 19.332 11.995 19.332 C 14.873 19.364 17.75 19.221 20.611 18.901 C 21.071 18.782 21.493 18.548 21.837 18.221 C 22.172 17.904 22.414 17.501 22.538 17.057 C 22.855 15.397 23.008 13.711 22.998 12.021 C 23.021 10.324 22.868 8.63 22.539 6.965 Z M 9.802 15.138 L 9.802 8.863 L 15.539 12.001 Z" fill="rgb(32,33,36)"></path></svg>',
            svgContentId: 3857237557,
            transition: transition,
            variants: {
              "Nwetb9ShE-hover": {
                opacity: .6
              }
            },
            withExternalLayout: true,
            ...addPropertyOverrides({
              "Nwetb9ShE-hover": {
                opacity: .6,
                svgContentId: 2307256350
              },
              Nwetb9ShE: {
                svgContentId: 3405036037
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed2() && /*#__PURE__*/_jsx(SVG, {
            className: "framer-1kwu6ex",
            "data-framer-name": "Twitter",
            layout: "position",
            layoutDependency: layoutDependency,
            layoutId: "cb2toD5v_",
            opacity: 1,
            radius: 0,
            svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><path d="M 23 5.008 C 22.191 5.375 21.322 5.624 20.408 5.736 C 21.351 5.158 22.056 4.248 22.392 3.175 C 21.507 3.715 20.537 4.094 19.526 4.298 C 18.847 3.554 17.946 3.061 16.965 2.895 C 15.984 2.729 14.977 2.9 14.1 3.381 C 13.223 3.862 12.526 4.627 12.116 5.556 C 11.707 6.485 11.608 7.526 11.835 8.519 C 10.041 8.427 8.285 7.948 6.682 7.115 C 5.079 6.282 3.665 5.113 2.532 3.684 C 2.144 4.369 1.921 5.164 1.921 6.01 C 1.921 6.772 2.104 7.522 2.454 8.194 C 2.804 8.866 3.311 9.439 3.929 9.862 C 3.212 9.838 2.511 9.64 1.885 9.283 L 1.885 9.342 C 1.885 10.411 2.245 11.446 2.905 12.273 C 3.565 13.1 4.484 13.668 5.505 13.879 C 4.841 14.064 4.143 14.091 3.467 13.959 C 3.755 14.878 4.317 15.682 5.073 16.258 C 5.829 16.834 6.742 17.153 7.684 17.171 C 6.085 18.457 4.11 19.155 2.077 19.152 C 1.717 19.152 1.358 19.131 1 19.088 C 3.063 20.448 5.465 21.169 7.918 21.167 C 16.221 21.167 20.761 14.117 20.761 8.002 C 20.761 7.804 20.756 7.603 20.747 7.405 C 21.63 6.75 22.392 5.939 22.998 5.011 L 23 5.008 Z" fill="rgb(32,33,36)"></path></svg>',
            svgContentId: 1937791629,
            transition: transition,
            variants: {
              "WOYt30IIU-hover": {
                opacity: .6
              }
            },
            withExternalLayout: true,
            ...addPropertyOverrides({
              "WOYt30IIU-hover": {
                opacity: .6,
                svgContentId: 4094706458
              },
              cR0QzlygM: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><path d="M 23 5.008 C 22.191 5.375 21.322 5.624 20.408 5.736 C 21.351 5.158 22.056 4.248 22.392 3.175 C 21.507 3.715 20.537 4.094 19.526 4.298 C 18.847 3.554 17.946 3.061 16.965 2.895 C 15.984 2.729 14.977 2.9 14.1 3.381 C 13.223 3.862 12.526 4.627 12.116 5.556 C 11.707 6.485 11.608 7.526 11.835 8.519 C 10.041 8.427 8.285 7.948 6.682 7.115 C 5.079 6.282 3.665 5.113 2.532 3.684 C 2.144 4.369 1.921 5.164 1.921 6.01 C 1.921 6.772 2.104 7.522 2.454 8.194 C 2.804 8.866 3.311 9.439 3.929 9.862 C 3.212 9.838 2.511 9.64 1.885 9.283 L 1.885 9.342 C 1.885 10.411 2.245 11.446 2.905 12.273 C 3.565 13.1 4.484 13.668 5.505 13.879 C 4.841 14.064 4.143 14.091 3.467 13.959 C 3.755 14.878 4.317 15.682 5.073 16.258 C 5.829 16.834 6.742 17.153 7.684 17.171 C 6.085 18.457 4.11 19.155 2.077 19.152 C 1.717 19.152 1.358 19.131 1 19.088 C 3.063 20.448 5.465 21.169 7.918 21.167 C 16.221 21.167 20.761 14.117 20.761 8.002 C 20.761 7.804 20.756 7.603 20.747 7.405 C 21.63 6.75 22.392 5.939 22.998 5.011 L 23 5.008 Z" fill="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */"></path></svg>',
                svgContentId: 739324438
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed3() && /*#__PURE__*/_jsx(SVG, {
            className: "framer-6h4hjp",
            "data-framer-name": "Instagram",
            fill: "rgba(0,0,0,1)",
            intrinsicHeight: 20,
            intrinsicWidth: 20,
            layoutDependency: layoutDependency,
            layoutId: "A6xsVRdT1",
            svg: '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M10 0C7.28625 0 6.945 0.0125 5.87875 0.06C4.8125 0.11 4.08625 0.2775 3.45 0.525C2.78262 0.776024 2.17811 1.16978 1.67875 1.67875C1.1701 2.17837 0.776384 2.7828 0.525 3.45C0.2775 4.085 0.10875 4.8125 0.06 5.875C0.0125 6.94375 0 7.28375 0 10.0013C0 12.7163 0.0125 13.0563 0.06 14.1225C0.11 15.1875 0.2775 15.9137 0.525 16.55C0.78125 17.2075 1.1225 17.765 1.67875 18.3212C2.23375 18.8775 2.79125 19.22 3.44875 19.475C4.08625 19.7225 4.81125 19.8912 5.87625 19.94C6.94375 19.9875 7.28375 20 10 20C12.7163 20 13.055 19.9875 14.1225 19.94C15.1863 19.89 15.915 19.7225 16.5513 19.475C17.2182 19.2239 17.8223 18.8301 18.3212 18.3212C18.8775 17.765 19.2187 17.2075 19.475 16.55C19.7212 15.9137 19.89 15.1875 19.94 14.1225C19.9875 13.0563 20 12.7163 20 10C20 7.28375 19.9875 6.94375 19.94 5.87625C19.89 4.8125 19.7212 4.085 19.475 3.45C19.2237 2.78278 18.8299 2.17834 18.3212 1.67875C17.822 1.16959 17.2175 0.775807 16.55 0.525C15.9125 0.2775 15.185 0.10875 14.1212 0.06C13.0537 0.0125 12.715 0 9.9975 0H10.0013H10ZM9.10375 1.8025H10.0013C12.6713 1.8025 12.9875 1.81125 14.0412 1.86C15.0162 1.90375 15.5462 2.0675 15.8987 2.20375C16.365 2.385 16.6987 2.6025 17.0487 2.9525C17.3987 3.3025 17.615 3.635 17.7963 4.1025C17.9338 4.45375 18.0963 4.98375 18.14 5.95875C18.1888 7.0125 18.1988 7.32875 18.1988 9.9975C18.1988 12.6663 18.1888 12.9837 18.14 14.0375C18.0963 15.0125 17.9325 15.5413 17.7963 15.8938C17.6359 16.3279 17.38 16.7205 17.0475 17.0425C16.6975 17.3925 16.365 17.6088 15.8975 17.79C15.5475 17.9275 15.0175 18.09 14.0412 18.135C12.9875 18.1825 12.6713 18.1938 10.0013 18.1938C7.33125 18.1938 7.01375 18.1825 5.96 18.135C4.985 18.09 4.45625 17.9275 4.10375 17.79C3.66937 17.6299 3.27641 17.3745 2.95375 17.0425C2.62094 16.72 2.36465 16.3271 2.20375 15.8925C2.0675 15.5412 1.90375 15.0113 1.86 14.0363C1.8125 12.9825 1.8025 12.6662 1.8025 9.995C1.8025 7.325 1.8125 7.01 1.86 5.95625C1.905 4.98125 2.0675 4.45125 2.205 4.09875C2.38625 3.6325 2.60375 3.29875 2.95375 2.94875C3.30375 2.59875 3.63625 2.3825 4.10375 2.20125C4.45625 2.06375 4.985 1.90125 5.96 1.85625C6.8825 1.81375 7.24 1.80125 9.10375 1.8V1.8025V1.8025ZM15.3387 3.4625C15.1812 3.4625 15.0251 3.49354 14.8795 3.55384C14.7339 3.61415 14.6017 3.70254 14.4902 3.81397C14.3788 3.9254 14.2904 4.05769 14.2301 4.20328C14.1698 4.34887 14.1387 4.50491 14.1387 4.6625C14.1387 4.82009 14.1698 4.97613 14.2301 5.12172C14.2904 5.26731 14.3788 5.3996 14.4902 5.51103C14.6017 5.62246 14.7339 5.71085 14.8795 5.77116C15.0251 5.83146 15.1812 5.8625 15.3387 5.8625C15.657 5.8625 15.9622 5.73607 16.1873 5.51103C16.4123 5.28598 16.5387 4.98076 16.5387 4.6625C16.5387 4.34424 16.4123 4.03902 16.1873 3.81397C15.9622 3.58893 15.657 3.4625 15.3387 3.4625V3.4625ZM10.0013 4.865C9.32009 4.85437 8.64362 4.97936 8.01122 5.23268C7.37883 5.486 6.80314 5.86259 6.31769 6.34053C5.83223 6.81847 5.44671 7.38821 5.18355 8.01657C4.9204 8.64494 4.78488 9.31938 4.78488 10.0006C4.78488 10.6819 4.9204 11.3563 5.18355 11.9847C5.44671 12.613 5.83223 13.1828 6.31769 13.6607C6.80314 14.1387 7.37883 14.5153 8.01122 14.7686C8.64362 15.0219 9.32009 15.1469 10.0013 15.1363C11.3494 15.1152 12.6353 14.5649 13.5812 13.6041C14.5272 12.6432 15.0574 11.349 15.0574 10.0006C15.0574 8.65228 14.5272 7.35802 13.5812 6.39719C12.6353 5.43636 11.3494 4.88603 10.0013 4.865V4.865ZM10.0013 6.66625C10.8854 6.66625 11.7334 7.01748 12.3586 7.64268C12.9838 8.26788 13.335 9.11583 13.335 10C13.335 10.8842 12.9838 11.7321 12.3586 12.3573C11.7334 12.9825 10.8854 13.3337 10.0013 13.3337C9.11709 13.3337 8.26913 12.9825 7.64393 12.3573C7.01873 11.7321 6.6675 10.8842 6.6675 10C6.6675 9.11583 7.01873 8.26788 7.64393 7.64268C8.26913 7.01748 9.11709 6.66625 10.0013 6.66625V6.66625Z" fill="black"/>\n</svg>\n',
            transition: transition,
            variants: {
              "aNF2qN77_-hover": {
                opacity: .6
              }
            },
            withExternalLayout: true
          })]
        })
      })
    })
  });
});
const css = ['.framer-jdoxF [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-jdoxF * { box-sizing: border-box; }", ".framer-jdoxF .framer-1gpju0 { display: block; }", ".framer-jdoxF .framer-3s2vxh { height: 24px; overflow: hidden; position: relative; text-decoration: none; width: 24px; }", ".framer-jdoxF .framer-1hy89en, .framer-jdoxF .framer-8azp5k, .framer-jdoxF .framer-1kwu6ex { flex: none; height: 24px; left: 0px; position: absolute; top: 0px; width: 24px; }", ".framer-jdoxF .framer-6h4hjp { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 20px); left: calc(50.00000000000002% - 20px / 2); position: absolute; top: calc(50.00000000000002% - 20px / 2); width: 20px; }", ".framer-jdoxF .framer-v-3s2vxh .framer-3s2vxh, .framer-jdoxF .framer-v-19y3961 .framer-3s2vxh, .framer-jdoxF .framer-v-cccw60 .framer-3s2vxh, .framer-jdoxF .framer-v-a79dv5 .framer-3s2vxh { cursor: pointer; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerIntrinsicHeight 24
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerIntrinsicWidth 24
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","fixed"]},"Dg1ZxD_GY":{"layout":["fixed","fixed"]},"Nwetb9ShE":{"layout":["fixed","fixed"]},"cR0QzlygM":{"layout":["fixed","fixed"]},"aNF2qN77_":{"layout":["fixed","fixed"]},"c2MNABKVi":{"layout":["fixed","fixed"]},"AAw0G06uy":{"layout":["fixed","fixed"]},"yGu84DxY7":{"layout":["fixed","fixed"]},"fzWX2do8x":{"layout":["fixed","fixed"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            */
const Framery7yooNBNE = withCSS(Component, css);
export default Framery7yooNBNE;
Framery7yooNBNE.displayName = "Socials";
Framery7yooNBNE.defaultProps = {
  height: 24,
  width: 24
};
addPropertyControls(Framery7yooNBNE, {
  variant: {
    options: ["WOYt30IIU", "Dg1ZxD_GY", "Nwetb9ShE", "cR0QzlygM", "aNF2qN77_"],
    optionTitles: ["Twitter", "Facebook", "Youtube", "Twitter White", "Instagram"],
    title: "Variant",
    type: ControlType.Enum
  }
});
addFonts(Framery7yooNBNE, []);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "Framery7yooNBNE",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "24",
        "framerIntrinsicWidth": "24",
        "framerContractVersion": "1",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"fixed\"]},\"Dg1ZxD_GY\":{\"layout\":[\"fixed\",\"fixed\"]},\"Nwetb9ShE\":{\"layout\":[\"fixed\",\"fixed\"]},\"cR0QzlygM\":{\"layout\":[\"fixed\",\"fixed\"]},\"aNF2qN77_\":{\"layout\":[\"fixed\",\"fixed\"]},\"c2MNABKVi\":{\"layout\":[\"fixed\",\"fixed\"]},\"AAw0G06uy\":{\"layout\":[\"fixed\",\"fixed\"]},\"yGu84DxY7\":{\"layout\":[\"fixed\",\"fixed\"]},\"fzWX2do8x\":{\"layout\":[\"fixed\",\"fixed\"]}}}"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./y7yooNBNE.map