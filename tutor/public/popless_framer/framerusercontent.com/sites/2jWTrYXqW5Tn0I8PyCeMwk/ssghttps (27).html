const metadata = params => ({
  bodyClassName: "framer-body-qFQZWHHvr",
  breakpoints: [{
    hash: "xokaa8",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "f7he9s",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "2i6wx8",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | New features | The all-in-one tutor platform for private tutors and group classes",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};