import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime"; // Generated by <PERSON>amer (76f10ba)
import { addFonts, addPropertyControls, ControlType, cx, getFonts, RichText, Text, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import { Icon as Phosphor } from "https://framerusercontent.com/modules/tYScH7LTqUtz5KUaUAYP/C31a5jsoI7tLti4X41zP/Phosphor.js";
import * as React from "react";
const PhosphorFonts = getFonts(Phosphor);
const cycleOrder = ["E8fyzpKQN", "QDi0tDYwF"];
const variantClassNames = {
  E8fyzpKQN: "framer-v-139nqr4",
  QDi0tDYwF: "framer-v-o4nu4d"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Desktop: "E8fyzpKQN",
  Phone: "QDi0tDYwF"
};
const transitions = {
  default: {
    type: "spring",
    ease: [.44, 0, .56, 1],
    duration: .3,
    delay: 0,
    stiffness: 500,
    damping: 60,
    mass: 1
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "E8fyzpKQN",
  feature: v24h3Kb1n = "External editors",
  amount2: yVwIWxWu9 = "50",
  check2: Q6sofBfF0 = false,
  amount3: ZYP9nkQ5k = "Unlimited",
  check3: SiWixZYQ0 = false,
  cross2: Ng1h5XbTv = false,
  close3: EIAYgw7zE = false,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    variants,
    baseVariant,
    gestureVariant,
    classNames,
    transition,
    setVariant,
    setGestureState
  } = useVariantState({
    defaultVariant: "E8fyzpKQN",
    variant,
    transitions,
    variantClassNames,
    cycleOrder
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const isDisplayed1 = () => {
    if (baseVariant === "QDi0tDYwF") return true;
    return false;
  };
  const isDisplayed2 = () => {
    if (baseVariant === "QDi0tDYwF") return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-oLFlJ", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsxs(motion.div, {
        ...restProps,
        className: cx("framer-139nqr4", className),
        style: {
          ...style
        },
        layoutId: "E8fyzpKQN",
        transition: transition,
        layoutDependency: layoutDependency,
        background: null,
        "data-framer-name": "Desktop",
        ref: ref,
        ...addPropertyOverrides({
          QDi0tDYwF: {
            "data-framer-name": "Phone",
            background: null
          }
        }, baseVariant, gestureVariant),
        children: [/*#__PURE__*/_jsx(motion.div, {
          className: "framer-1ahvnqf",
          style: {},
          layoutId: "M2wv7WZso",
          transition: transition,
          layoutDependency: layoutDependency,
          background: null,
          "data-framer-name": "Item",
          ...addPropertyOverrides({
            QDi0tDYwF: {
              background: null
            }
          }, baseVariant, gestureVariant),
          children: /*#__PURE__*/_jsx(Text, {
            style: {
              "--framer-font-family": '"Inter", sans-serif',
              "--framer-font-style": "normal",
              "--framer-font-weight": 400,
              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
              "--framer-font-size": "18px",
              "--framer-letter-spacing": "-0.5px",
              "--framer-text-transform": "none",
              "--framer-text-decoration": "none",
              "--framer-line-height": "1.1em",
              "--framer-text-alignment": "left",
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline"
            },
            withExternalLayout: true,
            verticalAlignment: "top",
            __fromCanvasComponent: true,
            alignment: "left",
            fonts: ["GF;Inter-regular"],
            className: "framer-1nkdkpl",
            layoutId: "v04CE3z7V",
            transition: transition,
            layoutDependency: layoutDependency,
            rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>External editors</span><br></span></span>",
            text: v24h3Kb1n
          })
        }), /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-mu29xm",
          style: {},
          layoutId: "xN7IsV6G0",
          transition: transition,
          layoutDependency: layoutDependency,
          background: null,
          ...addPropertyOverrides({
            QDi0tDYwF: {
              background: null
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1ovfmm0",
            style: {},
            layoutId: "wkV5qhDzz",
            transition: transition,
            layoutDependency: layoutDependency,
            background: null,
            children: [isDisplayed1() && /*#__PURE__*/_jsx(Text, {
              style: {
                opacity: .5,
                "--framer-font-family": '"Inter", sans-serif',
                "--framer-font-style": "normal",
                "--framer-font-weight": 500,
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                "--framer-font-size": "12px",
                "--framer-letter-spacing": "-0.5px",
                "--framer-text-transform": "none",
                "--framer-text-decoration": "none",
                "--framer-line-height": "1.1em",
                "--framer-text-alignment": "left",
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline"
              },
              withExternalLayout: true,
              verticalAlignment: "top",
              __fromCanvasComponent: true,
              alignment: "left",
              fonts: ["GF;Inter-500"],
              className: "framer-1juuemk",
              layoutId: "jXER5hHiC",
              transition: transition,
              layoutDependency: layoutDependency,
              rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Others</span><br></span></span>"
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1p2okk7",
              style: {},
              layoutId: "FGF2wkw85",
              transition: transition,
              layoutDependency: layoutDependency,
              background: null,
              "data-framer-name": "2",
              ...addPropertyOverrides({
                QDi0tDYwF: {
                  background: null
                }
              }, baseVariant, gestureVariant),
              children: [Q6sofBfF0 && /*#__PURE__*/_jsx(motion.div, {
                style: {},
                className: "framer-keur0i-container",
                layoutId: "YOyrh6qSA-container",
                transition: transition,
                layoutDependency: layoutDependency,
                "data-framer-name": "2 check",
                children: /*#__PURE__*/_jsx(Phosphor, {
                  width: "100%",
                  height: "100%",
                  layoutId: "YOyrh6qSA",
                  id: "YOyrh6qSA",
                  name: "2 check",
                  selectByList: true,
                  iconSelection: "CheckCircle",
                  iconSearch: "House",
                  color: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                  weight: "fill",
                  mirrored: false,
                  style: {
                    width: "100%",
                    height: "100%"
                  }
                })
              }), Ng1h5XbTv && /*#__PURE__*/_jsx(motion.div, {
                style: {},
                className: "framer-1sdgu7z-container",
                layoutId: "jhQOflXxt-container",
                transition: transition,
                layoutDependency: layoutDependency,
                "data-framer-name": "2 cross",
                children: /*#__PURE__*/_jsx(Phosphor, {
                  width: "100%",
                  height: "100%",
                  layoutId: "jhQOflXxt",
                  id: "jhQOflXxt",
                  name: "2 cross",
                  selectByList: true,
                  iconSelection: "XCircle",
                  iconSearch: "House",
                  color: 'var(--token-cd156118-158a-47d3-8fb6-822a4bbc99ee, rgb(224, 225, 227)) /* {"name":"Gray Pressed"} */',
                  weight: "fill",
                  mirrored: false,
                  style: {
                    width: "100%",
                    height: "100%"
                  }
                })
              }), /*#__PURE__*/_jsx(Text, {
                style: {
                  "--framer-font-family": '"Inter", sans-serif',
                  "--framer-font-style": "normal",
                  "--framer-font-weight": 400,
                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  "--framer-font-size": "18px",
                  "--framer-letter-spacing": "-0.4px",
                  "--framer-text-transform": "none",
                  "--framer-text-decoration": "none",
                  "--framer-line-height": "1.5em",
                  "--framer-text-alignment": "left",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline"
                },
                withExternalLayout: true,
                verticalAlignment: "top",
                __fromCanvasComponent: true,
                alignment: "left",
                fonts: ["GF;Inter-regular"],
                className: "framer-c0ykq5",
                layoutId: "o1p91wbvx",
                transition: transition,
                layoutDependency: layoutDependency,
                rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>50</span><br></span></span>",
                text: yVwIWxWu9
              })]
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1itnew8",
            style: {},
            layoutId: "Bj_Bg8zu9",
            transition: transition,
            layoutDependency: layoutDependency,
            background: null,
            children: [isDisplayed2() && /*#__PURE__*/_jsx(RichText, {
              className: "framer-ivz5b6",
              style: {
                "--framer-paragraph-spacing": "0px",
                opacity: .5,
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                whiteSpace: "pre",
                "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              layoutId: "j2Xdalcle",
              transition: transition,
              layoutDependency: layoutDependency,
              fonts: ["GF;Inter-500"],
              withExternalLayout: true,
              verticalAlignment: "top",
              __fromCanvasComponent: true,
              __htmlStructure: '<p style="--framer-font-size:12px; --framer-line-height:1.1em; --framer-text-alignment:left;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:500; --font-selector:R0Y7SW50ZXItNTAw; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:12px; --framer-letter-spacing:-0.5px;">{{ text-placeholder }}</span></p>',
              htmlFromDesign: '<p style="--framer-font-size:12px; --framer-line-height:1.1em; --framer-text-alignment:left;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:500; --font-selector:R0Y7SW50ZXItNTAw; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:12px; --framer-letter-spacing:-0.5px;">Popless</span></p>'
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-13bcadf",
              style: {},
              layoutId: "cE5Pg6kP0",
              transition: transition,
              layoutDependency: layoutDependency,
              background: null,
              "data-framer-name": "3",
              ...addPropertyOverrides({
                QDi0tDYwF: {
                  background: null
                }
              }, baseVariant, gestureVariant),
              children: [SiWixZYQ0 && /*#__PURE__*/_jsx(motion.div, {
                style: {},
                className: "framer-12nt1to-container",
                layoutId: "PVUxjT4vg-container",
                transition: transition,
                layoutDependency: layoutDependency,
                "data-framer-name": "3 check",
                children: /*#__PURE__*/_jsx(Phosphor, {
                  width: "100%",
                  height: "100%",
                  layoutId: "PVUxjT4vg",
                  id: "PVUxjT4vg",
                  name: "3 check",
                  selectByList: true,
                  iconSelection: "CheckCircle",
                  iconSearch: "House",
                  color: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                  weight: "fill",
                  mirrored: false,
                  style: {
                    width: "100%",
                    height: "100%"
                  }
                })
              }), EIAYgw7zE && /*#__PURE__*/_jsx(motion.div, {
                style: {},
                className: "framer-19dquay-container",
                layoutId: "Zscajp0up-container",
                transition: transition,
                layoutDependency: layoutDependency,
                "data-framer-name": "3 cross",
                children: /*#__PURE__*/_jsx(Phosphor, {
                  width: "100%",
                  height: "100%",
                  layoutId: "Zscajp0up",
                  id: "Zscajp0up",
                  name: "3 cross",
                  selectByList: true,
                  iconSelection: "XCircle",
                  iconSearch: "House",
                  color: 'var(--token-cd156118-158a-47d3-8fb6-822a4bbc99ee, rgb(224, 225, 227)) /* {"name":"Gray Pressed"} */',
                  weight: "fill",
                  mirrored: false,
                  style: {
                    width: "100%",
                    height: "100%"
                  }
                })
              }), /*#__PURE__*/_jsx(Text, {
                style: {
                  "--framer-font-family": '"Inter", sans-serif',
                  "--framer-font-style": "normal",
                  "--framer-font-weight": 400,
                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  "--framer-font-size": "18px",
                  "--framer-letter-spacing": "-0.4px",
                  "--framer-text-transform": "none",
                  "--framer-text-decoration": "none",
                  "--framer-line-height": "1.5em",
                  "--framer-text-alignment": "left",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline"
                },
                withExternalLayout: true,
                verticalAlignment: "top",
                __fromCanvasComponent: true,
                alignment: "left",
                fonts: ["GF;Inter-regular"],
                className: "framer-1f8amti",
                layoutId: "RbqoJqprA",
                transition: transition,
                layoutDependency: layoutDependency,
                rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Unlimited</span><br></span></span>",
                text: ZYP9nkQ5k
              })]
            })]
          })]
        })]
      })
    })
  });
});
const css = ['.framer-oLFlJ [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-oLFlJ * { box-sizing: border-box; }", ".framer-oLFlJ .framer-139nqr4 { position: relative; overflow: visible; width: 1000px; height: min-content; display: flex; flex-direction: row; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 20px; padding: 0px 0px 0px 0px; }", ".framer-oLFlJ .framer-1ahvnqf, .framer-oLFlJ .framer-1ovfmm0, .framer-oLFlJ .framer-1p2okk7, .framer-oLFlJ .framer-1itnew8, .framer-oLFlJ .framer-13bcadf { position: relative; overflow: visible; width: 1px; height: min-content; flex: 1 0 0px; display: flex; flex-direction: row; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 10px; padding: 0px 0px 0px 0px; }", ".framer-oLFlJ .framer-1nkdkpl, .framer-oLFlJ .framer-c0ykq5, .framer-oLFlJ .framer-1f8amti { position: relative; overflow: visible; width: 1px; height: auto; flex: 1 0 0px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; }", ".framer-oLFlJ .framer-mu29xm { position: relative; overflow: visible; width: 1px; height: min-content; flex: 1 0 0px; display: flex; flex-direction: row; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 20px; padding: 0px 0px 0px 0px; }", ".framer-oLFlJ .framer-1juuemk, .framer-oLFlJ .framer-ivz5b6 { position: relative; overflow: visible; width: auto; height: auto; flex: none; white-space: pre; }", ".framer-oLFlJ .framer-keur0i-container, .framer-oLFlJ .framer-1sdgu7z-container, .framer-oLFlJ .framer-12nt1to-container, .framer-oLFlJ .framer-19dquay-container { position: relative; width: 26px; height: 26px; flex: none; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-oLFlJ framer-139nqr4 > *, .framer-oLFlJ framer-mu29xm > * { margin: 0px; margin-left: calc(20px / 2); margin-right: calc(20px / 2); } .framer-oLFlJ framer-139nqr4 > :first-child, .framer-oLFlJ framer-1ahvnqf > :first-child, .framer-oLFlJ framer-mu29xm > :first-child, .framer-oLFlJ framer-1ovfmm0 > :first-child, .framer-oLFlJ framer-1p2okk7 > :first-child, .framer-oLFlJ framer-1itnew8 > :first-child, .framer-oLFlJ framer-13bcadf > :first-child { margin-left: 0px; } .framer-oLFlJ framer-139nqr4 > :last-child, .framer-oLFlJ framer-1ahvnqf > :last-child, .framer-oLFlJ framer-mu29xm > :last-child, .framer-oLFlJ framer-1ovfmm0 > :last-child, .framer-oLFlJ framer-1p2okk7 > :last-child, .framer-oLFlJ framer-1itnew8 > :last-child, .framer-oLFlJ framer-13bcadf > :last-child { margin-right: 0px; } .framer-oLFlJ framer-1ahvnqf > *, .framer-oLFlJ framer-1ovfmm0 > *, .framer-oLFlJ framer-1p2okk7 > *, .framer-oLFlJ framer-1itnew8 > *, .framer-oLFlJ framer-13bcadf > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-oLFlJ.framer-v-o4nu4d .framer-139nqr4 { width: 390px; height: min-content; display: flex; flex-direction: column; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 20px; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-1ahvnqf, .framer-oLFlJ.framer-v-o4nu4d .framer-1p2okk7, .framer-oLFlJ.framer-v-o4nu4d .framer-13bcadf { width: 100%; height: min-content; right: auto; bottom: auto; left: auto; top: auto; flex: none; align-self: auto; aspect-ratio: unset; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-mu29xm { width: 100%; height: min-content; right: auto; bottom: auto; left: auto; top: auto; flex: none; align-self: auto; aspect-ratio: unset; display: flex; flex-direction: row; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 20px; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-1ovfmm0, .framer-oLFlJ.framer-v-o4nu4d .framer-1itnew8 { display: flex; flex-direction: column; justify-content: flex-start; align-content: flex-start; align-items: flex-start; flex-wrap: nowrap; gap: 10px; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-1juuemk { width: auto; height: auto; right: auto; bottom: auto; left: auto; top: auto; flex: none; align-self: auto; white-space: pre; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-ivz5b6 { width: auto; height: auto; right: auto; bottom: auto; left: auto; top: auto; flex: none; align-self: auto; white-space: pre; aspect-ratio: unset; }", ".framer-oLFlJ.framer-v-o4nu4d .framer-1f8amti { width: 1px; height: auto; right: auto; bottom: auto; left: auto; top: auto; flex: 1 0 0px; align-self: auto; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerIntrinsicHeight 27
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerIntrinsicWidth 1000
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"QDi0tDYwF":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerVariables {"v24h3Kb1n":"feature","yVwIWxWu9":"amount2","Q6sofBfF0":"check2","ZYP9nkQ5k":"amount3","SiWixZYQ0":"check3","Ng1h5XbTv":"cross2","EIAYgw7zE":"close3"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              */
const FramerZCoG6G2JF = withCSS(Component, css);
export default FramerZCoG6G2JF;
FramerZCoG6G2JF.displayName = "Pricing Table Row";
FramerZCoG6G2JF.defaultProps = {
  width: 1e3,
  height: 27
};
addPropertyControls(FramerZCoG6G2JF, {
  variant: {
    type: ControlType.Enum,
    title: "Variant",
    options: ["E8fyzpKQN", "QDi0tDYwF"],
    optionTitles: ["Desktop", "Phone"]
  },
  v24h3Kb1n: {
    type: ControlType.String,
    title: "Feature",
    defaultValue: "External editors",
    displayTextArea: false
  },
  yVwIWxWu9: {
    type: ControlType.String,
    title: "Amount 2",
    defaultValue: "50",
    displayTextArea: false
  },
  Q6sofBfF0: {
    type: ControlType.Boolean,
    title: "Check 2",
    defaultValue: false
  },
  ZYP9nkQ5k: {
    type: ControlType.String,
    title: "Amount 3",
    defaultValue: "Unlimited",
    displayTextArea: false
  },
  SiWixZYQ0: {
    type: ControlType.Boolean,
    title: "Check 3",
    defaultValue: false
  },
  Ng1h5XbTv: {
    type: ControlType.Boolean,
    title: "Cross 2",
    defaultValue: false
  },
  EIAYgw7zE: {
    type: ControlType.Boolean,
    title: "Close 3",
    defaultValue: false
  }
});
addFonts(FramerZCoG6G2JF, [{
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  family: "Inter",
  style: "normal",
  weight: "400",
  moduleAsset: {
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
    localModuleIdentifier: "local-module:canvasComponent/ZCoG6G2JF:default"
  }
}, {
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  family: "Inter",
  style: "normal",
  weight: "500",
  moduleAsset: {
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
    localModuleIdentifier: "local-module:canvasComponent/ZCoG6G2JF:default"
  }
}, ...PhosphorFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerZCoG6G2JF",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "27",
        "framerContractVersion": "1",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"QDi0tDYwF\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerVariables": "{\"v24h3Kb1n\":\"feature\",\"yVwIWxWu9\":\"amount2\",\"Q6sofBfF0\":\"check2\",\"ZYP9nkQ5k\":\"amount3\",\"SiWixZYQ0\":\"check3\",\"Ng1h5XbTv\":\"cross2\",\"EIAYgw7zE\":\"close3\"}",
        "framerIntrinsicWidth": "1000"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./ZCoG6G2JF.map