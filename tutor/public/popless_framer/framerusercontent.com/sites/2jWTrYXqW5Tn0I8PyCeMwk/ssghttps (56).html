function toString_194x2gw(value) {
  return typeof value === "string" ? value : String(value);
}
const metadata = params => {
  return {
    breakpoints: [{
      hash: "1nuuy0c",
      mediaQuery: "(min-width: 1440px)"
    }, {
      hash: "oco35x",
      mediaQuery: "(min-width: 810px) and (max-width: 1439px)"
    }, {
      hash: "18na2tt",
      mediaQuery: "(max-width: 809px)"
    }],
    elements: {},
    robots: "noindex",
    title: `Popless | Legal - ${(params === null || params === void 0 ? void 0 : params["TLSnfROIL"]) !== undefined ? toString_194x2gw(params["TLSnfROIL"]) : "{{TLSnfROIL}}"}`,
    viewport: "width=device-width"
  };
};
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};