// Generated by Framer (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Image, Link, PropertyOverrides, removeHiddenBreakpointLayers, RichText, SVG, Text, useActiveVariantCallback, useHydratedBreakpointVariants, withCSS, withFX } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import Intercom from "https://framerusercontent.com/modules/UIhUTcd796YH7Ndybys8/totj55n8qE3VYpdXhshW/Intercom.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import FAQStack from "https://framerusercontent.com/modules/o88pEDyM5V3BTkcTTtDj/vP5d3bseQIJiN0eHwQPA/fpaWCS1ZP.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import * as sharedStyle1 from "https://framerusercontent.com/modules/jW4UrfhLrCwwfY1oTaaP/Dxxbn5oVzYdipYbLgMBY/ilRQrCGtC.js";
import * as sharedStyle from "https://framerusercontent.com/modules/ZPZUEfXbIcAJX6rsUnRl/VuIHlfLxD1KPRBDbWdIN/PBWcAvaL3.js";
import metadataProvider from "https://framerusercontent.com/modules/16O4bOLaeboXINGBRdnZ/0SJvFPev3FJHUwzL89RW/hR530hScY.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const RichTextWithFX = withFX(RichText);
const IntercomFonts = getFonts(Intercom);
const MotionDivWithFX = withFX(motion.div);
const ImageWithFX = withFX(Image);
const TextWithFX = withFX(Text);
const FAQStackFonts = getFonts(FAQStack);
const ContainerWithFX = withFX(Container);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["wp1fYs6VN", "N6NHWDbmj", "awsjvbeos"];
const breakpoints = {
  awsjvbeos: "(max-width: 809px)",
  N6NHWDbmj: "(min-width: 810px) and (max-width: 1439px)",
  wp1fYs6VN: "(min-width: 1440px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  awsjvbeos: "framer-v-11wlnnu",
  N6NHWDbmj: "framer-v-1b96m8x",
  wp1fYs6VN: "framer-v-1i45v68"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("wp1fYs6VN", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  Desktop: "wp1fYs6VN",
  Phone: "awsjvbeos",
  Tablet: "N6NHWDbmj"
};
const transitions = {
  default: {
    duration: 0
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "wp1fYs6VN",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata = metadataProvider();
    document.title = metadata.title || "";
    if (metadata.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata.viewport);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const poplessLink1hxdeaz = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noreferrer noopener");
  });
  const twitterLink1bj8fo6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noreferrer noopener");
  });
  const isDisplayed1 = () => {
    if (baseVariant === "awsjvbeos") return true;
    return !isBrowser();
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "wp1fYs6VN",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        "data-framer-generated": true,
        className: cx("framer-7bTD6", sharedStyle.className, sharedStyle1.className),
        style: {
          display: "contents",
          pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
        },
        children: [/*#__PURE__*/_jsx(motion.div, {
          ...restProps,
          className: cx("framer-1i45v68", className),
          ref: ref,
          style: {
            ...style
          },
          children: /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-2rcour",
            children: [/*#__PURE__*/_jsx(Container, {
              className: "framer-er8h8m-container",
              "data-framer-name": "Navigation",
              name: "Navigation",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  awsjvbeos: {
                    variant: "aZMkidfTG"
                  },
                  N6NHWDbmj: {
                    variant: "aZMkidfTG"
                  }
                },
                children: /*#__PURE__*/_jsx(HeaderNavigation, {
                  height: "100%",
                  id: "QMc_QD5E3zZm4u_Y_j",
                  layoutId: "QMc_QD5E3zZm4u_Y_j",
                  name: "Navigation",
                  style: {
                    width: "100%"
                  },
                  variant: "Dg2JgAbsI",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsxs(motion.main, {
              className: "framer-11ccy21",
              "data-framer-name": "Main",
              name: "Main",
              children: [/*#__PURE__*/_jsxs(motion.div, {
                className: "framer-1yib2au",
                children: [/*#__PURE__*/_jsxs(motion.header, {
                  className: "framer-1zrof8",
                  "data-framer-name": "Stack",
                  name: "Stack",
                  children: [/*#__PURE__*/_jsx(motion.div, {
                    className: "framer-1ux7c2s",
                    children: /*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        awsjvbeos: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("h1", {
                              style: {
                                "--framer-text-alignment": "center"
                              },
                              children: /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--font-selector": "R0Y7SW50ZXItNzAw",
                                  "--framer-font-family": '"Inter", sans-serif',
                                  "--framer-font-size": "38px",
                                  "--framer-font-style": "normal",
                                  "--framer-font-weight": "700",
                                  "--framer-letter-spacing": "-1.5px",
                                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                },
                                children: "Accessibility at Popless"
                              })
                            })
                          })
                        },
                        N6NHWDbmj: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("h1", {
                              style: {
                                "--framer-text-alignment": "center"
                              },
                              children: /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--font-selector": "R0Y7SW50ZXItNzAw",
                                  "--framer-font-family": '"Inter", sans-serif',
                                  "--framer-font-size": "60px",
                                  "--framer-font-style": "normal",
                                  "--framer-font-weight": "700",
                                  "--framer-letter-spacing": "-1.8px",
                                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                },
                                children: "Accessibility at Popless"
                              })
                            })
                          })
                        }
                      },
                      children: /*#__PURE__*/_jsx(RichTextWithFX, {
                        __framer__animate: {
                          opacity: 1,
                          rotate: 0,
                          rotateX: 0,
                          rotateY: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 0
                        },
                        __framer__animateOnce: true,
                        __framer__enter: {
                          opacity: 0,
                          scale: 1,
                          x: 0,
                          y: 20
                        },
                        __framer__exit: {
                          opacity: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 20
                        },
                        __framer__styleAppearEffectEnabled: true,
                        __framer__threshold: 0,
                        __fromCanvasComponent: true,
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsx("h1", {
                            style: {
                              "--framer-text-alignment": "center"
                            },
                            children: /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "64px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-1.8px",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "Accessibility at Popless"
                            })
                          })
                        }),
                        className: "framer-1y6frli",
                        fonts: ["GF;Inter-700"],
                        verticalAlignment: "top",
                        withExternalLayout: true
                      })
                    })
                  }), /*#__PURE__*/_jsx(PropertyOverrides, {
                    breakpoint: baseVariant,
                    overrides: {
                      awsjvbeos: {
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsxs("p", {
                            style: {
                              "--framer-text-alignment": "center"
                            },
                            children: [/*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "20px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "Here’s how we’re making it"
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "28px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: /*#__PURE__*/_jsx("br", {})
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "20px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "easier to book with us."
                            })]
                          })
                        })
                      },
                      N6NHWDbmj: {
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsxs("p", {
                            style: {
                              "--framer-text-alignment": "center"
                            },
                            children: [/*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "24px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "Here’s how we’re making it"
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "28px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: /*#__PURE__*/_jsx("br", {})
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "24px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "easier to book with us."
                            })]
                          })
                        })
                      }
                    },
                    children: /*#__PURE__*/_jsx(RichTextWithFX, {
                      __framer__animate: {
                        opacity: 1,
                        rotate: 0,
                        rotateX: 0,
                        rotateY: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .2,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 0
                      },
                      __framer__animateOnce: true,
                      __framer__enter: {
                        opacity: 0,
                        scale: 1,
                        x: 0,
                        y: 20
                      },
                      __framer__exit: {
                        opacity: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .2,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 20
                      },
                      __framer__styleAppearEffectEnabled: true,
                      __framer__threshold: 0,
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--framer-text-alignment": "center"
                          },
                          children: [/*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNTAw",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "28px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "500",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "Here’s how we’re making it"
                          }), /*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNTAw",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "28px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "500",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: /*#__PURE__*/_jsx("br", {})
                          }), /*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNTAw",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "28px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "500",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "easier to book with us."
                          })]
                        })
                      }),
                      className: "framer-arteur",
                      fonts: ["GF;Inter-500"],
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })
                  }), /*#__PURE__*/_jsx(Container, {
                    className: "framer-r24y31-container",
                    children: /*#__PURE__*/_jsx(Intercom, {
                      appId: "lsvujawt",
                      height: "100%",
                      id: "QMc_QD5E3hQVa3wNJ8",
                      layoutId: "QMc_QD5E3hQVa3wNJ8",
                      style: {
                        height: "100%",
                        width: "100%"
                      },
                      width: "100%"
                    })
                  })]
                }), /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-iohh48",
                  children: /*#__PURE__*/_jsxs(MotionDivWithFX, {
                    __framer__animate: {
                      opacity: 1,
                      rotate: 0,
                      rotateX: 0,
                      rotateY: 0,
                      scale: 1,
                      transition: {
                        damping: 60,
                        delay: .3,
                        mass: 1,
                        stiffness: 500,
                        type: "spring"
                      },
                      x: 0,
                      y: 0
                    },
                    __framer__animateOnce: true,
                    __framer__enter: {
                      opacity: 0,
                      scale: 1,
                      x: 0,
                      y: 20
                    },
                    __framer__exit: {
                      opacity: 0,
                      scale: 1,
                      transition: {
                        damping: 60,
                        delay: .3,
                        mass: 1,
                        stiffness: 500,
                        type: "spring"
                      },
                      x: 0,
                      y: 20
                    },
                    __framer__styleAppearEffectEnabled: true,
                    __framer__threshold: 0,
                    className: "framer-1ki5zud",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--framer-font-size": "12px",
                            "--framer-line-height": "1.4em"
                          },
                          children: /*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNTAw",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "12px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "500",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "Scroll"
                          })
                        })
                      }),
                      className: "framer-1gutuy0",
                      fonts: ["GF;Inter-500"],
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(SVG, {
                      className: "framer-16dkz8b",
                      "data-framer-name": "chevron",
                      layout: "position",
                      name: "chevron",
                      opacity: 1,
                      radius: 0,
                      svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 21 17"><path d="M 0 15.273 L 7.556 7.636 L 0 0" transform="translate(7.236 1.941) rotate(90 3.778 7.636)" fill="transparent" stroke-width="1.5" stroke="var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {&quot;name&quot;:&quot;Gray/Very Dark Gray&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
                      svgContentId: 2106090972,
                      withExternalLayout: true
                    })]
                  })
                })]
              }), /*#__PURE__*/_jsxs(motion.section, {
                className: "framer-ilrdo8",
                children: [/*#__PURE__*/_jsx(motion.div, {
                  className: "framer-1qy0r9b",
                  "data-framer-name": "Desktop",
                  name: "Desktop",
                  children: /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-1s4zsto",
                    children: [/*#__PURE__*/_jsx(motion.div, {
                      className: "framer-4zki66",
                      children: /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-j5ttzw",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("h3", {
                                  style: {
                                    "--framer-text-alignment": "center"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItNjAw",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "32px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: "Enhanced"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItNjAw",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "32px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-2px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItNjAw",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "32px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: "search filters"
                                  })]
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsxs("h3", {
                                style: {
                                  "--framer-text-alignment": "left"
                                },
                                children: [/*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItNjAw",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "32px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: "Enhanced"
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItNjAw",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "32px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-2px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: /*#__PURE__*/_jsx("br", {})
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItNjAw",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "32px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: "search filters"
                                })]
                              })
                            }),
                            className: "framer-11w6pf8",
                            fonts: ["GF;Inter-600"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-line-height": "1.4em",
                                    "--framer-text-alignment": "center"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "20px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "400",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: "We’ve simplified our accessibility filters to provide an even better search experience."
                                  })
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("p", {
                                style: {
                                  "--framer-line-height": "1.4em",
                                  "--framer-text-alignment": "left"
                                },
                                children: /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "20px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "400",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: "We’ve simplified our accessibility filters to provide an even better search experience."
                                })
                              })
                            }),
                            className: "framer-1cyc1yw",
                            fonts: ["GF;Inter-regular"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })]
                      })
                    }), /*#__PURE__*/_jsx(motion.div, {
                      background: {
                        alt: "",
                        fit: "fill",
                        intrinsicHeight: 5063,
                        intrinsicWidth: 3375,
                        loading: "lazy",
                        pixelHeight: 5063,
                        pixelWidth: 3375,
                        src: new URL("https://framerusercontent.com/images/IOZPPGbzw4W9DNyEDuUNJu3oEQk.jpg").href
                      },
                      className: "framer-26onoz",
                      children: /*#__PURE__*/_jsx(PropertyOverrides, {
                        breakpoint: baseVariant,
                        overrides: {
                          awsjvbeos: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "320.2636px",
                              src: new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href} 874w`
                            }
                          },
                          N6NHWDbmj: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "355.8484px",
                              src: new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href} 874w`
                            }
                          }
                        },
                        children: /*#__PURE__*/_jsx(ImageWithFX, {
                          __framer__animate: {
                            opacity: 1,
                            rotate: 0,
                            rotateX: 0,
                            rotateY: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 0
                          },
                          __framer__animateOnce: true,
                          __framer__enter: {
                            opacity: 0,
                            scale: 1,
                            x: 0,
                            y: 50
                          },
                          __framer__exit: {
                            opacity: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 50
                          },
                          __framer__styleAppearEffectEnabled: true,
                          __framer__threshold: 0,
                          background: {
                            alt: "",
                            fit: "fill",
                            intrinsicHeight: 1214,
                            intrinsicWidth: 874,
                            loading: "lazy",
                            pixelHeight: 1214,
                            pixelWidth: 874,
                            sizes: "432px",
                            src: new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href,
                            srcSet: `${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/QkfthoIKkyFUC0SuXu8ZlWt8ZL4.png").href} 874w`
                          },
                          className: "framer-zbqjv"
                        })
                      })
                    })]
                  })
                }), /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-1glm248",
                  "data-framer-name": "Desktop",
                  name: "Desktop",
                  children: /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-1bvsg92",
                    children: [/*#__PURE__*/_jsx(motion.div, {
                      className: "framer-1pl602x",
                      children: /*#__PURE__*/_jsx(PropertyOverrides, {
                        breakpoint: baseVariant,
                        overrides: {
                          awsjvbeos: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "320.2636px",
                              src: new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href} 874w`
                            }
                          },
                          N6NHWDbmj: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "355.8484px",
                              src: new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href} 874w`
                            }
                          }
                        },
                        children: /*#__PURE__*/_jsx(ImageWithFX, {
                          __framer__animate: {
                            opacity: 1,
                            rotate: 0,
                            rotateX: 0,
                            rotateY: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 0
                          },
                          __framer__animateOnce: true,
                          __framer__enter: {
                            opacity: 0,
                            scale: 1,
                            x: 0,
                            y: 50
                          },
                          __framer__exit: {
                            opacity: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 50
                          },
                          __framer__styleAppearEffectEnabled: true,
                          __framer__threshold: 0,
                          background: {
                            alt: "",
                            fit: "fill",
                            intrinsicHeight: 1214,
                            intrinsicWidth: 874,
                            loading: "lazy",
                            pixelHeight: 1214,
                            pixelWidth: 874,
                            sizes: "432px",
                            src: new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href,
                            srcSet: `${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/vxMKFvtO9zC0AaM728tQAislg.png").href} 874w`
                          },
                          className: "framer-1rrkqzw",
                          children: isDisplayed1() && /*#__PURE__*/_jsx(RichText, {
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("p", {
                                style: {
                                  "--framer-line-height": "1.2em",
                                  "--framer-text-alignment": "center"
                                },
                                children: /*#__PURE__*/_jsx("br", {
                                  className: "trailing-break"
                                })
                              })
                            }),
                            className: "framer-14mpycz hidden-1i45v68 hidden-1b96m8x",
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.div, {
                      className: "framer-101p86i",
                      children: /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-1b2uw8w",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              alignment: "center"
                            }
                          },
                          children: /*#__PURE__*/_jsx(TextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            alignment: "left",
                            className: "framer-f5y6mt",
                            fonts: ["GF;Inter-600"],
                            rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Accessibility review</span><br></span></span>",
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-line-height": "1.4em",
                                    "--framer-text-alignment": "center"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "20px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "400",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: "We review every accessibility feature listed by tutors for accuracy. Trust is at the core of our community."
                                  })
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("p", {
                                style: {
                                  "--framer-line-height": "1.4em",
                                  "--framer-text-alignment": "left"
                                },
                                children: /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "20px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "400",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: "We review every accessibility feature listed by tutors for accuracy. Trust is at the core of our community."
                                })
                              })
                            }),
                            className: "framer-19fu0dt",
                            fonts: ["GF;Inter-regular"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })]
                      })
                    })]
                  })
                }), /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-djhw8q",
                  "data-framer-name": "Desktop",
                  name: "Desktop",
                  children: /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-1c5bnwx",
                    children: [/*#__PURE__*/_jsx(motion.div, {
                      className: "framer-25bg7g",
                      children: /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-1iqb0as",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("h3", {
                                  style: {
                                    "--framer-text-alignment": "center"
                                  },
                                  children: /*#__PURE__*/_jsxs("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItNjAw",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "32px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: ["1:1 messaging", /*#__PURE__*/_jsx("br", {}), "with tutors"]
                                  })
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .1,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("h3", {
                                style: {
                                  "--framer-text-alignment": "left"
                                },
                                children: /*#__PURE__*/_jsxs("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItNjAw",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "32px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: ["1:1 messaging", /*#__PURE__*/_jsx("br", {}), "with tutors"]
                                })
                              })
                            }),
                            className: "framer-5iow6",
                            fonts: ["GF;Inter-600"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-line-height": "1.4em",
                                    "--framer-text-alignment": "center"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                      "--framer-font-family": '"Inter", sans-serif',
                                      "--framer-font-size": "20px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "400",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: "Chat directly with tutors for information about the accessibility features they offer when you schedule time with them."
                                  })
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("p", {
                                style: {
                                  "--framer-line-height": "1.4em",
                                  "--framer-text-alignment": "left"
                                },
                                children: /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                    "--framer-font-family": '"Inter", sans-serif',
                                    "--framer-font-size": "20px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "400",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: "Chat directly with tutors for information about the accessibility features they offer when you schedule time with them."
                                })
                              })
                            }),
                            className: "framer-zzdpt2",
                            fonts: ["GF;Inter-regular"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })]
                      })
                    }), /*#__PURE__*/_jsx(motion.div, {
                      background: {
                        alt: "",
                        fit: "fill",
                        intrinsicHeight: 5063,
                        intrinsicWidth: 3375,
                        loading: "lazy",
                        pixelHeight: 5063,
                        pixelWidth: 3375,
                        src: new URL("https://framerusercontent.com/images/IOZPPGbzw4W9DNyEDuUNJu3oEQk.jpg").href
                      },
                      className: "framer-uy6xl3",
                      children: /*#__PURE__*/_jsx(PropertyOverrides, {
                        breakpoint: baseVariant,
                        overrides: {
                          awsjvbeos: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "320.2636px",
                              src: new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href} 874w`
                            }
                          },
                          N6NHWDbmj: {
                            background: {
                              alt: "",
                              fit: "fill",
                              intrinsicHeight: 1214,
                              intrinsicWidth: 874,
                              loading: "lazy",
                              pixelHeight: 1214,
                              pixelWidth: 874,
                              sizes: "355.8484px",
                              src: new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href,
                              srcSet: `${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href} 874w`
                            }
                          }
                        },
                        children: /*#__PURE__*/_jsx(ImageWithFX, {
                          __framer__animate: {
                            opacity: 1,
                            rotate: 0,
                            rotateX: 0,
                            rotateY: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 0
                          },
                          __framer__animateOnce: true,
                          __framer__enter: {
                            opacity: 0,
                            scale: 1,
                            x: 0,
                            y: 50
                          },
                          __framer__exit: {
                            opacity: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 50
                          },
                          __framer__styleAppearEffectEnabled: true,
                          __framer__threshold: 0,
                          background: {
                            alt: "",
                            fit: "fill",
                            intrinsicHeight: 1214,
                            intrinsicWidth: 874,
                            loading: "lazy",
                            pixelHeight: 1214,
                            pixelWidth: 874,
                            sizes: "432px",
                            src: new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href,
                            srcSet: `${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=512").href} 368w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png?scale-down-to=1024").href} 737w, ${new URL("https://framerusercontent.com/images/5SU4njNMBuR6DkPexQMile6MNSQ.png").href} 874w`
                          },
                          className: "framer-1jr7kht"
                        })
                      })
                    })]
                  })
                })]
              }), /*#__PURE__*/_jsxs(motion.div, {
                className: "framer-31hrom",
                children: [/*#__PURE__*/_jsx(motion.div, {
                  className: "framer-1t0zj3z"
                }), /*#__PURE__*/_jsxs(motion.section, {
                  className: "framer-19gbzfw",
                  children: [/*#__PURE__*/_jsx(motion.section, {
                    className: "framer-jikund",
                    children: /*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        awsjvbeos: {
                          background: {
                            alt: "",
                            fit: "fill",
                            intrinsicHeight: 1278,
                            intrinsicWidth: 2560,
                            loading: "lazy",
                            pixelHeight: 1278,
                            pixelWidth: 2560,
                            sizes: "350px",
                            src: new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href,
                            srcSet: `${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href} 2560w`
                          }
                        },
                        N6NHWDbmj: {
                          background: {
                            alt: "",
                            fit: "fill",
                            intrinsicHeight: 1278,
                            intrinsicWidth: 2560,
                            loading: "lazy",
                            pixelHeight: 1278,
                            pixelWidth: 2560,
                            sizes: "762px",
                            src: new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href,
                            srcSet: `${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href} 2560w`
                          }
                        }
                      },
                      children: /*#__PURE__*/_jsx(ImageWithFX, {
                        __framer__animate: {
                          opacity: 1,
                          rotate: 0,
                          rotateX: 0,
                          rotateY: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 0
                        },
                        __framer__animateOnce: true,
                        __framer__enter: {
                          opacity: 0,
                          scale: 1,
                          x: 0,
                          y: 50
                        },
                        __framer__exit: {
                          opacity: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 50
                        },
                        __framer__styleAppearEffectEnabled: true,
                        __framer__threshold: 0,
                        background: {
                          alt: "",
                          fit: "fill",
                          intrinsicHeight: 1278,
                          intrinsicWidth: 2560,
                          loading: "lazy",
                          pixelHeight: 1278,
                          pixelWidth: 2560,
                          sizes: "1280px",
                          src: new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href,
                          srcSet: `${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/9sSmzbgGadcGxAoQu7ImlCnzA.jpg").href} 2560w`
                        },
                        className: "framer-idqe01"
                      })
                    })
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-dgea7b",
                    children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        awsjvbeos: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("h1", {
                              style: {
                                "--framer-line-height": "40px",
                                "--framer-text-alignment": "left"
                              },
                              children: /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--font-selector": "R0Y7SW50ZXItNzAw",
                                  "--framer-font-family": '"Inter", sans-serif',
                                  "--framer-font-size": "40px",
                                  "--framer-font-style": "normal",
                                  "--framer-font-weight": "700",
                                  "--framer-letter-spacing": "-1px",
                                  "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                },
                                children: "How we’re making Popless more accessible"
                              })
                            })
                          })
                        },
                        N6NHWDbmj: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("h1", {
                              style: {
                                "--framer-line-height": "42px",
                                "--framer-text-alignment": "left"
                              },
                              children: /*#__PURE__*/_jsxs("span", {
                                style: {
                                  "--font-selector": "R0Y7SW50ZXItNzAw",
                                  "--framer-font-family": '"Inter", sans-serif',
                                  "--framer-font-size": "38px",
                                  "--framer-font-style": "normal",
                                  "--framer-font-weight": "700",
                                  "--framer-letter-spacing": "-1px",
                                  "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                },
                                children: ["How we’re making Popless", /*#__PURE__*/_jsx("br", {}), "more accessible"]
                              })
                            })
                          })
                        }
                      },
                      children: /*#__PURE__*/_jsx(RichTextWithFX, {
                        __framer__animate: {
                          opacity: 1,
                          rotate: 0,
                          rotateX: 0,
                          rotateY: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 0
                        },
                        __framer__animateOnce: true,
                        __framer__enter: {
                          opacity: 0,
                          scale: 1,
                          x: 0,
                          y: 20
                        },
                        __framer__exit: {
                          opacity: 0,
                          scale: 1,
                          transition: {
                            damping: 60,
                            delay: .1,
                            mass: 1,
                            stiffness: 500,
                            type: "spring"
                          },
                          x: 0,
                          y: 20
                        },
                        __framer__styleAppearEffectEnabled: true,
                        __framer__threshold: 0,
                        __fromCanvasComponent: true,
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsxs("h1", {
                            style: {
                              "--framer-line-height": "66px",
                              "--framer-text-alignment": "left"
                            },
                            children: [/*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "60px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-2px",
                                "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                              },
                              children: "How we’re making Popless"
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "60px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-2px",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: /*#__PURE__*/_jsx("br", {})
                            }), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "60px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-2px",
                                "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                              },
                              children: "more accessible"
                            })]
                          })
                        }),
                        className: "framer-t0paq8",
                        fonts: ["GF;Inter-700"],
                        verticalAlignment: "top",
                        withExternalLayout: true
                      })
                    }), /*#__PURE__*/_jsxs(motion.div, {
                      className: "framer-1yi8bk7",
                      children: [/*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-19nqdz1",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Dedicated"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "teams"
                                  })]
                                })
                              })
                            },
                            N6NHWDbmj: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Dedicated"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "teams"
                                  })]
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsxs("p", {
                                style: {
                                  "--framer-line-height": "32px",
                                  "--framer-text-alignment": "left"
                                },
                                children: [/*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "Dedicated"
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: /*#__PURE__*/_jsx("br", {})
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "teams"
                                })]
                              })
                            }),
                            className: "framer-9eprh3",
                            fonts: ["Inter-SemiBold"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-line-height": "28px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-font-size": "18px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Popless has teams focused on building products that everyone can use. These teams work with engineers, designers, and others across the company to help ensure that our products are built with accessibility in mind."
                                  })
                                })
                              })
                            },
                            N6NHWDbmj: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsx("p", {
                                  style: {
                                    "--framer-line-height": "28px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Popless has teams focused on building products that everyone can use. These teams work with engineers, designers, and others across the company to help ensure that our products are built with accessibility in mind."
                                  })
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .3,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .3,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsx("p", {
                                style: {
                                  "--framer-line-height": "30px",
                                  "--framer-text-alignment": "left"
                                },
                                children: /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--framer-font-size": "18px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "Popless has teams focused on building products that everyone can use. These teams work with engineers, designers, and others across the company to help ensure that our products are built with accessibility in mind."
                                })
                              })
                            }),
                            className: "framer-7fpj7",
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })]
                      }), /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-7bpqfh",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Research and"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "advocacy"
                                  })]
                                })
                              })
                            },
                            N6NHWDbmj: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Research and"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "advocacy"
                                  })]
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsxs("p", {
                                style: {
                                  "--framer-line-height": "32px",
                                  "--framer-text-alignment": "left"
                                },
                                children: [/*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "Research and"
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: /*#__PURE__*/_jsx("br", {})
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "advocacy"
                                })]
                              })
                            }),
                            className: "framer-1j2tv8n",
                            fonts: ["Inter-SemiBold"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(RichTextWithFX, {
                          __framer__animate: {
                            opacity: 1,
                            rotate: 0,
                            rotateX: 0,
                            rotateY: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 0
                          },
                          __framer__animateOnce: true,
                          __framer__enter: {
                            opacity: 0,
                            scale: 1,
                            x: 0,
                            y: 20
                          },
                          __framer__exit: {
                            opacity: 0,
                            scale: 1,
                            transition: {
                              damping: 60,
                              delay: .3,
                              mass: 1,
                              stiffness: 500,
                              type: "spring"
                            },
                            x: 0,
                            y: 20
                          },
                          __framer__styleAppearEffectEnabled: true,
                          __framer__threshold: 0,
                          __fromCanvasComponent: true,
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsxs("p", {
                              style: {
                                "--framer-line-height": "30px",
                                "--framer-text-alignment": "left"
                              },
                              children: [/*#__PURE__*/_jsx("span", {
                                style: {
                                  "--framer-font-size": "18px",
                                  "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                },
                                children: "We conduct research with people who have accessibility needs and work with experts in the community. If you’re interested in participating in a session about accessibility at Popless, "
                              }), /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--framer-font-size": "18px",
                                  "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))",
                                  "--framer-text-decoration": "underline"
                                },
                                children: "get in touch"
                              }), /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--framer-font-size": "18px",
                                  "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                },
                                children: "."
                              })]
                            })
                          }),
                          className: "framer-wsozaf",
                          verticalAlignment: "top",
                          withExternalLayout: true
                        })]
                      }), /*#__PURE__*/_jsxs(motion.div, {
                        className: "framer-163t0mb",
                        children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Digital accessibility"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "standards"
                                  })]
                                })
                              })
                            },
                            N6NHWDbmj: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "32px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "Digital accessibility"
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "27px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                    },
                                    children: /*#__PURE__*/_jsx("br", {})
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                      "--framer-font-size": "26px",
                                      "--framer-font-style": "normal",
                                      "--framer-font-weight": "600",
                                      "--framer-letter-spacing": "-1px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "standards"
                                  })]
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .2,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsxs("p", {
                                style: {
                                  "--framer-line-height": "32px",
                                  "--framer-text-alignment": "left"
                                },
                                children: [/*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "Digital accessibility"
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                  },
                                  children: /*#__PURE__*/_jsx("br", {})
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                                    "--framer-font-size": "27px",
                                    "--framer-font-style": "normal",
                                    "--framer-font-weight": "600",
                                    "--framer-letter-spacing": "-1px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "standards"
                                })]
                              })
                            }),
                            className: "framer-1523moi",
                            fonts: ["Inter-SemiBold"],
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        }), /*#__PURE__*/_jsx(PropertyOverrides, {
                          breakpoint: baseVariant,
                          overrides: {
                            awsjvbeos: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "28px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-font-size": "18px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "We’re working towards the digital accessibility standards laid out by the "
                                  }), /*#__PURE__*/_jsx(Link, {
                                    href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                    openInNewTab: false,
                                    smoothScroll: false,
                                    children: /*#__PURE__*/_jsx("a", {
                                      className: "framer-styles-preset-1oq1n",
                                      "data-styles-preset": "PBWcAvaL3",
                                      href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                      rel: "noreferrer noopener",
                                      children: /*#__PURE__*/_jsx("span", {
                                        style: {
                                          "--framer-font-size": "18px",
                                          "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                        },
                                        children: "Web Content Accessibility Guidelines"
                                      })
                                    })
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-font-size": "18px",
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: ". We’re also investing in automated testing tools to help us catch more issues."
                                  })]
                                })
                              })
                            },
                            N6NHWDbmj: {
                              children: /*#__PURE__*/_jsx(React.Fragment, {
                                children: /*#__PURE__*/_jsxs("p", {
                                  style: {
                                    "--framer-line-height": "28px",
                                    "--framer-text-alignment": "left"
                                  },
                                  children: [/*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: "We’re working towards the digital accessibility standards laid out by the "
                                  }), /*#__PURE__*/_jsx(Link, {
                                    href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                    openInNewTab: false,
                                    smoothScroll: false,
                                    children: /*#__PURE__*/_jsx("a", {
                                      className: "framer-styles-preset-1oq1n",
                                      "data-styles-preset": "PBWcAvaL3",
                                      href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                      rel: "noreferrer noopener",
                                      children: /*#__PURE__*/_jsx("span", {
                                        style: {
                                          "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                        },
                                        children: "Web Content Accessibility Guidelines"
                                      })
                                    })
                                  }), /*#__PURE__*/_jsx("span", {
                                    style: {
                                      "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                    },
                                    children: ". We’re also investing in automated testing tools to help us catch more issues."
                                  })]
                                })
                              })
                            }
                          },
                          children: /*#__PURE__*/_jsx(RichTextWithFX, {
                            __framer__animate: {
                              opacity: 1,
                              rotate: 0,
                              rotateX: 0,
                              rotateY: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .3,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 0
                            },
                            __framer__animateOnce: true,
                            __framer__enter: {
                              opacity: 0,
                              scale: 1,
                              x: 0,
                              y: 20
                            },
                            __framer__exit: {
                              opacity: 0,
                              scale: 1,
                              transition: {
                                damping: 60,
                                delay: .3,
                                mass: 1,
                                stiffness: 500,
                                type: "spring"
                              },
                              x: 0,
                              y: 20
                            },
                            __framer__styleAppearEffectEnabled: true,
                            __framer__threshold: 0,
                            __fromCanvasComponent: true,
                            children: /*#__PURE__*/_jsx(React.Fragment, {
                              children: /*#__PURE__*/_jsxs("p", {
                                style: {
                                  "--framer-line-height": "30px",
                                  "--framer-text-alignment": "left"
                                },
                                children: [/*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--framer-font-size": "18px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: "We’re working towards the digital accessibility standards laid out by the "
                                }), /*#__PURE__*/_jsx(Link, {
                                  href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                  openInNewTab: false,
                                  smoothScroll: false,
                                  children: /*#__PURE__*/_jsx("a", {
                                    className: "framer-styles-preset-1oq1n",
                                    "data-styles-preset": "PBWcAvaL3",
                                    href: "https://www.w3.org/WAI/standards-guidelines/wcag/",
                                    rel: "noreferrer noopener",
                                    children: /*#__PURE__*/_jsx("span", {
                                      style: {
                                        "--framer-font-size": "18px",
                                        "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                      },
                                      children: "Web Content Accessibility Guidelines"
                                    })
                                  })
                                }), /*#__PURE__*/_jsx("span", {
                                  style: {
                                    "--framer-font-size": "18px",
                                    "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
                                  },
                                  children: ". We’re also investing in automated testing tools to help us catch more issues."
                                })]
                              })
                            }),
                            className: "framer-17dgnqz",
                            verticalAlignment: "top",
                            withExternalLayout: true
                          })
                        })]
                      })]
                    })]
                  })]
                })]
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-cwujt5",
                children: /*#__PURE__*/_jsxs(motion.section, {
                  className: "framer-olkg4l",
                  children: [/*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-9be7ge",
                    children: [/*#__PURE__*/_jsx(TextWithFX, {
                      __framer__animate: {
                        opacity: 1,
                        rotate: 0,
                        rotateX: 0,
                        rotateY: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .1,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 0
                      },
                      __framer__animateOnce: true,
                      __framer__enter: {
                        opacity: 0,
                        scale: 1,
                        x: 0,
                        y: 20
                      },
                      __framer__exit: {
                        opacity: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .1,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 20
                      },
                      __framer__styleAppearEffectEnabled: true,
                      __framer__threshold: 0,
                      __fromCanvasComponent: true,
                      alignment: "left",
                      className: "framer-jw6msd",
                      fonts: ["GF;Inter-600"],
                      rawHTML: "<h1 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>We’re here to help</span><br></span></h1>",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichTextWithFX, {
                      __framer__animate: {
                        opacity: 1,
                        rotate: 0,
                        rotateX: 0,
                        rotateY: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .2,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 0
                      },
                      __framer__animateOnce: true,
                      __framer__enter: {
                        opacity: 0,
                        scale: 1,
                        x: 0,
                        y: 20
                      },
                      __framer__exit: {
                        opacity: 0,
                        scale: 1,
                        transition: {
                          damping: 60,
                          delay: .2,
                          mass: 1,
                          stiffness: 500,
                          type: "spring"
                        },
                        x: 0,
                        y: 20
                      },
                      __framer__styleAppearEffectEnabled: true,
                      __framer__threshold: 0,
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("h1", {
                          style: {
                            "--framer-line-height": "32px",
                            "--framer-text-alignment": "left"
                          },
                          children: [/*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "18px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "400",
                              "--framer-letter-spacing": "-1px",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "Visit our "
                          }), /*#__PURE__*/_jsx(Link, {
                            href: "data:framer/page-link,CqoljZCO0",
                            openInNewTab: true,
                            smoothScroll: false,
                            children: /*#__PURE__*/_jsx("a", {
                              className: "framer-styles-preset-1bok01c",
                              "data-styles-preset": "ilRQrCGtC",
                              href: "data:framer/page-link,CqoljZCO0",
                              target: "_blank",
                              children: /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                  "--framer-font-family": '"Inter", sans-serif',
                                  "--framer-font-size": "18px",
                                  "--framer-font-style": "normal",
                                  "--framer-font-weight": "400",
                                  "--framer-letter-spacing": "-1px",
                                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                                },
                                children: "Support Center"
                              })
                            })
                          }), /*#__PURE__*/_jsx("span", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                              "--framer-font-family": '"Inter", sans-serif',
                              "--framer-font-size": "18px",
                              "--framer-font-style": "normal",
                              "--framer-font-weight": "400",
                              "--framer-letter-spacing": "-1px",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: " for more information."
                          })]
                        })
                      }),
                      className: "framer-1gmyzt1",
                      fonts: ["GF;Inter-regular"],
                      preload: ["CqoljZCO0"],
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  }), /*#__PURE__*/_jsx(ContainerWithFX, {
                    __framer__animate: {
                      opacity: 1,
                      rotate: 0,
                      rotateX: 0,
                      rotateY: 0,
                      scale: 1,
                      transition: {
                        damping: 60,
                        delay: .3,
                        mass: 1,
                        stiffness: 500,
                        type: "spring"
                      },
                      x: 0,
                      y: 0
                    },
                    __framer__animateOnce: true,
                    __framer__enter: {
                      opacity: 0,
                      scale: 1,
                      x: 0,
                      y: 20
                    },
                    __framer__exit: {
                      opacity: 0,
                      scale: 1,
                      transition: {
                        damping: 60,
                        delay: .3,
                        mass: 1,
                        stiffness: 500,
                        type: "spring"
                      },
                      x: 0,
                      y: 20
                    },
                    __framer__styleAppearEffectEnabled: true,
                    __framer__threshold: 0,
                    className: "framer-1ydacsj-container",
                    children: /*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        awsjvbeos: {
                          variant: "Cf0PIGeI_"
                        }
                      },
                      children: /*#__PURE__*/_jsx(FAQStack, {
                        copy1: "It’s easy to use our search filters to only show tutors who have the accessibility features you need. You can search for tutors who know sign language, have experience with students requiring accessibility features, and who offer video calls with closed captions with the ability to download a recording of the meeting once it’s ended.",
                        copy2: "Tutors are required to provide a written description of their accessible features which are subsequently validated by the Popless team. Our validation process is a manual review by a specialized team of the Popless team description of quality and skill level respectively.",
                        copy3: "Tutors can allow access providers to join the students they’re assisting at no additional cost. We consider access providers to be anyone over the age of 18 who regularly assists a person with a disability, mental illness, or a long-term illness with daily activities.",
                        copy4: "What makes the services offered by a tutor accessible varies depending on the student. That’s why we give guidance to our tutors on how to provide clear, accurate information about their listings, and encourage them to communicate with their students.",
                        copy5: "",
                        copy6: "",
                        copy7: "",
                        height: "100%",
                        id: "QMc_QD5E3VPA2i5puv",
                        layoutId: "QMc_QD5E3VPA2i5puv",
                        style: {
                          width: "100%"
                        },
                        title1: "How do I use search filters?",
                        title2: "How does Popless review accessibility features?",
                        title3: "Can the person who supports me attend meets I book?",
                        title4: "How can tutors support students with accessibility needs?",
                        title5: "How can tutors add accessibility features to listings?",
                        title6: "",
                        title7: "",
                        variant: "PNeO4qznx",
                        width: "100%"
                      })
                    })
                  })]
                })
              }), /*#__PURE__*/_jsx(Container, {
                className: "framer-xmoh62-container",
                children: /*#__PURE__*/_jsx(PropertyOverrides, {
                  breakpoint: baseVariant,
                  overrides: {
                    awsjvbeos: {
                      variant: "Zz_9kWOfb"
                    }
                  },
                  children: /*#__PURE__*/_jsx(FooterNew, {
                    height: "100%",
                    id: "QMc_QD5E3Dz2Ahm3qx",
                    layoutId: "QMc_QD5E3Dz2Ahm3qx",
                    poplessLink: poplessLink1hxdeaz,
                    style: {
                      width: "100%"
                    },
                    twitterLink: twitterLink1bj8fo6,
                    variant: "zyTRmFlly",
                    width: "100%"
                  })
                })
              })]
            })]
          })
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-7bTD6 [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-7bTD6 .framer-35jgc0 { display: block; }", ".framer-7bTD6 .framer-1i45v68 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1440px; }", ".framer-7bTD6 .framer-2rcour { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-er8h8m-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-7bTD6 .framer-11ccy21, .framer-7bTD6 .framer-1ux7c2s, .framer-7bTD6 .framer-ilrdo8 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1yib2au { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; height: 100vh; justify-content: space-between; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1440px; }", ".framer-7bTD6 .framer-1zrof8 { align-content: center; align-items: center; background-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, #ffffff); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: 80%; justify-content: center; overflow: visible; padding: 0px 120px 0px 120px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1y6frli, .framer-7bTD6 .framer-arteur, .framer-7bTD6 .framer-11w6pf8, .framer-7bTD6 .framer-19fu0dt, .framer-7bTD6 .framer-5iow6, .framer-7bTD6 .framer-t0paq8, .framer-7bTD6 .framer-1gmyzt1 { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-7bTD6 .framer-r24y31-container { flex: none; height: 1px; position: relative; width: 1px; }", ".framer-7bTD6 .framer-iohh48 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 20%; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1ki5zud { align-content: center; align-items: center; background-color: var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, #f5f5f5); border-bottom-left-radius: 100px; border-bottom-right-radius: 100px; border-top-left-radius: 100px; border-top-right-radius: 100px; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 5px; height: 85px; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 45px; will-change: transform; }", ".framer-7bTD6 .framer-1gutuy0 { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-7bTD6 .framer-16dkz8b { flex: none; height: 17px; position: relative; width: 21px; }", ".framer-7bTD6 .framer-1qy0r9b, .framer-7bTD6 .framer-djhw8q { align-content: center; align-items: center; background-color: #f6f6f6; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: 785px; justify-content: center; overflow: visible; padding: 0px 80px 0px 80px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1s4zsto, .framer-7bTD6 .framer-1bvsg92, .framer-7bTD6 .framer-1c5bnwx { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; max-width: 1280px; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-7bTD6 .framer-4zki66, .framer-7bTD6 .framer-101p86i, .framer-7bTD6 .framer-25bg7g { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-7bTD6 .framer-j5ttzw { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 330px; z-index: 1; }", ".framer-7bTD6 .framer-1cyc1yw { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 320px; word-break: break-word; word-wrap: break-word; }", ".framer-7bTD6 .framer-26onoz, .framer-7bTD6 .framer-uy6xl3 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 785px; justify-content: flex-end; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; z-index: 1; }", ".framer-7bTD6 .framer-zbqjv, .framer-7bTD6 .framer-1jr7kht { flex: none; height: 607px; overflow: hidden; position: relative; width: 432px; }", ".framer-7bTD6 .framer-1glm248 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: 785px; justify-content: center; overflow: hidden; padding: 0px 80px 0px 80px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1pl602x { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 785px; justify-content: flex-end; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-7bTD6 .framer-1rrkqzw { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 607px; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 432px; }", ".framer-7bTD6 .framer-14mpycz { --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-7bTD6 .framer-1b2uw8w { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 355px; }", '.framer-7bTD6 .framer-f5y6mt { --framer-font-family: "Inter", sans-serif; --framer-font-size: 32px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: -1px; --framer-line-height: 1.2em; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }', ".framer-7bTD6 .framer-1iqb0as { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 400px; z-index: 1; }", ".framer-7bTD6 .framer-zzdpt2 { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 98%; word-break: break-word; word-wrap: break-word; }", ".framer-7bTD6 .framer-31hrom { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1t0zj3z { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: 561px; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-19gbzfw { align-content: center; align-items: center; background-color: #000000; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 150px; height: min-content; justify-content: flex-start; overflow: visible; padding: 150px 80px 150px 80px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-jikund { aspect-ratio: 7.710843373493976 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 166px); overflow: visible; position: relative; width: 1280px; }", ".framer-7bTD6 .framer-idqe01 { aspect-ratio: 2.003129890453834 / 1; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px; border-top-left-radius: 12px; border-top-right-radius: 12px; bottom: 0px; flex: none; height: var(--framer-aspect-ratio-supported, 639px); left: calc(50.00000000000002% - 1280px / 2); overflow: hidden; position: absolute; width: 1280px; will-change: transform; }", ".framer-7bTD6 .framer-dgea7b { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 60px; height: min-content; justify-content: flex-start; max-width: 1280px; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-1yi8bk7 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 60px; height: min-content; justify-content: center; max-width: 1440px; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-19nqdz1, .framer-7bTD6 .framer-7bpqfh, .framer-7bTD6 .framer-163t0mb { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-7bTD6 .framer-9eprh3, .framer-7bTD6 .framer-7fpj7, .framer-7bTD6 .framer-1j2tv8n, .framer-7bTD6 .framer-1523moi { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-7bTD6 .framer-wsozaf, .framer-7bTD6 .framer-17dgnqz { --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-7bTD6 .framer-cwujt5 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 150px 80px 150px 80px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-olkg4l { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; max-width: 1280px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-7bTD6 .framer-9be7ge { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 19px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", '.framer-7bTD6 .framer-jw6msd { --framer-font-family: "Inter", sans-serif; --framer-font-size: 46px; --framer-font-style: normal; --framer-font-weight: 600; --framer-letter-spacing: -1px; --framer-line-height: 52px; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }', ".framer-7bTD6 .framer-1ydacsj-container { flex: 1 0 0px; height: auto; position: relative; width: 1px; }", ".framer-7bTD6 .framer-xmoh62-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-7bTD6 .framer-1i45v68, .framer-7bTD6 .framer-2rcour, .framer-7bTD6 .framer-11ccy21, .framer-7bTD6 .framer-1zrof8, .framer-7bTD6 .framer-1ux7c2s, .framer-7bTD6 .framer-iohh48, .framer-7bTD6 .framer-1ki5zud, .framer-7bTD6 .framer-ilrdo8, .framer-7bTD6 .framer-1qy0r9b, .framer-7bTD6 .framer-4zki66, .framer-7bTD6 .framer-j5ttzw, .framer-7bTD6 .framer-26onoz, .framer-7bTD6 .framer-1glm248, .framer-7bTD6 .framer-1pl602x, .framer-7bTD6 .framer-1rrkqzw, .framer-7bTD6 .framer-101p86i, .framer-7bTD6 .framer-1b2uw8w, .framer-7bTD6 .framer-djhw8q, .framer-7bTD6 .framer-25bg7g, .framer-7bTD6 .framer-1iqb0as, .framer-7bTD6 .framer-uy6xl3, .framer-7bTD6 .framer-31hrom, .framer-7bTD6 .framer-1t0zj3z, .framer-7bTD6 .framer-19gbzfw, .framer-7bTD6 .framer-dgea7b, .framer-7bTD6 .framer-1yi8bk7, .framer-7bTD6 .framer-19nqdz1, .framer-7bTD6 .framer-7bpqfh, .framer-7bTD6 .framer-163t0mb, .framer-7bTD6 .framer-cwujt5, .framer-7bTD6 .framer-olkg4l, .framer-7bTD6 .framer-9be7ge { gap: 0px; } .framer-7bTD6 .framer-1i45v68 > *, .framer-7bTD6 .framer-1rrkqzw > *, .framer-7bTD6 .framer-cwujt5 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-7bTD6 .framer-1i45v68 > :first-child, .framer-7bTD6 .framer-2rcour > :first-child, .framer-7bTD6 .framer-11ccy21 > :first-child, .framer-7bTD6 .framer-1zrof8 > :first-child, .framer-7bTD6 .framer-1ux7c2s > :first-child, .framer-7bTD6 .framer-iohh48 > :first-child, .framer-7bTD6 .framer-1ki5zud > :first-child, .framer-7bTD6 .framer-ilrdo8 > :first-child, .framer-7bTD6 .framer-4zki66 > :first-child, .framer-7bTD6 .framer-j5ttzw > :first-child, .framer-7bTD6 .framer-26onoz > :first-child, .framer-7bTD6 .framer-1pl602x > :first-child, .framer-7bTD6 .framer-1rrkqzw > :first-child, .framer-7bTD6 .framer-101p86i > :first-child, .framer-7bTD6 .framer-1b2uw8w > :first-child, .framer-7bTD6 .framer-25bg7g > :first-child, .framer-7bTD6 .framer-1iqb0as > :first-child, .framer-7bTD6 .framer-uy6xl3 > :first-child, .framer-7bTD6 .framer-31hrom > :first-child, .framer-7bTD6 .framer-1t0zj3z > :first-child, .framer-7bTD6 .framer-19gbzfw > :first-child, .framer-7bTD6 .framer-dgea7b > :first-child, .framer-7bTD6 .framer-19nqdz1 > :first-child, .framer-7bTD6 .framer-7bpqfh > :first-child, .framer-7bTD6 .framer-163t0mb > :first-child, .framer-7bTD6 .framer-cwujt5 > :first-child, .framer-7bTD6 .framer-9be7ge > :first-child { margin-top: 0px; } .framer-7bTD6 .framer-1i45v68 > :last-child, .framer-7bTD6 .framer-2rcour > :last-child, .framer-7bTD6 .framer-11ccy21 > :last-child, .framer-7bTD6 .framer-1zrof8 > :last-child, .framer-7bTD6 .framer-1ux7c2s > :last-child, .framer-7bTD6 .framer-iohh48 > :last-child, .framer-7bTD6 .framer-1ki5zud > :last-child, .framer-7bTD6 .framer-ilrdo8 > :last-child, .framer-7bTD6 .framer-4zki66 > :last-child, .framer-7bTD6 .framer-j5ttzw > :last-child, .framer-7bTD6 .framer-26onoz > :last-child, .framer-7bTD6 .framer-1pl602x > :last-child, .framer-7bTD6 .framer-1rrkqzw > :last-child, .framer-7bTD6 .framer-101p86i > :last-child, .framer-7bTD6 .framer-1b2uw8w > :last-child, .framer-7bTD6 .framer-25bg7g > :last-child, .framer-7bTD6 .framer-1iqb0as > :last-child, .framer-7bTD6 .framer-uy6xl3 > :last-child, .framer-7bTD6 .framer-31hrom > :last-child, .framer-7bTD6 .framer-1t0zj3z > :last-child, .framer-7bTD6 .framer-19gbzfw > :last-child, .framer-7bTD6 .framer-dgea7b > :last-child, .framer-7bTD6 .framer-19nqdz1 > :last-child, .framer-7bTD6 .framer-7bpqfh > :last-child, .framer-7bTD6 .framer-163t0mb > :last-child, .framer-7bTD6 .framer-cwujt5 > :last-child, .framer-7bTD6 .framer-9be7ge > :last-child { margin-bottom: 0px; } .framer-7bTD6 .framer-2rcour > *, .framer-7bTD6 .framer-11ccy21 > *, .framer-7bTD6 .framer-1ux7c2s > *, .framer-7bTD6 .framer-iohh48 > *, .framer-7bTD6 .framer-ilrdo8 > *, .framer-7bTD6 .framer-4zki66 > *, .framer-7bTD6 .framer-26onoz > *, .framer-7bTD6 .framer-1pl602x > *, .framer-7bTD6 .framer-101p86i > *, .framer-7bTD6 .framer-25bg7g > *, .framer-7bTD6 .framer-uy6xl3 > *, .framer-7bTD6 .framer-31hrom > *, .framer-7bTD6 .framer-1t0zj3z > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-7bTD6 .framer-1zrof8 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-7bTD6 .framer-1ki5zud > * { margin: 0px; margin-bottom: calc(5px / 2); margin-top: calc(5px / 2); } .framer-7bTD6 .framer-1qy0r9b > *, .framer-7bTD6 .framer-1glm248 > *, .framer-7bTD6 .framer-djhw8q > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-7bTD6 .framer-1qy0r9b > :first-child, .framer-7bTD6 .framer-1glm248 > :first-child, .framer-7bTD6 .framer-djhw8q > :first-child, .framer-7bTD6 .framer-1yi8bk7 > :first-child, .framer-7bTD6 .framer-olkg4l > :first-child { margin-left: 0px; } .framer-7bTD6 .framer-1qy0r9b > :last-child, .framer-7bTD6 .framer-1glm248 > :last-child, .framer-7bTD6 .framer-djhw8q > :last-child, .framer-7bTD6 .framer-1yi8bk7 > :last-child, .framer-7bTD6 .framer-olkg4l > :last-child { margin-right: 0px; } .framer-7bTD6 .framer-j5ttzw > *, .framer-7bTD6 .framer-1b2uw8w > *, .framer-7bTD6 .framer-1iqb0as > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } .framer-7bTD6 .framer-19gbzfw > * { margin: 0px; margin-bottom: calc(150px / 2); margin-top: calc(150px / 2); } .framer-7bTD6 .framer-dgea7b > * { margin: 0px; margin-bottom: calc(60px / 2); margin-top: calc(60px / 2); } .framer-7bTD6 .framer-1yi8bk7 > * { margin: 0px; margin-left: calc(60px / 2); margin-right: calc(60px / 2); } .framer-7bTD6 .framer-19nqdz1 > *, .framer-7bTD6 .framer-7bpqfh > *, .framer-7bTD6 .framer-163t0mb > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-7bTD6 .framer-olkg4l > * { margin: 0px; margin-left: calc(20px / 2); margin-right: calc(20px / 2); } .framer-7bTD6 .framer-9be7ge > * { margin: 0px; margin-bottom: calc(19px / 2); margin-top: calc(19px / 2); } }", "@media (min-width: 1440px) { .framer-7bTD6 .hidden-1i45v68 { display: none !important; } }", "@media (min-width: 810px) and (max-width: 1439px) { .framer-7bTD6 .hidden-1b96m8x { display: none !important; } .framer-7bTD6 .framer-1i45v68 { width: 810px; } .framer-7bTD6 .framer-1yib2au { width: 100%; } .framer-7bTD6 .framer-1zrof8 { padding: 0px 24px 0px 24px; } .framer-7bTD6 .framer-1qy0r9b, .framer-7bTD6 .framer-1glm248, .framer-7bTD6 .framer-djhw8q { height: 700px; padding: 0px 24px 0px 24px; } .framer-7bTD6 .framer-26onoz, .framer-7bTD6 .framer-1pl602x, .framer-7bTD6 .framer-uy6xl3 { height: 700px; } .framer-7bTD6 .framer-zbqjv, .framer-7bTD6 .framer-1rrkqzw, .framer-7bTD6 .framer-1jr7kht { aspect-ratio: 0.7116968698517299 / 1; height: var(--framer-aspect-ratio-supported, 500px); width: 356px; } .framer-7bTD6 .framer-19gbzfw, .framer-7bTD6 .framer-cwujt5 { padding: 150px 24px 150px 24px; } .framer-7bTD6 .framer-idqe01 { height: var(--framer-aspect-ratio-supported, 380px); left: calc(50.00000000000002% - 762px / 2); width: 762px; } .framer-7bTD6 .framer-1yi8bk7 { gap: 30px; } .framer-7bTD6 .framer-olkg4l { flex-direction: column; gap: 40px; } .framer-7bTD6 .framer-9be7ge, .framer-7bTD6 .framer-1ydacsj-container { flex: none; width: 100%; } .framer-7bTD6 .framer-jw6msd { --framer-font-size: 42px; --framer-line-height: 50px; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-7bTD6 .framer-1yi8bk7, .framer-7bTD6 .framer-olkg4l { gap: 0px; } .framer-7bTD6 .framer-1yi8bk7 > * { margin: 0px; margin-left: calc(30px / 2); margin-right: calc(30px / 2); } .framer-7bTD6 .framer-1yi8bk7 > :first-child { margin-left: 0px; } .framer-7bTD6 .framer-1yi8bk7 > :last-child { margin-right: 0px; } .framer-7bTD6 .framer-olkg4l > * { margin: 0px; margin-bottom: calc(40px / 2); margin-top: calc(40px / 2); } .framer-7bTD6 .framer-olkg4l > :first-child { margin-top: 0px; } .framer-7bTD6 .framer-olkg4l > :last-child { margin-bottom: 0px; } }}", "@media (max-width: 809px) { .framer-7bTD6 .hidden-11wlnnu { display: none !important; } .framer-7bTD6 .framer-1i45v68 { width: 390px; } .framer-7bTD6 .framer-1yib2au, .framer-7bTD6 .framer-4zki66, .framer-7bTD6 .framer-j5ttzw, .framer-7bTD6 .framer-1cyc1yw, .framer-7bTD6 .framer-1b2uw8w, .framer-7bTD6 .framer-25bg7g, .framer-7bTD6 .framer-1iqb0as, .framer-7bTD6 .framer-zzdpt2 { width: 100%; } .framer-7bTD6 .framer-1zrof8 { padding: 0px 20px 0px 20px; } .framer-7bTD6 .framer-1qy0r9b, .framer-7bTD6 .framer-1glm248, .framer-7bTD6 .framer-djhw8q { height: min-content; padding: 100px 20px 0px 20px; } .framer-7bTD6 .framer-1s4zsto, .framer-7bTD6 .framer-1bvsg92, .framer-7bTD6 .framer-1yi8bk7 { flex-direction: column; } .framer-7bTD6 .framer-26onoz, .framer-7bTD6 .framer-uy6xl3 { height: 550px; } .framer-7bTD6 .framer-zbqjv, .framer-7bTD6 .framer-1rrkqzw, .framer-7bTD6 .framer-1jr7kht { aspect-ratio: 0.7116968698517299 / 1; height: var(--framer-aspect-ratio-supported, 450px); width: 320px; } .framer-7bTD6 .framer-1pl602x { height: 550px; order: 1; } .framer-7bTD6 .framer-101p86i { order: 0; width: 100%; } .framer-7bTD6 .framer-f5y6mt { --framer-text-alignment: center; } .framer-7bTD6 .framer-1c5bnwx { flex-direction: column; gap: 0px; justify-content: flex-start; order: 0; } .framer-7bTD6 .framer-1t0zj3z { height: 252px; } .framer-7bTD6 .framer-19gbzfw { gap: 100px; padding: 150px 20px 150px 20px; } .framer-7bTD6 .framer-jikund { aspect-ratio: unset; height: 166px; width: 390px; } .framer-7bTD6 .framer-idqe01 { aspect-ratio: 0.8666666666666667 / 1; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border-top-left-radius: 8px; border-top-right-radius: 8px; height: var(--framer-aspect-ratio-supported, 404px); left: calc(50.00000000000002% - 350px / 2); width: 350px; } .framer-7bTD6 .framer-19nqdz1, .framer-7bTD6 .framer-7bpqfh, .framer-7bTD6 .framer-163t0mb, .framer-7bTD6 .framer-9be7ge, .framer-7bTD6 .framer-1ydacsj-container { flex: none; width: 100%; } .framer-7bTD6 .framer-cwujt5 { padding: 150px 20px 150px 20px; } .framer-7bTD6 .framer-olkg4l { flex-direction: column; gap: 30px; } .framer-7bTD6 .framer-jw6msd { --framer-font-size: 36px; --framer-line-height: 42px; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-7bTD6 .framer-1s4zsto, .framer-7bTD6 .framer-1bvsg92, .framer-7bTD6 .framer-1c5bnwx, .framer-7bTD6 .framer-19gbzfw, .framer-7bTD6 .framer-1yi8bk7, .framer-7bTD6 .framer-olkg4l { gap: 0px; } .framer-7bTD6 .framer-1s4zsto > *, .framer-7bTD6 .framer-1s4zsto > :first-child, .framer-7bTD6 .framer-1s4zsto > :last-child, .framer-7bTD6 .framer-1bvsg92 > *, .framer-7bTD6 .framer-1bvsg92 > :first-child, .framer-7bTD6 .framer-1bvsg92 > :last-child { margin: 0px; } .framer-7bTD6 .framer-1c5bnwx > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-7bTD6 .framer-1c5bnwx > :first-child, .framer-7bTD6 .framer-19gbzfw > :first-child, .framer-7bTD6 .framer-1yi8bk7 > :first-child, .framer-7bTD6 .framer-olkg4l > :first-child { margin-top: 0px; } .framer-7bTD6 .framer-1c5bnwx > :last-child, .framer-7bTD6 .framer-19gbzfw > :last-child, .framer-7bTD6 .framer-1yi8bk7 > :last-child, .framer-7bTD6 .framer-olkg4l > :last-child { margin-bottom: 0px; } .framer-7bTD6 .framer-19gbzfw > * { margin: 0px; margin-bottom: calc(100px / 2); margin-top: calc(100px / 2); } .framer-7bTD6 .framer-1yi8bk7 > * { margin: 0px; margin-bottom: calc(60px / 2); margin-top: calc(60px / 2); } .framer-7bTD6 .framer-olkg4l > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } }}", ...sharedStyle.css, ...sharedStyle1.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerIntrinsicHeight 6137
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerIntrinsicWidth 1440
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"N6NHWDbmj":{"layout":["fixed","auto"]},"awsjvbeos":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              */
const FramerhR530hScY = withCSS(Component, css);
export default FramerhR530hScY;
FramerhR530hScY.displayName = "Accessibility";
FramerhR530hScY.defaultProps = {
  height: 6137,
  width: 1440
};
addFonts(FramerhR530hScY, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/hR530hScY:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/hR530hScY:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/hR530hScY:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
  weight: "600"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/hR530hScY:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...HeaderNavigationFonts, ...IntercomFonts, ...FAQStackFonts, ...FooterNewFonts, ...sharedStyle.fonts, ...sharedStyle1.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerhR530hScY",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "1440",
        "framerIntrinsicHeight": "6137",
        "framerResponsiveScreen": "",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"N6NHWDbmj\":{\"layout\":[\"fixed\",\"auto\"]},\"awsjvbeos\":{\"layout\":[\"fixed\",\"auto\"]}}}"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};