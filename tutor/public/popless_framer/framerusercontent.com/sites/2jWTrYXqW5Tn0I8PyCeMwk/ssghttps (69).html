import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter-Bold"]);
export const fonts = [];
export const css = ['.framer-c3ljl .framer-styles-preset-glnyvv:not(.rich-text-wrapper), .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper h1, .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper [data-preset-tag="h1"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 39px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 43px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; }', '@media (max-width: 1279px) and (min-width: 810px) { .framer-c3ljl .framer-styles-preset-glnyvv:not(.rich-text-wrapper), .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper h1, .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper [data-preset-tag="h1"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 29px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 32px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-c3ljl .framer-styles-preset-glnyvv:not(.rich-text-wrapper), .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper h1, .framer-c3ljl .framer-styles-preset-glnyvv.rich-text-wrapper [data-preset-tag="h1"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 32px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 39px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; } }'];
export const className = "framer-c3ljl";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};