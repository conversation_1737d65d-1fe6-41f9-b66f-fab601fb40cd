import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors([]);
export const fonts = [];
export const css = [".framer-Sry1J .framer-styles-preset-1bok01c:not(.rich-text-wrapper), .framer-Sry1J .framer-styles-preset-1bok01c.rich-text-wrapper a { --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-link-hover-text-color: #A8A8A8; --framer-link-hover-text-decoration: underline; }"];
export const className = "framer-Sry1J";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};