import{a as C,b as $,c as ee,d as re,e as te,f as ae,g as ne}from"./chunk-HFIP75X5.mjs";import{a as ie}from"./chunk-OHJLLAOX.mjs";import{a as oe}from"./chunk-35NYKJKS.mjs";import"./chunk-OPNTFX3G.mjs";import{a as D,b as Q,c as J}from"./chunk-KFOPWKFO.mjs";import{a as O,b as U,c as Z,d as Y,e as X,f as W}from"./chunk-NLZSNHKY.mjs";import{a as I,c as k,e as q}from"./chunk-Y45OGLEC.mjs";import"./chunk-42U43NKG.mjs";import{b as e,c as a}from"./chunk-VWWF2A63.mjs";import{B as L,E as G,Q as v,W as n,X as K,Z as P,c as b,g as S,ga as T,ha as F,ia as A,ka as E,la as R,m as V,ma as H,o as j,t as r,ua as o,v as z,wa as p,xa as B,ya as i}from"./chunk-5F276QAW.mjs";import{c as s}from"./chunk-OIST4OYN.mjs";var ve=i(k),Fe=i(C),Ie=i(I),ke=i(q);var le={CUi6pvobl:"(max-width: 809px)",iOT3honh2:"(min-width: 810px) and (max-width: 1439px)",XKtYVjVNC:"(min-width: 1440px)"},qe=()=>typeof document<"u",fe={CUi6pvobl:"framer-v-18na2tt",iOT3honh2:"framer-v-oco35x",XKtYVjVNC:"framer-v-1nuuy0c"};qe()&&R("XKtYVjVNC",le,fe);var Ce={"Desktop M":"XKtYVjVNC",Phone:"CUi6pvobl",Tablet:"iOT3honh2"},Ne={default:{duration:0}},me="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";function Me(){let m=j(null);return m.current===null&&(m.current=Array(5).fill(0).map(()=>me[Math.floor(Math.random()*me.length)]).join("")),m.current}var _e=S(function({id:m,style:se={},className:pe,width:Ve,height:je,layoutId:c,variant:N="XKtYVjVNC",TLSnfROIL:l,X0Ze_jQ_J:h,jyjDkRVhw:x,CWC5gxz5qrvcPLLT_v:de,...ce},he){let xe=Ce[N]||N,M=G(),f=H(oe,M),u=t=>{if(!f)throw new L(`No data in "Legal" matches path variables: ${JSON.stringify(M)}`);return f[t]};x===void 0&&(x=u("jyjDkRVhw")),l===void 0&&(l=u("TLSnfROIL")),h===void 0&&(h=u("X0Ze_jQ_J")),V(()=>{let t=ie(f);if(document.title=t.title||"",t.viewport){var y;(y=document.querySelector('meta[name="viewport"]'))===null||y===void 0||y.setAttribute("content",t.viewport)}},[f]);let[_,Le]=E(xe,le,!1),Ge=void 0,Ke=Ne.default,{activeVariantCallback:g,delay:Pe}=A(void 0),ue=g(async(...t)=>{s.open("mailto:<EMAIL>","_blank","noreferrer noopener")}),ge=g(async(...t)=>{s.open("https://popless.com","_blank","noreferrer noopener")}),we=g(async(...t)=>{s.open("https://twitter.com/popless_hq","_blank","noreferrer noopener")}),ye=Me(),{pointerEvents:w,...be}=se;return e(K.Provider,{value:{primaryVariantId:"XKtYVjVNC",variantClassNames:fe},children:e(z,{id:c??ye,children:a(r.div,{"data-framer-generated":!0,className:v("framer-qImoF",Z,re,ne,W,J),style:{display:"contents",pointerEvents:w??void 0},children:[e(r.div,{...ce,className:v("framer-1nuuy0c",pe),ref:he,style:{...be},children:a(r.main,{className:"framer-160r3up","data-framer-name":"Main",name:"Main",children:[e(n,{className:"framer-1mmaov5-container",children:e(F,{breakpoint:_,overrides:{CUi6pvobl:{variant:"aZMkidfTG"},iOT3honh2:{variant:"aZMkidfTG"}},children:e(k,{height:"100%",id:"zPFGxXMjy",layoutId:"zPFGxXMjy",style:{width:"100%"},variant:"yBBOIO6L6",width:"100%"})})}),a(r.header,{className:"framer-1u68r3i","data-framer-name":"Stack",name:"Stack",children:[e(p,{__fromCanvasComponent:!0,__link:"data:framer/page-link,P8liG2Pv7?transition=instant",children:e(b,{children:e("p",{style:{"--framer-line-height":"30px","--framer-text-alignment":"left"},children:e(P,{href:"data:framer/page-link,P8liG2Pv7?transition=instant",openInNewTab:!1,smoothScroll:!1,children:e("a",{href:"data:framer/page-link,P8liG2Pv7?transition=instant",children:e("span",{style:{"--font-selector":"R0Y7SW50ZXItcmVndWxhcg==","--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":"400","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Terms and Policies"})})})})}),className:"framer-bnjkjm",fonts:["GF;Inter-regular"],preload:["P8liG2Pv7"],verticalAlignment:"top",withExternalLayout:!0}),e(o,{__fromCanvasComponent:!0,alignment:"left",className:"framer-qmzgi2",fonts:["GF;Inter-regular"],rawHTML:"<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>/</span><br></span></p>",verticalAlignment:"top",withExternalLayout:!0}),e(o,{__fromCanvasComponent:!0,alignment:"left",className:"framer-sy86wg",fonts:["GF;Inter-regular"],rawHTML:"<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Privacy Policy</span><br></span></p>",text:l,verticalAlignment:"top",withExternalLayout:!0})]}),a(r.div,{className:"framer-1yede27",children:[a(r.section,{className:"framer-1eqkdl",children:[a(r.header,{className:"framer-1n8eokp","data-framer-name":"Stack",name:"Stack",children:[e(n,{className:"framer-wf2vpc-container",children:e(C,{height:"100%",id:"aZFCM_bex",layoutId:"aZFCM_bex",style:{width:"100%"},title:l,width:"100%"})}),e(o,{__fromCanvasComponent:!0,alignment:"left",className:"framer-yssz50",fonts:["GF;Inter-regular"],rawHTML:"<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Last updated on Feb 10, 2022</span><br></span></p>",text:h,verticalAlignment:"top",withExternalLayout:!0})]}),e(p,{__fromCanvasComponent:!0,children:x,className:"framer-252i6g",fonts:["GF;Inter-regular"],stylesPresetsClassNames:{a:"framer-styles-preset-1bok01c",h1:"framer-styles-preset-o3e5h0",h2:"framer-styles-preset-1m9bzi2",h3:"framer-styles-preset-ci2ngw",p:"framer-styles-preset-16bzrdu"},verticalAlignment:"top",withExternalLayout:!0})]}),a(r.section,{className:"framer-10wg483",children:[e(o,{__fromCanvasComponent:!0,alignment:"left",className:"framer-16p9vjk",fonts:["GF;Inter-500"],rawHTML:"<h2 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Need to get in touch?</span><br></span></h2>",text:de,verticalAlignment:"top",withExternalLayout:!0}),e(p,{__fromCanvasComponent:!0,children:e(b,{children:e("p",{style:{"--framer-line-height":"30px","--framer-text-alignment":"left"},children:e("span",{style:{"--font-selector":"R0Y7SW50ZXItcmVndWxhcg==","--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":"400","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"We\u2019ll start with some questions and get you to the right place."})})}),className:"framer-9jrax2",fonts:["GF;Inter-regular"],verticalAlignment:"top",withExternalLayout:!0}),e(n,{className:"framer-1j4cru0-container",children:e(I,{background:'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',buttonBG:"rgb(0, 0, 0)",height:"100%",id:"F5YS6oZUc",layoutId:"F5YS6oZUc",tap:ue,textColour:"rgb(255, 255, 255)",title:"Contact us",variant:"J6qwcVywR",width:"100%"})})]})]}),e(n,{className:"framer-15ccr6e-container",children:e(F,{breakpoint:_,overrides:{CUi6pvobl:{variant:"Zz_9kWOfb"}},children:e(q,{height:"100%",id:"C7uq1nl2A",layoutId:"C7uq1nl2A",poplessLink:ge,style:{width:"100%"},twitterLink:we,variant:"zyTRmFlly",width:"100%"})})})]})}),e("div",{id:"overlay"})]})})})}),Se=['.framer-qImoF [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }',"@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }",".framer-qImoF .framer-jp0h8z { display: block; }",".framer-qImoF .framer-1nuuy0c { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 80px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1440px; }",".framer-qImoF .framer-160r3up { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 100px 0px 0px 0px; position: relative; width: 100%; }",".framer-qImoF .framer-1mmaov5-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }",".framer-qImoF .framer-1u68r3i { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 20px 120px 40px 120px; position: relative; width: 1200px; }",".framer-qImoF .framer-bnjkjm { --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }",'.framer-qImoF .framer-qmzgi2 { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, #a8a8a8); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }','.framer-qImoF .framer-sy86wg { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }',".framer-qImoF .framer-1yede27 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 48px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 120px 150px 120px; position: relative; width: 1200px; }",".framer-qImoF .framer-1eqkdl { align-content: flex-end; align-items: flex-end; display: flex; flex: 1.5 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }",".framer-qImoF .framer-1n8eokp { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 0px 0px 40px 0px; position: relative; width: 100%; }",".framer-qImoF .framer-wf2vpc-container, .framer-qImoF .framer-15ccr6e-container { flex: none; height: auto; position: relative; width: 100%; }",'.framer-qImoF .framer-yssz50 { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }',".framer-qImoF .framer-252i6g { --framer-paragraph-spacing: 20px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }",".framer-qImoF .framer-10wg483 { align-content: flex-start; align-items: flex-start; display: flex; flex: 0.75 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }",'.framer-qImoF .framer-16p9vjk { --framer-font-family: "Inter", sans-serif; --framer-font-size: 24px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -1px; --framer-line-height: 1.4em; --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }',".framer-qImoF .framer-9jrax2 { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }",".framer-qImoF .framer-1j4cru0-container { flex: none; height: auto; position: relative; width: auto; }","@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1nuuy0c, .framer-qImoF .framer-160r3up, .framer-qImoF .framer-1u68r3i, .framer-qImoF .framer-1yede27, .framer-qImoF .framer-1eqkdl, .framer-qImoF .framer-1n8eokp, .framer-qImoF .framer-10wg483 { gap: 0px; } .framer-qImoF .framer-1nuuy0c > * { margin: 0px; margin-bottom: calc(80px / 2); margin-top: calc(80px / 2); } .framer-qImoF .framer-1nuuy0c > :first-child, .framer-qImoF .framer-160r3up > :first-child, .framer-qImoF .framer-1eqkdl > :first-child, .framer-qImoF .framer-1n8eokp > :first-child, .framer-qImoF .framer-10wg483 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1nuuy0c > :last-child, .framer-qImoF .framer-160r3up > :last-child, .framer-qImoF .framer-1eqkdl > :last-child, .framer-qImoF .framer-1n8eokp > :last-child, .framer-qImoF .framer-10wg483 > :last-child { margin-bottom: 0px; } .framer-qImoF .framer-160r3up > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-qImoF .framer-1u68r3i > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-qImoF .framer-1u68r3i > :first-child, .framer-qImoF .framer-1yede27 > :first-child { margin-left: 0px; } .framer-qImoF .framer-1u68r3i > :last-child, .framer-qImoF .framer-1yede27 > :last-child { margin-right: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-left: calc(48px / 2); margin-right: calc(48px / 2); } .framer-qImoF .framer-1eqkdl > *, .framer-qImoF .framer-1n8eokp > *, .framer-qImoF .framer-10wg483 > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } }","@media (min-width: 1440px) { .framer-qImoF .hidden-1nuuy0c { display: none !important; } }","@media (min-width: 810px) and (max-width: 1439px) { .framer-qImoF .hidden-oco35x { display: none !important; } .framer-qImoF .framer-1nuuy0c { width: 810px; } .framer-qImoF .framer-1u68r3i { max-width: unset; width: 100%; } .framer-qImoF .framer-1yede27 { flex-direction: column; width: 100%; } .framer-qImoF .framer-1eqkdl { align-content: center; align-items: center; flex: none; width: 100%; } .framer-qImoF .framer-10wg483 { flex: none; width: 100%; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1yede27 { gap: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-bottom: calc(48px / 2); margin-top: calc(48px / 2); } .framer-qImoF .framer-1yede27 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1yede27 > :last-child { margin-bottom: 0px; } }}","@media (max-width: 809px) { .framer-qImoF .hidden-18na2tt { display: none !important; } .framer-qImoF .framer-1nuuy0c { width: 390px; } .framer-qImoF .framer-1u68r3i { max-width: unset; padding: 20px 24px 40px 24px; width: 100%; } .framer-qImoF .framer-sy86wg { flex: 1 0 0px; overflow: hidden; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; } .framer-qImoF .framer-1yede27 { flex-direction: column; padding: 0px 24px 100px 24px; width: 100%; } .framer-qImoF .framer-1eqkdl { align-content: center; align-items: center; flex: none; width: 100%; } .framer-qImoF .framer-10wg483 { flex: none; width: 100%; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1yede27 { gap: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-bottom: calc(48px / 2); margin-top: calc(48px / 2); } .framer-qImoF .framer-1yede27 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1yede27 > :last-child { margin-bottom: 0px; } }}",...U,...ee,...ae,...X,...Q],d=T(_e,Se),rr=d;d.displayName="Page 2";d.defaultProps={height:42369,width:1440};B(d,[{family:"Inter",moduleAsset:{localModuleIdentifier:"local-module:screen/caI63G_Mf:default",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"},style:"normal",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",weight:"400"},{family:"Inter",moduleAsset:{localModuleIdentifier:"local-module:screen/caI63G_Mf:default",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"},style:"normal",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",weight:"500"},...ve,...Fe,...Ie,...ke,...O,...$,...te,...Y,...D]);var tr={exports:{default:{type:"reactComponent",name:"FramercaI63G_Mf",slots:[],annotations:{framerIntrinsicHeight:"42369",framerResponsiveScreen:"",framerContractVersion:"1",framerIntrinsicWidth:"1440",framerCanvasComponentVariantDetails:'{"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"iOT3honh2":{"layout":["fixed","auto"]},"CUi6pvobl":{"layout":["fixed","auto"]}}}'}},Props:{type:"tsType",annotations:{framerContractVersion:"1"}},__FramerMetadata__:{type:"variable"}}};export{tr as __FramerMetadata__,rr as default};
//# sourceMappingURL=HL265JttCmctH30wzbjwhHy1fAKAyDWfTU9VQFcyDaM.Y7YWNFRH.mjs.map
