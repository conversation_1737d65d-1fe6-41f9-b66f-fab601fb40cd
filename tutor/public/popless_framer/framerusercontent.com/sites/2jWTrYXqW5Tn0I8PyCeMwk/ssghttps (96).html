// Generated by Fr<PERSON>r (716dd6f)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, Link, RichText, Text, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import * as sharedStyle from "https://framerusercontent.com/modules/OFcCOdZuXVAHgZw0qo8P/OHRPv25zV8IUgdxfLUQn/GUvozIcPY.js";
import AskAQuestionFooter from "https://framerusercontent.com/modules/ITQeu3xwqQZwa5DSZ99g/a7tvxX4AmzIR9yxLswUx/xmXIrm86E.js";
import AssetsLogo from "https://framerusercontent.com/modules/2C84XLlICCTdnOkBT7Qv/UhnyqDy13mV1BKFPdxi9/xp2JJtRrF.js";
import Socials from "https://framerusercontent.com/modules/aB9dI5VoAPfp7g6iRTck/kdOpiIdd4hMiAy7kOGA9/y7yooNBNE.js";
const AssetsLogoFonts = getFonts(AssetsLogo);
const AskAQuestionFooterFonts = getFonts(AskAQuestionFooter);
const SocialsFonts = getFonts(Socials);
const cycleOrder = ["zyTRmFlly", "Zz_9kWOfb"];
const variantClassNames = {
  zyTRmFlly: "framer-v-112s07e",
  Zz_9kWOfb: "framer-v-ww8nno"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Desktop: "zyTRmFlly",
  Mobile: "Zz_9kWOfb"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "zyTRmFlly",
  poplessLink: ZmHtMReCL,
  twitterLink: CNlFvoj2A,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "zyTRmFlly",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const tapppjj7r = activeVariantCallback(async (...args) => {
    if (ZmHtMReCL) {
      const res = await ZmHtMReCL(...args);
      if (res === false) return false;
    }
  });
  const tap1jpqjwg = activeVariantCallback(async (...args) => {
    if (CNlFvoj2A) {
      const res = await CNlFvoj2A(...args);
      if (res === false) return false;
    }
  });
  const isDisplayed = () => {
    if (baseVariant === "Zz_9kWOfb") return false;
    return true;
  };
  const isDisplayed1 = () => {
    if (baseVariant === "Zz_9kWOfb") return true;
    return false;
  };
  const isDisplayed2 = () => {
    if (baseVariant === "Zz_9kWOfb") return true;
    return false;
  };
  const isDisplayed3 = () => {
    if (baseVariant === "Zz_9kWOfb") return true;
    return false;
  };
  const isDisplayed4 = () => {
    if (baseVariant === "Zz_9kWOfb") return true;
    return false;
  };
  const isDisplayed5 = () => {
    if (baseVariant === "Zz_9kWOfb") return true;
    return false;
  };
  const isDisplayed6 = () => {
    if (baseVariant === "Zz_9kWOfb") return false;
    return true;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-n5YHk", sharedStyle.className, classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsxs(motion.div, {
        ...restProps,
        className: cx("framer-112s07e", className),
        "data-framer-name": "Desktop",
        layoutDependency: layoutDependency,
        layoutId: "zyTRmFlly",
        ref: ref,
        style: {
          backgroundColor: "rgb(0, 0, 0)",
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          Zz_9kWOfb: {
            "data-framer-name": "Mobile"
          }
        }, baseVariant, gestureVariant),
        children: [/*#__PURE__*/_jsx(motion.div, {
          className: "framer-16a15kt",
          layoutDependency: layoutDependency,
          layoutId: "mUeEKTx1E",
          transition: transition,
          children: /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-yhtyou",
            layoutDependency: layoutDependency,
            layoutId: "XSp_7DYTY",
            transition: transition,
            children: [isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1ybi6cv-container",
              layoutDependency: layoutDependency,
              layoutId: "YaMRAL0TU-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(AssetsLogo, {
                color: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                height: "100%",
                id: "YaMRAL0TU",
                layoutId: "YaMRAL0TU",
                style: {
                  height: "100%",
                  width: "100%"
                },
                tap: tapppjj7r,
                title: "Popless",
                variant: "mIIVyqeUM",
                width: "100%"
              })
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1s9n895",
              layoutDependency: layoutDependency,
              layoutId: "nd_wYoQbD",
              transition: transition,
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItNTAw",
                      "--framer-font-size": "14px",
                      "--framer-font-weight": "500",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Support"
                  })
                }),
                className: "framer-y0amv1",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "m7PwRI_0u",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                  "--framer-link-text-decoration": "none",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,ijFjMFkqN",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,ijFjMFkqN",
                        children: "FAQs"
                      })
                    })
                  })
                }),
                className: "framer-14yi8l",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "JHo2HMkEY",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,hR530hScY",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,hR530hScY",
                        children: "Accessibility"
                      })
                    })
                  })
                }),
                className: "framer-1fp5w0o",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "P0zUgqWNW",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,CqoljZCO0",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Support center"
                      })
                    })
                  })
                }),
                className: "framer-agco53",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "flmU4oNXb",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,BhhXd9j5C",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Community"
                      })
                    })
                  })
                }),
                className: "framer-quedik",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "TULFWknbl",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-1y8zfja-container",
                layoutDependency: layoutDependency,
                layoutId: "r5VtmH0Rb-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(AskAQuestionFooter, {
                  height: "100%",
                  id: "r5VtmH0Rb",
                  layoutId: "r5VtmH0Rb",
                  title: "",
                  variant: "TfJ77UFlF",
                  width: "100%"
                })
              })]
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1dstxc4",
              layoutDependency: layoutDependency,
              layoutId: "KuybVbMn2",
              transition: transition,
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItNTAw",
                      "--framer-font-size": "14px",
                      "--framer-font-weight": "500",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Tutoring"
                  })
                }),
                className: "framer-1mnx26r",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "XqCVwDLpZ",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                  "--framer-link-text-decoration": "none",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,eAoNDui3w",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,eAoNDui3w",
                        children: "Become a tutor"
                      })
                    })
                  })
                }),
                className: "framer-16djjs3",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "qVqIZwOm3",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,QXYyFVKW8",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,QXYyFVKW8",
                        children: "Teach a class"
                      })
                    })
                  })
                }),
                className: "framer-ucccz3",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "WOCmm4E9M",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,ioeg9oqlo",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,ioeg9oqlo",
                        children: "Find new students"
                      })
                    })
                  })
                }),
                className: "framer-1z11s15",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "QJYVWnvKK",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,AYia5YfcP",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Refer program"
                      })
                    })
                  })
                }),
                className: "framer-17me7hs",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "PLR8TbEYL",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,fwhrKHx6D",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Pricing"
                      })
                    })
                  })
                }),
                className: "framer-1mct1k9",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "RQbMqQkXv",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-yo16e2",
              layoutDependency: layoutDependency,
              layoutId: "p6Ta64fet",
              transition: transition,
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItNTAw",
                      "--framer-font-size": "14px",
                      "--framer-font-weight": "500",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Popless"
                  })
                }),
                className: "framer-k4n9x6",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "xdR4eXCGV",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                  "--framer-link-text-decoration": "none",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,UNJugco9I",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Tutor platform"
                      })
                    })
                  })
                }),
                className: "framer-hv9ytq",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "H1JZyOlEr",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,qFQZWHHvr",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Feature releases"
                      })
                    })
                  })
                }),
                className: "framer-1a6zdoi",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "A6pjwYJWm",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,ioeg9oqlo",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "How it works"
                      })
                    })
                  })
                }),
                className: "framer-1bqxovp",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "TCfNh2mc7",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "https://calendly.com/popless/intro",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "https://calendly.com/popless/intro",
                        rel: "noreferrer noopener",
                        children: "Request a demo"
                      })
                    })
                  })
                }),
                className: "framer-1b9a349",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "BDxneu9I6",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            }), isDisplayed1() && /*#__PURE__*/_jsx(motion.div, {
              className: "framer-b5no9j",
              layoutDependency: layoutDependency,
              layoutId: "KaSu7SufR",
              style: {
                backgroundColor: "rgb(116, 116, 116)",
                borderBottomLeftRadius: 100,
                borderBottomRightRadius: 100,
                borderTopLeftRadius: 100,
                borderTopRightRadius: 100
              },
              transition: transition
            }), isDisplayed2() && /*#__PURE__*/_jsx(motion.div, {
              className: "framer-6dxnzh",
              layoutDependency: layoutDependency,
              layoutId: "vnfVKuN4q",
              style: {
                backgroundColor: "rgb(116, 116, 116)",
                borderBottomLeftRadius: 100,
                borderBottomRightRadius: 100,
                borderTopLeftRadius: 100,
                borderTopRightRadius: 100
              },
              transition: transition
            }), isDisplayed3() && /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1k7wxqh",
              layoutDependency: layoutDependency,
              layoutId: "ABh_W6Wds",
              style: {
                backgroundColor: "rgb(116, 116, 116)",
                borderBottomLeftRadius: 100,
                borderBottomRightRadius: 100,
                borderTopLeftRadius: 100,
                borderTopRightRadius: 100
              },
              transition: transition
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-15qsfkm",
              layoutDependency: layoutDependency,
              layoutId: "mm7smmTRo",
              transition: transition,
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItNTAw",
                      "--framer-font-size": "14px",
                      "--framer-font-weight": "500",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "About"
                  })
                }),
                className: "framer-1mr3ez9",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "leTBlpwfJ",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                  "--framer-link-text-decoration": "none",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,tgeC919VQ",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Blog"
                      })
                    })
                  })
                }),
                className: "framer-19lnh9a",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "eT3J1CMXV",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,J_W7bxNYj",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Mission"
                      })
                    })
                  })
                }),
                className: "framer-2ic09p",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "okWxaHr1P",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,QdObsJ2ml",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        children: "Careers"
                      })
                    })
                  })
                }),
                className: "framer-hh4uwx",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "MD8YDOFCG",
                style: {
                  "--extracted-r6o4lv": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-font-size": "14px",
                      "--framer-line-height": "22px"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,fkcJWEs7D",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        className: "framer-styles-preset-1vs0812",
                        "data-styles-preset": "GUvozIcPY",
                        href: "data:framer/page-link,fkcJWEs7D",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "14px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-text-color": "var(--extracted-hl0iuy)"
                          },
                          children: "Contact"
                        })
                      })
                    })
                  })
                }),
                className: "framer-53oyn0",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "qwyqRoadX",
                style: {
                  "--extracted-hl0iuy": "rgb(255, 255, 255)",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            })]
          })
        }), isDisplayed4() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1hgqahc",
          layoutDependency: layoutDependency,
          layoutId: "YdCozIi7U",
          style: {
            backgroundColor: "rgb(116, 116, 116)",
            borderBottomLeftRadius: 100,
            borderBottomRightRadius: 100,
            borderTopLeftRadius: 100,
            borderTopRightRadius: 100
          },
          transition: transition
        }), isDisplayed5() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1t8a5t2",
          layoutDependency: layoutDependency,
          layoutId: "VIBfT4Yfr",
          transition: transition,
          children: /*#__PURE__*/_jsx(motion.div, {
            className: "framer-vgmt40",
            layoutDependency: layoutDependency,
            layoutId: "A9Lz4bPNk",
            transition: transition,
            children: /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1dtpzlt",
              layoutDependency: layoutDependency,
              layoutId: "b8J_UweJM",
              transition: transition,
              children: /*#__PURE__*/_jsxs(motion.div, {
                className: "framer-4wip8j",
                layoutDependency: layoutDependency,
                layoutId: "tZtG0XGDe",
                transition: transition,
                children: [/*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx(motion.p, {
                      style: {
                        "--framer-font-size": "14px",
                        "--framer-line-height": "22px"
                      },
                      children: /*#__PURE__*/_jsx(motion.span, {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                          "--framer-font-family": '"Inter", sans-serif',
                          "--framer-font-size": "14px",
                          "--framer-font-style": "normal",
                          "--framer-font-weight": "400",
                          "--framer-text-color": "var(--extracted-1w3ko1f)"
                        },
                        children: "Copyright \xa9 2022 Popless"
                      })
                    })
                  }),
                  className: "framer-cdqb51",
                  fonts: ["GF;Inter-regular"],
                  layoutDependency: layoutDependency,
                  layoutId: "rL0MzOOAs",
                  style: {
                    "--extracted-1w3ko1f": "rgb(255, 255, 255)",
                    "--framer-paragraph-spacing": "0px"
                  },
                  transition: transition,
                  verticalAlignment: "top",
                  withExternalLayout: true
                }), /*#__PURE__*/_jsxs(motion.div, {
                  className: "framer-1kqlr9l",
                  layoutDependency: layoutDependency,
                  layoutId: "CQfmG90dp",
                  transition: transition,
                  children: [/*#__PURE__*/_jsx(Text, {
                    __fromCanvasComponent: true,
                    __link: "data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&collectionItemId=QqQfO6VWF&pathVariables=L4KcAaijt%3Dprivacy",
                    className: "framer-8p729x",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "PzzOnpQv4",
                    preload: ["caI63G_Mf"],
                    rawHTML: "<a style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit' href=\"data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&amp;collectionItemId=QqQfO6VWF&amp;pathVariables=L4KcAaijt%3Dprivacy\"><span><span style='direction: ltr; font-size: 0'><span style=''>Privacy</span><br></span></span></a>",
                    style: {
                      "--framer-font-family": '"Inter", sans-serif',
                      "--framer-font-size": "14px",
                      "--framer-font-style": "normal",
                      "--framer-font-weight": 400,
                      "--framer-letter-spacing": "0px",
                      "--framer-line-height": "22px",
                      "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                      "--framer-link-text-decoration": "none",
                      "--framer-text-alignment": "start",
                      "--framer-text-color": "rgb(255, 255, 255)",
                      "--framer-text-decoration": "none",
                      "--framer-text-transform": "none"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(Text, {
                    __fromCanvasComponent: true,
                    __link: "data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&collectionItemId=EG7dKKygZ&pathVariables=L4KcAaijt%3Dterms",
                    className: "framer-7n429p",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "KsjVHFKLa",
                    preload: ["caI63G_Mf"],
                    rawHTML: "<a style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit' href=\"data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&amp;collectionItemId=EG7dKKygZ&amp;pathVariables=L4KcAaijt%3Dterms\"><span><span style='direction: ltr; font-size: 0'><span style=''>Terms</span><br></span></span></a>",
                    style: {
                      "--framer-font-family": '"Inter", sans-serif',
                      "--framer-font-size": "14px",
                      "--framer-font-style": "normal",
                      "--framer-font-weight": 400,
                      "--framer-letter-spacing": "0px",
                      "--framer-line-height": "22px",
                      "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                      "--framer-link-text-decoration": "none",
                      "--framer-text-alignment": "start",
                      "--framer-text-color": "rgb(255, 255, 255)",
                      "--framer-text-decoration": "none",
                      "--framer-text-transform": "none"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px",
                          "--framer-text-color": "var(--extracted-r6o4lv)"
                        },
                        children: /*#__PURE__*/_jsx(Link, {
                          href: "data:framer/page-link,fkcJWEs7D",
                          openInNewTab: false,
                          smoothScroll: false,
                          children: /*#__PURE__*/_jsx(motion.a, {
                            className: "framer-styles-preset-1vs0812",
                            "data-styles-preset": "GUvozIcPY",
                            href: "data:framer/page-link,fkcJWEs7D",
                            children: "Contact"
                          })
                        })
                      })
                    }),
                    className: "framer-1jz30gp",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "EchzVYseB",
                    style: {
                      "--extracted-r6o4lv": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px",
                          "--framer-text-color": "var(--extracted-r6o4lv)"
                        },
                        children: /*#__PURE__*/_jsx(Link, {
                          href: "https://twitter.com/popless_hq",
                          openInNewTab: false,
                          smoothScroll: false,
                          children: /*#__PURE__*/_jsx(motion.a, {
                            className: "framer-styles-preset-1vs0812",
                            "data-styles-preset": "GUvozIcPY",
                            href: "https://twitter.com/popless_hq",
                            rel: "noreferrer noopener",
                            children: "Twitter"
                          })
                        })
                      })
                    }),
                    className: "framer-1yb12vk",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "Ehqald8l2",
                    style: {
                      "--extracted-r6o4lv": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  })]
                })]
              })
            })
          })
        }), isDisplayed6() && /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1uw5sv0",
          layoutDependency: layoutDependency,
          layoutId: "TOJCQqm5Y",
          transition: transition,
          children: [/*#__PURE__*/_jsx(motion.div, {
            className: "framer-1faobnr",
            layoutDependency: layoutDependency,
            layoutId: "LZZiHFd5j",
            style: {
              backgroundColor: "rgb(116, 116, 116)",
              borderBottomLeftRadius: 100,
              borderBottomRightRadius: 100,
              borderTopLeftRadius: 100,
              borderTopRightRadius: 100
            },
            transition: transition
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-1bvflhh",
            layoutDependency: layoutDependency,
            layoutId: "EXfg_OZ1W",
            transition: transition,
            children: /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1mdh93q",
              layoutDependency: layoutDependency,
              layoutId: "vCcOuPekl",
              transition: transition,
              children: /*#__PURE__*/_jsxs(motion.div, {
                className: "framer-hw0kuw",
                layoutDependency: layoutDependency,
                layoutId: "ru13foxOm",
                transition: transition,
                children: [/*#__PURE__*/_jsxs(motion.div, {
                  className: "framer-4uykzm",
                  layoutDependency: layoutDependency,
                  layoutId: "i1jedj7tM",
                  transition: transition,
                  children: [/*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px"
                        },
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "14px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-text-color": "var(--extracted-1w3ko1f)"
                          },
                          children: "Copyright \xa9 2022 Popless"
                        })
                      })
                    }),
                    className: "framer-4g2085",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "b2zfgDCc9",
                    style: {
                      "--extracted-1w3ko1f": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px"
                        },
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "14px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-text-color": "var(--extracted-1w3ko1f)"
                          },
                          children: "\xb7"
                        })
                      })
                    }),
                    className: "framer-1oiqhik",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "UWT8DQsUK",
                    style: {
                      "--extracted-1w3ko1f": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(Text, {
                    __fromCanvasComponent: true,
                    __link: "data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&collectionItemId=QqQfO6VWF&pathVariables=L4KcAaijt%3Dprivacy",
                    className: "framer-701nfs",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "AcCTC1IJs",
                    preload: ["caI63G_Mf"],
                    rawHTML: "<a style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit' href=\"data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&amp;collectionItemId=QqQfO6VWF&amp;pathVariables=L4KcAaijt%3Dprivacy\"><span><span style='direction: ltr; font-size: 0'><span style=''>Privacy</span><br></span></span></a>",
                    style: {
                      "--framer-font-family": '"Inter", sans-serif',
                      "--framer-font-size": "14px",
                      "--framer-font-style": "normal",
                      "--framer-font-weight": 400,
                      "--framer-letter-spacing": "0px",
                      "--framer-line-height": "22px",
                      "--framer-link-hover-text-color": "rgba(255, 255, 255, 0.8)",
                      "--framer-link-text-decoration": "none",
                      "--framer-text-alignment": "start",
                      "--framer-text-color": "rgb(255, 255, 255)",
                      "--framer-text-decoration": "none",
                      "--framer-text-transform": "none"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px"
                        },
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "14px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-text-color": "var(--extracted-1w3ko1f)"
                          },
                          children: "\xb7"
                        })
                      })
                    }),
                    className: "framer-19yn1j8",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "XrwCooIQC",
                    style: {
                      "--extracted-1w3ko1f": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsx(RichText, {
                    __fromCanvasComponent: true,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx(motion.p, {
                        style: {
                          "--framer-font-size": "14px",
                          "--framer-line-height": "22px"
                        },
                        children: /*#__PURE__*/_jsx(Link, {
                          href: "data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&collectionItemId=EG7dKKygZ&pathVariables=L4KcAaijt%3Dterms",
                          openInNewTab: false,
                          smoothScroll: false,
                          children: /*#__PURE__*/_jsx(motion.a, {
                            className: "framer-styles-preset-1vs0812",
                            "data-styles-preset": "GUvozIcPY",
                            href: "data:framer/page-link,caI63G_Mf?collection=local-module%3Acollection%2FEWmio7u6f%3Adefault&collectionItemId=EG7dKKygZ&pathVariables=L4KcAaijt%3Dterms",
                            children: /*#__PURE__*/_jsx(motion.span, {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                                "--framer-font-family": '"Inter", sans-serif',
                                "--framer-font-size": "14px",
                                "--framer-font-style": "normal",
                                "--framer-font-weight": "400",
                                "--framer-text-color": "var(--extracted-hl0iuy)"
                              },
                              children: "Terms"
                            })
                          })
                        })
                      })
                    }),
                    className: "framer-1i8wo2r",
                    fonts: ["GF;Inter-regular"],
                    layoutDependency: layoutDependency,
                    layoutId: "XDn0MTAYd",
                    style: {
                      "--extracted-hl0iuy": "rgb(255, 255, 255)",
                      "--framer-paragraph-spacing": "0px"
                    },
                    transition: transition,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  })]
                }), /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-1wqxfsb-container",
                  "data-framer-name": "Socials",
                  layoutDependency: layoutDependency,
                  layoutId: "Dk_sjRoWA-container",
                  name: "Socials",
                  transition: transition,
                  children: /*#__PURE__*/_jsx(Socials, {
                    height: "100%",
                    id: "Dk_sjRoWA",
                    layoutId: "Dk_sjRoWA",
                    name: "Socials",
                    variant: "cR0QzlygM",
                    width: "100%"
                  })
                })]
              })
            })
          })]
        })]
      })
    })
  });
});
const css = ['.framer-n5YHk [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-n5YHk * { box-sizing: border-box; }", ".framer-n5YHk .framer-ydfcn7 { display: block; }", ".framer-n5YHk .framer-112s07e { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 40px; height: min-content; justify-content: center; overflow: visible; padding: 80px 50px 20px 50px; position: relative; width: 1920px; }", ".framer-n5YHk .framer-16a15kt, .framer-n5YHk .framer-1bvflhh { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; max-width: 1200px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-n5YHk .framer-yhtyou { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; max-width: 1200px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-n5YHk .framer-1ybi6cv-container { flex: none; height: 32px; position: relative; width: 32px; }", ".framer-n5YHk .framer-1s9n895, .framer-n5YHk .framer-1dstxc4, .framer-n5YHk .framer-yo16e2, .framer-n5YHk .framer-15qsfkm { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-n5YHk .framer-y0amv1, .framer-n5YHk .framer-14yi8l, .framer-n5YHk .framer-1fp5w0o, .framer-n5YHk .framer-agco53, .framer-n5YHk .framer-quedik, .framer-n5YHk .framer-1mnx26r, .framer-n5YHk .framer-16djjs3, .framer-n5YHk .framer-ucccz3, .framer-n5YHk .framer-1z11s15, .framer-n5YHk .framer-17me7hs, .framer-n5YHk .framer-1mct1k9, .framer-n5YHk .framer-k4n9x6, .framer-n5YHk .framer-hv9ytq, .framer-n5YHk .framer-1a6zdoi, .framer-n5YHk .framer-1bqxovp, .framer-n5YHk .framer-1b9a349, .framer-n5YHk .framer-1mr3ez9, .framer-n5YHk .framer-19lnh9a, .framer-n5YHk .framer-2ic09p, .framer-n5YHk .framer-hh4uwx, .framer-n5YHk .framer-53oyn0, .framer-n5YHk .framer-20fr0u, .framer-n5YHk .framer-1jz30gp, .framer-n5YHk .framer-1yb12vk, .framer-n5YHk .framer-198ywlq, .framer-n5YHk .framer-4g2085, .framer-n5YHk .framer-1oiqhik, .framer-n5YHk .framer-19yn1j8, .framer-n5YHk .framer-1i8wo2r { flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }", ".framer-n5YHk .framer-1y8zfja-container, .framer-n5YHk .framer-m3gpod-container, .framer-n5YHk .framer-1wqxfsb-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-n5YHk .framer-b5no9j, .framer-n5YHk .framer-6dxnzh, .framer-n5YHk .framer-1k7wxqh { flex: 1 0 0px; height: 1px; max-width: 1440px; overflow: hidden; position: relative; width: 1px; will-change: transform; }", ".framer-n5YHk .framer-1hgqahc { flex: none; height: 1px; overflow: hidden; position: relative; width: 100%; will-change: transform; }", ".framer-n5YHk .framer-1t8a5t2 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-n5YHk .framer-vgmt40 { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: 54px; justify-content: space-between; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-n5YHk .framer-1dtpzlt { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: 100%; justify-content: space-between; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-n5YHk .framer-4wip8j { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-n5YHk .framer-cdqb51 { flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-n5YHk .framer-qadfkb, .framer-n5YHk .framer-8p729x, .framer-n5YHk .framer-7n429p, .framer-n5YHk .framer-rcan28, .framer-n5YHk .framer-701nfs { flex: none; height: auto; overflow: visible; position: relative; text-decoration: none; white-space: pre; width: auto; }", ".framer-n5YHk .framer-1kqlr9l { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-n5YHk .framer-1uw5sv0 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-n5YHk .framer-1faobnr { flex: none; height: 1px; max-width: 1200px; overflow: hidden; position: relative; width: 100%; will-change: transform; }", ".framer-n5YHk .framer-1mdh93q, .framer-n5YHk .framer-hw0kuw { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; max-width: 1200px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-n5YHk .framer-4uykzm { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-n5YHk .framer-1wsxv5q { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: min-content; justify-content: flex-start; min-height: 200px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-n5YHk .framer-112s07e, .framer-n5YHk .framer-1s9n895, .framer-n5YHk .framer-1dstxc4, .framer-n5YHk .framer-yo16e2, .framer-n5YHk .framer-15qsfkm, .framer-n5YHk .framer-4wip8j, .framer-n5YHk .framer-1kqlr9l, .framer-n5YHk .framer-1uw5sv0, .framer-n5YHk .framer-4uykzm, .framer-n5YHk .framer-1wsxv5q { gap: 0px; } .framer-n5YHk .framer-112s07e > * { margin: 0px; margin-bottom: calc(40px / 2); margin-top: calc(40px / 2); } .framer-n5YHk .framer-112s07e > :first-child, .framer-n5YHk .framer-1s9n895 > :first-child, .framer-n5YHk .framer-1dstxc4 > :first-child, .framer-n5YHk .framer-yo16e2 > :first-child, .framer-n5YHk .framer-15qsfkm > :first-child, .framer-n5YHk .framer-4wip8j > :first-child, .framer-n5YHk .framer-1uw5sv0 > :first-child { margin-top: 0px; } .framer-n5YHk .framer-112s07e > :last-child, .framer-n5YHk .framer-1s9n895 > :last-child, .framer-n5YHk .framer-1dstxc4 > :last-child, .framer-n5YHk .framer-yo16e2 > :last-child, .framer-n5YHk .framer-15qsfkm > :last-child, .framer-n5YHk .framer-4wip8j > :last-child, .framer-n5YHk .framer-1uw5sv0 > :last-child { margin-bottom: 0px; } .framer-n5YHk .framer-1s9n895 > *, .framer-n5YHk .framer-1dstxc4 > *, .framer-n5YHk .framer-yo16e2 > *, .framer-n5YHk .framer-15qsfkm > *, .framer-n5YHk .framer-4wip8j > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-n5YHk .framer-1kqlr9l > *, .framer-n5YHk .framer-1wsxv5q > * { margin: 0px; margin-left: calc(15px / 2); margin-right: calc(15px / 2); } .framer-n5YHk .framer-1kqlr9l > :first-child, .framer-n5YHk .framer-4uykzm > :first-child, .framer-n5YHk .framer-1wsxv5q > :first-child { margin-left: 0px; } .framer-n5YHk .framer-1kqlr9l > :last-child, .framer-n5YHk .framer-4uykzm > :last-child, .framer-n5YHk .framer-1wsxv5q > :last-child { margin-right: 0px; } .framer-n5YHk .framer-1uw5sv0 > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-n5YHk .framer-4uykzm > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-n5YHk.framer-v-ww8nno .framer-112s07e { padding: 40px 20px 40px 20px; width: 390px; }", ".framer-n5YHk.framer-v-ww8nno .framer-16a15kt { max-width: unset; order: 0; }", ".framer-n5YHk.framer-v-ww8nno .framer-yhtyou { flex-direction: column; gap: 30px; justify-content: flex-start; max-width: unset; }", ".framer-n5YHk.framer-v-ww8nno .framer-1s9n895 { gap: 20px; order: 1; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-y0amv1, .framer-n5YHk.framer-v-ww8nno .framer-14yi8l, .framer-n5YHk.framer-v-ww8nno .framer-1fp5w0o, .framer-n5YHk.framer-v-ww8nno .framer-agco53, .framer-n5YHk.framer-v-ww8nno .framer-quedik, .framer-n5YHk.framer-v-ww8nno .framer-1mnx26r, .framer-n5YHk.framer-v-ww8nno .framer-16djjs3, .framer-n5YHk.framer-v-ww8nno .framer-ucccz3, .framer-n5YHk.framer-v-ww8nno .framer-1z11s15, .framer-n5YHk.framer-v-ww8nno .framer-17me7hs, .framer-n5YHk.framer-v-ww8nno .framer-1mct1k9, .framer-n5YHk.framer-v-ww8nno .framer-hv9ytq, .framer-n5YHk.framer-v-ww8nno .framer-1a6zdoi, .framer-n5YHk.framer-v-ww8nno .framer-1bqxovp, .framer-n5YHk.framer-v-ww8nno .framer-1b9a349, .framer-n5YHk.framer-v-ww8nno .framer-19lnh9a, .framer-n5YHk.framer-v-ww8nno .framer-2ic09p, .framer-n5YHk.framer-v-ww8nno .framer-hh4uwx, .framer-n5YHk.framer-v-ww8nno .framer-53oyn0 { overflow: hidden; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-n5YHk.framer-v-ww8nno .framer-1dstxc4 { gap: 20px; order: 3; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-yo16e2 { gap: 20px; order: 5; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-b5no9j { flex: none; order: 2; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-6dxnzh { flex: none; order: 6; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-1k7wxqh { flex: none; order: 4; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-15qsfkm { gap: 20px; order: 7; width: 100%; }", ".framer-n5YHk.framer-v-ww8nno .framer-1hgqahc { order: 1; }", ".framer-n5YHk.framer-v-ww8nno .framer-1t8a5t2 { order: 2; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-n5YHk.framer-v-ww8nno .framer-yhtyou, .framer-n5YHk.framer-v-ww8nno .framer-1s9n895, .framer-n5YHk.framer-v-ww8nno .framer-1dstxc4, .framer-n5YHk.framer-v-ww8nno .framer-yo16e2, .framer-n5YHk.framer-v-ww8nno .framer-15qsfkm { gap: 0px; } .framer-n5YHk.framer-v-ww8nno .framer-yhtyou > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-n5YHk.framer-v-ww8nno .framer-yhtyou > :first-child, .framer-n5YHk.framer-v-ww8nno .framer-1s9n895 > :first-child, .framer-n5YHk.framer-v-ww8nno .framer-1dstxc4 > :first-child, .framer-n5YHk.framer-v-ww8nno .framer-yo16e2 > :first-child, .framer-n5YHk.framer-v-ww8nno .framer-15qsfkm > :first-child { margin-top: 0px; } .framer-n5YHk.framer-v-ww8nno .framer-yhtyou > :last-child, .framer-n5YHk.framer-v-ww8nno .framer-1s9n895 > :last-child, .framer-n5YHk.framer-v-ww8nno .framer-1dstxc4 > :last-child, .framer-n5YHk.framer-v-ww8nno .framer-yo16e2 > :last-child, .framer-n5YHk.framer-v-ww8nno .framer-15qsfkm > :last-child { margin-bottom: 0px; } .framer-n5YHk.framer-v-ww8nno .framer-1s9n895 > *, .framer-n5YHk.framer-v-ww8nno .framer-1dstxc4 > *, .framer-n5YHk.framer-v-ww8nno .framer-yo16e2 > *, .framer-n5YHk.framer-v-ww8nno .framer-15qsfkm > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } }", ...sharedStyle.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerIntrinsicHeight 367
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerIntrinsicWidth 1920
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"Zz_9kWOfb":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            * @framerVariables {"ZmHtMReCL":"poplessLink","CNlFvoj2A":"twitterLink"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            */
const FramerZH45lfARx = withCSS(Component, css);
export default FramerZH45lfARx;
FramerZH45lfARx.displayName = "Footer - New";
FramerZH45lfARx.defaultProps = {
  height: 367,
  width: 1920
};
addPropertyControls(FramerZH45lfARx, {
  variant: {
    options: ["zyTRmFlly", "Zz_9kWOfb"],
    optionTitles: ["Desktop", "Mobile"],
    title: "Variant",
    type: ControlType.Enum
  },
  ZmHtMReCL: {
    title: "Popless Link",
    type: ControlType.EventHandler
  },
  CNlFvoj2A: {
    title: "Twitter Link",
    type: ControlType.EventHandler
  }
});
addFonts(FramerZH45lfARx, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/ZH45lfARx:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/ZH45lfARx:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...AssetsLogoFonts, ...AskAQuestionFooterFonts, ...SocialsFonts, ...sharedStyle.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerZH45lfARx",
      "slots": [],
      "annotations": {
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"Zz_9kWOfb\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerVariables": "{\"ZmHtMReCL\":\"poplessLink\",\"CNlFvoj2A\":\"twitterLink\"}",
        "framerIntrinsicWidth": "1920",
        "framerIntrinsicHeight": "367",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};