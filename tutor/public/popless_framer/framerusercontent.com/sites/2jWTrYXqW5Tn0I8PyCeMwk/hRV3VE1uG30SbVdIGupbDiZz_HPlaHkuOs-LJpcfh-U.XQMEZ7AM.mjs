import{a as Ce}from"./chunk-ILWRYHMR.mjs";import{a as _e,b as Oe,c as Le}from"./chunk-V3HRZXTD.mjs";import{a as oe}from"./chunk-QDKAFU6T.mjs";import"./chunk-7FSZCHHR.mjs";import{b as te,c as we}from"./chunk-BI4OMGMN.mjs";import{c as ie,e as se}from"./chunk-Y45OGLEC.mjs";import"./chunk-42U43NKG.mjs";import{b as g,c as B}from"./chunk-VWWF2A63.mjs";import{K as q,N as pe,Q as ne,W as Z,X as he,Z as ve,c as W,g as de,ga as $,ha as Y,ia as ge,k as fe,ka as ye,la as be,m as ue,o as Q,t as F,v as me,va as ae,wa as ee,xa as xe,ya as J}from"./chunk-5F276QAW.mjs";import{b as U,c as L,d as V}from"./chunk-OIST4OYN.mjs";var He=typeof globalThis<"u"?globalThis:typeof L<"u"?L:typeof V<"u"?V:typeof self<"u"?self:{};function Ne(z,A,H){return H={path:A,exports:{},require:function(R,h){return Fe(R,h??H.path)}},z(H,H.exports),H.exports}function Fe(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var X=Ne(function(z,A){(function(H,R){z.exports=R()})(He,function(){return function(){var H={27:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.FORM_BASE_URL=e.POPUP_SIZE=e.SLIDER_WIDTH=e.SLIDER_POSITION=e.SIDETAB_ATTRIBUTE=e.WIDGET_ATTRIBUTE=e.SLIDER_ATTRIBUTE=e.POPUP_ATTRIBUTE=e.POPOVER_ATTRIBUTE=void 0,e.POPOVER_ATTRIBUTE="data-tf-popover",e.POPUP_ATTRIBUTE="data-tf-popup",e.SLIDER_ATTRIBUTE="data-tf-slider",e.WIDGET_ATTRIBUTE="data-tf-widget",e.SIDETAB_ATTRIBUTE="data-tf-sidetab",e.SLIDER_POSITION="right",e.SLIDER_WIDTH=800,e.POPUP_SIZE=100,e.FORM_BASE_URL="https://form.typeform.com"},528:function(h,e,i){var l=this&&this.__assign||function(){return(l=Object.assign||function(s){for(var c,f=1,u=arguments.length;f<u;f++)for(var d in c=arguments[f])Object.prototype.hasOwnProperty.call(c,d)&&(s[d]=c[d]);return s}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.createPopover=void 0;var r=i(747),o=i(320),a=function(s,c){var f=s.parentNode;f&&(f.removeChild(s),f.appendChild(c))},t=function(s,c){s===void 0&&(s="div"),c===void 0&&(c="tf-v1-popover-button-icon");var f=document.createElement(s);return f.className=c+" tf-v1-close-icon",f.innerHTML="&times;",f.dataset.testid=c,f},n={buttonColor:"#3a7685"};e.createPopover=function(s,c){c===void 0&&(c={});var f,u,d=l(l({},n),c),w=r.createIframe(s,"popover",d),_=w.iframe,v=w.embedId,S=w.refresh,y=function(b,C){var E=document.createElement("div");return E.className="tf-v1-popover",E.dataset.testid="tf-v1-popover",r.setElementSize(E,{width:b,height:C})}(d.width,d.height),m=function(){var b=document.createElement("div");return b.className="tf-v1-popover-wrapper",b.dataset.testid="tf-v1-popover-wrapper",b}(),p=function(b,C){var E=r.getTextColor(C),N=document.createElement("div");N.className="tf-v1-popover-button-icon";var D=`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 0H0V9L10.5743 24V16.5H21C22.6567 16.5 24 15.1567 24 13.5V3C24 1.34325 22.6567 0 21 0ZM7.5
    9.75C6.672 9.75 6 9.07875 6 8.25C6 7.42125 6.672 6.75 7.5 6.75C8.328 6.75 9 7.42125 9 8.25C9 9.07875 8.328 9.75
    7.5 9.75ZM12.75 9.75C11.922 9.75 11.25 9.07875 11.25 8.25C11.25 7.42125 11.922 6.75 12.75 6.75C13.578 6.75 14.25
    7.42125 14.25 8.25C14.25 9.07875 13.578 9.75 12.75 9.75ZM18 9.75C17.172 9.75 16.5 9.07875 16.5 8.25C16.5 7.42125
    17.172 6.75 18 6.75C18.828 6.75 19.5 7.42125 19.5 8.25C19.5 9.07875 18.828 9.75 18 9.75Z" fill="`+E+`"></path>
  </svg>`,G=b?.startsWith("http");return N.innerHTML=G?"<img alt='popover trigger icon button' src='"+b+"'/>":b??D,N.dataset.testid="default-icon",N}(d.customIcon,d.buttonColor||n.buttonColor),x=function(){var b=document.createElement("div");b.className="tf-v1-spinner";var C=document.createElement("div");return C.className="tf-v1-popover-button-icon",C.dataset.testid="spinner-icon",C.append(b),C}(),P=t(),I=t("a","tf-v1-popover-close"),j=function(b){var C=r.getTextColor(b),E=document.createElement("button");return E.className="tf-v1-popover-button",E.dataset.testid="tf-v1-popover-button",E.style.backgroundColor=b,E.style.color=C,E}(d.buttonColor||n.buttonColor);(d.container||document.body).append(y),m.append(_),y.append(j),y.append(I),j.append(p);var M=function(){f&&f.parentNode&&(f.classList.add("closing"),setTimeout(function(){r.unmountElement(f)},250))};d.tooltip&&d.tooltip.length>0&&(f=function(b,C){var E=document.createElement("span");E.className="tf-v1-popover-tooltip-close",E.dataset.testid="tf-v1-popover-tooltip-close",E.innerHTML="&times;",E.onclick=C;var N=document.createElement("div");N.className="tf-v1-popover-tooltip-text",N.innerHTML=b;var D=document.createElement("div");return D.className="tf-v1-popover-tooltip",D.dataset.testid="tf-v1-popover-tooltip",D.appendChild(N),D.appendChild(E),D}(d.tooltip,M),y.append(f)),d.notificationDays&&(d.enableSandbox||o.canBuildNotificationDot(s))&&(u=o.buildNotificationDot(),j.append(u)),_.onload=function(){y.classList.add("open"),m.style.opacity="1",I.style.opacity="1",a(x,P),r.addCustomKeyboardListener(O)};var T=function(){r.isOpen(m)||(M(),u&&(u.classList.add("closing"),d.notificationDays&&!d.enableSandbox&&o.saveNotificationDotHideUntilTime(s,d.notificationDays),setTimeout(function(){r.unmountElement(u)},250)),setTimeout(function(){r.isInPage(m)?(m.style.opacity="0",I.style.opacity="0",m.style.display="flex",setTimeout(function(){y.classList.add("open"),m.style.opacity="1",I.style.opacity="1"}),a(p,P)):(y.append(m),a(p,x),m.style.opacity="0",I.style.opacity="0")}))},O=function(){var b;r.isOpen(y)&&((b=c.onClose)===null||b===void 0||b.call(c),setTimeout(function(){d.keepSession?m.style.display="none":r.unmountElement(m),y.classList.remove("open"),a(P,p)},250))};r.setAutoClose(v,d.autoClose,O);var k=function(){r.isOpen(m)?O():T()};return j.onclick=k,I.onclick=O,d.open&&!r.isOpen(m)&&r.handleCustomOpen(T,d.open,d.openValue),{open:T,close:O,toggle:k,refresh:S,unmount:function(){r.unmountElement(y)}}}},797:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(528),e),r(i(100),e)},320:function(h,e){var i=this&&this.__assign||function(){return(i=Object.assign||function(a){for(var t,n=1,s=arguments.length;n<s;n++)for(var c in t=arguments[n])Object.prototype.hasOwnProperty.call(t,c)&&(a[c]=t[c]);return a}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.buildNotificationDot=e.canBuildNotificationDot=e.saveNotificationDotHideUntilTime=void 0;var l="tfNotificationData",r=function(){var a=localStorage.getItem(l);return a?JSON.parse(a):{}},o=function(a){a&&localStorage.setItem(l,JSON.stringify(a))};e.saveNotificationDotHideUntilTime=function(a,t){var n,s=new Date;s.setDate(s.getDate()+t),o(i(i({},r()),((n={})[a]={hideUntilTime:s.getTime()},n)))},e.canBuildNotificationDot=function(a){var t=function(n){var s;return((s=r()[n])===null||s===void 0?void 0:s.hideUntilTime)||0}(a);return new Date().getTime()>t&&(t&&function(n){var s=r();delete s[n],o(s)}(a),!0)},e.buildNotificationDot=function(){var a=document.createElement("span");return a.className="tf-v1-popover-unread-dot",a.dataset.testid="tf-v1-popover-unread-dot",a}},100:function(h,e){Object.defineProperty(e,"__esModule",{value:!0})},630:function(h,e,i){var l=this&&this.__rest||function(t,n){var s={};for(var c in t)Object.prototype.hasOwnProperty.call(t,c)&&n.indexOf(c)<0&&(s[c]=t[c]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function"){var f=0;for(c=Object.getOwnPropertySymbols(t);f<c.length;f++)n.indexOf(c[f])<0&&Object.prototype.propertyIsEnumerable.call(t,c[f])&&(s[c[f]]=t[c[f]])}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.createPopup=void 0;var r=i(747),o=i(27),a=i(747);e.createPopup=function(t,n){if(n===void 0&&(n={}),!r.hasDom())return{open:function(){},close:function(){},toggle:function(){},refresh:function(){},unmount:function(){}};var s=n.width,c=n.height,f=n.size,u=f===void 0?o.POPUP_SIZE:f,d=n.onClose,w=l(n,["width","height","size","onClose"]),_=r.createIframe(t,"popup",w),v=_.iframe,S=_.embedId,y=_.refresh,m=document.body.style.overflow,p=function(){var O=document.createElement("div");return O.className="tf-v1-popup",O.style.opacity="0",O}(),x=function(){var O=document.createElement("div");return O.className="tf-v1-spinner",O}(),P=function(O,k,b){var C=document.createElement("div");return C.className="tf-v1-iframe-wrapper",C.style.opacity="0",r.isDefined(O)&&r.isDefined(k)?r.setElementSize(C,{width:O,height:k}):(C.style.width="calc("+b+"% - 80px)",C.style.height="calc("+b+"% - 80px)",C)}(s,c,u);P.append(v),p.append(x),p.append(P);var I=w.container||document.body;v.onload=function(){P.style.opacity="1",setTimeout(function(){x.style.display="none"},250),r.addCustomKeyboardListener(M)};var j=function(){a.isOpen(p)||(a.isInPage(p)?p.style.display="flex":(x.style.display="block",I.append(p)),document.body.style.overflow="hidden",setTimeout(function(){p.style.opacity="1"}))},M=function(){a.isOpen(p)&&(d?.(),p.style.opacity="0",document.body.style.overflow=m,setTimeout(function(){w.keepSession?p.style.display="none":T()},250))};P.append(function(O){var k=document.createElement("a");return k.className="tf-v1-close tf-v1-close-icon",k.innerHTML="&times;",k.onclick=O,k}(M)),r.setAutoClose(S,w.autoClose,M);var T=function(){r.unmountElement(p)};return w.open&&!a.isOpen(p)&&r.handleCustomOpen(j,w.open,w.openValue),{open:j,close:M,toggle:function(){a.isOpen(p)?M():j()},refresh:y,unmount:T}}},970:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(630),e),r(i(394),e)},394:function(h,e){Object.defineProperty(e,"__esModule",{value:!0})},382:function(h,e,i){var l=this&&this.__assign||function(){return(l=Object.assign||function(n){for(var s,c=1,f=arguments.length;c<f;c++)for(var u in s=arguments[c])Object.prototype.hasOwnProperty.call(s,u)&&(n[u]=s[u]);return n}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.createSidetab=void 0;var r=i(747),o={buttonColor:"#3a7685",buttonText:"Launch me"},a=function(n,s){n===void 0&&(n="div"),s===void 0&&(s="tf-v1-sidetab-button-icon");var c=document.createElement(n);return c.className=s+" tf-v1-close-icon",c.innerHTML="&times;",c.dataset.testid=s,c},t=function(n,s){var c=n.parentNode;c&&(c.removeChild(n),c.appendChild(s))};e.createSidetab=function(n,s){s===void 0&&(s={});var c,f,u,d=l(l({},o),s),w=r.createIframe(n,"side-tab",d),_=w.iframe,v=w.embedId,S=w.refresh,y=(c=d.width,f=d.height,(u=document.createElement("div")).className="tf-v1-sidetab",u.dataset.testid="tf-v1-sidetab",r.setElementSize(u,{width:c,height:f})),m=function(){var b=document.createElement("div");return b.className="tf-v1-sidetab-wrapper",b.dataset.testid="tf-v1-sidetab-wrapper",b}(),p=function(){var b=document.createElement("div");b.className="tf-v1-spinner";var C=document.createElement("div");return C.className="tf-v1-sidetab-button-icon",C.dataset.testid="spinner-icon",C.append(b),C}(),x=function(b){var C=r.getTextColor(b),E=document.createElement("button");return E.className="tf-v1-sidetab-button",E.style.backgroundColor=b,E.style.color=C,E}(d.buttonColor||o.buttonColor),P=function(b){var C=document.createElement("span");return C.className="tf-v1-sidetab-button-text",C.innerHTML=b,C}(d.buttonText||o.buttonText),I=function(b,C){var E=r.getTextColor(C),N=document.createElement("div");N.className="tf-v1-sidetab-button-icon";var D=`<svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 0H0V9L10.5743 24V16.5H21C22.6567 16.5 24 15.1567 24 13.5V3C24 1.34325 22.6567 0 21 0ZM7.5 9.75C6.672 9.75 6 9.07875 6 8.25C6 7.42125 6.672 6.75 7.5 6.75C8.328 6.75 9 7.42125 9 8.25C9 9.07875 8.328 9.75 7.5 9.75ZM12.75 9.75C11.922 9.75 11.25 9.07875 11.25 8.25C11.25 7.42125 11.922 6.75 12.75 6.75C13.578 6.75 14.25 7.42125 14.25 8.25C14.25 9.07875 13.578 9.75 12.75 9.75ZM18 9.75C17.172 9.75 16.5 9.07875 16.5 8.25C16.5 7.42125 17.172 6.75 18 6.75C18.828 6.75 19.5 7.42125 19.5 8.25C19.5 9.07875 18.828 9.75 18 9.75Z" fill="`+E+`"></path>
  </svg>`,G=b?.startsWith("http");return N.innerHTML=G?"<img alt='popover trigger icon button' src='"+b+"'/>":b??D,N.dataset.testid="default-icon",N}(d.customIcon,d.buttonColor||o.buttonColor),j=a(),M=a("a","tf-v1-sidetab-close");(d.container||document.body).append(y),m.append(_),y.append(x),y.append(M),x.append(I),x.append(P),setTimeout(function(){y.classList.add("ready")},250),_.onload=function(){y.classList.add("open"),t(p,j),r.addCustomKeyboardListener(O)};var T=function(){r.isOpen(m)||(r.isInPage(m)?(m.style.display="flex",y.classList.add("open"),t(I,j)):(y.append(m),t(I,p)))},O=function(){var b;r.isOpen(m)&&((b=d.onClose)===null||b===void 0||b.call(d),y.classList.remove("open"),setTimeout(function(){d.keepSession?m.style.display="none":r.unmountElement(m),t(j,I)},250))};r.setAutoClose(v,d.autoClose,O);var k=function(){r.isOpen(m)?O():T()};return x.onclick=k,M.onclick=O,d.open&&!r.isOpen(m)&&r.handleCustomOpen(T,d.open,d.openValue),{open:T,close:O,toggle:k,refresh:S,unmount:function(){r.unmountElement(y)}}}},434:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(382),e),r(i(668),e)},668:function(h,e){Object.defineProperty(e,"__esModule",{value:!0})},603:function(h,e,i){var l=this&&this.__rest||function(a,t){var n={};for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&t.indexOf(s)<0&&(n[s]=a[s]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function"){var c=0;for(s=Object.getOwnPropertySymbols(a);c<s.length;c++)t.indexOf(s[c])<0&&Object.prototype.propertyIsEnumerable.call(a,s[c])&&(n[s[c]]=a[s[c]])}return n};Object.defineProperty(e,"__esModule",{value:!0}),e.createSlider=void 0;var r=i(747),o=i(27);e.createSlider=function(a,t){if(t===void 0&&(t={}),!r.hasDom())return{open:function(){},close:function(){},toggle:function(){},refresh:function(){},unmount:function(){}};var n=t.position,s=n===void 0?o.SLIDER_POSITION:n,c=t.width,f=c===void 0?o.SLIDER_WIDTH:c,u=t.onClose,d=l(t,["position","width","onClose"]),w=r.createIframe(a,"slider",d),_=w.iframe,v=w.embedId,S=w.refresh,y=document.body.style.overflow,m=function(T){var O=document.createElement("div");return O.className="tf-v1-slider "+T,O.style.opacity="0",O}(s),p=function(){var T=document.createElement("div");return T.className="tf-v1-spinner",T}(),x=function(T,O){var k=document.createElement("div");return k.className="tf-v1-iframe-wrapper",k.style[T]="-100%",r.setElementSize(k,{width:O})}(s,f);x.append(_),m.append(p),m.append(x);var P=d.container||document.body;_.onload=function(){x.style[s]="0",setTimeout(function(){p.style.display="none"},500),r.addCustomKeyboardListener(j)};var I=function(){r.isOpen(m)||(r.isInPage(m)?(m.style.display="flex",setTimeout(function(){x.style[s]="0"})):(P.append(m),p.style.display="block"),document.body.style.overflow="hidden",setTimeout(function(){m.style.opacity="1"}))},j=function(){r.isOpen(m)&&(u?.(),m.style.opacity="0",x.style[s]="-100%",document.body.style.overflow=y,setTimeout(function(){d.keepSession?m.style.display="none":M()},500))};r.setAutoClose(v,d.autoClose,j);var M=function(){r.unmountElement(m)};return x.append(function(T){var O=document.createElement("a");return O.className="tf-v1-close tf-v1-close-icon",O.innerHTML="&times;",O.onclick=T,O}(j)),d.open&&!r.isOpen(m)&&r.handleCustomOpen(I,d.open,d.openValue),{open:I,close:j,toggle:function(){r.isOpen(m)?j():I()},refresh:S,unmount:M}}},331:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(603),e),r(i(162),e)},162:function(h,e){Object.defineProperty(e,"__esModule",{value:!0})},718:function(h,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.createWidget=void 0;var l=i(747),r=i(554),o=i(313);e.createWidget=function(a,t){if(!l.hasDom())return{refresh:function(){},unmount:function(){}};var n=t;t.inlineOnMobile||!t.forceTouch&&!l.isFullscreen()||(n.enableFullscreen=!0,n.forceTouch=!0);var s=l.createIframe(a,"widget",n),c=s.embedId,f=s.iframe,u=s.refresh,d=o.buildWidget(f,t.width,t.height);if(n.autoResize){var w=typeof n.autoResize=="string"?n.autoResize.split(",").map(function(x){return parseInt(x)}):[],_=w[0],v=w[1];L.addEventListener("message",r.getFormHeightChangedHandler(c,function(x){var P=Math.max(x.height+20,_||0);v&&(P=Math.min(P,v)),t.container.style.height=P+"px"}))}var S,y=function(){return t.container.append(d)};if(t.container.innerHTML="",t.lazy?l.lazyInitialize(t.container,y):y(),n.enableFullscreen){var m=t.container;L.addEventListener("message",r.getWelcomeScreenHiddenHandler(c,m));var p=((S=document.createElement("a")).className="tf-v1-widget-close tf-v1-close-icon",S.innerHTML="&times;",S);p.onclick=function(){var x;if((x=t.onClose)===null||x===void 0||x.call(t),m.classList.remove("tf-v1-widget-fullscreen"),t.keepSession){var P=document.createElement("div");P.className="tf-v1-widget-iframe-overlay",P.onclick=function(){m.classList.add("tf-v1-widget-fullscreen"),l.unmountElement(P)},d.append(P)}else t.container.innerHTML="",y(),m.append(p)},m.append(p)}return{refresh:u,unmount:function(){return l.unmountElement(d)}}}},419:function(h,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.buildWidget=void 0;var l=i(747);e.buildWidget=function(r,o,a){var t=document.createElement("div");return t.className="tf-v1-widget",t.append(r),l.setElementSize(t,{width:o,height:a})}},313:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(419),e)},321:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(718),e),r(i(58),e)},58:function(h,e){Object.defineProperty(e,"__esModule",{value:!0})},920:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(797),e),r(i(970),e),r(i(331),e),r(i(321),e),r(i(434),e)},626:function(h,e,i){var l=this&&this.__assign||function(){return(l=Object.assign||function(s){for(var c,f=1,u=arguments.length;f<u;f++)for(var d in c=arguments[f])Object.prototype.hasOwnProperty.call(c,d)&&(s[d]=c[d]);return s}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.buildIframeSrc=void 0;var r=i(27),o=i(527),a=i(346),t=i(698),n={widget:"embed-widget",popup:"popup-blank",slider:"popup-drawer",popover:"popup-popover","side-tab":"popup-side-panel"};e.buildIframeSrc=function(s){var c=s.formId,f=s.type,u=s.embedId,d=s.options,w=function(y,m,p){var x=p.transitiveSearchParams,P=p.source,I=p.medium,j=p.mediumVersion,M=p.hideFooter,T=p.hideHeaders,O=p.opacity,k=p.disableTracking,b=p.enableSandbox,C=p.disableAutoFocus,E=p.shareGaInstance,N=p.forceTouch,D=p.enableFullscreen,G=p.tracking,ke=p.redirectTarget,Me=t.getTransitiveSearchParams(x);return l(l(l({},{"typeform-embed-id":m,"typeform-embed":n[y],"typeform-source":P,"typeform-medium":I,"typeform-medium-version":j,"embed-hide-footer":M?"true":void 0,"embed-hide-headers":T?"true":void 0,"embed-opacity":O,"disable-tracking":k||b?"true":void 0,"disable-auto-focus":C?"true":void 0,"__dangerous-disable-submissions":b?"true":void 0,"share-ga-instance":E?"true":void 0,"force-touch":N?"true":void 0,"add-placeholder-ws":y==="widget"&&D?"true":void 0,"typeform-embed-redirect-target":ke}),Me),G)}(f,u,function(y){return l(l({},{source:(m=L===null||L===void 0?void 0:L.location)===null||m===void 0?void 0:m.hostname.replace(/^www\./,""),medium:"embed-sdk",mediumVersion:"next"}),o.removeUndefinedKeys(y));var m}(d)),_=function(y,m){m===void 0&&(m=!1);var p=m?"c":"to";return new URL(y.startsWith("http://")||y.startsWith("https://")?y:r.FORM_BASE_URL+"/"+p+"/"+y)}(c,d.chat);if(Object.entries(w).filter(function(y){var m=y[1];return a.isDefined(m)}).forEach(function(y){var m=y[0],p=y[1];_.searchParams.set(m,p)}),d.hidden){var v=new URL(r.FORM_BASE_URL);Object.entries(d.hidden).filter(function(y){var m=y[1];return a.isDefined(m)}).forEach(function(y){var m=y[0],p=y[1];v.searchParams.set(m,p)});var S=v.searchParams.toString();S&&(_.hash=S)}return _.href}},972:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.handleCustomOpen=void 0,e.handleCustomOpen=function(i,l,r){switch(l){case"load":i();break;case"exit":r&&function(o,a){var t=0,n=function(s){s.clientY<o&&s.clientY<t?(document.removeEventListener("mousemove",n),a()):t=s.clientY};document.addEventListener("mousemove",n)}(r,i);break;case"time":setTimeout(function(){i()},r);break;case"scroll":r&&function(o,a){var t=function(){var n=L.pageYOffset||document.documentElement.scrollTop,s=document.documentElement.clientTop||0,c=document.documentElement.scrollHeight,f=n-s,u=f/c*100,d=f+L.innerHeight>=c;(u>=o||d)&&(a(),document.removeEventListener("scroll",t))};document.addEventListener("scroll",t)}(r,i)}}},553:function(h,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.createIframe=void 0;var l=i(626),r=i(747),o=i(866),a=i(554),t=i(256),n=i(144),s=i(511);e.createIframe=function(c,f,u){var d=o.generateEmbedId(),w=u.iframeProps,_=w===void 0?{}:w,v=u.onReady,S=u.onQuestionChanged,y=u.onHeightChanged,m=u.onSubmit,p=u.onEndingButtonClick,x=u.shareGaInstance,P=l.buildIframeSrc({formId:c,embedId:d,type:f,options:u}),I=document.createElement("iframe");return I.src=P,I.dataset.testid="iframe",I.style.border="0px",I.allow="microphone; camera",Object.keys(_).forEach(function(j){I.setAttribute(j,_[j])}),I.addEventListener("load",t.triggerIframeRedraw,{once:!0}),L.addEventListener("message",a.getFormReadyHandler(d,v)),L.addEventListener("message",a.getFormQuestionChangedHandler(d,S)),L.addEventListener("message",a.getFormHeightChangedHandler(d,y)),L.addEventListener("message",a.getFormSubmitHandler(d,m)),L.addEventListener("message",a.getFormThemeHandler(d)),L.addEventListener("message",a.getThankYouScreenButtonClickHandler(d,p)),f!=="widget"&&L.addEventListener("message",n.dispatchCustomKeyEventFromIframe),x&&L.addEventListener("message",a.getFormReadyHandler(d,function(){r.setupGaInstance(I,d,x)})),{iframe:I,embedId:d,refresh:function(){return s.refreshIframe(I)}}}},866:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.generateEmbedId=void 0,e.generateEmbedId=function(){var i=Math.random();return String(i).split(".")[1]}},554:function(h,e){var i=this&&this.__rest||function(r,o){var a={};for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&o.indexOf(t)<0&&(a[t]=r[t]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function"){var n=0;for(t=Object.getOwnPropertySymbols(r);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(r,t[n])&&(a[t[n]]=r[t[n]])}return a};function l(r,o,a){return function(t){var n=t.data,s=n.type,c=n.embedId,f=i(n,["type","embedId"]);s===r&&c===o&&a?.(f)}}Object.defineProperty(e,"__esModule",{value:!0}),e.getThankYouScreenButtonClickHandler=e.getFormThemeHandler=e.getWelcomeScreenHiddenHandler=e.getFormSubmitHandler=e.getFormHeightChangedHandler=e.getFormQuestionChangedHandler=e.getFormReadyHandler=void 0,e.getFormReadyHandler=function(r,o){return l("form-ready",r,o)},e.getFormQuestionChangedHandler=function(r,o){return l("form-screen-changed",r,o)},e.getFormHeightChangedHandler=function(r,o){return l("form-height-changed",r,o)},e.getFormSubmitHandler=function(r,o){return l("form-submit",r,o)},e.getWelcomeScreenHiddenHandler=function(r,o){return l("welcome-screen-hidden",r,function(){o.classList.add("tf-v1-widget-fullscreen")})},e.getFormThemeHandler=function(r){return l("form-theme",r,function(o){var a;if(o?.theme){var t=document.querySelector(".tf-v1-close-icon");t&&(t.style.color=(a=o.theme)===null||a===void 0?void 0:a.color)}})},e.getThankYouScreenButtonClickHandler=function(r,o){return l("thank-you-screen-button-click",r,o)}},339:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(553),e),r(i(144),e)},511:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.refreshIframe=void 0,e.refreshIframe=function(i){if(i){var l=i.src;if(l.includes("&refresh")){var r=l.split("&refresh#");i.src=r.join("#")}else(r=l.split("#"))[0]=r[0]+"&refresh",i.src=r.join("#")}}},144:function(h,e){var i=this&&this.__awaiter||function(a,t,n,s){return new(n||(n=Promise))(function(c,f){function u(_){try{w(s.next(_))}catch(v){f(v)}}function d(_){try{w(s.throw(_))}catch(v){f(v)}}function w(_){var v;_.done?c(_.value):(v=_.value,v instanceof n?v:new n(function(S){S(v)})).then(u,d)}w((s=s.apply(a,t||[])).next())})},l=this&&this.__generator||function(a,t){var n,s,c,f,u={label:0,sent:function(){if(1&c[0])throw c[1];return c[1]},trys:[],ops:[]};return f={next:d(0),throw:d(1),return:d(2)},typeof Symbol=="function"&&(f[Symbol.iterator]=function(){return this}),f;function d(w){return function(_){return function(v){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,s&&(c=2&v[0]?s.return:v[0]?s.throw||((c=s.return)&&c.call(s),0):s.next)&&!(c=c.call(s,v[1])).done)return c;switch(s=0,c&&(v=[2&v[0],c.value]),v[0]){case 0:case 1:c=v;break;case 4:return u.label++,{value:v[1],done:!1};case 5:u.label++,s=v[1],v=[0];continue;case 7:v=u.ops.pop(),u.trys.pop();continue;default:if(!((c=(c=u.trys).length>0&&c[c.length-1])||v[0]!==6&&v[0]!==2)){u=0;continue}if(v[0]===3&&(!c||v[1]>c[0]&&v[1]<c[3])){u.label=v[1];break}if(v[0]===6&&u.label<c[1]){u.label=c[1],c=v;break}if(c&&u.label<c[2]){u.label=c[2],u.ops.push(v);break}c[2]&&u.ops.pop(),u.trys.pop();continue}v=t.call(a,u)}catch(S){v=[6,S],s=0}finally{n=c=0}if(5&v[0])throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}([w,_])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.dispatchCustomKeyEventFromIframe=e.removeCustomKeyboardListener=e.addCustomKeyboardListener=void 0;var r="Escape",o=function(a,t){return i(void 0,void 0,void 0,function(){return l(this,function(n){return a.code===r&&typeof t=="function"&&(t(),e.removeCustomKeyboardListener()),[2]})})};e.addCustomKeyboardListener=function(a){return L.document.addEventListener("keydown",function(t){return o(t,a)})},e.removeCustomKeyboardListener=function(){return L.document.removeEventListener("keydown",o)},e.dispatchCustomKeyEventFromIframe=function(a){a.data.type==="form-close"&&L.document.dispatchEvent(new KeyboardEvent("keydown",{code:r}))}},256:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.triggerIframeRedraw=void 0,e.triggerIframeRedraw=function(){this.style.transform="translateZ(0)"}},939:function(h,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.getTextColor=void 0;var l=i(938);e.getTextColor=function(r){if(!r)return"#FFFFFF";var o=r.startsWith("#")?l.hexRgb(r):function(s){var c={red:0,green:0,blue:0},f=s.match(/\d+/g);return f&&(c.red=parseInt(f[0],10),c.green=parseInt(f[0],10),c.blue=parseInt(f[0],10)),c}(r),a=o.red,t=o.green,n=o.blue;return Math.round((299*a+587*t+114*n)/1e3)>125?"#000000":"#FFFFFF"}},698:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.getTransitiveSearchParams=void 0,e.getTransitiveSearchParams=function(i){var l=new URL(L.location.href),r={};return i&&i.length>0&&i.forEach(function(o){l.searchParams.has(o)&&(r[o]=l.searchParams.get(o))}),r}},252:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.hasDom=void 0,e.hasDom=function(){return typeof document<"u"&&typeof L<"u"}},938:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.hexRgb=void 0;var i=new RegExp("[^#a-f\\d]","gi"),l=new RegExp("^#?[a-f\\d]{3}[a-f\\d]?$|^#?[a-f\\d]{6}([a-f\\d]{2})?$","i");e.hexRgb=function(r){if(typeof r!="string"||i.test(r)||!l.test(r))throw new TypeError("Expected a valid hex string");(r=r.replace(/^#/,"")).length===8&&(r=r.slice(0,6)),r.length===4&&(r=r.slice(0,3)),r.length===3&&(r=r[0]+r[0]+r[1]+r[1]+r[2]+r[2]);var o=Number.parseInt(r,16);return{red:o>>16,green:o>>8&255,blue:255&o}}},71:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.includeCss=void 0,e.includeCss=function(i){var l=function(o){return"https://embed.typeform.com/next/css/"+o}(i);if(!document.querySelector('link[href="'+l+'"]')){var r=document.createElement("link");r.rel="stylesheet",r.href=l,document.head.append(r)}}},747:function(h,e,i){var l=this&&this.__createBinding||(Object.create?function(o,a,t,n){n===void 0&&(n=t),Object.defineProperty(o,n,{enumerable:!0,get:function(){return a[t]}})}:function(o,a,t,n){n===void 0&&(n=t),o[n]=a[t]}),r=this&&this.__exportStar||function(o,a){for(var t in o)t==="default"||Object.prototype.hasOwnProperty.call(a,t)||l(a,o,t)};Object.defineProperty(e,"__esModule",{value:!0}),r(i(626),e),r(i(339),e),r(i(252),e),r(i(71),e),r(i(346),e),r(i(377),e),r(i(563),e),r(i(527),e),r(i(533),e),r(i(451),e),r(i(972),e),r(i(748),e),r(i(392),e),r(i(939),e),r(i(917),e),r(i(987),e)},346:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.isDefined=void 0,e.isDefined=function(i){return i!=null}},987:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.isVisible=e.isInPage=e.isOpen=void 0,e.isOpen=function(i){return e.isInPage(i)&&e.isVisible(i)},e.isInPage=function(i){return!!i.parentNode},e.isVisible=function(i){return i.style.display!=="none"}},917:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.lazyInitialize=void 0,e.lazyInitialize=function(i,l){var r=new IntersectionObserver(function(o){o.forEach(function(a){a.isIntersecting&&(l(),r.unobserve(a.target))})});r.observe(i)}},377:function(h,e){var i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var n,s=1,c=arguments.length;s<c;s++)for(var f in n=arguments[s])Object.prototype.hasOwnProperty.call(n,f)&&(t[f]=n[f]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.loadOptionsFromAttributes=e.transformAttributeValue=e.camelCaseToKebabCase=void 0,e.camelCaseToKebabCase=function(t){return t.split("").map(function(n,s){return n.toUpperCase()===n?(s!==0?"-":"")+n.toLowerCase():n}).join("")};var l=function(t){return t||void 0},r=function(t){return t===""||t==="yes"||t==="true"},o=function(t){var n=t?parseInt(t,10):NaN;return isNaN(n)?void 0:n},a="%ESCAPED_COMMA%";e.transformAttributeValue=function(t,n){var s,c;switch(n){case"string":return l(t);case"boolean":return r(t);case"integer":return o(t);case"function":return function(f){var u=f&&f in L?L[f]:void 0;return typeof u=="function"?u:void 0}(t);case"array":return function(f){if(f)return f.replace(/\s/g,"").replace(/\\,/g,a).split(",").filter(function(u){return!!u}).map(function(u){return u.replace(a,",")})}(t);case"record":return function(f){if(f)return f.replace(/\\,/g,a).split(",").filter(function(u){return!!u}).map(function(u){return u.replace(a,",")}).reduce(function(u,d){var w,_=d.match(/^([^=]+)=(.*)$/);if(_){var v=_[1],S=_[2];return i(i({},u),((w={})[v.trim()]=S,w))}return u},{})}(t);case"integerOrBoolean":return(s=o(t))!==null&&s!==void 0?s:r(t);case"stringOrBoolean":return(c=l(t))!==null&&c!==void 0?c:r(t);default:throw new Error("Invalid attribute transformation "+n)}},e.loadOptionsFromAttributes=function(t,n){return Object.keys(n).reduce(function(s,c){var f;return i(i({},s),((f={})[c]=e.transformAttributeValue(t.getAttribute("data-tf-"+e.camelCaseToKebabCase(c)),n[c]),f))},{})}},563:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.isFullscreen=e.isMobile=e.isBigScreen=void 0,e.isBigScreen=function(){return L.screen.width>=1024&&L.screen.height>=768},e.isMobile=function(){return/mobile|tablet|android/i.test(U.userAgent.toLowerCase())},e.isFullscreen=function(){return e.isMobile()&&!e.isBigScreen()}},527:function(h,e,i){var l=this&&this.__assign||function(){return(l=Object.assign||function(o){for(var a,t=1,n=arguments.length;t<n;t++)for(var s in a=arguments[t])Object.prototype.hasOwnProperty.call(a,s)&&(o[s]=a[s]);return o}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.removeUndefinedKeys=void 0;var r=i(346);e.removeUndefinedKeys=function(o){return Object.entries(o).filter(function(a){var t=a[1];return r.isDefined(t)}).reduce(function(a,t){var n,s=t[0],c=t[1];return l(l({},a),((n={})[s]=c,n))},{})}},748:function(h,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.setAutoClose=void 0;var l=i(554);e.setAutoClose=function(r,o,a){if(o&&a){var t=typeof o=="number"?o:0;L.addEventListener("message",l.getFormSubmitHandler(r,function(){return setTimeout(a,t)}))}}},533:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.setElementSize=void 0,e.setElementSize=function(i,l){var r=l.width,o=l.height;return r&&(i.style.width=r+"px"),o&&(i.style.height=o+"px"),i}},392:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.setupGaInstance=e.sendGaIdMessage=void 0,e.sendGaIdMessage=function(l,r,o){var a={embedId:l,gaClientId:r};setTimeout(function(){o&&o.contentWindow&&o.contentWindow.postMessage({type:"ga-client-id",data:a},"*")},0)};var i=function(l){console.error(l)};e.setupGaInstance=function(l,r,o){try{var a=L[L.GoogleAnalyticsObject],t=typeof o=="string"?o:void 0,n=function(s,c){return c?s.find(function(f){return f.get("trackingId")===c}):s[0]}(a.getAll(),t);n?e.sendGaIdMessage(r,n.get("clientId"),l):i("Whoops! You enabled the shareGaInstance feature in your typeform embed but the tracker with ID "+t+" was not found. Make sure to include Google Analytics Javascript code before the Typeform Embed Javascript code in your page and use correct tracker ID. ")}catch(s){i("Whoops! You enabled the shareGaInstance feature in your typeform embed but the Google Analytics object has not been found. Make sure to include Google Analytics Javascript code before the Typeform Embed Javascript code in your page. "),i(s)}}},451:function(h,e){Object.defineProperty(e,"__esModule",{value:!0}),e.unmountElement=void 0,e.unmountElement=function(i){var l;(l=i.parentNode)===null||l===void 0||l.removeChild(i)}}},R={};return function h(e){if(R[e])return R[e].exports;var i=R[e]={exports:{}};return H[e].call(i.exports,i,i.exports,h),i.exports}(920)}()})});var Ge=X.createPopover,qe=X.createPopup,Ye=X.createSidetab,Je=X.createSlider,Ie=X.createWidget;var ce={...te,...we,textAlign:"center",padding:15,width:200,height:100,overflow:"hidden"},ot={...ce,color:"#09f",background:"rgb(0, 153, 255, 0.1)",borderColor:"#09f"},Ee={fontSize:12,fontWeight:600,margin:0},Pe={fontSize:12,maxWidth:200,lineHeight:1.4,margin:"5px 0 0 0"};var Re=$(function({id:A,style:H,onSubmit:R,hideFooter:h,hideHeaders:e,...i}){let l=Q(null);return fe(()=>{if(l.current){let r=Ie(A,{width:"100%",height:"100%",container:l.current,transitiveSearchParams:["utm_source","utm_medium","utm_campaign"],onSubmit:R,hideFooter:h,hideHeaders:e});return()=>{r.unmount()}}},[A,R,h,e]),g("div",{style:H,className:"framer-typeform",ref:l,...i})},[".framer-typeform div, .framer-typeform iframe { width: 100%; height: 100%; border: none !important; }"]);function K({formId:z,style:A,onSubmit:H,hideFooter:R,hideHeaders:h,...e}){function i(l){H!==void 0&&H(l),globalThis.__send_framer_conversion__&&globalThis.__send_framer_conversion__("typeform")}return z?g(F.div,{style:{...te,...A},...e,children:g(Re,{id:z,style:{width:"100%",height:"100%"},onSubmit:i,hideFooter:R,hideHeaders:h})}):B(F.div,{style:{...ce,...A},...e,children:[g("h1",{style:Ee,children:"Typeform"}),g("p",{style:Pe,children:"Set a form ID in the Properties."})]})}pe(K,{formId:{title:"ID",type:q.String,placeholder:"12ABCD34",defaultValue:"R2s5BM",description:"Create a [Typeform](https://www.typeform.com/) account, add a form and copy its ID. [Learn more\u2026](https://www.framer.com/sites/integrations/typeform/)"},hideFooter:{title:"Footer",type:q.Boolean,enabledTitle:"Hide",disabledTitle:"Show",defaultValue:!1},hideHeaders:{title:"Headers",type:q.Boolean,enabledTitle:"Hide",disabledTitle:"Show",defaultValue:!1},onSubmit:{type:q.EventHandler}});var ze=J(ie),De=J(oe),Ae=J(K),Be=J(se);var je={frcFRkhPR:"(min-width: 810px) and (max-width: 1279px)",HOZChxxat:"(max-width: 809px)",n6_stw_Hi:"(min-width: 1280px)"},le=()=>typeof document<"u",Te={frcFRkhPR:"framer-v-6g857d",HOZChxxat:"framer-v-183mh6c",n6_stw_Hi:"framer-v-roxc3z"};le()&&be("n6_stw_Hi",je,Te);var Ve={Desktop:"n6_stw_Hi",Phone:"HOZChxxat",Tablet:"frcFRkhPR"},We={default:{duration:0}},Se="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";function Ue(){let z=Q(null);return z.current===null&&(z.current=Array(5).fill(0).map(()=>Se[Math.floor(Math.random()*Se.length)]).join("")),z.current}var Ze=de(function({id:z,style:A={},className:H,width:R,height:h,layoutId:e,variant:i="n6_stw_Hi",...l},r){let a=Ve[i]||i;ue(()=>{let p=Ce();if(document.title=p.title||"",p.viewport){var x;(x=document.querySelector('meta[name="viewport"]'))===null||x===void 0||x.setAttribute("content",p.viewport)}},[]);let[t,n]=ye(a,je,!1),s=void 0,c=We.default,{activeVariantCallback:f,delay:u}=ge(void 0),d=f(async(...p)=>{L.open("https://popless.com","_blank","noreferrer noopener")}),w=f(async(...p)=>{L.open("https://twitter.com/popless_hq","_blank","noreferrer noopener")}),_=()=>t==="HOZChxxat"?!le():!0,v=()=>t==="HOZChxxat"?!0:!le(),S=Ue(),{pointerEvents:y,...m}=A;return g(he.Provider,{value:{primaryVariantId:"n6_stw_Hi",variantClassNames:Te},children:g(me,{id:e??S,children:B(F.div,{"data-framer-generated":!0,className:ne("framer-hsemL",Le),style:{display:"contents",pointerEvents:y??void 0},children:[g(F.div,{...l,className:ne("framer-roxc3z",H),ref:r,style:{...m},children:B(F.main,{className:"framer-gc2xgh","data-framer-name":"Main",name:"Main",children:[g(Z,{className:"framer-1myr2tr-container",children:g(Y,{breakpoint:t,overrides:{frcFRkhPR:{variant:"aZMkidfTG"},HOZChxxat:{variant:"aZMkidfTG"}},children:g(ie,{height:"100%",id:"bSsIh2z2V",layoutId:"bSsIh2z2V",style:{width:"100%"},variant:"Dg2JgAbsI",width:"100%"})})}),B(F.header,{className:"framer-uazonv","data-framer-name":"Stack",name:"Stack",children:[g(Y,{breakpoint:t,overrides:{frcFRkhPR:{children:g(W,{children:g("h1",{style:{"--framer-line-height":"60px","--framer-text-alignment":"center"},children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNzAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-size":"58px","--framer-font-style":"normal","--framer-font-weight":"700","--framer-letter-spacing":"-2px","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Get started."})})})},HOZChxxat:{children:g(W,{children:g("h1",{style:{"--framer-text-alignment":"center"},children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNzAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-size":"41px","--framer-font-style":"normal","--framer-font-weight":"700","--framer-letter-spacing":"-2px","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Get started."})})})}},children:g(ee,{__fromCanvasComponent:!0,children:g(W,{children:g("h1",{style:{"--framer-line-height":"60px","--framer-text-alignment":"center"},children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNzAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-size":"60px","--framer-font-style":"normal","--framer-font-weight":"700","--framer-letter-spacing":"-2px","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Get started."})})}),className:"framer-og4any",fonts:["GF;Inter-700"],verticalAlignment:"top",withExternalLayout:!0})}),g(Y,{breakpoint:t,overrides:{HOZChxxat:{children:g(W,{children:g("h1",{style:{"--framer-line-height":"56px","--framer-text-alignment":"center"},children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNzAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-size":"39px","--framer-font-style":"normal","--framer-font-weight":"700","--framer-letter-spacing":"-0.9px","--framer-text-color":"var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"},children:"It takes 20 seconds."})})})}},children:g(ee,{__fromCanvasComponent:!0,children:g(W,{children:g("h1",{style:{"--framer-line-height":"56px","--framer-text-alignment":"center"},children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNzAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-size":"60px","--framer-font-style":"normal","--framer-font-weight":"700","--framer-letter-spacing":"-2px","--framer-text-color":"var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"},children:"It takes 20 seconds."})})}),className:"framer-1fgdjgm",fonts:["GF;Inter-700"],verticalAlignment:"top",withExternalLayout:!0})}),g(Z,{className:"framer-gftnkb-container",children:g(oe,{appId:"lsvujawt",height:"100%",id:"n8V9Lrkx0",layoutId:"n8V9Lrkx0",style:{height:"100%",width:"100%"},width:"100%"})})]}),B(F.section,{className:"framer-1nd8bih",children:[_()&&g(ae,{background:{alt:"",fit:"fill",intrinsicHeight:479,intrinsicWidth:832,pixelHeight:479,pixelWidth:832,src:new URL("https://framerusercontent.com/images/ZRyYZtThcnxvGZQSqDFTZNvtwjE.png").href},className:"framer-113s7uk hidden-183mh6c",children:g(F.div,{className:"framer-71j67q",children:g(F.div,{className:"framer-3yh45l",transformTemplate:(p,x)=>`translateX(-50%) ${x}`,children:g(Z,{className:"framer-1dkviub-container",children:g(K,{formId:"chvbrCM3",height:"100%",hideFooter:!1,hideHeaders:!1,id:"I4VPt3Cwm",layoutId:"I4VPt3Cwm",style:{height:"100%",width:"100%"},width:"100%"})})})})}),v()&&g(ae,{background:{alt:"",fit:"fill",intrinsicHeight:510,intrinsicWidth:440,pixelHeight:510,pixelWidth:440,src:new URL("https://framerusercontent.com/images/BoBENhrUUU9OHt58etj4KxLzto.png").href},className:"framer-67xgze hidden-roxc3z hidden-6g857d",children:g(F.div,{className:"framer-16755m6",children:g(F.div,{className:"framer-yqvsky",transformTemplate:(p,x)=>`translate(-50%, -50%) ${x}`,children:g(Z,{className:"framer-2wjeq-container",children:g(K,{formId:"chvbrCM3",height:"100%",hideFooter:!0,hideHeaders:!0,id:"m6VLCC5oc",layoutId:"m6VLCC5oc",style:{height:"100%",width:"100%"},width:"100%"})})})})})]}),g(F.section,{className:"framer-1b5l3up",children:g(ee,{__fromCanvasComponent:!0,children:g(W,{children:B("p",{style:{"--framer-line-height":"30px","--framer-text-alignment":"center"},children:[g("span",{style:{"--font-selector":"R0Y7SW50ZXItNTAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":"500","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Form not loading? "}),g(ve,{href:"https://popless.typeform.com/request-access",openInNewTab:!0,smoothScroll:!1,children:g("a",{className:"framer-styles-preset-1utq9v6","data-styles-preset":"COkUUCscg",href:"https://popless.typeform.com/request-access",rel:"noreferrer noopener",target:"_blank",children:g("span",{style:{"--font-selector":"R0Y7SW50ZXItNTAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":"500","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"Click here"})})}),g("span",{style:{"--font-selector":"R0Y7SW50ZXItNTAw","--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":"500","--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"},children:"."})]})}),className:"framer-19ya4uo",fonts:["GF;Inter-500"],verticalAlignment:"top",withExternalLayout:!0})}),g(Z,{className:"framer-1vi4wrj-container",children:g(Y,{breakpoint:t,overrides:{HOZChxxat:{variant:"Zz_9kWOfb"}},children:g(se,{height:"100%",id:"yLSqPWe2r",layoutId:"yLSqPWe2r",poplessLink:d,style:{width:"100%"},twitterLink:w,variant:"zyTRmFlly",width:"100%"})})})]})}),g("div",{id:"overlay"})]})})})}),Ke=['.framer-hsemL [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }',"@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }",".framer-hsemL .framer-hhr2pe { display: block; }",".framer-hsemL .framer-roxc3z { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }",".framer-hsemL .framer-gc2xgh { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }",".framer-hsemL .framer-1myr2tr-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }",".framer-hsemL .framer-uazonv { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 2px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 150px 120px 11px 120px; position: relative; width: 1200px; }",".framer-hsemL .framer-og4any, .framer-hsemL .framer-1fgdjgm { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 780px; word-break: break-word; word-wrap: break-word; }",".framer-hsemL .framer-gftnkb-container { flex: none; height: 1px; position: relative; width: 1px; }",".framer-hsemL .framer-1nd8bih { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: visible; padding: 28px 0px 73px 0px; position: relative; width: 1200px; }",".framer-hsemL .framer-113s7uk { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 479px; justify-content: flex-start; overflow: visible; padding: 48px 48px 48px 48px; position: relative; width: 69%; }",".framer-hsemL .framer-71j67q { flex: none; height: 490px; overflow: visible; position: relative; width: 760px; }",".framer-hsemL .framer-3yh45l { align-content: center; align-items: center; background-color: #ffffff; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; border-top-left-radius: 24px; border-top-right-radius: 24px; bottom: -40px; box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; left: 50%; overflow: visible; padding: 20px 20px 20px 20px; position: absolute; transform: translateX(-50%); width: min-content; }",".framer-hsemL .framer-1dkviub-container { flex: none; height: 450px; position: relative; width: 720px; }",".framer-hsemL .framer-67xgze { align-content: center; align-items: center; aspect-ratio: 0.8706624605678234 / 1; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 130%; justify-content: flex-start; overflow: visible; padding: 48px 48px 48px 48px; position: relative; width: var(--framer-aspect-ratio-supported, 174px); }",".framer-hsemL .framer-16755m6 { flex: none; height: 385px; overflow: visible; position: relative; width: 100%; }",".framer-hsemL .framer-yqvsky { align-content: center; align-items: center; background-color: #ffffff; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; border-top-left-radius: 24px; border-top-right-radius: 24px; box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; left: 50%; overflow: visible; padding: 20px 20px 20px 20px; position: absolute; top: 57%; transform: translate(-50%, -50%); width: min-content; }",".framer-hsemL .framer-2wjeq-container { flex: none; height: 400px; position: relative; width: 300px; }",".framer-hsemL .framer-1b5l3up { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 150px 0px; position: relative; width: 1200px; }",".framer-hsemL .framer-19ya4uo { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 700px; word-break: break-word; word-wrap: break-word; }",".framer-hsemL .framer-1vi4wrj-container { flex: none; height: auto; position: relative; width: 100%; }","@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hsemL .framer-roxc3z, .framer-hsemL .framer-gc2xgh, .framer-hsemL .framer-uazonv, .framer-hsemL .framer-1nd8bih, .framer-hsemL .framer-113s7uk, .framer-hsemL .framer-3yh45l, .framer-hsemL .framer-67xgze, .framer-hsemL .framer-yqvsky, .framer-hsemL .framer-1b5l3up { gap: 0px; } .framer-hsemL .framer-roxc3z > *, .framer-hsemL .framer-3yh45l > *, .framer-hsemL .framer-67xgze > *, .framer-hsemL .framer-yqvsky > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-hsemL .framer-roxc3z > :first-child, .framer-hsemL .framer-gc2xgh > :first-child, .framer-hsemL .framer-uazonv > :first-child, .framer-hsemL .framer-1nd8bih > :first-child, .framer-hsemL .framer-3yh45l > :first-child, .framer-hsemL .framer-67xgze > :first-child, .framer-hsemL .framer-yqvsky > :first-child, .framer-hsemL .framer-1b5l3up > :first-child { margin-top: 0px; } .framer-hsemL .framer-roxc3z > :last-child, .framer-hsemL .framer-gc2xgh > :last-child, .framer-hsemL .framer-uazonv > :last-child, .framer-hsemL .framer-1nd8bih > :last-child, .framer-hsemL .framer-3yh45l > :last-child, .framer-hsemL .framer-67xgze > :last-child, .framer-hsemL .framer-yqvsky > :last-child, .framer-hsemL .framer-1b5l3up > :last-child { margin-bottom: 0px; } .framer-hsemL .framer-gc2xgh > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-hsemL .framer-uazonv > * { margin: 0px; margin-bottom: calc(2px / 2); margin-top: calc(2px / 2); } .framer-hsemL .framer-1nd8bih > *, .framer-hsemL .framer-1b5l3up > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-hsemL .framer-113s7uk > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-hsemL .framer-113s7uk > :first-child { margin-left: 0px; } .framer-hsemL .framer-113s7uk > :last-child { margin-right: 0px; } }","@media (min-width: 1280px) { .framer-hsemL .hidden-roxc3z { display: none !important; } }","@media (min-width: 810px) and (max-width: 1279px) { .framer-hsemL .hidden-6g857d { display: none !important; } .framer-hsemL .framer-roxc3z { width: 810px; } .framer-hsemL .framer-uazonv { padding: 150px 24px 11px 24px; width: 100%; } .framer-hsemL .framer-og4any, .framer-hsemL .framer-1b5l3up, .framer-hsemL .framer-19ya4uo { width: 100%; } .framer-hsemL .framer-1nd8bih { justify-content: center; padding: 28px 24px 142px 24px; width: 100%; } .framer-hsemL .framer-113s7uk { height: 439px; width: 762px; } .framer-hsemL .framer-71j67q { width: 666px; } .framer-hsemL .framer-3yh45l { bottom: unset; height: 490px; justify-content: center; top: 61px; } .framer-hsemL .framer-1dkviub-container { width: 650px; }}","@media (max-width: 809px) { .framer-hsemL .hidden-183mh6c { display: none !important; } .framer-hsemL .framer-roxc3z { width: 390px; } .framer-hsemL .framer-1myr2tr-container { order: 0; } .framer-hsemL .framer-uazonv { gap: 0px; order: 1; padding: 150px 24px 22px 24px; width: 100%; } .framer-hsemL .framer-og4any, .framer-hsemL .framer-19ya4uo { width: 100%; } .framer-hsemL .framer-1nd8bih { gap: 69px; height: 567px; order: 2; padding: 25px 0px 200px 0px; width: 100%; } .framer-hsemL .framer-67xgze { width: var(--framer-aspect-ratio-supported, 388px); } .framer-hsemL .framer-1b5l3up { order: 3; padding: 0px 0px 100px 0px; width: 100%; } .framer-hsemL .framer-1vi4wrj-container { order: 4; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hsemL .framer-uazonv, .framer-hsemL .framer-1nd8bih { gap: 0px; } .framer-hsemL .framer-uazonv > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-hsemL .framer-uazonv > :first-child, .framer-hsemL .framer-1nd8bih > :first-child { margin-top: 0px; } .framer-hsemL .framer-uazonv > :last-child, .framer-hsemL .framer-1nd8bih > :last-child { margin-bottom: 0px; } .framer-hsemL .framer-1nd8bih > * { margin: 0px; margin-bottom: calc(69px / 2); margin-top: calc(69px / 2); } }}",...Oe],re=$(Ze,Ke),Et=re;re.displayName="Request Access";re.defaultProps={height:1441,width:1280};xe(re,[{family:"Inter",moduleAsset:{localModuleIdentifier:"local-module:screen/gMU7brHGJ:default",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"},style:"normal",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",weight:"700"},{family:"Inter",moduleAsset:{localModuleIdentifier:"local-module:screen/gMU7brHGJ:default",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"},style:"normal",url:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",weight:"500"},...ze,...De,...Ae,...Be,..._e]);var Pt={exports:{Props:{type:"tsType",annotations:{framerContractVersion:"1"}},default:{type:"reactComponent",name:"FramergMU7brHGJ",slots:[],annotations:{framerIntrinsicWidth:"1280",framerCanvasComponentVariantDetails:'{"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"frcFRkhPR":{"layout":["fixed","auto"]},"HOZChxxat":{"layout":["fixed","auto"]}}}',framerResponsiveScreen:"",framerIntrinsicHeight:"1441",framerContractVersion:"1"}},__FramerMetadata__:{type:"variable"}}};export{Pt as __FramerMetadata__,Et as default};
//# sourceMappingURL=hRV3VE1uG30SbVdIGupbDiZz_HPlaHkuOs-LJpcfh-U.XQMEZ7AM.mjs.map
