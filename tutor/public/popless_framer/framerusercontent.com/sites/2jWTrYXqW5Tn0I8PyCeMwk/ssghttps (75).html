var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
function createCommonjsModule(fn, basedir, module) {
  return module = {
    path: basedir,
    exports: {},
    require: function(path, base) {
      return commonjsRequire(path, base === void 0 || base === null ? module.path : base);
    }
  }, fn(module, module.exports), module.exports;
}
function commonjsRequire() {
  throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs");
}
var build = createCommonjsModule(function(module, exports) {
  !function(e, t) {
    module.exports = t();
  }(commonjsGlobal, function() {
    return function() {
      var e = {27: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.FORM_BASE_URL = t2.POPUP_SIZE = t2.SLIDER_WIDTH = t2.SLIDER_POSITION = t2.SIDETAB_ATTRIBUTE = t2.WIDGET_ATTRIBUTE = t2.SLIDER_ATTRIBUTE = t2.POPUP_ATTRIBUTE = t2.POPOVER_ATTRIBUTE = void 0, t2.POPOVER_ATTRIBUTE = "data-tf-popover", t2.POPUP_ATTRIBUTE = "data-tf-popup", t2.SLIDER_ATTRIBUTE = "data-tf-slider", t2.WIDGET_ATTRIBUTE = "data-tf-widget", t2.SIDETAB_ATTRIBUTE = "data-tf-sidetab", t2.SLIDER_POSITION = "right", t2.SLIDER_WIDTH = 800, t2.POPUP_SIZE = 100, t2.FORM_BASE_URL = "https://form.typeform.com";
      }, 528: function(e2, t2, n) {
        var o = this && this.__assign || function() {
          return (o = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createPopover = void 0;
        var r = n(747), i = n(320), a = function(e3, t3) {
          var n2 = e3.parentNode;
          n2 && (n2.removeChild(e3), n2.appendChild(t3));
        }, c = function(e3, t3) {
          e3 === void 0 && (e3 = "div"), t3 === void 0 && (t3 = "tf-v1-popover-button-icon");
          var n2 = document.createElement(e3);
          return n2.className = t3 + " tf-v1-close-icon", n2.innerHTML = "&times;", n2.dataset.testid = t3, n2;
        }, u = {buttonColor: "#3a7685"};
        t2.createPopover = function(e3, t3) {
          t3 === void 0 && (t3 = {});
          var n2, s, d = o(o({}, u), t3), l = r.createIframe(e3, "popover", d), f = l.iframe, p = l.embedId, v = l.refresh, m = function(e4, t4) {
            var n3 = document.createElement("div");
            return n3.className = "tf-v1-popover", n3.dataset.testid = "tf-v1-popover", r.setElementSize(n3, {width: e4, height: t4});
          }(d.width, d.height), h = function() {
            var e4 = document.createElement("div");
            return e4.className = "tf-v1-popover-wrapper", e4.dataset.testid = "tf-v1-popover-wrapper", e4;
          }(), b = function(e4, t4) {
            var n3 = r.getTextColor(t4), o2 = document.createElement("div");
            o2.className = "tf-v1-popover-button-icon";
            var i2 = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M21 0H0V9L10.5743 24V16.5H21C22.6567 16.5 24 15.1567 24 13.5V3C24 1.34325 22.6567 0 21 0ZM7.5\n    9.75C6.672 9.75 6 9.07875 6 8.25C6 7.42125 6.672 6.75 7.5 6.75C8.328 6.75 9 7.42125 9 8.25C9 9.07875 8.328 9.75\n    7.5 9.75ZM12.75 9.75C11.922 9.75 11.25 9.07875 11.25 8.25C11.25 7.42125 11.922 6.75 12.75 6.75C13.578 6.75 14.25\n    7.42125 14.25 8.25C14.25 9.07875 13.578 9.75 12.75 9.75ZM18 9.75C17.172 9.75 16.5 9.07875 16.5 8.25C16.5 7.42125\n    17.172 6.75 18 6.75C18.828 6.75 19.5 7.42125 19.5 8.25C19.5 9.07875 18.828 9.75 18 9.75Z" fill="' + n3 + '"></path>\n  </svg>', a2 = e4 == null ? void 0 : e4.startsWith("http");
            return o2.innerHTML = a2 ? "<img alt='popover trigger icon button' src='" + e4 + "'/>" : e4 != null ? e4 : i2, o2.dataset.testid = "default-icon", o2;
          }(d.customIcon, d.buttonColor || u.buttonColor), y = function() {
            var e4 = document.createElement("div");
            e4.className = "tf-v1-spinner";
            var t4 = document.createElement("div");
            return t4.className = "tf-v1-popover-button-icon", t4.dataset.testid = "spinner-icon", t4.append(e4), t4;
          }(), g = c(), O = c("a", "tf-v1-popover-close"), _ = function(e4) {
            var t4 = r.getTextColor(e4), n3 = document.createElement("button");
            return n3.className = "tf-v1-popover-button", n3.dataset.testid = "tf-v1-popover-button", n3.style.backgroundColor = e4, n3.style.color = t4, n3;
          }(d.buttonColor || u.buttonColor);
          (d.container || document.body).append(m), h.append(f), m.append(_), m.append(O), _.append(b);
          var w = function() {
            n2 && n2.parentNode && (n2.classList.add("closing"), setTimeout(function() {
              r.unmountElement(n2);
            }, 250));
          };
          d.tooltip && d.tooltip.length > 0 && (n2 = function(e4, t4) {
            var n3 = document.createElement("span");
            n3.className = "tf-v1-popover-tooltip-close", n3.dataset.testid = "tf-v1-popover-tooltip-close", n3.innerHTML = "&times;", n3.onclick = t4;
            var o2 = document.createElement("div");
            o2.className = "tf-v1-popover-tooltip-text", o2.innerHTML = e4;
            var r2 = document.createElement("div");
            return r2.className = "tf-v1-popover-tooltip", r2.dataset.testid = "tf-v1-popover-tooltip", r2.appendChild(o2), r2.appendChild(n3), r2;
          }(d.tooltip, w), m.append(n2)), d.notificationDays && (d.enableSandbox || i.canBuildNotificationDot(e3)) && (s = i.buildNotificationDot(), _.append(s)), f.onload = function() {
            m.classList.add("open"), h.style.opacity = "1", O.style.opacity = "1", a(y, g), r.addCustomKeyboardListener(P);
          };
          var E = function() {
            r.isOpen(h) || (w(), s && (s.classList.add("closing"), d.notificationDays && !d.enableSandbox && i.saveNotificationDotHideUntilTime(e3, d.notificationDays), setTimeout(function() {
              r.unmountElement(s);
            }, 250)), setTimeout(function() {
              r.isInPage(h) ? (h.style.opacity = "0", O.style.opacity = "0", h.style.display = "flex", setTimeout(function() {
                m.classList.add("open"), h.style.opacity = "1", O.style.opacity = "1";
              }), a(b, g)) : (m.append(h), a(b, y), h.style.opacity = "0", O.style.opacity = "0");
            }));
          }, P = function() {
            var e4;
            r.isOpen(m) && ((e4 = t3.onClose) === null || e4 === void 0 || e4.call(t3), setTimeout(function() {
              d.keepSession ? h.style.display = "none" : r.unmountElement(h), m.classList.remove("open"), a(g, b);
            }, 250));
          };
          r.setAutoClose(p, d.autoClose, P);
          var C = function() {
            r.isOpen(h) ? P() : E();
          };
          return _.onclick = C, O.onclick = P, d.open && !r.isOpen(h) && r.handleCustomOpen(E, d.open, d.openValue), {open: E, close: P, toggle: C, refresh: v, unmount: function() {
            r.unmountElement(m);
          }};
        };
      }, 797: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(528), t2), r(n(100), t2);
      }, 320: function(e2, t2) {
        var n = this && this.__assign || function() {
          return (n = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.buildNotificationDot = t2.canBuildNotificationDot = t2.saveNotificationDotHideUntilTime = void 0;
        var o = "tfNotificationData", r = function() {
          var e3 = localStorage.getItem(o);
          return e3 ? JSON.parse(e3) : {};
        }, i = function(e3) {
          e3 && localStorage.setItem(o, JSON.stringify(e3));
        };
        t2.saveNotificationDotHideUntilTime = function(e3, t3) {
          var o2, a = new Date();
          a.setDate(a.getDate() + t3), i(n(n({}, r()), ((o2 = {})[e3] = {hideUntilTime: a.getTime()}, o2)));
        }, t2.canBuildNotificationDot = function(e3) {
          var t3 = function(e4) {
            var t4;
            return ((t4 = r()[e4]) === null || t4 === void 0 ? void 0 : t4.hideUntilTime) || 0;
          }(e3);
          return new Date().getTime() > t3 && (t3 && function(e4) {
            var t4 = r();
            delete t4[e4], i(t4);
          }(e3), true);
        }, t2.buildNotificationDot = function() {
          var e3 = document.createElement("span");
          return e3.className = "tf-v1-popover-unread-dot", e3.dataset.testid = "tf-v1-popover-unread-dot", e3;
        };
      }, 100: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true});
      }, 630: function(e2, t2, n) {
        var o = this && this.__rest || function(e3, t3) {
          var n2 = {};
          for (var o2 in e3)
            Object.prototype.hasOwnProperty.call(e3, o2) && t3.indexOf(o2) < 0 && (n2[o2] = e3[o2]);
          if (e3 != null && typeof Object.getOwnPropertySymbols == "function") {
            var r2 = 0;
            for (o2 = Object.getOwnPropertySymbols(e3); r2 < o2.length; r2++)
              t3.indexOf(o2[r2]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, o2[r2]) && (n2[o2[r2]] = e3[o2[r2]]);
          }
          return n2;
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createPopup = void 0;
        var r = n(747), i = n(27), a = n(747);
        t2.createPopup = function(e3, t3) {
          if (t3 === void 0 && (t3 = {}), !r.hasDom())
            return {open: function() {
            }, close: function() {
            }, toggle: function() {
            }, refresh: function() {
            }, unmount: function() {
            }};
          var n2 = t3.width, c = t3.height, u = t3.size, s = u === void 0 ? i.POPUP_SIZE : u, d = t3.onClose, l = o(t3, ["width", "height", "size", "onClose"]), f = r.createIframe(e3, "popup", l), p = f.iframe, v = f.embedId, m = f.refresh, h = document.body.style.overflow, b = function() {
            var e4 = document.createElement("div");
            return e4.className = "tf-v1-popup", e4.style.opacity = "0", e4;
          }(), y = function() {
            var e4 = document.createElement("div");
            return e4.className = "tf-v1-spinner", e4;
          }(), g = function(e4, t4, n3) {
            var o2 = document.createElement("div");
            return o2.className = "tf-v1-iframe-wrapper", o2.style.opacity = "0", r.isDefined(e4) && r.isDefined(t4) ? r.setElementSize(o2, {width: e4, height: t4}) : (o2.style.width = "calc(" + n3 + "% - 80px)", o2.style.height = "calc(" + n3 + "% - 80px)", o2);
          }(n2, c, s);
          g.append(p), b.append(y), b.append(g);
          var O = l.container || document.body;
          p.onload = function() {
            g.style.opacity = "1", setTimeout(function() {
              y.style.display = "none";
            }, 250), r.addCustomKeyboardListener(w);
          };
          var _ = function() {
            a.isOpen(b) || (a.isInPage(b) ? b.style.display = "flex" : (y.style.display = "block", O.append(b)), document.body.style.overflow = "hidden", setTimeout(function() {
              b.style.opacity = "1";
            }));
          }, w = function() {
            a.isOpen(b) && (d == null || d(), b.style.opacity = "0", document.body.style.overflow = h, setTimeout(function() {
              l.keepSession ? b.style.display = "none" : E();
            }, 250));
          };
          g.append(function(e4) {
            var t4 = document.createElement("a");
            return t4.className = "tf-v1-close tf-v1-close-icon", t4.innerHTML = "&times;", t4.onclick = e4, t4;
          }(w)), r.setAutoClose(v, l.autoClose, w);
          var E = function() {
            r.unmountElement(b);
          };
          return l.open && !a.isOpen(b) && r.handleCustomOpen(_, l.open, l.openValue), {open: _, close: w, toggle: function() {
            a.isOpen(b) ? w() : _();
          }, refresh: m, unmount: E};
        };
      }, 970: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(630), t2), r(n(394), t2);
      }, 394: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true});
      }, 382: function(e2, t2, n) {
        var o = this && this.__assign || function() {
          return (o = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createSidetab = void 0;
        var r = n(747), i = {buttonColor: "#3a7685", buttonText: "Launch me"}, a = function(e3, t3) {
          e3 === void 0 && (e3 = "div"), t3 === void 0 && (t3 = "tf-v1-sidetab-button-icon");
          var n2 = document.createElement(e3);
          return n2.className = t3 + " tf-v1-close-icon", n2.innerHTML = "&times;", n2.dataset.testid = t3, n2;
        }, c = function(e3, t3) {
          var n2 = e3.parentNode;
          n2 && (n2.removeChild(e3), n2.appendChild(t3));
        };
        t2.createSidetab = function(e3, t3) {
          t3 === void 0 && (t3 = {});
          var n2, u, s, d = o(o({}, i), t3), l = r.createIframe(e3, "side-tab", d), f = l.iframe, p = l.embedId, v = l.refresh, m = (n2 = d.width, u = d.height, (s = document.createElement("div")).className = "tf-v1-sidetab", s.dataset.testid = "tf-v1-sidetab", r.setElementSize(s, {width: n2, height: u})), h = function() {
            var e4 = document.createElement("div");
            return e4.className = "tf-v1-sidetab-wrapper", e4.dataset.testid = "tf-v1-sidetab-wrapper", e4;
          }(), b = function() {
            var e4 = document.createElement("div");
            e4.className = "tf-v1-spinner";
            var t4 = document.createElement("div");
            return t4.className = "tf-v1-sidetab-button-icon", t4.dataset.testid = "spinner-icon", t4.append(e4), t4;
          }(), y = function(e4) {
            var t4 = r.getTextColor(e4), n3 = document.createElement("button");
            return n3.className = "tf-v1-sidetab-button", n3.style.backgroundColor = e4, n3.style.color = t4, n3;
          }(d.buttonColor || i.buttonColor), g = function(e4) {
            var t4 = document.createElement("span");
            return t4.className = "tf-v1-sidetab-button-text", t4.innerHTML = e4, t4;
          }(d.buttonText || i.buttonText), O = function(e4, t4) {
            var n3 = r.getTextColor(t4), o2 = document.createElement("div");
            o2.className = "tf-v1-sidetab-button-icon";
            var i2 = '<svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M21 0H0V9L10.5743 24V16.5H21C22.6567 16.5 24 15.1567 24 13.5V3C24 1.34325 22.6567 0 21 0ZM7.5 9.75C6.672 9.75 6 9.07875 6 8.25C6 7.42125 6.672 6.75 7.5 6.75C8.328 6.75 9 7.42125 9 8.25C9 9.07875 8.328 9.75 7.5 9.75ZM12.75 9.75C11.922 9.75 11.25 9.07875 11.25 8.25C11.25 7.42125 11.922 6.75 12.75 6.75C13.578 6.75 14.25 7.42125 14.25 8.25C14.25 9.07875 13.578 9.75 12.75 9.75ZM18 9.75C17.172 9.75 16.5 9.07875 16.5 8.25C16.5 7.42125 17.172 6.75 18 6.75C18.828 6.75 19.5 7.42125 19.5 8.25C19.5 9.07875 18.828 9.75 18 9.75Z" fill="' + n3 + '"></path>\n  </svg>', a2 = e4 == null ? void 0 : e4.startsWith("http");
            return o2.innerHTML = a2 ? "<img alt='popover trigger icon button' src='" + e4 + "'/>" : e4 != null ? e4 : i2, o2.dataset.testid = "default-icon", o2;
          }(d.customIcon, d.buttonColor || i.buttonColor), _ = a(), w = a("a", "tf-v1-sidetab-close");
          (d.container || document.body).append(m), h.append(f), m.append(y), m.append(w), y.append(O), y.append(g), setTimeout(function() {
            m.classList.add("ready");
          }, 250), f.onload = function() {
            m.classList.add("open"), c(b, _), r.addCustomKeyboardListener(P);
          };
          var E = function() {
            r.isOpen(h) || (r.isInPage(h) ? (h.style.display = "flex", m.classList.add("open"), c(O, _)) : (m.append(h), c(O, b)));
          }, P = function() {
            var e4;
            r.isOpen(h) && ((e4 = d.onClose) === null || e4 === void 0 || e4.call(d), m.classList.remove("open"), setTimeout(function() {
              d.keepSession ? h.style.display = "none" : r.unmountElement(h), c(_, O);
            }, 250));
          };
          r.setAutoClose(p, d.autoClose, P);
          var C = function() {
            r.isOpen(h) ? P() : E();
          };
          return y.onclick = C, w.onclick = P, d.open && !r.isOpen(h) && r.handleCustomOpen(E, d.open, d.openValue), {open: E, close: P, toggle: C, refresh: v, unmount: function() {
            r.unmountElement(m);
          }};
        };
      }, 434: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(382), t2), r(n(668), t2);
      }, 668: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true});
      }, 603: function(e2, t2, n) {
        var o = this && this.__rest || function(e3, t3) {
          var n2 = {};
          for (var o2 in e3)
            Object.prototype.hasOwnProperty.call(e3, o2) && t3.indexOf(o2) < 0 && (n2[o2] = e3[o2]);
          if (e3 != null && typeof Object.getOwnPropertySymbols == "function") {
            var r2 = 0;
            for (o2 = Object.getOwnPropertySymbols(e3); r2 < o2.length; r2++)
              t3.indexOf(o2[r2]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, o2[r2]) && (n2[o2[r2]] = e3[o2[r2]]);
          }
          return n2;
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createSlider = void 0;
        var r = n(747), i = n(27);
        t2.createSlider = function(e3, t3) {
          if (t3 === void 0 && (t3 = {}), !r.hasDom())
            return {open: function() {
            }, close: function() {
            }, toggle: function() {
            }, refresh: function() {
            }, unmount: function() {
            }};
          var n2 = t3.position, a = n2 === void 0 ? i.SLIDER_POSITION : n2, c = t3.width, u = c === void 0 ? i.SLIDER_WIDTH : c, s = t3.onClose, d = o(t3, ["position", "width", "onClose"]), l = r.createIframe(e3, "slider", d), f = l.iframe, p = l.embedId, v = l.refresh, m = document.body.style.overflow, h = function(e4) {
            var t4 = document.createElement("div");
            return t4.className = "tf-v1-slider " + e4, t4.style.opacity = "0", t4;
          }(a), b = function() {
            var e4 = document.createElement("div");
            return e4.className = "tf-v1-spinner", e4;
          }(), y = function(e4, t4) {
            var n3 = document.createElement("div");
            return n3.className = "tf-v1-iframe-wrapper", n3.style[e4] = "-100%", r.setElementSize(n3, {width: t4});
          }(a, u);
          y.append(f), h.append(b), h.append(y);
          var g = d.container || document.body;
          f.onload = function() {
            y.style[a] = "0", setTimeout(function() {
              b.style.display = "none";
            }, 500), r.addCustomKeyboardListener(_);
          };
          var O = function() {
            r.isOpen(h) || (r.isInPage(h) ? (h.style.display = "flex", setTimeout(function() {
              y.style[a] = "0";
            })) : (g.append(h), b.style.display = "block"), document.body.style.overflow = "hidden", setTimeout(function() {
              h.style.opacity = "1";
            }));
          }, _ = function() {
            r.isOpen(h) && (s == null || s(), h.style.opacity = "0", y.style[a] = "-100%", document.body.style.overflow = m, setTimeout(function() {
              d.keepSession ? h.style.display = "none" : w();
            }, 500));
          };
          r.setAutoClose(p, d.autoClose, _);
          var w = function() {
            r.unmountElement(h);
          };
          return y.append(function(e4) {
            var t4 = document.createElement("a");
            return t4.className = "tf-v1-close tf-v1-close-icon", t4.innerHTML = "&times;", t4.onclick = e4, t4;
          }(_)), d.open && !r.isOpen(h) && r.handleCustomOpen(O, d.open, d.openValue), {open: O, close: _, toggle: function() {
            r.isOpen(h) ? _() : O();
          }, refresh: v, unmount: w};
        };
      }, 331: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(603), t2), r(n(162), t2);
      }, 162: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true});
      }, 718: function(e2, t2, n) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createWidget = void 0;
        var o = n(747), r = n(554), i = n(313);
        t2.createWidget = function(e3, t3) {
          if (!o.hasDom())
            return {refresh: function() {
            }, unmount: function() {
            }};
          var n2 = t3;
          t3.inlineOnMobile || !t3.forceTouch && !o.isFullscreen() || (n2.enableFullscreen = true, n2.forceTouch = true);
          var a = o.createIframe(e3, "widget", n2), c = a.embedId, u = a.iframe, s = a.refresh, d = i.buildWidget(u, t3.width, t3.height);
          if (n2.autoResize) {
            var l = typeof n2.autoResize == "string" ? n2.autoResize.split(",").map(function(e4) {
              return parseInt(e4);
            }) : [], f = l[0], p = l[1];
            window.addEventListener("message", r.getFormHeightChangedHandler(c, function(e4) {
              var n3 = Math.max(e4.height + 20, f || 0);
              p && (n3 = Math.min(n3, p)), t3.container.style.height = n3 + "px";
            }));
          }
          var v, m = function() {
            return t3.container.append(d);
          };
          if (t3.container.innerHTML = "", t3.lazy ? o.lazyInitialize(t3.container, m) : m(), n2.enableFullscreen) {
            var h = t3.container;
            window.addEventListener("message", r.getWelcomeScreenHiddenHandler(c, h));
            var b = ((v = document.createElement("a")).className = "tf-v1-widget-close tf-v1-close-icon", v.innerHTML = "&times;", v);
            b.onclick = function() {
              var e4;
              if ((e4 = t3.onClose) === null || e4 === void 0 || e4.call(t3), h.classList.remove("tf-v1-widget-fullscreen"), t3.keepSession) {
                var n3 = document.createElement("div");
                n3.className = "tf-v1-widget-iframe-overlay", n3.onclick = function() {
                  h.classList.add("tf-v1-widget-fullscreen"), o.unmountElement(n3);
                }, d.append(n3);
              } else
                t3.container.innerHTML = "", m(), h.append(b);
            }, h.append(b);
          }
          return {refresh: s, unmount: function() {
            return o.unmountElement(d);
          }};
        };
      }, 419: function(e2, t2, n) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.buildWidget = void 0;
        var o = n(747);
        t2.buildWidget = function(e3, t3, n2) {
          var r = document.createElement("div");
          return r.className = "tf-v1-widget", r.append(e3), o.setElementSize(r, {width: t3, height: n2});
        };
      }, 313: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(419), t2);
      }, 321: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(718), t2), r(n(58), t2);
      }, 58: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true});
      }, 920: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(797), t2), r(n(970), t2), r(n(331), t2), r(n(321), t2), r(n(434), t2);
      }, 626: function(e2, t2, n) {
        var o = this && this.__assign || function() {
          return (o = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.buildIframeSrc = void 0;
        var r = n(27), i = n(527), a = n(346), c = n(698), u = {widget: "embed-widget", popup: "popup-blank", slider: "popup-drawer", popover: "popup-popover", "side-tab": "popup-side-panel"};
        t2.buildIframeSrc = function(e3) {
          var t3 = e3.formId, n2 = e3.type, s = e3.embedId, d = e3.options, l = function(e4, t4, n3) {
            var r2 = n3.transitiveSearchParams, i2 = n3.source, a2 = n3.medium, s2 = n3.mediumVersion, d2 = n3.hideFooter, l2 = n3.hideHeaders, f2 = n3.opacity, p2 = n3.disableTracking, v2 = n3.enableSandbox, m = n3.disableAutoFocus, h = n3.shareGaInstance, b = n3.forceTouch, y = n3.enableFullscreen, g = n3.tracking, O = n3.redirectTarget, _ = c.getTransitiveSearchParams(r2);
            return o(o(o({}, {"typeform-embed-id": t4, "typeform-embed": u[e4], "typeform-source": i2, "typeform-medium": a2, "typeform-medium-version": s2, "embed-hide-footer": d2 ? "true" : void 0, "embed-hide-headers": l2 ? "true" : void 0, "embed-opacity": f2, "disable-tracking": p2 || v2 ? "true" : void 0, "disable-auto-focus": m ? "true" : void 0, "__dangerous-disable-submissions": v2 ? "true" : void 0, "share-ga-instance": h ? "true" : void 0, "force-touch": b ? "true" : void 0, "add-placeholder-ws": e4 === "widget" && y ? "true" : void 0, "typeform-embed-redirect-target": O}), _), g);
          }(n2, s, function(e4) {
            return o(o({}, {source: (t4 = window === null || window === void 0 ? void 0 : window.location) === null || t4 === void 0 ? void 0 : t4.hostname.replace(/^www\./, ""), medium: "embed-sdk", mediumVersion: "next"}), i.removeUndefinedKeys(e4));
            var t4;
          }(d)), f = function(e4, t4) {
            t4 === void 0 && (t4 = false);
            var n3 = t4 ? "c" : "to";
            return new URL(e4.startsWith("http://") || e4.startsWith("https://") ? e4 : r.FORM_BASE_URL + "/" + n3 + "/" + e4);
          }(t3, d.chat);
          if (Object.entries(l).filter(function(e4) {
            var t4 = e4[1];
            return a.isDefined(t4);
          }).forEach(function(e4) {
            var t4 = e4[0], n3 = e4[1];
            f.searchParams.set(t4, n3);
          }), d.hidden) {
            var p = new URL(r.FORM_BASE_URL);
            Object.entries(d.hidden).filter(function(e4) {
              var t4 = e4[1];
              return a.isDefined(t4);
            }).forEach(function(e4) {
              var t4 = e4[0], n3 = e4[1];
              p.searchParams.set(t4, n3);
            });
            var v = p.searchParams.toString();
            v && (f.hash = v);
          }
          return f.href;
        };
      }, 972: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.handleCustomOpen = void 0, t2.handleCustomOpen = function(e3, t3, n) {
          switch (t3) {
            case "load":
              e3();
              break;
            case "exit":
              n && function(e4, t4) {
                var n2 = 0, o = function(r) {
                  r.clientY < e4 && r.clientY < n2 ? (document.removeEventListener("mousemove", o), t4()) : n2 = r.clientY;
                };
                document.addEventListener("mousemove", o);
              }(n, e3);
              break;
            case "time":
              setTimeout(function() {
                e3();
              }, n);
              break;
            case "scroll":
              n && function(e4, t4) {
                var n2 = function() {
                  var o = window.pageYOffset || document.documentElement.scrollTop, r = document.documentElement.clientTop || 0, i = document.documentElement.scrollHeight, a = o - r, c = a / i * 100, u = a + window.innerHeight >= i;
                  (c >= e4 || u) && (t4(), document.removeEventListener("scroll", n2));
                };
                document.addEventListener("scroll", n2);
              }(n, e3);
          }
        };
      }, 553: function(e2, t2, n) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.createIframe = void 0;
        var o = n(626), r = n(747), i = n(866), a = n(554), c = n(256), u = n(144), s = n(511);
        t2.createIframe = function(e3, t3, n2) {
          var d = i.generateEmbedId(), l = n2.iframeProps, f = l === void 0 ? {} : l, p = n2.onReady, v = n2.onQuestionChanged, m = n2.onHeightChanged, h = n2.onSubmit, b = n2.onEndingButtonClick, y = n2.shareGaInstance, g = o.buildIframeSrc({formId: e3, embedId: d, type: t3, options: n2}), O = document.createElement("iframe");
          return O.src = g, O.dataset.testid = "iframe", O.style.border = "0px", O.allow = "microphone; camera", Object.keys(f).forEach(function(e4) {
            O.setAttribute(e4, f[e4]);
          }), O.addEventListener("load", c.triggerIframeRedraw, {once: true}), window.addEventListener("message", a.getFormReadyHandler(d, p)), window.addEventListener("message", a.getFormQuestionChangedHandler(d, v)), window.addEventListener("message", a.getFormHeightChangedHandler(d, m)), window.addEventListener("message", a.getFormSubmitHandler(d, h)), window.addEventListener("message", a.getFormThemeHandler(d)), window.addEventListener("message", a.getThankYouScreenButtonClickHandler(d, b)), t3 !== "widget" && window.addEventListener("message", u.dispatchCustomKeyEventFromIframe), y && window.addEventListener("message", a.getFormReadyHandler(d, function() {
            r.setupGaInstance(O, d, y);
          })), {iframe: O, embedId: d, refresh: function() {
            return s.refreshIframe(O);
          }};
        };
      }, 866: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.generateEmbedId = void 0, t2.generateEmbedId = function() {
          var e3 = Math.random();
          return String(e3).split(".")[1];
        };
      }, 554: function(e2, t2) {
        var n = this && this.__rest || function(e3, t3) {
          var n2 = {};
          for (var o2 in e3)
            Object.prototype.hasOwnProperty.call(e3, o2) && t3.indexOf(o2) < 0 && (n2[o2] = e3[o2]);
          if (e3 != null && typeof Object.getOwnPropertySymbols == "function") {
            var r = 0;
            for (o2 = Object.getOwnPropertySymbols(e3); r < o2.length; r++)
              t3.indexOf(o2[r]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, o2[r]) && (n2[o2[r]] = e3[o2[r]]);
          }
          return n2;
        };
        function o(e3, t3, o2) {
          return function(r) {
            var i = r.data, a = i.type, c = i.embedId, u = n(i, ["type", "embedId"]);
            a === e3 && c === t3 && (o2 == null || o2(u));
          };
        }
        Object.defineProperty(t2, "__esModule", {value: true}), t2.getThankYouScreenButtonClickHandler = t2.getFormThemeHandler = t2.getWelcomeScreenHiddenHandler = t2.getFormSubmitHandler = t2.getFormHeightChangedHandler = t2.getFormQuestionChangedHandler = t2.getFormReadyHandler = void 0, t2.getFormReadyHandler = function(e3, t3) {
          return o("form-ready", e3, t3);
        }, t2.getFormQuestionChangedHandler = function(e3, t3) {
          return o("form-screen-changed", e3, t3);
        }, t2.getFormHeightChangedHandler = function(e3, t3) {
          return o("form-height-changed", e3, t3);
        }, t2.getFormSubmitHandler = function(e3, t3) {
          return o("form-submit", e3, t3);
        }, t2.getWelcomeScreenHiddenHandler = function(e3, t3) {
          return o("welcome-screen-hidden", e3, function() {
            t3.classList.add("tf-v1-widget-fullscreen");
          });
        }, t2.getFormThemeHandler = function(e3) {
          return o("form-theme", e3, function(e4) {
            var t3;
            if (e4 == null ? void 0 : e4.theme) {
              var n2 = document.querySelector(".tf-v1-close-icon");
              n2 && (n2.style.color = (t3 = e4.theme) === null || t3 === void 0 ? void 0 : t3.color);
            }
          });
        }, t2.getThankYouScreenButtonClickHandler = function(e3, t3) {
          return o("thank-you-screen-button-click", e3, t3);
        };
      }, 339: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(553), t2), r(n(144), t2);
      }, 511: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.refreshIframe = void 0, t2.refreshIframe = function(e3) {
          if (e3) {
            var t3 = e3.src;
            if (t3.includes("&refresh")) {
              var n = t3.split("&refresh#");
              e3.src = n.join("#");
            } else
              (n = t3.split("#"))[0] = n[0] + "&refresh", e3.src = n.join("#");
          }
        };
      }, 144: function(e2, t2) {
        var n = this && this.__awaiter || function(e3, t3, n2, o2) {
          return new (n2 || (n2 = Promise))(function(r2, i2) {
            function a(e4) {
              try {
                u(o2.next(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function c(e4) {
              try {
                u(o2.throw(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function u(e4) {
              var t4;
              e4.done ? r2(e4.value) : (t4 = e4.value, t4 instanceof n2 ? t4 : new n2(function(e5) {
                e5(t4);
              })).then(a, c);
            }
            u((o2 = o2.apply(e3, t3 || [])).next());
          });
        }, o = this && this.__generator || function(e3, t3) {
          var n2, o2, r2, i2, a = {label: 0, sent: function() {
            if (1 & r2[0])
              throw r2[1];
            return r2[1];
          }, trys: [], ops: []};
          return i2 = {next: c(0), throw: c(1), return: c(2)}, typeof Symbol == "function" && (i2[Symbol.iterator] = function() {
            return this;
          }), i2;
          function c(i3) {
            return function(c2) {
              return function(i4) {
                if (n2)
                  throw new TypeError("Generator is already executing.");
                for (; a; )
                  try {
                    if (n2 = 1, o2 && (r2 = 2 & i4[0] ? o2.return : i4[0] ? o2.throw || ((r2 = o2.return) && r2.call(o2), 0) : o2.next) && !(r2 = r2.call(o2, i4[1])).done)
                      return r2;
                    switch (o2 = 0, r2 && (i4 = [2 & i4[0], r2.value]), i4[0]) {
                      case 0:
                      case 1:
                        r2 = i4;
                        break;
                      case 4:
                        return a.label++, {value: i4[1], done: false};
                      case 5:
                        a.label++, o2 = i4[1], i4 = [0];
                        continue;
                      case 7:
                        i4 = a.ops.pop(), a.trys.pop();
                        continue;
                      default:
                        if (!((r2 = (r2 = a.trys).length > 0 && r2[r2.length - 1]) || i4[0] !== 6 && i4[0] !== 2)) {
                          a = 0;
                          continue;
                        }
                        if (i4[0] === 3 && (!r2 || i4[1] > r2[0] && i4[1] < r2[3])) {
                          a.label = i4[1];
                          break;
                        }
                        if (i4[0] === 6 && a.label < r2[1]) {
                          a.label = r2[1], r2 = i4;
                          break;
                        }
                        if (r2 && a.label < r2[2]) {
                          a.label = r2[2], a.ops.push(i4);
                          break;
                        }
                        r2[2] && a.ops.pop(), a.trys.pop();
                        continue;
                    }
                    i4 = t3.call(e3, a);
                  } catch (e4) {
                    i4 = [6, e4], o2 = 0;
                  } finally {
                    n2 = r2 = 0;
                  }
                if (5 & i4[0])
                  throw i4[1];
                return {value: i4[0] ? i4[1] : void 0, done: true};
              }([i3, c2]);
            };
          }
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.dispatchCustomKeyEventFromIframe = t2.removeCustomKeyboardListener = t2.addCustomKeyboardListener = void 0;
        var r = "Escape", i = function(e3, i2) {
          return n(void 0, void 0, void 0, function() {
            return o(this, function(n2) {
              return e3.code === r && typeof i2 == "function" && (i2(), t2.removeCustomKeyboardListener()), [2];
            });
          });
        };
        t2.addCustomKeyboardListener = function(e3) {
          return window.document.addEventListener("keydown", function(t3) {
            return i(t3, e3);
          });
        }, t2.removeCustomKeyboardListener = function() {
          return window.document.removeEventListener("keydown", i);
        }, t2.dispatchCustomKeyEventFromIframe = function(e3) {
          e3.data.type === "form-close" && window.document.dispatchEvent(new KeyboardEvent("keydown", {code: r}));
        };
      }, 256: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.triggerIframeRedraw = void 0, t2.triggerIframeRedraw = function() {
          this.style.transform = "translateZ(0)";
        };
      }, 939: function(e2, t2, n) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.getTextColor = void 0;
        var o = n(938);
        t2.getTextColor = function(e3) {
          if (!e3)
            return "#FFFFFF";
          var t3 = e3.startsWith("#") ? o.hexRgb(e3) : function(e4) {
            var t4 = {red: 0, green: 0, blue: 0}, n3 = e4.match(/\d+/g);
            return n3 && (t4.red = parseInt(n3[0], 10), t4.green = parseInt(n3[0], 10), t4.blue = parseInt(n3[0], 10)), t4;
          }(e3), n2 = t3.red, r = t3.green, i = t3.blue;
          return Math.round((299 * n2 + 587 * r + 114 * i) / 1e3) > 125 ? "#000000" : "#FFFFFF";
        };
      }, 698: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.getTransitiveSearchParams = void 0, t2.getTransitiveSearchParams = function(e3) {
          var t3 = new URL(window.location.href), n = {};
          return e3 && e3.length > 0 && e3.forEach(function(e4) {
            t3.searchParams.has(e4) && (n[e4] = t3.searchParams.get(e4));
          }), n;
        };
      }, 252: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.hasDom = void 0, t2.hasDom = function() {
          return typeof document != "undefined" && typeof window != "undefined";
        };
      }, 938: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.hexRgb = void 0;
        var n = new RegExp("[^#a-f\\d]", "gi"), o = new RegExp("^#?[a-f\\d]{3}[a-f\\d]?$|^#?[a-f\\d]{6}([a-f\\d]{2})?$", "i");
        t2.hexRgb = function(e3) {
          if (typeof e3 != "string" || n.test(e3) || !o.test(e3))
            throw new TypeError("Expected a valid hex string");
          (e3 = e3.replace(/^#/, "")).length === 8 && (e3 = e3.slice(0, 6)), e3.length === 4 && (e3 = e3.slice(0, 3)), e3.length === 3 && (e3 = e3[0] + e3[0] + e3[1] + e3[1] + e3[2] + e3[2]);
          var t3 = Number.parseInt(e3, 16);
          return {red: t3 >> 16, green: t3 >> 8 & 255, blue: 255 & t3};
        };
      }, 71: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.includeCss = void 0, t2.includeCss = function(e3) {
          var t3 = function(e4) {
            return "https://embed.typeform.com/next/css/" + e4;
          }(e3);
          if (!document.querySelector('link[href="' + t3 + '"]')) {
            var n = document.createElement("link");
            n.rel = "stylesheet", n.href = t3, document.head.append(n);
          }
        };
      }, 747: function(e2, t2, n) {
        var o = this && this.__createBinding || (Object.create ? function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), Object.defineProperty(e3, o2, {enumerable: true, get: function() {
            return t3[n2];
          }});
        } : function(e3, t3, n2, o2) {
          o2 === void 0 && (o2 = n2), e3[o2] = t3[n2];
        }), r = this && this.__exportStar || function(e3, t3) {
          for (var n2 in e3)
            n2 === "default" || Object.prototype.hasOwnProperty.call(t3, n2) || o(t3, e3, n2);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), r(n(626), t2), r(n(339), t2), r(n(252), t2), r(n(71), t2), r(n(346), t2), r(n(377), t2), r(n(563), t2), r(n(527), t2), r(n(533), t2), r(n(451), t2), r(n(972), t2), r(n(748), t2), r(n(392), t2), r(n(939), t2), r(n(917), t2), r(n(987), t2);
      }, 346: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.isDefined = void 0, t2.isDefined = function(e3) {
          return e3 != null;
        };
      }, 987: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.isVisible = t2.isInPage = t2.isOpen = void 0, t2.isOpen = function(e3) {
          return t2.isInPage(e3) && t2.isVisible(e3);
        }, t2.isInPage = function(e3) {
          return !!e3.parentNode;
        }, t2.isVisible = function(e3) {
          return e3.style.display !== "none";
        };
      }, 917: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.lazyInitialize = void 0, t2.lazyInitialize = function(e3, t3) {
          var n = new IntersectionObserver(function(e4) {
            e4.forEach(function(e5) {
              e5.isIntersecting && (t3(), n.unobserve(e5.target));
            });
          });
          n.observe(e3);
        };
      }, 377: function(e2, t2) {
        var n = this && this.__assign || function() {
          return (n = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.loadOptionsFromAttributes = t2.transformAttributeValue = t2.camelCaseToKebabCase = void 0, t2.camelCaseToKebabCase = function(e3) {
          return e3.split("").map(function(e4, t3) {
            return e4.toUpperCase() === e4 ? (t3 !== 0 ? "-" : "") + e4.toLowerCase() : e4;
          }).join("");
        };
        var o = function(e3) {
          return e3 || void 0;
        }, r = function(e3) {
          return e3 === "" || e3 === "yes" || e3 === "true";
        }, i = function(e3) {
          var t3 = e3 ? parseInt(e3, 10) : NaN;
          return isNaN(t3) ? void 0 : t3;
        }, a = "%ESCAPED_COMMA%";
        t2.transformAttributeValue = function(e3, t3) {
          var c, u;
          switch (t3) {
            case "string":
              return o(e3);
            case "boolean":
              return r(e3);
            case "integer":
              return i(e3);
            case "function":
              return function(e4) {
                var t4 = e4 && e4 in window ? window[e4] : void 0;
                return typeof t4 == "function" ? t4 : void 0;
              }(e3);
            case "array":
              return function(e4) {
                if (e4)
                  return e4.replace(/\s/g, "").replace(/\\,/g, a).split(",").filter(function(e5) {
                    return !!e5;
                  }).map(function(e5) {
                    return e5.replace(a, ",");
                  });
              }(e3);
            case "record":
              return function(e4) {
                if (e4)
                  return e4.replace(/\\,/g, a).split(",").filter(function(e5) {
                    return !!e5;
                  }).map(function(e5) {
                    return e5.replace(a, ",");
                  }).reduce(function(e5, t4) {
                    var o2, r2 = t4.match(/^([^=]+)=(.*)$/);
                    if (r2) {
                      var i2 = r2[1], a2 = r2[2];
                      return n(n({}, e5), ((o2 = {})[i2.trim()] = a2, o2));
                    }
                    return e5;
                  }, {});
              }(e3);
            case "integerOrBoolean":
              return (c = i(e3)) !== null && c !== void 0 ? c : r(e3);
            case "stringOrBoolean":
              return (u = o(e3)) !== null && u !== void 0 ? u : r(e3);
            default:
              throw new Error("Invalid attribute transformation " + t3);
          }
        }, t2.loadOptionsFromAttributes = function(e3, o2) {
          return Object.keys(o2).reduce(function(r2, i2) {
            var a2;
            return n(n({}, r2), ((a2 = {})[i2] = t2.transformAttributeValue(e3.getAttribute("data-tf-" + t2.camelCaseToKebabCase(i2)), o2[i2]), a2));
          }, {});
        };
      }, 563: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.isFullscreen = t2.isMobile = t2.isBigScreen = void 0, t2.isBigScreen = function() {
          return window.screen.width >= 1024 && window.screen.height >= 768;
        }, t2.isMobile = function() {
          return /mobile|tablet|android/i.test(navigator.userAgent.toLowerCase());
        }, t2.isFullscreen = function() {
          return t2.isMobile() && !t2.isBigScreen();
        };
      }, 527: function(e2, t2, n) {
        var o = this && this.__assign || function() {
          return (o = Object.assign || function(e3) {
            for (var t3, n2 = 1, o2 = arguments.length; n2 < o2; n2++)
              for (var r2 in t3 = arguments[n2])
                Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }).apply(this, arguments);
        };
        Object.defineProperty(t2, "__esModule", {value: true}), t2.removeUndefinedKeys = void 0;
        var r = n(346);
        t2.removeUndefinedKeys = function(e3) {
          return Object.entries(e3).filter(function(e4) {
            var t3 = e4[1];
            return r.isDefined(t3);
          }).reduce(function(e4, t3) {
            var n2, r2 = t3[0], i = t3[1];
            return o(o({}, e4), ((n2 = {})[r2] = i, n2));
          }, {});
        };
      }, 748: function(e2, t2, n) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.setAutoClose = void 0;
        var o = n(554);
        t2.setAutoClose = function(e3, t3, n2) {
          if (t3 && n2) {
            var r = typeof t3 == "number" ? t3 : 0;
            window.addEventListener("message", o.getFormSubmitHandler(e3, function() {
              return setTimeout(n2, r);
            }));
          }
        };
      }, 533: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.setElementSize = void 0, t2.setElementSize = function(e3, t3) {
          var n = t3.width, o = t3.height;
          return n && (e3.style.width = n + "px"), o && (e3.style.height = o + "px"), e3;
        };
      }, 392: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.setupGaInstance = t2.sendGaIdMessage = void 0, t2.sendGaIdMessage = function(e3, t3, n2) {
          var o = {embedId: e3, gaClientId: t3};
          setTimeout(function() {
            n2 && n2.contentWindow && n2.contentWindow.postMessage({type: "ga-client-id", data: o}, "*");
          }, 0);
        };
        var n = function(e3) {
          console.error(e3);
        };
        t2.setupGaInstance = function(e3, o, r) {
          try {
            var i = window[window.GoogleAnalyticsObject], a = typeof r == "string" ? r : void 0, c = function(e4, t3) {
              return t3 ? e4.find(function(e5) {
                return e5.get("trackingId") === t3;
              }) : e4[0];
            }(i.getAll(), a);
            c ? t2.sendGaIdMessage(o, c.get("clientId"), e3) : n("Whoops! You enabled the shareGaInstance feature in your typeform embed but the tracker with ID " + a + " was not found. Make sure to include Google Analytics Javascript code before the Typeform Embed Javascript code in your page and use correct tracker ID. ");
          } catch (e4) {
            n("Whoops! You enabled the shareGaInstance feature in your typeform embed but the Google Analytics object has not been found. Make sure to include Google Analytics Javascript code before the Typeform Embed Javascript code in your page. "), n(e4);
          }
        };
      }, 451: function(e2, t2) {
        Object.defineProperty(t2, "__esModule", {value: true}), t2.unmountElement = void 0, t2.unmountElement = function(e3) {
          var t3;
          (t3 = e3.parentNode) === null || t3 === void 0 || t3.removeChild(e3);
        };
      }}, t = {};
      return function n(o) {
        if (t[o])
          return t[o].exports;
        var r = t[o] = {exports: {}};
        return e[o].call(r.exports, r, r.exports, n), r.exports;
      }(920);
    }();
  });
});
var __pika_web_default_export_for_treeshaking__ = /* @__PURE__ */ getDefaultExportFromCjs(build);
var createPopover = build.createPopover;
var createPopup = build.createPopup;
var createSidetab = build.createSidetab;
var createSlider = build.createSlider;
var createWidget = build.createWidget;
export default __pika_web_default_export_for_treeshaking__;
export {build as __moduleExports, createPopover, createPopup, createSidetab, createSlider, createWidget};
