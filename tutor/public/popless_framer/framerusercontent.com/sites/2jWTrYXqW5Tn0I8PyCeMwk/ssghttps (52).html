// Generated by Fr<PERSON>r (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Image, PropertyOverrides, removeHiddenBreakpointLayers, RichText, useActiveVariantCallback, useHydratedBreakpointVariants, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import Intercom from "https://framerusercontent.com/modules/UIhUTcd796YH7Ndybys8/totj55n8qE3VYpdXhshW/Intercom.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import AssetsButtonMain from "https://framerusercontent.com/modules/ocbwDwrCSLiVC873hFen/SZ3EEPoJIeHkm0wEe9x3/W7ao3lSRJ.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import metadataProvider from "https://framerusercontent.com/modules/Z9DqAv9Vr6VKXtVdDfqm/Zj16i5SAKEpcrZv2uA55/J_W7bxNYj.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const AssetsButtonMainFonts = getFonts(AssetsButtonMain);
const IntercomFonts = getFonts(Intercom);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["J1QNDlTtn", "AziPymTHJ", "S_GY7Kvhs", "sfPBTBpOT"];
const breakpoints = {
  AziPymTHJ: "(min-width: 1440px) and (max-width: 1919px)",
  J1QNDlTtn: "(min-width: 1920px)",
  S_GY7Kvhs: "(min-width: 810px) and (max-width: 1439px)",
  sfPBTBpOT: "(max-width: 809px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  AziPymTHJ: "framer-v-1es1ua8",
  J1QNDlTtn: "framer-v-16hy4t9",
  S_GY7Kvhs: "framer-v-59rpun",
  sfPBTBpOT: "framer-v-1ke8ub5"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("J1QNDlTtn", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  "Desktop L": "J1QNDlTtn",
  Desktop: "AziPymTHJ",
  Phone: "sfPBTBpOT",
  Tablet: "S_GY7Kvhs"
};
const transitions = {
  default: {
    duration: 0
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "J1QNDlTtn",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata = metadataProvider();
    document.title = metadata.title || "";
    if (metadata.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata.viewport);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const tapctt1eq = activeVariantCallback(async (...args) => {
    window.open("https://www.popless.com/signup", "_blank", "noreferrer noopener");
  });
  const poplessLink1hxdeaz = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noreferrer noopener");
  });
  const twitterLink1bj8fo6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noreferrer noopener");
  });
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "J1QNDlTtn",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        "data-framer-generated": true,
        className: cx("framer-hvoAh"),
        style: {
          display: "contents",
          pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
        },
        children: [/*#__PURE__*/_jsx(motion.div, {
          ...restProps,
          className: cx("framer-16hy4t9", className),
          ref: ref,
          style: {
            ...style
          },
          children: /*#__PURE__*/_jsxs(motion.main, {
            className: "framer-1kxdmj",
            "data-framer-name": "Main",
            name: "Main",
            children: [/*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                S_GY7Kvhs: {
                  background: {
                    alt: "",
                    fit: "fill",
                    intrinsicHeight: 2100,
                    intrinsicWidth: 2434,
                    pixelHeight: 2100,
                    pixelWidth: 2434,
                    sizes: "100vw",
                    src: new URL("https://framerusercontent.com/images/rOepB3OKcbNdmqDc4ePfWykhY.jpg").href,
                    srcSet: `${new URL("https://framerusercontent.com/images/rOepB3OKcbNdmqDc4ePfWykhY.jpg?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/rOepB3OKcbNdmqDc4ePfWykhY.jpg?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/rOepB3OKcbNdmqDc4ePfWykhY.jpg?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/rOepB3OKcbNdmqDc4ePfWykhY.jpg").href} 2434w`
                  }
                },
                sfPBTBpOT: {
                  background: {
                    alt: "",
                    fit: "fill",
                    intrinsicHeight: 2100,
                    intrinsicWidth: 1378,
                    pixelHeight: 2100,
                    pixelWidth: 1378,
                    sizes: "100vw",
                    src: new URL("https://framerusercontent.com/images/3KyIgqDvpFW2dhiqSAzR1EBW8t4.jpg").href,
                    srcSet: `${new URL("https://framerusercontent.com/images/3KyIgqDvpFW2dhiqSAzR1EBW8t4.jpg?scale-down-to=512").href} 335w, ${new URL("https://framerusercontent.com/images/3KyIgqDvpFW2dhiqSAzR1EBW8t4.jpg?scale-down-to=1024").href} 671w, ${new URL("https://framerusercontent.com/images/3KyIgqDvpFW2dhiqSAzR1EBW8t4.jpg?scale-down-to=2048").href} 1343w, ${new URL("https://framerusercontent.com/images/3KyIgqDvpFW2dhiqSAzR1EBW8t4.jpg").href} 1378w`
                  }
                }
              },
              children: /*#__PURE__*/_jsxs(Image, {
                as: "header",
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 2160,
                  intrinsicWidth: 3826,
                  pixelHeight: 2160,
                  pixelWidth: 3826,
                  sizes: "100vw",
                  src: new URL("https://framerusercontent.com/images/G2rrnYrVZknZkLZ5EFDcqVHGmH8.jpg").href,
                  srcSet: `${new URL("https://framerusercontent.com/images/G2rrnYrVZknZkLZ5EFDcqVHGmH8.jpg?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/G2rrnYrVZknZkLZ5EFDcqVHGmH8.jpg?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/G2rrnYrVZknZkLZ5EFDcqVHGmH8.jpg?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/G2rrnYrVZknZkLZ5EFDcqVHGmH8.jpg").href} 3826w`
                },
                className: "framer-1h16pk9",
                "data-framer-name": "Stack",
                name: "Stack",
                children: [/*#__PURE__*/_jsx(Container, {
                  className: "framer-10myskv-container",
                  children: /*#__PURE__*/_jsx(PropertyOverrides, {
                    breakpoint: baseVariant,
                    overrides: {
                      S_GY7Kvhs: {
                        variant: "aZMkidfTG"
                      },
                      sfPBTBpOT: {
                        variant: "aZMkidfTG"
                      }
                    },
                    children: /*#__PURE__*/_jsx(HeaderNavigation, {
                      height: "100%",
                      id: "Q1t870qQX",
                      layoutId: "Q1t870qQX",
                      style: {
                        width: "100%"
                      },
                      variant: "Dg2JgAbsI",
                      width: "100%"
                    })
                  })
                }), /*#__PURE__*/_jsxs(motion.div, {
                  className: "framer-79qxle",
                  children: [/*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-efggbp",
                    children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        sfPBTBpOT: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("h1", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-size": "19px",
                                "--framer-font-weight": "500",
                                "--framer-line-height": "100%",
                                "--framer-text-alignment": "left",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: "Mission"
                            })
                          })
                        }
                      },
                      children: /*#__PURE__*/_jsx(RichText, {
                        __fromCanvasComponent: true,
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsx("h1", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNTAw",
                              "--framer-font-size": "20px",
                              "--framer-font-weight": "500",
                              "--framer-line-height": "100%",
                              "--framer-text-alignment": "left",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "Mission"
                          })
                        }),
                        className: "framer-kyd1m0",
                        fonts: ["GF;Inter-500"],
                        verticalAlignment: "top",
                        withExternalLayout: true
                      })
                    }), /*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        S_GY7Kvhs: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsxs("h1", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-size": "64px",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-0.1px",
                                "--framer-line-height": "100%",
                                "--framer-text-alignment": "left",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: [/*#__PURE__*/_jsx("span", {
                                style: {
                                  "--framer-font-size": "80px"
                                },
                                children: "Inspire."
                              }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                                style: {
                                  "--framer-font-size": "80px"
                                },
                                children: "Everyone."
                              })]
                            })
                          })
                        },
                        sfPBTBpOT: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsxs("h1", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNzAw",
                                "--framer-font-size": "46px",
                                "--framer-font-weight": "700",
                                "--framer-letter-spacing": "-0.1px",
                                "--framer-line-height": "100%",
                                "--framer-text-alignment": "left",
                                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                              },
                              children: ["Inspire.", /*#__PURE__*/_jsx("br", {}), "Everyone."]
                            })
                          })
                        }
                      },
                      children: /*#__PURE__*/_jsx(RichText, {
                        __fromCanvasComponent: true,
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsxs("h1", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItNzAw",
                              "--framer-font-size": "64px",
                              "--framer-font-weight": "700",
                              "--framer-letter-spacing": "-0.1px",
                              "--framer-line-height": "100%",
                              "--framer-text-alignment": "left",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: [/*#__PURE__*/_jsx("span", {
                              style: {
                                "--framer-font-size": "83px"
                              },
                              children: "Inspire."
                            }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                              style: {
                                "--framer-font-size": "83px"
                              },
                              children: "Everyone."
                            })]
                          })
                        }),
                        className: "framer-pirsj8",
                        fonts: ["GF;Inter-700"],
                        verticalAlignment: "top",
                        withExternalLayout: true
                      })
                    })]
                  }), /*#__PURE__*/_jsx(PropertyOverrides, {
                    breakpoint: baseVariant,
                    overrides: {
                      sfPBTBpOT: {
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsx("h1", {
                            style: {
                              "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                              "--framer-font-size": "17px",
                              "--framer-line-height": "155%",
                              "--framer-text-alignment": "left",
                              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                            },
                            children: "Popless is a tutoring platform for every kind of educator — and every kind of student — to learn, create, and define their own success."
                          })
                        })
                      }
                    },
                    children: /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("h1", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-size": "23px",
                            "--framer-line-height": "155%",
                            "--framer-text-alignment": "left",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Popless is a tutoring platform for every kind of educator — and every kind of student — to learn, create, and define their own success."
                        })
                      }),
                      className: "framer-1vmjzt",
                      fonts: ["GF;Inter-regular"],
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })
                  })]
                }), /*#__PURE__*/_jsx(Container, {
                  className: "framer-6km3si-container",
                  children: /*#__PURE__*/_jsx(AssetsButtonMain, {
                    background: "rgb(3, 104, 224)",
                    buttonBG: "rgb(0, 0, 0)",
                    height: "100%",
                    id: "t7y4xWQwl",
                    layoutId: "t7y4xWQwl",
                    style: {
                      height: "100%"
                    },
                    tap: tapctt1eq,
                    textColour: "rgb(255, 255, 255)",
                    title: "Get started",
                    variant: "5231:108745",
                    width: "100%"
                  })
                }), /*#__PURE__*/_jsx(Container, {
                  className: "framer-1yukddt-container",
                  children: /*#__PURE__*/_jsx(Intercom, {
                    appId: "lsvujawt",
                    height: "100%",
                    id: "pAE4JDmlH",
                    layoutId: "pAE4JDmlH",
                    style: {
                      height: "100%",
                      width: "100%"
                    },
                    width: "100%"
                  })
                })]
              })
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-1ikqup6-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  sfPBTBpOT: {
                    variant: "Zz_9kWOfb"
                  }
                },
                children: /*#__PURE__*/_jsx(FooterNew, {
                  height: "100%",
                  id: "DIXjLP9xp",
                  layoutId: "DIXjLP9xp",
                  poplessLink: poplessLink1hxdeaz,
                  style: {
                    width: "100%"
                  },
                  twitterLink: twitterLink1bj8fo6,
                  variant: "zyTRmFlly",
                  width: "100%"
                })
              })
            })]
          })
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-hvoAh [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-hvoAh .framer-1jimos6 { display: block; }", ".framer-hvoAh .framer-16hy4t9 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1920px; }", ".framer-hvoAh .framer-1kxdmj { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-hvoAh .framer-1h16pk9 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 18px; height: 100vh; justify-content: center; max-width: 100%; overflow: visible; padding: 0px 240px 0px 240px; position: relative; width: 100%; }", ".framer-hvoAh .framer-10myskv-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-hvoAh .framer-79qxle { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1440px; }", ".framer-hvoAh .framer-efggbp { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 8px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-hvoAh .framer-kyd1m0 { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 50%; word-break: break-word; word-wrap: break-word; }", ".framer-hvoAh .framer-pirsj8 { --framer-paragraph-spacing: 0px; flex: none; height: 178px; overflow: hidden; position: relative; white-space: pre-wrap; width: 50%; word-break: break-word; word-wrap: break-word; }", ".framer-hvoAh .framer-1vmjzt { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 40%; word-break: break-word; word-wrap: break-word; }", ".framer-hvoAh .framer-6km3si-container { flex: none; height: 40px; position: relative; width: auto; }", ".framer-hvoAh .framer-1yukddt-container { flex: none; height: 1px; position: relative; width: 1px; }", ".framer-hvoAh .framer-1ikqup6-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hvoAh .framer-16hy4t9, .framer-hvoAh .framer-1kxdmj, .framer-hvoAh .framer-1h16pk9, .framer-hvoAh .framer-79qxle, .framer-hvoAh .framer-efggbp { gap: 0px; } .framer-hvoAh .framer-16hy4t9 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-hvoAh .framer-16hy4t9 > :first-child, .framer-hvoAh .framer-1kxdmj > :first-child, .framer-hvoAh .framer-1h16pk9 > :first-child, .framer-hvoAh .framer-79qxle > :first-child, .framer-hvoAh .framer-efggbp > :first-child { margin-top: 0px; } .framer-hvoAh .framer-16hy4t9 > :last-child, .framer-hvoAh .framer-1kxdmj > :last-child, .framer-hvoAh .framer-1h16pk9 > :last-child, .framer-hvoAh .framer-79qxle > :last-child, .framer-hvoAh .framer-efggbp > :last-child { margin-bottom: 0px; } .framer-hvoAh .framer-1kxdmj > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-hvoAh .framer-1h16pk9 > * { margin: 0px; margin-bottom: calc(18px / 2); margin-top: calc(18px / 2); } .framer-hvoAh .framer-79qxle > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } .framer-hvoAh .framer-efggbp > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } }", "@media (min-width: 1920px) { .framer-hvoAh .hidden-16hy4t9 { display: none !important; } }", "@media (min-width: 1440px) and (max-width: 1919px) { .framer-hvoAh .hidden-1es1ua8 { display: none !important; } .framer-hvoAh .framer-16hy4t9 { width: 1440px; }}", "@media (min-width: 810px) and (max-width: 1439px) { .framer-hvoAh .hidden-59rpun { display: none !important; } .framer-hvoAh .framer-16hy4t9 { width: 810px; } .framer-hvoAh .framer-1h16pk9 { padding: 0px 80px 0px 80px; } .framer-hvoAh .framer-79qxle, .framer-hvoAh .framer-kyd1m0, .framer-hvoAh .framer-pirsj8 { width: 100%; } .framer-hvoAh .framer-1vmjzt { width: 75%; }}", "@media (max-width: 809px) { .framer-hvoAh .hidden-1ke8ub5 { display: none !important; } .framer-hvoAh .framer-16hy4t9 { width: 390px; } .framer-hvoAh .framer-1h16pk9 { padding: 0px 20px 0px 20px; } .framer-hvoAh .framer-79qxle { gap: 5px; width: 100%; } .framer-hvoAh .framer-kyd1m0 { width: 100%; } .framer-hvoAh .framer-pirsj8 { height: 106px; width: 100%; } .framer-hvoAh .framer-1vmjzt { width: 75%; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hvoAh .framer-79qxle { gap: 0px; } .framer-hvoAh .framer-79qxle > * { margin: 0px; margin-bottom: calc(5px / 2); margin-top: calc(5px / 2); } .framer-hvoAh .framer-79qxle > :first-child { margin-top: 0px; } .framer-hvoAh .framer-79qxle > :last-child { margin-bottom: 0px; } }}"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 * @framerIntrinsicHeight 1447
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 * @framerIntrinsicWidth 1920
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"AziPymTHJ":{"layout":["fixed","auto"]},"S_GY7Kvhs":{"layout":["fixed","auto"]},"sfPBTBpOT":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 */
const FramerJ_W7bxNYj = withCSS(Component, css);
export default FramerJ_W7bxNYj;
FramerJ_W7bxNYj.displayName = "Mission";
FramerJ_W7bxNYj.defaultProps = {
  height: 1447,
  width: 1920
};
addFonts(FramerJ_W7bxNYj, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/J_W7bxNYj:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/J_W7bxNYj:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/J_W7bxNYj:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...HeaderNavigationFonts, ...AssetsButtonMainFonts, ...IntercomFonts, ...FooterNewFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerJ_W7bxNYj",
      "slots": [],
      "annotations": {
        "framerResponsiveScreen": "",
        "framerContractVersion": "1",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"AziPymTHJ\":{\"layout\":[\"fixed\",\"auto\"]},\"S_GY7Kvhs\":{\"layout\":[\"fixed\",\"auto\"]},\"sfPBTBpOT\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerIntrinsicWidth": "1920",
        "framerIntrinsicHeight": "1447"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};