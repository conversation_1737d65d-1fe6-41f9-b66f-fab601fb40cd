const metadata = params => ({
  breakpoints: [{
    hash: "roxc3z",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "6g857d",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "183mh6c",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  robots: "noindex",
  title: "Popless | Get started as a tutor on the leading tutoring platform",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};