import{a as P,b as T,c as b}from"./chunk-AXDY7YJP.mjs";import{b as n}from"./chunk-VWWF2A63.mjs";import{H as S,K as l,N as F,k as L,o as f,p as A,q as h,t as k}from"./chunk-5F276QAW.mjs";var g,y=e=>{if(!g){let i=(r,a,c)=>c.get(r)?c.get(r)(a):null,o=new Map;o.set("bold",r=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",fill:"none",stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"24"}))),o.set("duotone",r=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",opacity:"0.2"}),e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",fill:"none",stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))),o.set("fill",()=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M218.8,103.7,138.8,31a16,16,0,0,0-21.6,0l-80,72.7A16,16,0,0,0,32,115.5v92.1a16.4,16.4,0,0,0,4,11A15.9,15.9,0,0,0,48,224H96a8,8,0,0,0,8-8V168a8,8,0,0,1,8-8h32a8,8,0,0,1,8,8v48a8,8,0,0,0,8,8h48a15.6,15.6,0,0,0,7.6-1.9A16.1,16.1,0,0,0,224,208V115.5A16,16,0,0,0,218.8,103.7Z"}))),o.set("light",r=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",fill:"none",stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"}))),o.set("thin",r=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",fill:"none",stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"8"}))),o.set("regular",r=>e.createElement(e.Fragment,null,e.createElement("path",{d:"M152,208V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v48a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V115.5a8.3,8.3,0,0,1,2.6-5.9l80-72.7a8,8,0,0,1,10.8,0l80,72.7a8.3,8.3,0,0,1,2.6,5.9V208a8,8,0,0,1-8,8H160A8,8,0,0,1,152,208Z",fill:"none",stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"})));let p=(r,a)=>i(r,a,o),s=e.forwardRef((r,a)=>e.createElement("g",{ref:a,...r},p(r.weight,r.color)));s.displayName="House",g=s}return g};var m=["Activity","AddressBook","Airplane","AirplaneInFlight","AirplaneLanding","AirplaneTakeoff","AirplaneTilt","Airplay","Alarm","Alien","AlignBottom","AlignBottomSimple","AlignCenterVertical","AlignLeft","AlignLeftSimple","AlignRight","AlignRightSimple","AlignTop","AlignTopSimple","Anchor","AnchorSimple","AndroidLogo","AngularLogo","Aperture","AppStoreLogo","AppWindow","AppleLogo","ApplePodcastsLogo","Archive","ArchiveBox","ArchiveTray","Armchair","ArrowArcLeft","ArrowArcRight","ArrowBendDownLeft","ArrowBendDownRight","ArrowBendLeftDown","ArrowBendLeftUp","ArrowBendRightDown","ArrowBendRightUp","ArrowBendUpLeft","ArrowBendUpRight","ArrowCircleDown","ArrowCircleDownLeft","ArrowCircleDownRight","ArrowCircleLeft","ArrowCircleRight","ArrowCircleUp","ArrowCircleUpLeft","ArrowCircleUpRight","ArrowClockwise","ArrowDown","ArrowDownLeft","ArrowDownRight","ArrowElbowDownLeft","ArrowElbowDownRight","ArrowElbowLeft","ArrowElbowLeftDown","ArrowElbowLeftUp","ArrowElbowRight","ArrowElbowRightDown","ArrowElbowRightUp","ArrowElbowUpLeft","ArrowElbowUpRight","ArrowFatDown","ArrowFatLeft","ArrowFatLineDown","ArrowFatLineLeft","ArrowFatLineRight","ArrowFatLineUp","ArrowFatLinesDown","ArrowFatLinesLeft","ArrowFatLinesRight","ArrowFatLinesUp","ArrowFatRight","ArrowFatUp","ArrowLeft","ArrowLineDown","ArrowLineDownLeft","ArrowLineDownRight","ArrowLineLeft","ArrowLineRight","ArrowLineUp","ArrowLineUpLeft","ArrowLineUpRight","ArrowRight","ArrowSquareDown","ArrowSquareDownLeft","ArrowSquareDownRight","ArrowSquareIn","ArrowSquareLeft","ArrowSquareOut","ArrowSquareRight","ArrowSquareUp","ArrowSquareUpLeft","ArrowSquareUpRight","ArrowUDownLeft","ArrowUDownRight","ArrowULeftDown","ArrowULeftUp","ArrowURightDown","ArrowURightUp","ArrowUUpLeft","ArrowUUpRight","ArrowUp","ArrowUpLeft","ArrowUpRight","ArrowsClockwise","ArrowsDownUp","ArrowsHorizontal","ArrowsIn","ArrowsInCardinal","ArrowsInLineVertical","ArrowsInSimple","ArrowsLeftRight","ArrowsOut","ArrowsOutCardinal","ArrowsOutSimple","ArrowsVertical","Article","ArticleMedium","ArticleNyTimes","Asterisk","AsteriskSimple","At","Atom","Baby","Backpack","Backspace","Bag","BagSimple","Balloon","Bandaids","Bank","Barbell","Barcode","Barricade","Baseball","Basketball","Bathtub","BatteryCharging","BatteryEmpty","BatteryFull","BatteryHigh","BatteryLow","BatteryMedium","BatteryPlus","BatteryWarning","Bed","BeerBottle","BehanceLogo","Bell","BellRinging","BellSimple","BellSimpleRinging","BellSimpleSlash","BellSimpleZ","BellSlash","BellZ","BezierCurve","Bicycle","Binoculars","Bird","Bluetooth","BluetoothConnected","BluetoothSlash","BluetoothX","Boat","Book","BookBookmark","BookOpen","Bookmark","BookmarkSimple","Bookmarks","BookmarksSimple","Books","BoundingBox","BracketsAngle","BracketsCurly","BracketsRound","BracketsSquare","Brain","Brandy","Briefcase","BriefcaseMetal","Broadcast","Browser","Browsers","Bug","BugBeetle","BugDroid","Buildings","Bus","Butterfly","Cactus","Cake","Calculator","Calendar","CalendarBlank","CalendarCheck","CalendarPlus","CalendarX","Camera","CameraRotate","CameraSlash","Campfire","Car","CarSimple","Cardholder","Cards","CaretCircleDoubleUp","CaretCircleDown","CaretCircleLeft","CaretCircleRight","CaretCircleUp","CaretDoubleDown","CaretDoubleLeft","CaretDoubleRight","CaretDoubleUp","CaretDown","CaretLeft","CaretRight","CaretUp","Cat","CellSignalFull","CellSignalHigh","CellSignalLow","CellSignalMedium","CellSignalNone","CellSignalSlash","CellSignalX","Chalkboard","ChalkboardSimple","ChalkboardTeacher","ChartBar","ChartBarHorizontal","ChartLine","ChartLineUp","ChartPie","ChartPieSlice","Chat","ChatCentered","ChatCenteredDots","ChatCenteredText","ChatCircle","ChatCircleDots","ChatCircleText","ChatDots","ChatTeardrop","ChatTeardropDots","ChatTeardropText","ChatText","Chats","ChatsCircle","ChatsTeardrop","Check","CheckCircle","CheckSquare","CheckSquareOffset","Checks","Circle","CircleDashed","CircleHalf","CircleHalfTilt","CircleNotch","CircleWavy","CircleWavyCheck","CircleWavyQuestion","CircleWavyWarning","CirclesFour","CirclesThree","CirclesThreePlus","Clipboard","ClipboardText","Clock","ClockAfternoon","ClockClockwise","ClosedCaptioning","Cloud","CloudArrowDown","CloudArrowUp","CloudCheck","CloudFog","CloudLightning","CloudMoon","CloudRain","CloudSlash","CloudSnow","CloudSun","Club","CoatHanger","Code","CodeSimple","CodepenLogo","CodesandboxLogo","Coffee","Coin","CoinVertical","Coins","Columns","Command","Compass","ComputerTower","Confetti","Cookie","CookingPot","Copy","CopySimple","Copyleft","Copyright","CornersIn","CornersOut","Cpu","CreditCard","Crop","Crosshair","CrosshairSimple","Crown","CrownSimple","Cube","CurrencyBtc","CurrencyCircleDollar","CurrencyCny","CurrencyDollar","CurrencyDollarSimple","CurrencyEth","CurrencyEur","CurrencyGbp","CurrencyInr","CurrencyJpy","CurrencyKrw","CurrencyKzt","CurrencyNgn","CurrencyRub","Cursor","CursorText","Cylinder","Database","Desktop","DesktopTower","Detective","DeviceMobile","DeviceMobileCamera","DeviceMobileSpeaker","DeviceTablet","DeviceTabletCamera","DeviceTabletSpeaker","Diamond","DiamondsFour","DiceFive","DiceFour","DiceOne","DiceSix","DiceThree","DiceTwo","Disc","DiscordLogo","Divide","Dog","Door","DotsNine","DotsSix","DotsSixVertical","DotsThree","DotsThreeCircle","DotsThreeOutline","DotsThreeVertical","Download","DownloadSimple","DribbbleLogo","Drop","DropHalf","DropHalfBottom","Ear","EarSlash","Egg","EggCrack","Eject","EjectSimple","Envelope","EnvelopeOpen","EnvelopeSimple","EnvelopeSimpleOpen","Equalizer","Equals","Eraser","Exam","Export","Eye","EyeClosed","EyeSlash","Eyedropper","EyedropperSample","Eyeglasses","FaceMask","FacebookLogo","Factory","Faders","FadersHorizontal","FastForward","FastForwardCircle","FigmaLogo","File","FileArrowDown","FileArrowUp","FileAudio","FileCloud","FileCode","FileCss","FileCsv","FileDoc","FileDotted","FileHtml","FileImage","FileJpg","FileJs","FileJsx","FileLock","FileMinus","FilePdf","FilePlus","FilePng","FilePpt","FileRs","FileSearch","FileText","FileTs","FileTsx","FileVideo","FileVue","FileX","FileXls","FileZip","Files","FilmScript","FilmSlate","FilmStrip","Fingerprint","FingerprintSimple","FinnTheHuman","Fire","FireSimple","FirstAid","FirstAidKit","Fish","FishSimple","Flag","FlagBanner","FlagCheckered","Flame","Flashlight","Flask","FloppyDisk","FloppyDiskBack","FlowArrow","Flower","FlowerLotus","FlyingSaucer","Folder","FolderDotted","FolderLock","FolderMinus","FolderNotch","FolderNotchMinus","FolderNotchOpen","FolderNotchPlus","FolderOpen","FolderPlus","FolderSimple","FolderSimpleDotted","FolderSimpleLock","FolderSimpleMinus","FolderSimplePlus","FolderSimpleStar","FolderSimpleUser","FolderStar","FolderUser","Folders","Football","ForkKnife","FrameCorners","FramerLogo","Function","Funnel","FunnelSimple","GameController","GasPump","Gauge","Gear","GearSix","GenderFemale","GenderIntersex","GenderMale","GenderNeuter","GenderNonbinary","GenderTransgender","Ghost","Gif","Gift","GitBranch","GitCommit","GitDiff","GitFork","GitMerge","GitPullRequest","GithubLogo","GitlabLogo","GitlabLogoSimple","Globe","GlobeHemisphereEast","GlobeHemisphereWest","GlobeSimple","GlobeStand","GoogleChromeLogo","GoogleLogo","GooglePhotosLogo","GooglePlayLogo","GooglePodcastsLogo","Gradient","GraduationCap","Graph","GridFour","Hamburger","Hand","HandEye","HandFist","HandGrabbing","HandPalm","HandPointing","HandSoap","HandWaving","Handbag","HandbagSimple","HandsClapping","Handshake","HardDrive","HardDrives","Hash","HashStraight","Headlights","Headphones","Headset","Heart","HeartBreak","HeartStraight","HeartStraightBreak","Heartbeat","Hexagon","HighlighterCircle","Horse","Hourglass","HourglassHigh","HourglassLow","HourglassMedium","HourglassSimple","HourglassSimpleHigh","HourglassSimpleLow","House","HouseLine","HouseSimple","IdentificationBadge","IdentificationCard","Image","ImageSquare","Infinity","Info","InstagramLogo","Intersect","Jeep","Kanban","Key","KeyReturn","Keyboard","Keyhole","Knife","Ladder","LadderSimple","Lamp","Laptop","Layout","Leaf","Lifebuoy","Lightbulb","LightbulbFilament","Lightning","LightningSlash","LineSegment","LineSegments","Link","LinkBreak","LinkSimple","LinkSimpleBreak","LinkSimpleHorizontal","LinkedinLogo","LinuxLogo","List","ListBullets","ListChecks","ListDashes","ListNumbers","ListPlus","Lock","LockKey","LockKeyOpen","LockLaminated","LockLaminatedOpen","LockOpen","LockSimple","LockSimpleOpen","MagicWand","Magnet","MagnetStraight","MagnifyingGlass","MagnifyingGlassMinus","MagnifyingGlassPlus","MapPin","MapPinLine","MapTrifold","MarkerCircle","Martini","MaskHappy","MaskSad","MathOperations","Medal","MediumLogo","Megaphone","MegaphoneSimple","MessengerLogo","Microphone","MicrophoneSlash","MicrophoneStage","MicrosoftExcelLogo","MicrosoftTeamsLogo","MicrosoftWordLogo","Minus","MinusCircle","Money","Monitor","MonitorPlay","Moon","MoonStars","Mountains","Mouse","MouseSimple","MusicNote","MusicNoteSimple","MusicNotes","MusicNotesPlus","MusicNotesSimple","NavigationArrow","Needle","Newspaper","NewspaperClipping","Note","NoteBlank","NotePencil","Notebook","Notepad","Notification","NumberCircleEight","NumberCircleFive","NumberCircleFour","NumberCircleNine","NumberCircleOne","NumberCircleSeven","NumberCircleSix","NumberCircleThree","NumberCircleTwo","NumberCircleZero","NumberEight","NumberFive","NumberFour","NumberNine","NumberOne","NumberSeven","NumberSix","NumberSquareEight","NumberSquareFive","NumberSquareFour","NumberSquareNine","NumberSquareOne","NumberSquareSeven","NumberSquareSix","NumberSquareThree","NumberSquareTwo","NumberSquareZero","NumberThree","NumberTwo","NumberZero","Nut","NyTimesLogo","Octagon","Option","Package","PaintBrush","PaintBrushBroad","PaintBrushHousehold","PaintBucket","PaintRoller","Palette","PaperPlane","PaperPlaneRight","PaperPlaneTilt","Paperclip","PaperclipHorizontal","Parachute","Password","Path","Pause","PauseCircle","PawPrint","Peace","Pen","PenNib","PenNibStraight","Pencil","PencilCircle","PencilLine","PencilSimple","PencilSimpleLine","Percent","Person","PersonSimple","PersonSimpleRun","PersonSimpleWalk","Perspective","Phone","PhoneCall","PhoneDisconnect","PhoneIncoming","PhoneOutgoing","PhoneSlash","PhoneX","PhosphorLogo","PianoKeys","PictureInPicture","Pill","PinterestLogo","Pinwheel","Pizza","Placeholder","Planet","Play","PlayCircle","Playlist","Plug","Plugs","PlugsConnected","Plus","PlusCircle","PlusMinus","PokerChip","PoliceCar","Polygon","Popcorn","Power","Prescription","Presentation","PresentationChart","Printer","Prohibit","ProhibitInset","ProjectorScreen","ProjectorScreenChart","PushPin","PushPinSimple","PushPinSimpleSlash","PushPinSlash","PuzzlePiece","QrCode","Question","Queue","Quotes","Radical","Radio","RadioButton","Rainbow","RainbowCloud","Receipt","Record","Rectangle","Recycle","RedditLogo","Repeat","RepeatOnce","Rewind","RewindCircle","Robot","Rocket","RocketLaunch","Rows","Rss","RssSimple","Rug","Ruler","Scales","Scan","Scissors","Screencast","ScribbleLoop","Scroll","Selection","SelectionAll","SelectionBackground","SelectionForeground","SelectionInverse","SelectionPlus","SelectionSlash","Share","ShareNetwork","Shield","ShieldCheck","ShieldCheckered","ShieldChevron","ShieldPlus","ShieldSlash","ShieldStar","ShieldWarning","ShoppingBag","ShoppingBagOpen","ShoppingCart","ShoppingCartSimple","Shower","Shuffle","ShuffleAngular","ShuffleSimple","Sidebar","SidebarSimple","SignIn","SignOut","Signpost","SimCard","SketchLogo","SkipBack","SkipBackCircle","SkipForward","SkipForwardCircle","Skull","SlackLogo","Sliders","SlidersHorizontal","Smiley","SmileyBlank","SmileyMeh","SmileyNervous","SmileySad","SmileySticker","SmileyWink","SmileyXEyes","SnapchatLogo","Snowflake","SoccerBall","SortAscending","SortDescending","Spade","Sparkle","SpeakerHigh","SpeakerLow","SpeakerNone","SpeakerSimpleHigh","SpeakerSimpleLow","SpeakerSimpleNone","SpeakerSimpleSlash","SpeakerSimpleX","SpeakerSlash","SpeakerX","Spinner","SpinnerGap","Spiral","SpotifyLogo","Square","SquareHalf","SquareHalfBottom","SquareLogo","SquaresFour","Stack","StackOverflowLogo","StackSimple","Stamp","Star","StarFour","StarHalf","Sticker","Stop","StopCircle","Storefront","Strategy","StripeLogo","Student","Suitcase","SuitcaseSimple","Sun","SunDim","SunHorizon","Sunglasses","Swap","Swatches","Sword","Syringe","TShirt","Table","Tabs","Tag","TagChevron","TagSimple","Target","Taxi","TelegramLogo","Television","TelevisionSimple","TennisBall","Terminal","TerminalWindow","TestTube","TextAa","TextAlignCenter","TextAlignJustify","TextAlignLeft","TextAlignRight","TextBolder","TextH","TextHFive","TextHFour","TextHOne","TextHSix","TextHThree","TextHTwo","TextIndent","TextItalic","TextOutdent","TextStrikethrough","TextT","TextUnderline","Textbox","Thermometer","ThermometerCold","ThermometerHot","ThermometerSimple","ThumbsDown","ThumbsUp","Ticket","TiktokLogo","Timer","ToggleLeft","ToggleRight","Toilet","ToiletPaper","Tote","ToteSimple","TrademarkRegistered","TrafficCone","TrafficSign","TrafficSignal","Train","TrainRegional","TrainSimple","Translate","Trash","TrashSimple","Tray","Tree","TreeEvergreen","TreeStructure","TrendDown","TrendUp","Triangle","Trophy","Truck","TwitchLogo","TwitterLogo","Umbrella","UmbrellaSimple","Upload","UploadSimple","User","UserCircle","UserCircleGear","UserCircleMinus","UserCirclePlus","UserFocus","UserGear","UserList","UserMinus","UserPlus","UserRectangle","UserSquare","UserSwitch","Users","UsersFour","UsersThree","Vault","Vibrate","VideoCamera","VideoCameraSlash","Vignette","Voicemail","Volleyball","Wall","Wallet","Warning","WarningCircle","WarningOctagon","Watch","WaveSawtooth","WaveSine","WaveSquare","WaveTriangle","Waves","Webcam","WhatsappLogo","Wheelchair","WifiHigh","WifiLow","WifiMedium","WifiNone","WifiSlash","WifiX","Wind","WindowsLogo","Wine","Wrench","X","XCircle","XSquare","YinYang","YoutubeLogo"],V="https://framer.com/m/phosphor-icons/",B=["thin","light","regular","bold","fill","duotone"],q=m.reduce((e,i)=>(e[i.toLowerCase()]=i,e),{});function t(e){let{color:i,selectByList:o,iconSearch:p,iconSelection:s,onClick:r,onMouseDown:a,onMouseUp:c,onMouseEnter:H,onMouseLeave:D,weight:M,mirrored:N}=e,u=f(!1),d=b(m,o,p,s,q),[C,w]=A(d==="Home"?y(h):null);async function U(){try{let E=await import(`${V}${d}.js@0.0.50`);u.current&&w(E.default(h))}catch{u.current&&w(null)}}L(()=>(u.current=!0,U(),()=>{u.current=!1}),[d]);let v=S.current()===S.canvas?n(P,{}):null;return n(k.div,{style:{display:"contents"},onClick:r,onMouseEnter:H,onMouseLeave:D,onMouseDown:a,onMouseUp:c,children:C?n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",style:{userSelect:"none",width:"100%",height:"100%",display:"inline-block",fill:i,flexShrink:0,cursor:r?"pointer":"auto",transform:N?"scale(-1, 1)":void 0},focusable:"false",color:i,children:n(C,{color:i,weight:M})}):v})}t.displayName="Phosphor";t.defaultProps={width:24,height:24,iconSelection:"House",iconSearch:"House",color:"#66F",selectByList:!0,weight:"regular",mirrored:!1};F(t,{selectByList:{type:l.Boolean,title:"Select",enabledTitle:"List",disabledTitle:"Search",defaultValue:t.defaultProps.selectByList},iconSelection:{type:l.Enum,options:m,defaultValue:t.defaultProps.iconSelection,title:"Name",hidden:({selectByList:e})=>!e,description:"Find every icon name on the [Phosphor site](https://phosphoricons.com/)"},iconSearch:{type:l.String,title:"Name",placeholder:"Menu, Wifi, Box\u2026",hidden:({selectByList:e})=>e},color:{type:l.Color,title:"Color",defaultValue:t.defaultProps.color},weight:{type:l.Enum,title:"Weight",optionTitles:B.map(e=>e.charAt(0).toUpperCase()+e.slice(1)),options:B,defaultValue:t.defaultProps.weight},mirrored:{type:l.Boolean,enabledTitle:"Yes",disabledTitle:"No",defaultValue:t.defaultProps.mirrored},...T});export{t as a};
//# sourceMappingURL=chunk-AOLOIBUT.mjs.map
