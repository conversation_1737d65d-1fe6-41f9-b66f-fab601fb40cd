// Generated by Fr<PERSON><PERSON> (fd34323)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, SVG, Text, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  Y36mg5IJf: {
    hover: true
  }
};
const cycleOrder = ["Y36mg5IJf", "vjgY5LON1", "mIIVyqeUM", "kTOjJiYbK", "QEaKm5hGj", "hLgGNrzO0", "DA2_NFxuE", "NZuFXp3mt", "ZmKXV3yPR", "vOkJ3G6Tc"];
const variantClassNames = {
  DA2_NFxuE: "framer-v-1e10kv6",
  hLgGNrzO0: "framer-v-a7f1ce",
  kTOjJiYbK: "framer-v-1qevzhd",
  mIIVyqeUM: "framer-v-1doy51y",
  NZuFXp3mt: "framer-v-115y6lx",
  QEaKm5hGj: "framer-v-sedfmz",
  vjgY5LON1: "framer-v-1v99ti8",
  vOkJ3G6Tc: "framer-v-1v7gowz",
  Y36mg5IJf: "framer-v-1o77pht",
  ZmKXV3yPR: "framer-v-b41fuk"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "48px - B": "vOkJ3G6Tc",
  "Logo Reversed": "mIIVyqeUM",
  "Logomark - Beta": "hLgGNrzO0",
  "Logomark - Reversed - Beta": "QEaKm5hGj",
  "Logomark - Reversed": "kTOjJiYbK",
  "Variant 8": "NZuFXp3mt",
  "Variant 9": "ZmKXV3yPR",
  Logo: "vjgY5LON1",
  Logomark: "Y36mg5IJf",
  Mobile: "DA2_NFxuE"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  },
  hLgGNrzO0: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "Y36mg5IJf",
  title: yMiJFcMkh = "Popless",
  tap: terIUUEwW,
  color: UF6dTPEKi = 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "Y36mg5IJf",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTapvthpry = activeVariantCallback(async (...args) => {
    if (terIUUEwW) {
      const res = await terIUUEwW(...args);
      if (res === false) return false;
    }
  });
  const isDisplayed1 = () => {
    if (["vjgY5LON1", "mIIVyqeUM", "ZmKXV3yPR", "vOkJ3G6Tc"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed2 = () => {
    if (["QEaKm5hGj", "hLgGNrzO0", "DA2_NFxuE", "NZuFXp3mt"].includes(baseVariant)) return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-su7oC", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: "data:framer/page-link,Urj0RhJ3n",
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-1o77pht", className)} framer-1pi3ej6`,
          "data-framer-name": "Logomark",
          "data-highlight": true,
          layoutDependency: layoutDependency,
          layoutId: "Y36mg5IJf",
          onTap: onTapvthpry,
          ref: ref,
          style: {
            opacity: 1,
            ...style
          },
          transition: transition,
          variants: {
            "Y36mg5IJf-hover": {
              opacity: .5
            }
          },
          ...addPropertyOverrides({
            "Y36mg5IJf-hover": {
              "data-framer-name": undefined
            },
            DA2_NFxuE: {
              "data-framer-name": "Mobile"
            },
            hLgGNrzO0: {
              "data-framer-name": "Logomark - Beta"
            },
            kTOjJiYbK: {
              "data-framer-name": "Logomark - Reversed"
            },
            mIIVyqeUM: {
              "data-framer-name": "Logo Reversed"
            },
            NZuFXp3mt: {
              "data-framer-name": "Variant 8"
            },
            QEaKm5hGj: {
              "data-framer-name": "Logomark - Reversed - Beta"
            },
            vjgY5LON1: {
              "data-framer-name": "Logo"
            },
            vOkJ3G6Tc: {
              "data-framer-name": "48px - B"
            },
            ZmKXV3yPR: {
              "data-framer-name": "Variant 9"
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(SVG, {
            className: "framer-1ip3699",
            "data-framer-name": "Logo",
            layout: "position",
            layoutDependency: layoutDependency,
            layoutId: "NFixWMWeJ",
            opacity: 1,
            radius: 0,
            svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="rgb(0,0,0)"></path><path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="rgb(0,0,0)"></path></svg>',
            svgContentId: 1216569769,
            transition: transition,
            withExternalLayout: true,
            ...addPropertyOverrides({
              kTOjJiYbK: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="#FFFFFF"></path><path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="#FFFFFF"></path></svg>',
                svgContentId: 673830931
              },
              mIIVyqeUM: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="#FFFFFF"></path><path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="#FFFFFF"></path></svg>',
                svgContentId: 673830931
              },
              NZuFXp3mt: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */"></path><path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */"></path></svg>',
                svgContentId: 3959465873
              },
              QEaKm5hGj: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M 12.9 31 L 5.7 26.2 C 5.44 26.025 5.23 25.785 5.09 25.505 C 4.95 25.225 4.884 24.913 4.9 24.6 L 4.9 1.9 C 4.897 1.701 4.949 1.505 5.051 1.334 C 5.152 1.163 5.299 1.023 5.475 0.93 C 5.65 0.837 5.849 0.794 6.047 0.806 C 6.246 0.819 6.437 0.886 6.6 1 L 13.8 5.8 C 14.06 5.975 14.27 6.215 14.41 6.495 C 14.55 6.775 14.616 7.087 14.6 7.4 L 14.6 30.2 C 14.582 30.389 14.516 30.57 14.407 30.726 C 14.298 30.881 14.151 31.005 13.979 31.086 C 13.807 31.167 13.617 31.201 13.428 31.186 C 13.239 31.171 13.057 31.107 12.9 31 Z" fill="#FFFFFF"></path><path d="M 25.5 18.7 L 18.3 13.9 C 18.04 13.725 17.83 13.485 17.69 13.205 C 17.55 12.925 17.484 12.613 17.5 12.3 L 17.5 1.9 C 17.497 1.701 17.549 1.505 17.651 1.334 C 17.752 1.163 17.899 1.023 18.075 0.93 C 18.25 0.837 18.448 0.794 18.647 0.806 C 18.846 0.819 19.037 0.886 19.2 1 L 26.4 5.8 C 26.66 5.975 26.87 6.215 27.01 6.495 C 27.15 6.775 27.216 7.087 27.2 7.4 L 27.2 17.8 C 27.193 17.995 27.135 18.184 27.031 18.35 C 26.928 18.515 26.782 18.65 26.61 18.741 C 26.438 18.832 26.244 18.877 26.049 18.869 C 25.855 18.862 25.665 18.804 25.5 18.7 Z" fill="#FFFFFF"></path></svg>',
                svgContentId: 673830931
              },
              vOkJ3G6Tc: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M 19.35 46.5 L 8.55 39.3 C 8.16 39.037 7.845 38.678 7.635 38.257 C 7.425 37.837 7.326 37.37 7.35 36.9 L 7.35 2.85 C 7.346 2.552 7.424 2.258 7.576 2.001 C 7.728 1.744 7.948 1.534 8.212 1.395 C 8.476 1.255 8.773 1.191 9.071 1.209 C 9.369 1.228 9.656 1.329 9.9 1.5 L 20.7 8.7 C 21.09 8.963 21.405 9.322 21.615 9.742 C 21.825 10.163 21.924 10.63 21.9 11.1 L 21.9 45.3 C 21.874 45.583 21.774 45.855 21.611 46.088 C 21.447 46.322 21.226 46.508 20.968 46.629 C 20.711 46.75 20.426 46.802 20.142 46.779 C 19.858 46.757 19.585 46.661 19.35 46.5 Z" fill="#000000"></path><path d="M 38.25 28.05 L 27.45 20.85 C 27.06 20.587 26.745 20.228 26.535 19.807 C 26.325 19.387 26.226 18.919 26.25 18.45 L 26.25 2.85 C 26.246 2.552 26.324 2.258 26.476 2.001 C 26.628 1.744 26.848 1.534 27.112 1.395 C 27.376 1.255 27.673 1.191 27.971 1.209 C 28.269 1.228 28.556 1.329 28.8 1.5 L 39.6 8.7 C 39.99 8.963 40.305 9.322 40.515 9.742 C 40.725 10.163 40.824 10.63 40.8 11.1 L 40.8 26.7 C 40.79 26.992 40.702 27.277 40.547 27.525 C 40.391 27.772 40.174 27.975 39.915 28.112 C 39.657 28.249 39.366 28.315 39.074 28.304 C 38.782 28.293 38.498 28.206 38.25 28.05 Z" fill="#000000"></path></svg>',
                svgContentId: 1175919191
              },
              ZmKXV3yPR: {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50"><path d="M 20.156 48.438 L 8.906 40.938 C 8.5 40.664 8.172 40.29 7.953 39.852 C 7.734 39.414 7.632 38.927 7.656 38.438 L 7.656 2.969 C 7.652 2.658 7.733 2.352 7.892 2.084 C 8.05 1.817 8.279 1.598 8.554 1.453 C 8.829 1.307 9.138 1.24 9.449 1.26 C 9.759 1.279 10.058 1.384 10.312 1.562 L 21.563 9.062 C 21.968 9.336 22.297 9.71 22.516 10.148 C 22.735 10.586 22.837 11.073 22.813 11.562 L 22.813 47.188 C 22.785 47.483 22.681 47.766 22.511 48.009 C 22.341 48.252 22.11 48.446 21.842 48.572 C 21.573 48.698 21.277 48.752 20.981 48.729 C 20.685 48.705 20.401 48.605 20.156 48.438 Z" fill="rgb(0,0,0)"></path><path d="M 39.844 29.219 L 28.594 21.719 C 28.188 21.445 27.859 21.071 27.64 20.633 C 27.421 20.195 27.319 19.708 27.344 19.219 L 27.344 2.969 C 27.339 2.658 27.421 2.352 27.579 2.084 C 27.737 1.817 27.967 1.598 28.241 1.453 C 28.516 1.307 28.826 1.24 29.136 1.26 C 29.446 1.279 29.745 1.384 30 1.562 L 41.25 9.062 C 41.656 9.336 41.984 9.71 42.203 10.148 C 42.422 10.586 42.525 11.073 42.5 11.562 L 42.5 27.813 C 42.489 28.117 42.398 28.413 42.236 28.671 C 42.074 28.93 41.848 29.141 41.578 29.283 C 41.309 29.426 41.007 29.495 40.702 29.483 C 40.398 29.472 40.102 29.381 39.844 29.219 Z" fill="rgb(0,0,0)"></path></svg>',
                svgContentId: 2182433370
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed1() && /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-7qhj7p",
            layoutDependency: layoutDependency,
            layoutId: "kRhg8Bjqx",
            transition: transition,
            children: [/*#__PURE__*/_jsx(Text, {
              __fromCanvasComponent: true,
              alignment: "center",
              className: "framer-16gi991",
              fonts: ["Inter-ExtraBold"],
              layoutDependency: layoutDependency,
              layoutId: "QXs7nskIp",
              rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Popless</span><br></span></span>",
              style: {
                "--framer-font-family": '"Inter-ExtraBold", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-style": "normal",
                "--framer-font-weight": 800,
                "--framer-letter-spacing": "-0.2px",
                "--framer-line-height": "1em",
                "--framer-link-hover-text-color": "#222",
                "--framer-text-alignment": "center",
                "--framer-text-color": UF6dTPEKi,
                "--framer-text-decoration": "none",
                "--framer-text-transform": "none"
              },
              text: yMiJFcMkh,
              transition: transition,
              variants: {
                kTOjJiYbK: {
                  "--framer-text-color": "rgb(255, 255, 255)"
                },
                QEaKm5hGj: {
                  "--framer-text-color": "rgb(255, 255, 255)"
                }
              },
              verticalAlignment: "top",
              withExternalLayout: true
            }), isDisplayed2() && /*#__PURE__*/_jsx(SVG, {
              className: "framer-iumx0f",
              "data-framer-name": "p_beta",
              intrinsicHeight: 9,
              intrinsicWidth: 24,
              layoutDependency: layoutDependency,
              layoutId: "oEUn2KoA6",
              svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23.96 8.85"><g data-name="Layer 2"><path d="M0 8.73V0h3.49A4 4 0 0 1 5.1.29a2.14 2.14 0 0 1 1 .79 2 2 0 0 1 .32 1.16 1.92 1.92 0 0 1-.2.89 1.7 1.7 0 0 1-.56.63 2.27 2.27 0 0 1-.81.35v.09a1.94 1.94 0 0 1 .93.28 1.92 1.92 0 0 1 .71.73 2.16 2.16 0 0 1 .27 1.1 2.24 2.24 0 0 1-.34 1.24 2.32 2.32 0 0 1-1 .86 3.88 3.88 0 0 1-1.65.32Zm1.85-5.08h1.36a1.62 1.62 0 0 0 .68-.13 1.18 1.18 0 0 0 .47-.38 1.08 1.08 0 0 0 .17-.6 1 1 0 0 0-.33-.76 1.44 1.44 0 0 0-1-.29H1.85Zm0 3.57h1.5a1.71 1.71 0 0 0 1.12-.3 1 1 0 0 0 .36-.79 1.17 1.17 0 0 0-.18-.64 1.15 1.15 0 0 0-.49-.43 1.68 1.68 0 0 0-.77-.16H1.85Zm11.61-1.8A4.3 4.3 0 0 0 13.23 4a2.9 2.9 0 0 0-.66-1 2.78 2.78 0 0 0-1-.62 3.38 3.38 0 0 0-1.21-.2 3.21 3.21 0 0 0-1.67.42A2.9 2.9 0 0 0 7.6 3.71a3.85 3.85 0 0 0-.39 1.77 3.92 3.92 0 0 0 .39 1.8 2.78 2.78 0 0 0 1.12 1.16 3.4 3.4 0 0 0 1.74.41 3.76 3.76 0 0 0 1.43-.25 2.53 2.53 0 0 0 1-.69 2.3 2.3 0 0 0 .52-1.06l-1.68-.11a1.06 1.06 0 0 1-.7.67 1.63 1.63 0 0 1-.55.09 1.52 1.52 0 0 1-.79-.19 1.29 1.29 0 0 1-.51-.55A1.68 1.68 0 0 1 9 5.92h4.46ZM9 4.79a1.36 1.36 0 0 1 .18-.64 1.45 1.45 0 0 1 1.92-.53 1.27 1.27 0 0 1 .47.48 1.31 1.31 0 0 1 .17.69Zm8.77 3.86-.39.1a4.14 4.14 0 0 1-.6.07 2.93 2.93 0 0 1-1.15-.18 1.6 1.6 0 0 1-.76-.64 2 2 0 0 1-.27-1V3.54h-.89V2.18h.89V.61h1.82v1.57h1.23v1.36h-1.23v3.18a.81.81 0 0 0 .07.38.39.39 0 0 0 .22.2.8.8 0 0 0 .32 0 1.06 1.06 0 0 0 .25 0h.2Zm2.51.2a2.67 2.67 0 0 1-1.12-.22 1.75 1.75 0 0 1-.77-.63 1.93 1.93 0 0 1-.29-1.1 2 2 0 0 1 .2-.9 1.73 1.73 0 0 1 .55-.59 2.5 2.5 0 0 1 .78-.34 5.65 5.65 0 0 1 .93-.17c.38 0 .69-.07.92-.11a1.46 1.46 0 0 0 .52-.18.37.37 0 0 0 .16-.31.8.8 0 0 0-.26-.63 1.08 1.08 0 0 0-.71-.22 1.23 1.23 0 0 0-.78.22 1 1 0 0 0-.39.54L18.33 4a2.33 2.33 0 0 1 .51-1 2.37 2.37 0 0 1 1-.68 3.82 3.82 0 0 1 1.37-.23 4.44 4.44 0 0 1 1.05.12 2.88 2.88 0 0 1 .89.4 2 2 0 0 1 .62.69 2.08 2.08 0 0 1 .22 1v4.43h-1.75v-.91a2.18 2.18 0 0 1-.42.54 2.09 2.09 0 0 1-.64.36 2.45 2.45 0 0 1-.9.13Zm.52-1.25a1.57 1.57 0 0 0 .7-.16A1.29 1.29 0 0 0 22 7a1.2 1.2 0 0 0 .17-.62v-.7a.83.83 0 0 1-.23.1l-.33.08-.37.07-.34.07a2.18 2.18 0 0 0-.56.15.87.87 0 0 0-.37.27.69.69 0 0 0-.13.43.64.64 0 0 0 .27.56 1.15 1.15 0 0 0 .69.19Z" data-name="Layer 1"/></g></svg>',
              transition: transition,
              withExternalLayout: true
            })]
          })]
        })
      })
    })
  });
});
const css = ['.framer-su7oC [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-su7oC * { box-sizing: border-box; }", ".framer-su7oC .framer-1pi3ej6 { display: block; }", ".framer-su7oC .framer-1o77pht { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-su7oC .framer-1ip3699 { flex: none; height: 32px; position: relative; width: 32px; }", ".framer-su7oC .framer-7qhj7p { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-su7oC .framer-16gi991 { flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }", ".framer-su7oC .framer-iumx0f { aspect-ratio: 2.6666666666666665 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 14px); position: relative; width: 38px; }", ".framer-su7oC .framer-v-1o77pht .framer-1o77pht { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC .framer-1o77pht, .framer-su7oC .framer-7qhj7p { gap: 0px; } .framer-su7oC .framer-1o77pht > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-su7oC .framer-1o77pht > :first-child, .framer-su7oC .framer-7qhj7p > :first-child { margin-left: 0px; } .framer-su7oC .framer-1o77pht > :last-child, .framer-su7oC .framer-7qhj7p > :last-child { margin-right: 0px; } .framer-su7oC .framer-7qhj7p > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } }", ".framer-su7oC.framer-v-1doy51y .framer-1o77pht { gap: 0px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-1doy51y .framer-1o77pht { gap: 0px; } .framer-su7oC.framer-v-1doy51y .framer-1o77pht > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-su7oC.framer-v-1doy51y .framer-1o77pht > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-1doy51y .framer-1o77pht > :last-child { margin-right: 0px; } }", ".framer-su7oC.framer-v-sedfmz .framer-7qhj7p, .framer-su7oC.framer-v-a7f1ce .framer-7qhj7p, .framer-su7oC.framer-v-1e10kv6 .framer-7qhj7p, .framer-su7oC.framer-v-115y6lx .framer-7qhj7p { align-content: center; align-items: center; gap: 5px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-sedfmz .framer-7qhj7p { gap: 0px; } .framer-su7oC.framer-v-sedfmz .framer-7qhj7p > * { margin: 0px; margin-left: calc(5px / 2); margin-right: calc(5px / 2); } .framer-su7oC.framer-v-sedfmz .framer-7qhj7p > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-sedfmz .framer-7qhj7p > :last-child { margin-right: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-a7f1ce .framer-7qhj7p { gap: 0px; } .framer-su7oC.framer-v-a7f1ce .framer-7qhj7p > * { margin: 0px; margin-left: calc(5px / 2); margin-right: calc(5px / 2); } .framer-su7oC.framer-v-a7f1ce .framer-7qhj7p > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-a7f1ce .framer-7qhj7p > :last-child { margin-right: 0px; } }", ".framer-su7oC.framer-v-1e10kv6 .framer-iumx0f { height: var(--framer-aspect-ratio-supported, 10px); width: 28px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-1e10kv6 .framer-7qhj7p { gap: 0px; } .framer-su7oC.framer-v-1e10kv6 .framer-7qhj7p > * { margin: 0px; margin-left: calc(5px / 2); margin-right: calc(5px / 2); } .framer-su7oC.framer-v-1e10kv6 .framer-7qhj7p > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-1e10kv6 .framer-7qhj7p > :last-child { margin-right: 0px; } }", ".framer-su7oC.framer-v-115y6lx .framer-iumx0f { height: var(--framer-aspect-ratio-supported, 11px); width: 28px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-115y6lx .framer-7qhj7p { gap: 0px; } .framer-su7oC.framer-v-115y6lx .framer-7qhj7p > * { margin: 0px; margin-left: calc(5px / 2); margin-right: calc(5px / 2); } .framer-su7oC.framer-v-115y6lx .framer-7qhj7p > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-115y6lx .framer-7qhj7p > :last-child { margin-right: 0px; } }", ".framer-su7oC.framer-v-b41fuk .framer-1ip3699 { height: 50px; width: 50px; }", ".framer-su7oC.framer-v-1v7gowz .framer-1o77pht { aspect-ratio: 1 / 1; gap: 0px; height: var(--framer-aspect-ratio-supported, 48px); width: 48px; }", ".framer-su7oC.framer-v-1v7gowz .framer-1ip3699 { height: 48px; width: 48px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-su7oC.framer-v-1v7gowz .framer-1o77pht { gap: 0px; } .framer-su7oC.framer-v-1v7gowz .framer-1o77pht > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-su7oC.framer-v-1v7gowz .framer-1o77pht > :first-child { margin-left: 0px; } .framer-su7oC.framer-v-1v7gowz .framer-1o77pht > :last-child { margin-right: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             * @framerIntrinsicHeight 32
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             * @framerIntrinsicWidth 111
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]},"vjgY5LON1":{"layout":["auto","auto"]},"mIIVyqeUM":{"layout":["auto","auto"]},"kTOjJiYbK":{"layout":["auto","auto"]},"QEaKm5hGj":{"layout":["auto","auto"]},"hLgGNrzO0":{"layout":["auto","auto"]},"DA2_NFxuE":{"layout":["auto","auto"]},"NZuFXp3mt":{"layout":["auto","auto"]},"ZmKXV3yPR":{"layout":["auto","auto"]},"vOkJ3G6Tc":{"layout":["fixed","fixed"]},"EuG_3L2F3":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             * @framerVariables {"yMiJFcMkh":"title","terIUUEwW":"tap","UF6dTPEKi":"color"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             */
const Framerxp2JJtRrF = withCSS(Component, css);
export default Framerxp2JJtRrF;
Framerxp2JJtRrF.displayName = "Assets/Logo";
Framerxp2JJtRrF.defaultProps = {
  height: 32,
  width: 111
};
addPropertyControls(Framerxp2JJtRrF, {
  variant: {
    options: ["Y36mg5IJf", "vjgY5LON1", "mIIVyqeUM", "kTOjJiYbK", "QEaKm5hGj", "hLgGNrzO0", "DA2_NFxuE", "NZuFXp3mt", "ZmKXV3yPR", "vOkJ3G6Tc"],
    optionTitles: ["Logomark", "Logo", "Logo Reversed", "Logomark - Reversed", "Logomark - Reversed - Beta", "Logomark - Beta", "Mobile", "Variant 8", "Variant 9", "48px - B"],
    title: "Variant",
    type: ControlType.Enum
  },
  yMiJFcMkh: {
    defaultValue: "Popless",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  terIUUEwW: {
    title: "Tap",
    type: ControlType.EventHandler
  },
  UF6dTPEKi: {
    defaultValue: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
    title: "Color",
    type: ControlType.Color
  }
});
addFonts(Framerxp2JJtRrF, []);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "Framerxp2JJtRrF",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]},\"vjgY5LON1\":{\"layout\":[\"auto\",\"auto\"]},\"mIIVyqeUM\":{\"layout\":[\"auto\",\"auto\"]},\"kTOjJiYbK\":{\"layout\":[\"auto\",\"auto\"]},\"QEaKm5hGj\":{\"layout\":[\"auto\",\"auto\"]},\"hLgGNrzO0\":{\"layout\":[\"auto\",\"auto\"]},\"DA2_NFxuE\":{\"layout\":[\"auto\",\"auto\"]},\"NZuFXp3mt\":{\"layout\":[\"auto\",\"auto\"]},\"ZmKXV3yPR\":{\"layout\":[\"auto\",\"auto\"]},\"vOkJ3G6Tc\":{\"layout\":[\"fixed\",\"fixed\"]},\"EuG_3L2F3\":{\"layout\":[\"auto\",\"auto\"]}}}",
        "framerIntrinsicWidth": "111",
        "framerVariables": "{\"yMiJFcMkh\":\"title\",\"terIUUEwW\":\"tap\",\"UF6dTPEKi\":\"color\"}",
        "framerIntrinsicHeight": "32"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./xp2JJtRrF.map