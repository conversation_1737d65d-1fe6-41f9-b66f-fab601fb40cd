import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useRef, useEffect } from "react";
import { addPropertyControls, ControlType, withCSS } from "framer";
import { motion } from "framer-motion";
import { containerStyles } from "https://framer.com/m/framer/default-utils.js@^0.45.0";
import { createWidget } from "https://cdn.skypack.dev/@typeform/embed@1.32.0";
import { emptyStateStyle, stateParagraphStyle, stateTitleStyle } from "https://framer.com/m/framer/integrations-styles.js@0.2.0";
const Widget = withCSS(function Widget({
  id,
  style,
  onSubmit,
  hideFooter,
  hideHeaders,
  ...props
}) {
  const container = useRef(null);
  useEffect(() => {
    if (container.current) {
      const widget = createWidget(id, {
        width: "100%",
        height: "100%",
        container: container.current,
        transitiveSearchParams: ["utm_source", "utm_medium", "utm_campaign"],
        onSubmit,
        hideFooter,
        hideHeaders
      });
      return () => {
        widget.unmount();
      };
    }
  }, [id, onSubmit, hideFooter, hideHeaders]);
  return /*#__PURE__*/_jsx("div", {
    style: style,
    className: "framer-typeform",
    ref: container,
    ...props
  });
}, [".framer-typeform div, .framer-typeform iframe { width: 100%; height: 100%; border: none !important; }"]); /**
                                                                                                               * TYPEFORM
                                                                                                               *
                                                                                                               * @framerIntrinsicWidth 800
                                                                                                               * @framerIntrinsicHeight 600
                                                                                                               *
                                                                                                               * @framerSupportedLayoutWidth fixed
                                                                                                               * @framerSupportedLayoutHeight fixed
                                                                                                               */
export default function Typeform({
  formId,
  style,
  onSubmit,
  hideFooter,
  hideHeaders,
  ...props
}) {
  function handleSubmit(e) {
    if (onSubmit !== undefined) {
      onSubmit(e);
    }
    if (globalThis.__send_framer_conversion__) {
      globalThis.__send_framer_conversion__("typeform");
    }
  }
  return formId ? /*#__PURE__*/_jsx(motion.div, {
    style: {
      ...containerStyles,
      ...style
    },
    ...props,
    children: /*#__PURE__*/_jsx(Widget, {
      id: formId,
      style: {
        width: "100%",
        height: "100%"
      },
      onSubmit: handleSubmit,
      hideFooter: hideFooter,
      hideHeaders: hideHeaders
    })
  }) : /*#__PURE__*/_jsxs(motion.div, {
    style: {
      ...emptyStateStyle,
      ...style
    },
    ...props,
    children: [/*#__PURE__*/_jsx("h1", {
      style: stateTitleStyle,
      children: "Typeform"
    }), /*#__PURE__*/_jsx("p", {
      style: stateParagraphStyle,
      children: "Set a form ID in the Properties."
    })]
  });
}
;
addPropertyControls(Typeform, {
  formId: {
    title: "ID",
    type: ControlType.String,
    placeholder: "12ABCD34",
    defaultValue: "R2s5BM",
    description: "Create a [Typeform](https://www.typeform.com/) account, add a form and copy its ID. [Learn more…](https://www.framer.com/sites/integrations/typeform/)"
  },
  hideFooter: {
    title: "Footer",
    type: ControlType.Boolean,
    enabledTitle: "Hide",
    disabledTitle: "Show",
    defaultValue: false
  },
  hideHeaders: {
    title: "Headers",
    type: ControlType.Boolean,
    enabledTitle: "Hide",
    disabledTitle: "Show",
    defaultValue: false
  },
  onSubmit: {
    type: ControlType.EventHandler
  }
});
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "Typeform",
      "slots": [],
      "annotations": {
        "framerSupportedLayoutWidth": "fixed",
        "framerIntrinsicHeight": "600",
        "framerSupportedLayoutHeight": "fixed",
        "framerIntrinsicWidth": "800",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./Typeform.map