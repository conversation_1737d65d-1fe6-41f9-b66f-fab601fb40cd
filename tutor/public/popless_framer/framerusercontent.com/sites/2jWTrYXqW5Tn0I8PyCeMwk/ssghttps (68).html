// Generated by <PERSON><PERSON>r (716dd6f)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Image, Link, RichText, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import * as sharedStyle from "https://framerusercontent.com/modules/0GyZj7YJNmzmKbQefHa9/v0OH5ol5DbMExnBCvDu9/DFB6IUEyR.js";
const cycleOrder = ["xNf5mlzS1", "eOYdUdpde"];
const variantClassNames = {
  eOYdUdpde: "framer-v-1cgd1gw",
  xNf5mlzS1: "framer-v-15zmlvb"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Default: "xNf5mlzS1",
  Hover: "eOYdUdpde"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
function toResponsiveImage_194x2gw(value) {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? {
    src: value
  } : undefined;
}
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "xNf5mlzS1",
  link: x54YDyvfV,
  date: psedFjEhD = "date",
  title: QR3vnvsfW = "long title to test all of this will go here",
  image: MgxsmugkC,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "xNf5mlzS1",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onMouseEnterqr2lbh = activeVariantCallback(async (...args) => {
    setVariant("eOYdUdpde");
  });
  const onMouseLeaveyc491f = activeVariantCallback(async (...args) => {
    setVariant("xNf5mlzS1");
  });
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-RgI2W", sharedStyle.className, classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: x54YDyvfV,
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-15zmlvb", className)} framer-104dlfh`,
          "data-framer-name": "Default",
          "data-highlight": true,
          layoutDependency: layoutDependency,
          layoutId: "xNf5mlzS1",
          onMouseEnter: onMouseEnterqr2lbh,
          ref: ref,
          style: {
            ...style
          },
          transition: transition,
          ...addPropertyOverrides({
            eOYdUdpde: {
              "data-framer-name": "Hover",
              onMouseLeave: onMouseLeaveyc491f
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(motion.div, {
            className: "framer-12flsli",
            "data-framer-name": "Background",
            layoutDependency: layoutDependency,
            layoutId: "zPnI9G8dZ",
            style: {
              backgroundColor: "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))",
              borderBottomLeftRadius: 10,
              borderBottomRightRadius: 10,
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10
            },
            transition: transition,
            variants: {
              eOYdUdpde: {
                backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))"
              }
            }
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1pjbfnh",
            "data-framer-name": "Container",
            layoutDependency: layoutDependency,
            layoutId: "APlSjYql3",
            transition: transition,
            children: [/*#__PURE__*/_jsx(Image, {
              background: {
                alt: "",
                fit: "fill",
                sizes: "60px",
                ...toResponsiveImage_194x2gw(MgxsmugkC)
              },
              className: "framer-9t318h",
              "data-framer-name": "Image",
              layoutDependency: layoutDependency,
              layoutId: "RUSKtmUJC",
              style: {
                borderBottomLeftRadius: 8,
                borderBottomRightRadius: 8,
                borderTopLeftRadius: 8,
                borderTopRightRadius: 8
              },
              transition: transition
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-30nio6",
              "data-framer-name": "Copy",
              layoutDependency: layoutDependency,
              layoutId: "B_voExaFn",
              transition: transition,
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.h2, {
                    style: {
                      "--font-selector": "SW50ZXItTWVkaXVt",
                      "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                      "--framer-font-size": "15px",
                      "--framer-font-weight": "500",
                      "--framer-line-height": "125%",
                      "--framer-text-alignment": "left"
                    },
                    children: "Lila Downs shares her magical Oaxaca getaway for Day of the Dead"
                  })
                }),
                className: "framer-em7g8i",
                "data-framer-name": "Title",
                fonts: ["Inter-Medium"],
                layoutDependency: layoutDependency,
                layoutId: "wlM_5L5VQ",
                style: {
                  "--framer-paragraph-spacing": "0px"
                },
                text: QR3vnvsfW,
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    className: "framer-styles-preset-1dhfv1g",
                    "data-styles-preset": "DFB6IUEyR",
                    style: {
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Content"
                  })
                }),
                className: "framer-1dntgw6",
                "data-framer-name": "Date",
                layoutDependency: layoutDependency,
                layoutId: "Q2TzftEFb",
                style: {
                  "--extracted-r6o4lv": "rgb(0, 0, 0)",
                  "--framer-paragraph-spacing": "0px"
                },
                text: psedFjEhD,
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            })]
          })]
        })
      })
    })
  });
});
const css = ['.framer-RgI2W [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-RgI2W * { box-sizing: border-box; }", ".framer-RgI2W .framer-104dlfh { display: block; }", ".framer-RgI2W .framer-15zmlvb { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; text-decoration: none; width: 356px; }", ".framer-RgI2W .framer-12flsli { bottom: -8px; flex: none; left: -8px; position: absolute; right: -8px; top: -8px; z-index: 1; }", ".framer-RgI2W .framer-1pjbfnh { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; z-index: 1; }", ".framer-RgI2W .framer-9t318h { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 60px); position: relative; width: 60px; }", ".framer-RgI2W .framer-30nio6 { align-content: flex-start; align-items: flex-start; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-RgI2W .framer-em7g8i, .framer-RgI2W .framer-1dntgw6 { flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-RgI2W .framer-15zmlvb, .framer-RgI2W .framer-1pjbfnh, .framer-RgI2W .framer-30nio6 { gap: 0px; } .framer-RgI2W .framer-15zmlvb > *, .framer-RgI2W .framer-1pjbfnh > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-RgI2W .framer-15zmlvb > :first-child, .framer-RgI2W .framer-1pjbfnh > :first-child { margin-left: 0px; } .framer-RgI2W .framer-15zmlvb > :last-child, .framer-RgI2W .framer-1pjbfnh > :last-child { margin-right: 0px; } .framer-RgI2W .framer-30nio6 > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-RgI2W .framer-30nio6 > :first-child { margin-top: 0px; } .framer-RgI2W .framer-30nio6 > :last-child { margin-bottom: 0px; } }", ...sharedStyle.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * @framerIntrinsicHeight 60
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * @framerIntrinsicWidth 356
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"eOYdUdpde":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      * @framerVariables {"x54YDyvfV":"link","psedFjEhD":"date","QR3vnvsfW":"title","MgxsmugkC":"image"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      */
const Framerd4Tb0VIGz = withCSS(Component, css);
export default Framerd4Tb0VIGz;
Framerd4Tb0VIGz.displayName = "Blog / Blog Featured";
Framerd4Tb0VIGz.defaultProps = {
  height: 60,
  width: 356
};
addPropertyControls(Framerd4Tb0VIGz, {
  variant: {
    options: ["xNf5mlzS1", "eOYdUdpde"],
    optionTitles: ["Default", "Hover"],
    title: "Variant",
    type: ControlType.Enum
  },
  x54YDyvfV: {
    title: "Link",
    type: ControlType.Link
  },
  psedFjEhD: {
    defaultValue: "date",
    displayTextArea: false,
    title: "Date",
    type: ControlType.String
  },
  QR3vnvsfW: {
    defaultValue: "long title to test all of this will go here",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  MgxsmugkC: {
    title: "Image",
    type: ControlType.ResponsiveImage
  }
});
addFonts(Framerd4Tb0VIGz, [...sharedStyle.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "Framerd4Tb0VIGz",
      "slots": [],
      "annotations": {
        "framerVariables": "{\"x54YDyvfV\":\"link\",\"psedFjEhD\":\"date\",\"QR3vnvsfW\":\"title\",\"MgxsmugkC\":\"image\"}",
        "framerContractVersion": "1",
        "framerIntrinsicHeight": "60",
        "framerIntrinsicWidth": "356",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"eOYdUdpde\":{\"layout\":[\"fixed\",\"auto\"]}}}"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./d4Tb0VIGz.map