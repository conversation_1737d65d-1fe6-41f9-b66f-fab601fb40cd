import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors([]);
export const fonts = [];
export const css = ['.framer-uiysn .framer-styles-preset-1utq9v6:not(.rich-text-wrapper), .framer-uiysn .framer-styles-preset-1utq9v6.rich-text-wrapper a { --framer-link-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0); --framer-link-text-decoration: underline; --framer-link-hover-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0) /* {"name":"Blue/Default"} */; --framer-link-hover-text-decoration: underline; }'];
export const className = "framer-uiysn";
export const __FramerMetadata__ = {
  "exports": {
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};