// Generated by <PERSON><PERSON><PERSON> (b35efa8)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, SVG, Text, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  "5231:108745": {
    hover: true,
    pressed: true
  },
  biGcS_1Nr: {
    hover: true,
    pressed: true
  },
  dUkIdSVGd: {
    hover: true
  },
  J6qwcVywR: {
    hover: true,
    pressed: true
  },
  jaSanL1Ld: {
    hover: true,
    pressed: true
  },
  JmEDWj0ys: {
    hover: true
  },
  m7ICNTpLf: {
    hover: true
  },
  QQtnHKQQd: {
    hover: true,
    pressed: true
  },
  wAL4axq0N: {
    hover: true
  },
  yD4vrs0op: {
    hover: true,
    pressed: true
  }
};
const cycleOrder = ["5231:108745", "J6qwcVywR", "NIKcshCiT", "QQtnHKQQd", "jaSanL1Ld", "yD4vrs0op", "wAL4axq0N", "biGcS_1Nr", "w5a5PqQdU", "m7ICNTpLf", "JmEDWj0ys", "dUkIdSVGd"];
const variantClassNames = {
  "5231:108745": "framer-v-1e84z8s",
  biGcS_1Nr: "framer-v-1nr57ge",
  dUkIdSVGd: "framer-v-14kln6o",
  J6qwcVywR: "framer-v-milg9a",
  jaSanL1Ld: "framer-v-jycvde",
  JmEDWj0ys: "framer-v-1rm2apr",
  m7ICNTpLf: "framer-v-1apr27j",
  NIKcshCiT: "framer-v-1erbgvf",
  QQtnHKQQd: "framer-v-4ux8gn",
  w5a5PqQdU: "framer-v-14cxhsk",
  wAL4axq0N: "framer-v-hcqxnr",
  yD4vrs0op: "framer-v-1oqroif"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Button - White": "biGcS_1Nr",
  "Get Started": "wAL4axq0N",
  "Job Opening": "yD4vrs0op",
  "Line - Black Hover": "JmEDWj0ys",
  "Line - White": "m7ICNTpLf",
  "Round Mobile": "jaSanL1Ld",
  "Solid - Round Bottom": "NIKcshCiT",
  Careers: "w5a5PqQdU",
  Green: "dUkIdSVGd",
  Line: "J6qwcVywR",
  Round: "QQtnHKQQd",
  Solid: "5231:108745"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "5231:108745",
  title: KvumNH_7m = " asd asd as dsda",
  background: NGWbmCoOo = "rgb(3, 104, 224)",
  tap: OXtioU_0I,
  buttonBG: Ii0PsZPlj = "rgb(0, 0, 0)",
  textColour: vh337UjuQ = "rgb(255, 255, 255)",
  link: GdcaFmgrg,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "5231:108745",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTap4mdqj6 = activeVariantCallback(async (...args) => {
    if (OXtioU_0I) {
      const res = await OXtioU_0I(...args);
      if (res === false) return false;
    }
  });
  const isDisplayed = () => {
    if (["QQtnHKQQd-hover", "QQtnHKQQd-pressed", "jaSanL1Ld-hover", "jaSanL1Ld-pressed"].includes(gestureVariant)) return true;
    if (["QQtnHKQQd", "jaSanL1Ld"].includes(baseVariant)) return true;
    return false;
  };
  const defaultLayoutId = React.useId();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-cKThw", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: GdcaFmgrg,
        openInNewTab: false,
        ...addPropertyOverrides({
          jaSanL1Ld: {
            href: "data:framer/page-link,gMU7brHGJ"
          },
          QQtnHKQQd: {
            href: "data:framer/page-link,gMU7brHGJ"
          }
        }, baseVariant, gestureVariant),
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-1e84z8s", className)} framer-4srpbw`,
          "data-framer-name": "Solid",
          "data-highlight": true,
          layoutDependency: layoutDependency,
          layoutId: "5231:108745",
          onTap: onTap4mdqj6,
          ref: ref,
          style: {
            "--border-bottom-width": "0px",
            "--border-color": "rgba(0, 0, 0, 0)",
            "--border-left-width": "0px",
            "--border-right-width": "0px",
            "--border-style": "solid",
            "--border-top-width": "0px",
            backgroundColor: "rgb(0, 0, 0)",
            borderBottomLeftRadius: 6,
            borderBottomRightRadius: 6,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6,
            boxShadow: "0px 2px 4px 0px rgba(0, 0, 0, 0.15)",
            opacity: 1,
            ...style
          },
          transition: transition,
          variants: {
            "5231:108745-hover": {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
            },
            "5231:108745-pressed": {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
            },
            "biGcS_1Nr-hover": {
              backgroundColor: "rgb(247, 247, 247)"
            },
            "biGcS_1Nr-pressed": {
              backgroundColor: "rgb(247, 247, 247)"
            },
            "J6qwcVywR-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))",
              boxShadow: "none"
            },
            "J6qwcVywR-pressed": {
              backgroundColor: "var(--token-cd156118-158a-47d3-8fb6-822a4bbc99ee, rgb(224, 225, 227))",
              boxShadow: "none"
            },
            "jaSanL1Ld-hover": {
              boxShadow: "none",
              opacity: .8
            },
            "jaSanL1Ld-pressed": {
              boxShadow: "none",
              opacity: .6
            },
            "JmEDWj0ys-hover": {
              backgroundColor: "rgb(0, 0, 0)",
              boxShadow: "none"
            },
            "m7ICNTpLf-hover": {
              backgroundColor: "rgb(255, 255, 255)",
              boxShadow: "none"
            },
            "QQtnHKQQd-hover": {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
              boxShadow: "none",
              opacity: .8
            },
            "QQtnHKQQd-pressed": {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
              boxShadow: "none",
              opacity: .6
            },
            "wAL4axq0N-hover": {
              boxShadow: "none"
            },
            "yD4vrs0op-hover": {
              backgroundColor: "var(--token-cd156118-158a-47d3-8fb6-822a4bbc99ee, rgb(224, 225, 227))",
              boxShadow: "none"
            },
            "yD4vrs0op-pressed": {
              backgroundColor: "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))",
              boxShadow: "none"
            },
            biGcS_1Nr: {
              backgroundColor: "rgb(255, 255, 255)"
            },
            dUkIdSVGd: {
              backgroundColor: "rgb(41, 71, 48)"
            },
            J6qwcVywR: {
              "--border-bottom-width": "1px",
              "--border-color": 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
              "--border-left-width": "1px",
              "--border-right-width": "1px",
              "--border-style": "solid",
              "--border-top-width": "1px",
              backgroundColor: "rgb(255, 255, 255)",
              boxShadow: "none"
            },
            jaSanL1Ld: {
              backgroundColor: NGWbmCoOo,
              borderBottomLeftRadius: 100,
              borderBottomRightRadius: 100,
              borderTopLeftRadius: 100,
              borderTopRightRadius: 100,
              boxShadow: "none"
            },
            JmEDWj0ys: {
              "--border-bottom-width": "1px",
              "--border-color": 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
              "--border-left-width": "1px",
              "--border-right-width": "1px",
              "--border-style": "solid",
              "--border-top-width": "1px",
              backgroundColor: "rgb(255, 255, 255)",
              boxShadow: "none"
            },
            m7ICNTpLf: {
              "--border-bottom-width": "1px",
              "--border-color": "rgb(255, 255, 255)",
              "--border-left-width": "1px",
              "--border-right-width": "1px",
              "--border-style": "solid",
              "--border-top-width": "1px",
              boxShadow: "none"
            },
            NIKcshCiT: {
              backgroundColor: NGWbmCoOo,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0
            },
            QQtnHKQQd: {
              backgroundColor: NGWbmCoOo,
              borderBottomLeftRadius: 100,
              borderBottomRightRadius: 100,
              borderTopLeftRadius: 100,
              borderTopRightRadius: 100,
              boxShadow: "none"
            },
            w5a5PqQdU: {
              boxShadow: "none"
            },
            wAL4axq0N: {
              backgroundColor: Ii0PsZPlj,
              borderBottomLeftRadius: 4,
              borderBottomRightRadius: 4,
              borderTopLeftRadius: 4,
              borderTopRightRadius: 4,
              boxShadow: "none"
            },
            yD4vrs0op: {
              backgroundColor: "var(--token-a1e13941-d14a-4692-855b-e794eebdfa6f, rgb(229, 232, 232))",
              borderBottomLeftRadius: 7,
              borderBottomRightRadius: 7,
              borderTopLeftRadius: 7,
              borderTopRightRadius: 7,
              boxShadow: "none"
            }
          },
          ...addPropertyOverrides({
            "5231:108745-hover": {
              "data-framer-name": undefined
            },
            "5231:108745-pressed": {
              "data-framer-name": undefined
            },
            "biGcS_1Nr-hover": {
              "data-framer-name": undefined
            },
            "biGcS_1Nr-pressed": {
              "data-framer-name": undefined
            },
            "dUkIdSVGd-hover": {
              "data-framer-name": undefined
            },
            "J6qwcVywR-hover": {
              "data-framer-name": undefined
            },
            "J6qwcVywR-pressed": {
              "data-framer-name": undefined
            },
            "jaSanL1Ld-hover": {
              "data-framer-name": undefined,
              "data-highlight": undefined,
              onTap: undefined
            },
            "jaSanL1Ld-pressed": {
              "data-framer-name": undefined,
              "data-highlight": undefined,
              onTap: undefined
            },
            "JmEDWj0ys-hover": {
              "data-framer-name": undefined
            },
            "m7ICNTpLf-hover": {
              "data-framer-name": undefined
            },
            "QQtnHKQQd-hover": {
              "data-framer-name": undefined,
              "data-highlight": undefined,
              onTap: undefined
            },
            "QQtnHKQQd-pressed": {
              "data-framer-name": undefined,
              "data-highlight": undefined,
              onTap: undefined
            },
            "wAL4axq0N-hover": {
              "data-framer-name": undefined
            },
            "yD4vrs0op-hover": {
              "data-framer-name": undefined
            },
            "yD4vrs0op-pressed": {
              "data-framer-name": undefined
            },
            biGcS_1Nr: {
              "data-framer-name": "Button - White"
            },
            dUkIdSVGd: {
              "data-framer-name": "Green"
            },
            J6qwcVywR: {
              "data-border": true,
              "data-framer-name": "Line"
            },
            jaSanL1Ld: {
              "data-framer-name": "Round Mobile",
              "data-highlight": undefined,
              onTap: undefined
            },
            JmEDWj0ys: {
              "data-border": true,
              "data-framer-name": "Line - Black Hover"
            },
            m7ICNTpLf: {
              "data-border": true,
              "data-framer-name": "Line - White"
            },
            NIKcshCiT: {
              "data-framer-name": "Solid - Round Bottom"
            },
            QQtnHKQQd: {
              "data-framer-name": "Round",
              "data-highlight": undefined,
              onTap: undefined
            },
            w5a5PqQdU: {
              "data-framer-name": "Careers"
            },
            wAL4axq0N: {
              "data-framer-name": "Get Started"
            },
            yD4vrs0op: {
              "data-framer-name": "Job Opening"
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(Text, {
            __fromCanvasComponent: true,
            __link: "",
            alignment: "center",
            className: "framer-338jv4",
            "data-framer-name": "Button",
            fonts: ["GF;Inter-regular"],
            layoutDependency: layoutDependency,
            layoutId: "I5231:108745;4753:88554",
            rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''> asd asd as dsda</span><br></span></span>",
            style: {
              "--framer-font-family": '"Inter", sans-serif',
              "--framer-font-size": "16px",
              "--framer-font-style": "normal",
              "--framer-font-weight": 400,
              "--framer-letter-spacing": "0px",
              "--framer-line-height": "16px",
              "--framer-text-alignment": "center",
              "--framer-text-color": "rgb(255, 255, 255)",
              "--framer-text-decoration": "none",
              "--framer-text-transform": "none"
            },
            text: KvumNH_7m,
            transition: transition,
            variants: {
              "JmEDWj0ys-hover": {
                "--framer-text-color": "rgb(255, 255, 255)"
              },
              "m7ICNTpLf-hover": {
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
              },
              biGcS_1Nr: {
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
              },
              J6qwcVywR: {
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
              },
              jaSanL1Ld: {
                "--framer-font-size": "18px",
                "--framer-font-weight": 500,
                "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
              },
              JmEDWj0ys: {
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
              },
              QQtnHKQQd: {
                "--framer-font-size": "24px",
                "--framer-font-weight": 500,
                "--framer-text-color": "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))"
              },
              wAL4axq0N: {
                "--framer-font-weight": 500,
                "--framer-line-height": "24px",
                "--framer-text-color": vh337UjuQ
              },
              yD4vrs0op: {
                "--framer-font-weight": 600,
                "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
              }
            },
            verticalAlignment: "center",
            withExternalLayout: true,
            ...addPropertyOverrides({
              jaSanL1Ld: {
                fonts: ["GF;Inter-500"]
              },
              QQtnHKQQd: {
                fonts: ["GF;Inter-500"]
              },
              wAL4axq0N: {
                fonts: ["GF;Inter-500"]
              },
              yD4vrs0op: {
                fonts: ["GF;Inter-600"]
              }
            }, baseVariant, gestureVariant)
          }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
            className: "framer-1f0im9j",
            layoutDependency: layoutDependency,
            layoutId: "I82QCJwgF",
            transition: transition,
            children: /*#__PURE__*/_jsx(SVG, {
              className: "framer-b3kzs",
              "data-framer-name": "chevron",
              layout: "position",
              layoutDependency: layoutDependency,
              layoutId: "mKiaYMA3M",
              opacity: 1,
              radius: 0,
              svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 22 22"><path d="M 6.769 19.434 L 15.86 11.252 L 6.769 3.071" fill="transparent" stroke-width="1.38" stroke="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
              svgContentId: 3222367310,
              transition: transition,
              withExternalLayout: true,
              ...addPropertyOverrides({
                jaSanL1Ld: {
                  svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 18 18"><path d="M 5.86 14.752 L 11.86 9.252 L 5.86 3.752" fill="transparent" stroke-width="2" stroke="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
                  svgContentId: 3153607373
                },
                QQtnHKQQd: {
                  svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 22 22"><path d="M 6.769 19.434 L 15.86 11.252 L 6.769 3.071" fill="transparent" stroke-width="2" stroke="var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255)) /* {&quot;name&quot;:&quot;White&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
                  svgContentId: 2108223669
                }
              }, baseVariant, gestureVariant)
            })
          })]
        })
      })
    })
  });
});
const css = ['.framer-cKThw [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-cKThw * { box-sizing: border-box; }", ".framer-cKThw .framer-4srpbw { display: block; }", ".framer-cKThw .framer-1e84z8s { align-content: center; align-items: center; cursor: pointer; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: center; overflow: visible; padding: 16px 20px 16px 20px; position: relative; text-decoration: none; width: min-content; }", ".framer-cKThw .framer-338jv4 { flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }", ".framer-cKThw .framer-1f0im9j { align-content: center; align-items: center; align-self: stretch; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-cKThw .framer-b3kzs { flex: none; height: 22px; position: relative; width: 22px; }", ".framer-cKThw .framer-v-1e84z8s .framer-1e84z8s, .framer-cKThw .framer-v-milg9a .framer-1e84z8s, .framer-cKThw .framer-v-4ux8gn .framer-1e84z8s, .framer-cKThw .framer-v-jycvde .framer-1e84z8s, .framer-cKThw .framer-v-1oqroif .framer-1e84z8s, .framer-cKThw .framer-v-hcqxnr .framer-1e84z8s, .framer-cKThw .framer-v-1nr57ge .framer-1e84z8s, .framer-cKThw .framer-v-1apr27j .framer-1e84z8s, .framer-cKThw .framer-v-1rm2apr .framer-1e84z8s, .framer-cKThw .framer-v-14kln6o .framer-1e84z8s { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-cKThw .framer-1e84z8s, .framer-cKThw .framer-1f0im9j { gap: 0px; } .framer-cKThw .framer-1e84z8s > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-cKThw .framer-1e84z8s > :first-child, .framer-cKThw .framer-1f0im9j > :first-child { margin-left: 0px; } .framer-cKThw .framer-1e84z8s > :last-child, .framer-cKThw .framer-1f0im9j > :last-child { margin-right: 0px; } .framer-cKThw .framer-1f0im9j > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", ".framer-cKThw.framer-v-milg9a .framer-1e84z8s, .framer-cKThw.framer-v-1apr27j .framer-1e84z8s, .framer-cKThw.framer-v-1rm2apr .framer-1e84z8s { padding: 18px 24px 18px 24px; }", ".framer-cKThw.framer-v-4ux8gn .framer-1e84z8s { cursor: unset; gap: 8px; padding: 18px 32px 18px 32px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-cKThw.framer-v-4ux8gn .framer-1e84z8s { gap: 0px; } .framer-cKThw.framer-v-4ux8gn .framer-1e84z8s > * { margin: 0px; margin-left: calc(8px / 2); margin-right: calc(8px / 2); } .framer-cKThw.framer-v-4ux8gn .framer-1e84z8s > :first-child { margin-left: 0px; } .framer-cKThw.framer-v-4ux8gn .framer-1e84z8s > :last-child { margin-right: 0px; } }", ".framer-cKThw.framer-v-jycvde .framer-1e84z8s { cursor: unset; gap: 8px; padding: 18px 28px 18px 28px; }", ".framer-cKThw.framer-v-jycvde .framer-b3kzs { height: 18px; width: 18px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-cKThw.framer-v-jycvde .framer-1e84z8s { gap: 0px; } .framer-cKThw.framer-v-jycvde .framer-1e84z8s > * { margin: 0px; margin-left: calc(8px / 2); margin-right: calc(8px / 2); } .framer-cKThw.framer-v-jycvde .framer-1e84z8s > :first-child { margin-left: 0px; } .framer-cKThw.framer-v-jycvde .framer-1e84z8s > :last-child { margin-right: 0px; } }", ".framer-cKThw.framer-v-hcqxnr .framer-1e84z8s { padding: 8px 14px 8px 14px; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerIntrinsicHeight 48
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerIntrinsicWidth 167
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]},"J6qwcVywR":{"layout":["auto","auto"]},"NIKcshCiT":{"layout":["auto","auto"]},"QQtnHKQQd":{"layout":["auto","auto"]},"jaSanL1Ld":{"layout":["auto","auto"]},"yD4vrs0op":{"layout":["auto","auto"]},"wAL4axq0N":{"layout":["auto","auto"]},"biGcS_1Nr":{"layout":["auto","auto"]},"w5a5PqQdU":{"layout":["auto","auto"]},"m7ICNTpLf":{"layout":["auto","auto"]},"JmEDWj0ys":{"layout":["auto","auto"]},"dUkIdSVGd":{"layout":["auto","auto"]},"yFIZkmaYR":{"layout":["auto","auto"]},"wrnV9Bju5":{"layout":["auto","auto"]},"P3SaPwMeM":{"layout":["auto","auto"]},"xtuJruji9":{"layout":["auto","auto"]},"meMoqa74T":{"layout":["auto","auto"]},"iASPPqQdt":{"layout":["auto","auto"]},"mkRsUGgfj":{"layout":["auto","auto"]},"jnthlbEur":{"layout":["auto","auto"]},"ui3kBdxWi":{"layout":["auto","auto"]},"LA1ux4Y6y":{"layout":["auto","auto"]},"g95_XVI27":{"layout":["auto","auto"]},"dhyFQrrEL":{"layout":["auto","auto"]},"sV3Gl8Fvp":{"layout":["auto","auto"]},"hHjxLSXpJ":{"layout":["auto","auto"]},"KraLU7ahm":{"layout":["auto","auto"]},"JVmOAtk3n":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerVariables {"KvumNH_7m":"title","NGWbmCoOo":"background","OXtioU_0I":"tap","Ii0PsZPlj":"buttonBG","vh337UjuQ":"textColour","GdcaFmgrg":"link"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          */
const FramerW7ao3lSRJ = withCSS(Component, css);
export default FramerW7ao3lSRJ;
FramerW7ao3lSRJ.displayName = "Assets/Button Main";
FramerW7ao3lSRJ.defaultProps = {
  height: 48,
  width: 167
};
addPropertyControls(FramerW7ao3lSRJ, {
  variant: {
    options: ["5231:108745", "J6qwcVywR", "NIKcshCiT", "QQtnHKQQd", "jaSanL1Ld", "yD4vrs0op", "wAL4axq0N", "biGcS_1Nr", "w5a5PqQdU", "m7ICNTpLf", "JmEDWj0ys", "dUkIdSVGd"],
    optionTitles: ["Solid", "Line", "Solid - Round Bottom", "Round", "Round Mobile", "Job Opening", "Get Started", "Button - White", "Careers", "Line - White", "Line - Black Hover", "Green"],
    title: "Variant",
    type: ControlType.Enum
  },
  KvumNH_7m: {
    defaultValue: " asd asd as dsda",
    displayTextArea: false,
    placeholder: "",
    title: "Title",
    type: ControlType.String
  },
  NGWbmCoOo: {
    defaultValue: "rgb(3, 104, 224)",
    title: "Background",
    type: ControlType.Color
  },
  OXtioU_0I: {
    title: "Tap",
    type: ControlType.EventHandler
  },
  Ii0PsZPlj: {
    defaultValue: "rgb(0, 0, 0)",
    title: "Button BG",
    type: ControlType.Color
  },
  vh337UjuQ: {
    defaultValue: "rgb(255, 255, 255)",
    title: "Text Colour",
    type: ControlType.Color
  },
  GdcaFmgrg: {
    title: "Link",
    type: ControlType.Link
  }
});
addFonts(FramerW7ao3lSRJ, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/W7ao3lSRJ:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/W7ao3lSRJ:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/W7ao3lSRJ:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
  weight: "600"
}]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerW7ao3lSRJ",
      "slots": [],
      "annotations": {
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]},\"J6qwcVywR\":{\"layout\":[\"auto\",\"auto\"]},\"NIKcshCiT\":{\"layout\":[\"auto\",\"auto\"]},\"QQtnHKQQd\":{\"layout\":[\"auto\",\"auto\"]},\"jaSanL1Ld\":{\"layout\":[\"auto\",\"auto\"]},\"yD4vrs0op\":{\"layout\":[\"auto\",\"auto\"]},\"wAL4axq0N\":{\"layout\":[\"auto\",\"auto\"]},\"biGcS_1Nr\":{\"layout\":[\"auto\",\"auto\"]},\"w5a5PqQdU\":{\"layout\":[\"auto\",\"auto\"]},\"m7ICNTpLf\":{\"layout\":[\"auto\",\"auto\"]},\"JmEDWj0ys\":{\"layout\":[\"auto\",\"auto\"]},\"dUkIdSVGd\":{\"layout\":[\"auto\",\"auto\"]},\"yFIZkmaYR\":{\"layout\":[\"auto\",\"auto\"]},\"wrnV9Bju5\":{\"layout\":[\"auto\",\"auto\"]},\"P3SaPwMeM\":{\"layout\":[\"auto\",\"auto\"]},\"xtuJruji9\":{\"layout\":[\"auto\",\"auto\"]},\"meMoqa74T\":{\"layout\":[\"auto\",\"auto\"]},\"iASPPqQdt\":{\"layout\":[\"auto\",\"auto\"]},\"mkRsUGgfj\":{\"layout\":[\"auto\",\"auto\"]},\"jnthlbEur\":{\"layout\":[\"auto\",\"auto\"]},\"ui3kBdxWi\":{\"layout\":[\"auto\",\"auto\"]},\"LA1ux4Y6y\":{\"layout\":[\"auto\",\"auto\"]},\"g95_XVI27\":{\"layout\":[\"auto\",\"auto\"]},\"dhyFQrrEL\":{\"layout\":[\"auto\",\"auto\"]},\"sV3Gl8Fvp\":{\"layout\":[\"auto\",\"auto\"]},\"hHjxLSXpJ\":{\"layout\":[\"auto\",\"auto\"]},\"KraLU7ahm\":{\"layout\":[\"auto\",\"auto\"]},\"JVmOAtk3n\":{\"layout\":[\"auto\",\"auto\"]}}}",
        "framerIntrinsicHeight": "48",
        "framerVariables": "{\"KvumNH_7m\":\"title\",\"NGWbmCoOo\":\"background\",\"OXtioU_0I\":\"tap\",\"Ii0PsZPlj\":\"buttonBG\",\"vh337UjuQ\":\"textColour\",\"GdcaFmgrg\":\"link\"}",
        "framerIntrinsicWidth": "167",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./W7ao3lSRJ.map