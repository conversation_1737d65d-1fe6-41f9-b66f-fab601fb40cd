import{ta as r}from"./chunk-5F276QAW.mjs";r.loadWebFontsFromSelectors(["Inter"]);var n=[],m=['.framer-cWdEz .framer-styles-preset-1dhfv1g:not(.rich-text-wrapper), .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper p, .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; }','@media (max-width: 1279px) and (min-width: 810px) { .framer-cWdEz .framer-styles-preset-1dhfv1g:not(.rich-text-wrapper), .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper p, .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 13px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 20px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; } }','@media (max-width: 809px) and (min-width: 0px) { .framer-cWdEz .framer-styles-preset-1dhfv1g:not(.rich-text-wrapper), .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper p, .framer-cWdEz .framer-styles-preset-1dhfv1g.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 12px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 18px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: #000000; --framer-text-decoration: none; --framer-text-transform: none; } }'],s="framer-cWdEz";export{n as a,m as b,s as c};
//# sourceMappingURL=chunk-3NK7SZBQ.mjs.map
