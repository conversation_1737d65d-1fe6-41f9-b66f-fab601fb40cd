const metadata = params => ({
  breakpoints: [{
    hash: "1vaew8q",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "dj9och",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "khol1p",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | Compare Popless to other tutoring solutions",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};