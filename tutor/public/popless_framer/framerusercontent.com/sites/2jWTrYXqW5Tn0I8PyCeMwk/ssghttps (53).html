// Generated by Framer (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Link, NotFoundError, PropertyOverrides, removeHiddenBreakpointLayers, RichText, Text, useActiveVariantCallback, useCurrentPathVariables, useDataRecord, useHydratedBreakpointVariants, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import TermsH1 from "https://framerusercontent.com/modules/1vdXW9frIjo7Q6mOk8uh/u7Eu8HqQdTjgvfJAClor/BlyUUTgI8.js";
import AssetsButtonMain from "https://framerusercontent.com/modules/ocbwDwrCSLiVC873hFen/SZ3EEPoJIeHkm0wEe9x3/W7ao3lSRJ.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import Legal from "https://framerusercontent.com/modules/JfKFXoFL7baMBEefR0t6/6mPGLUaqI7Px0ERqJySJ/EWmio7u6f.js";
import * as sharedStyle4 from "https://framerusercontent.com/modules/jW4UrfhLrCwwfY1oTaaP/Dxxbn5oVzYdipYbLgMBY/ilRQrCGtC.js";
import * as sharedStyle from "https://framerusercontent.com/modules/yjPEtrSg4SBLUDhnOWiU/OHJkwjsINSCrOIeYbYWF/stylesPresetHeading1.js";
import * as sharedStyle1 from "https://framerusercontent.com/modules/yMplYgnydszHKitQ4MOP/NthVGsAJ6uDkD4B2vlbl/stylesPresetHeading2.js";
import * as sharedStyle2 from "https://framerusercontent.com/modules/xHjNJy4OmzeEjchRV6Tg/HOGpXlwS5GtPImZxVXRy/stylesPresetHeading3.js";
import * as sharedStyle3 from "https://framerusercontent.com/modules/wyZs1JsaYdpuznv4eH4t/xaFQHlOum3gr4elZTJsL/stylesPresetParagraph.js";
import metadataProvider from "https://framerusercontent.com/modules/FbY5LoyQAgMBqki70TnT/wlHRXDI1b3oemnyQnuAJ/caI63G_Mf.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const TermsH1Fonts = getFonts(TermsH1);
const AssetsButtonMainFonts = getFonts(AssetsButtonMain);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["XKtYVjVNC", "iOT3honh2", "CUi6pvobl"];
const breakpoints = {
  CUi6pvobl: "(max-width: 809px)",
  iOT3honh2: "(min-width: 810px) and (max-width: 1439px)",
  XKtYVjVNC: "(min-width: 1440px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  CUi6pvobl: "framer-v-18na2tt",
  iOT3honh2: "framer-v-oco35x",
  XKtYVjVNC: "framer-v-1nuuy0c"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("XKtYVjVNC", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  "Desktop M": "XKtYVjVNC",
  Phone: "CUi6pvobl",
  Tablet: "iOT3honh2"
};
const transitions = {
  default: {
    duration: 0
  }
};
const BASE62 = "**********ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "XKtYVjVNC",
  TLSnfROIL,
  X0Ze_jQ_J,
  jyjDkRVhw,
  CWC5gxz5qrvcPLLT_v,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const currentPathVariables = useCurrentPathVariables();
  const currentRouteData = useDataRecord(Legal, currentPathVariables);
  const getFromCurrentRouteData = key => {
    if (!currentRouteData) throw new NotFoundError(`No data in "Legal" matches path variables: ${JSON.stringify(currentPathVariables)}`);
    return currentRouteData[key];
  };
  if (jyjDkRVhw === undefined) jyjDkRVhw = getFromCurrentRouteData("jyjDkRVhw");
  if (TLSnfROIL === undefined) TLSnfROIL = getFromCurrentRouteData("TLSnfROIL");
  if (X0Ze_jQ_J === undefined) X0Ze_jQ_J = getFromCurrentRouteData("X0Ze_jQ_J");
  React.useLayoutEffect(() => {
    const metadata = metadataProvider(currentRouteData);
    document.title = metadata.title || "";
    if (metadata.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata.viewport);
    }
  }, [currentRouteData]);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const tapg1wkj6 = activeVariantCallback(async (...args) => {
    window.open("mailto:<EMAIL>", "_blank", "noreferrer noopener");
  });
  const poplessLink1hxdeaz = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noreferrer noopener");
  });
  const twitterLink1bj8fo6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noreferrer noopener");
  });
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "XKtYVjVNC",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        "data-framer-generated": true,
        className: cx("framer-qImoF", sharedStyle.className, sharedStyle1.className, sharedStyle2.className, sharedStyle3.className, sharedStyle4.className),
        style: {
          display: "contents",
          pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
        },
        children: [/*#__PURE__*/_jsx(motion.div, {
          ...restProps,
          className: cx("framer-1nuuy0c", className),
          ref: ref,
          style: {
            ...style
          },
          children: /*#__PURE__*/_jsxs(motion.main, {
            className: "framer-160r3up",
            "data-framer-name": "Main",
            name: "Main",
            children: [/*#__PURE__*/_jsx(Container, {
              className: "framer-1mmaov5-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  CUi6pvobl: {
                    variant: "aZMkidfTG"
                  },
                  iOT3honh2: {
                    variant: "aZMkidfTG"
                  }
                },
                children: /*#__PURE__*/_jsx(HeaderNavigation, {
                  height: "100%",
                  id: "zPFGxXMjy",
                  layoutId: "zPFGxXMjy",
                  style: {
                    width: "100%"
                  },
                  variant: "yBBOIO6L6",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsxs(motion.header, {
              className: "framer-1u68r3i",
              "data-framer-name": "Stack",
              name: "Stack",
              children: [/*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                __link: "data:framer/page-link,P8liG2Pv7?transition=instant",
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx("p", {
                    style: {
                      "--framer-line-height": "30px",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(Link, {
                      href: "data:framer/page-link,P8liG2Pv7?transition=instant",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx("a", {
                        href: "data:framer/page-link,P8liG2Pv7?transition=instant",
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Terms and Policies"
                        })
                      })
                    })
                  })
                }),
                className: "framer-bnjkjm",
                fonts: ["GF;Inter-regular"],
                preload: ["P8liG2Pv7"],
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "left",
                className: "framer-qmzgi2",
                fonts: ["GF;Inter-regular"],
                rawHTML: "<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>/</span><br></span></p>",
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "left",
                className: "framer-sy86wg",
                fonts: ["GF;Inter-regular"],
                rawHTML: "<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Privacy Policy</span><br></span></p>",
                text: TLSnfROIL,
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1yede27",
              children: [/*#__PURE__*/_jsxs(motion.section, {
                className: "framer-1eqkdl",
                children: [/*#__PURE__*/_jsxs(motion.header, {
                  className: "framer-1n8eokp",
                  "data-framer-name": "Stack",
                  name: "Stack",
                  children: [/*#__PURE__*/_jsx(Container, {
                    className: "framer-wf2vpc-container",
                    children: /*#__PURE__*/_jsx(TermsH1, {
                      height: "100%",
                      id: "aZFCM_bex",
                      layoutId: "aZFCM_bex",
                      style: {
                        width: "100%"
                      },
                      title: TLSnfROIL,
                      width: "100%"
                    })
                  }), /*#__PURE__*/_jsx(Text, {
                    __fromCanvasComponent: true,
                    alignment: "left",
                    className: "framer-yssz50",
                    fonts: ["GF;Inter-regular"],
                    rawHTML: "<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Last updated on Feb 10, 2022</span><br></span></p>",
                    text: X0Ze_jQ_J,
                    verticalAlignment: "top",
                    withExternalLayout: true
                  })]
                }), /*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: jyjDkRVhw,
                  className: "framer-252i6g",
                  fonts: ["GF;Inter-regular"],
                  stylesPresetsClassNames: {
                    a: "framer-styles-preset-1bok01c",
                    h1: "framer-styles-preset-o3e5h0",
                    h2: "framer-styles-preset-1m9bzi2",
                    h3: "framer-styles-preset-ci2ngw",
                    p: "framer-styles-preset-16bzrdu"
                  },
                  verticalAlignment: "top",
                  withExternalLayout: true
                })]
              }), /*#__PURE__*/_jsxs(motion.section, {
                className: "framer-10wg483",
                children: [/*#__PURE__*/_jsx(Text, {
                  __fromCanvasComponent: true,
                  alignment: "left",
                  className: "framer-16p9vjk",
                  fonts: ["GF;Inter-500"],
                  rawHTML: "<h2 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Need to get in touch?</span><br></span></h2>",
                  text: CWC5gxz5qrvcPLLT_v,
                  verticalAlignment: "top",
                  withExternalLayout: true
                }), /*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--framer-line-height": "30px",
                        "--framer-text-alignment": "left"
                      },
                      children: /*#__PURE__*/_jsx("span", {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                          "--framer-font-family": '"Inter", sans-serif',
                          "--framer-font-style": "normal",
                          "--framer-font-weight": "400",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "We’ll start with some questions and get you to the right place."
                      })
                    })
                  }),
                  className: "framer-9jrax2",
                  fonts: ["GF;Inter-regular"],
                  verticalAlignment: "top",
                  withExternalLayout: true
                }), /*#__PURE__*/_jsx(Container, {
                  className: "framer-1j4cru0-container",
                  children: /*#__PURE__*/_jsx(AssetsButtonMain, {
                    background: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                    buttonBG: "rgb(0, 0, 0)",
                    height: "100%",
                    id: "F5YS6oZUc",
                    layoutId: "F5YS6oZUc",
                    tap: tapg1wkj6,
                    textColour: "rgb(255, 255, 255)",
                    title: "Contact us",
                    variant: "J6qwcVywR",
                    width: "100%"
                  })
                })]
              })]
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-15ccr6e-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  CUi6pvobl: {
                    variant: "Zz_9kWOfb"
                  }
                },
                children: /*#__PURE__*/_jsx(FooterNew, {
                  height: "100%",
                  id: "C7uq1nl2A",
                  layoutId: "C7uq1nl2A",
                  poplessLink: poplessLink1hxdeaz,
                  style: {
                    width: "100%"
                  },
                  twitterLink: twitterLink1bj8fo6,
                  variant: "zyTRmFlly",
                  width: "100%"
                })
              })
            })]
          })
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-qImoF [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-qImoF .framer-jp0h8z { display: block; }", ".framer-qImoF .framer-1nuuy0c { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 80px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1440px; }", ".framer-qImoF .framer-160r3up { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 100px 0px 0px 0px; position: relative; width: 100%; }", ".framer-qImoF .framer-1mmaov5-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 1; }", ".framer-qImoF .framer-1u68r3i { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 20px 120px 40px 120px; position: relative; width: 1200px; }", ".framer-qImoF .framer-bnjkjm { --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }", '.framer-qImoF .framer-qmzgi2 { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, #a8a8a8); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }', '.framer-qImoF .framer-sy86wg { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre; width: auto; }', ".framer-qImoF .framer-1yede27 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 48px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 120px 150px 120px; position: relative; width: 1200px; }", ".framer-qImoF .framer-1eqkdl { align-content: flex-end; align-items: flex-end; display: flex; flex: 1.5 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-qImoF .framer-1n8eokp { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 0px 0px 40px 0px; position: relative; width: 100%; }", ".framer-qImoF .framer-wf2vpc-container, .framer-qImoF .framer-15ccr6e-container { flex: none; height: auto; position: relative; width: 100%; }", '.framer-qImoF .framer-yssz50 { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 30px; --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }', ".framer-qImoF .framer-252i6g { --framer-paragraph-spacing: 20px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-qImoF .framer-10wg483 { align-content: flex-start; align-items: flex-start; display: flex; flex: 0.75 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 12px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", '.framer-qImoF .framer-16p9vjk { --framer-font-family: "Inter", sans-serif; --framer-font-size: 24px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -1px; --framer-line-height: 1.4em; --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: visible; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }', ".framer-qImoF .framer-9jrax2 { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-qImoF .framer-1j4cru0-container { flex: none; height: auto; position: relative; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1nuuy0c, .framer-qImoF .framer-160r3up, .framer-qImoF .framer-1u68r3i, .framer-qImoF .framer-1yede27, .framer-qImoF .framer-1eqkdl, .framer-qImoF .framer-1n8eokp, .framer-qImoF .framer-10wg483 { gap: 0px; } .framer-qImoF .framer-1nuuy0c > * { margin: 0px; margin-bottom: calc(80px / 2); margin-top: calc(80px / 2); } .framer-qImoF .framer-1nuuy0c > :first-child, .framer-qImoF .framer-160r3up > :first-child, .framer-qImoF .framer-1eqkdl > :first-child, .framer-qImoF .framer-1n8eokp > :first-child, .framer-qImoF .framer-10wg483 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1nuuy0c > :last-child, .framer-qImoF .framer-160r3up > :last-child, .framer-qImoF .framer-1eqkdl > :last-child, .framer-qImoF .framer-1n8eokp > :last-child, .framer-qImoF .framer-10wg483 > :last-child { margin-bottom: 0px; } .framer-qImoF .framer-160r3up > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-qImoF .framer-1u68r3i > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-qImoF .framer-1u68r3i > :first-child, .framer-qImoF .framer-1yede27 > :first-child { margin-left: 0px; } .framer-qImoF .framer-1u68r3i > :last-child, .framer-qImoF .framer-1yede27 > :last-child { margin-right: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-left: calc(48px / 2); margin-right: calc(48px / 2); } .framer-qImoF .framer-1eqkdl > *, .framer-qImoF .framer-1n8eokp > *, .framer-qImoF .framer-10wg483 > * { margin: 0px; margin-bottom: calc(12px / 2); margin-top: calc(12px / 2); } }", "@media (min-width: 1440px) { .framer-qImoF .hidden-1nuuy0c { display: none !important; } }", "@media (min-width: 810px) and (max-width: 1439px) { .framer-qImoF .hidden-oco35x { display: none !important; } .framer-qImoF .framer-1nuuy0c { width: 810px; } .framer-qImoF .framer-1u68r3i { max-width: unset; width: 100%; } .framer-qImoF .framer-1yede27 { flex-direction: column; width: 100%; } .framer-qImoF .framer-1eqkdl { align-content: center; align-items: center; flex: none; width: 100%; } .framer-qImoF .framer-10wg483 { flex: none; width: 100%; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1yede27 { gap: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-bottom: calc(48px / 2); margin-top: calc(48px / 2); } .framer-qImoF .framer-1yede27 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1yede27 > :last-child { margin-bottom: 0px; } }}", "@media (max-width: 809px) { .framer-qImoF .hidden-18na2tt { display: none !important; } .framer-qImoF .framer-1nuuy0c { width: 390px; } .framer-qImoF .framer-1u68r3i { max-width: unset; padding: 20px 24px 40px 24px; width: 100%; } .framer-qImoF .framer-sy86wg { flex: 1 0 0px; overflow: hidden; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; } .framer-qImoF .framer-1yede27 { flex-direction: column; padding: 0px 24px 100px 24px; width: 100%; } .framer-qImoF .framer-1eqkdl { align-content: center; align-items: center; flex: none; width: 100%; } .framer-qImoF .framer-10wg483 { flex: none; width: 100%; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-qImoF .framer-1yede27 { gap: 0px; } .framer-qImoF .framer-1yede27 > * { margin: 0px; margin-bottom: calc(48px / 2); margin-top: calc(48px / 2); } .framer-qImoF .framer-1yede27 > :first-child { margin-top: 0px; } .framer-qImoF .framer-1yede27 > :last-child { margin-bottom: 0px; } }}", ...sharedStyle.css, ...sharedStyle1.css, ...sharedStyle2.css, ...sharedStyle3.css, ...sharedStyle4.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerIntrinsicHeight 42369
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerIntrinsicWidth 1440
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"iOT3honh2":{"layout":["fixed","auto"]},"CUi6pvobl":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           */
const FramercaI63G_Mf = withCSS(Component, css);
export default FramercaI63G_Mf;
FramercaI63G_Mf.displayName = "Page 2";
FramercaI63G_Mf.defaultProps = {
  height: 42369,
  width: 1440
};
addFonts(FramercaI63G_Mf, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/caI63G_Mf:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/caI63G_Mf:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, ...HeaderNavigationFonts, ...TermsH1Fonts, ...AssetsButtonMainFonts, ...FooterNewFonts, ...sharedStyle.fonts, ...sharedStyle1.fonts, ...sharedStyle2.fonts, ...sharedStyle3.fonts, ...sharedStyle4.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramercaI63G_Mf",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "42369",
        "framerResponsiveScreen": "",
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "1440",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"iOT3honh2\":{\"layout\":[\"fixed\",\"auto\"]},\"CUi6pvobl\":{\"layout\":[\"fixed\",\"auto\"]}}}"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};