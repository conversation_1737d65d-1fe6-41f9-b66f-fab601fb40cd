// Generated by Fr<PERSON>r (1b71f6a)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, Image, RichText, Text, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import ButtonWhite from "https://framerusercontent.com/modules/YddvjhZiW0pAiZorFwdl/GtcA7l9jMF0GxFMETu1F/kbM8MMn1e.js";
const ButtonWhiteFonts = getFonts(ButtonWhite);
const cycleOrder = ["AyxWLsMP5", "r_vmqNXfb", "cpn7zS1xe", "hSjW0ns7B", "yoPaw5baI", "RlHqUMvIS"];
const variantClassNames = {
  AyxWLsMP5: "framer-v-18xv3n2",
  cpn7zS1xe: "framer-v-1bnfmlb",
  hSjW0ns7B: "framer-v-1li6tw4",
  r_vmqNXfb: "framer-v-1090sm9",
  RlHqUMvIS: "framer-v-1q74l2c",
  yoPaw5baI: "framer-v-1268evn"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Default - No Link - Small": "yoPaw5baI",
  "Default - No Link": "cpn7zS1xe",
  "No Button - No Link - Small": "RlHqUMvIS",
  "No Button - No Link": "hSjW0ns7B",
  "No Button": "r_vmqNXfb",
  Default: "AyxWLsMP5"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .6,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  },
  hSjW0ns7B: {
    damping: 60,
    delay: 0,
    duration: .6,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  },
  r_vmqNXfb: {
    damping: 60,
    delay: 0,
    duration: .6,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  },
  RlHqUMvIS: {
    damping: 60,
    delay: 0,
    duration: .6,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  }
};
function toResponsiveImage_194x2gw(value) {
  if (typeof value === "object" && value !== null && typeof value.src === "string") {
    return value;
  }
  return typeof value === "string" ? {
    src: value
  } : undefined;
}
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "AyxWLsMP5",
  heading: NOrVnDLK7 = "Write and publish",
  subHeading: U7P9U6oXB = "Collection",
  hero: dgTE1s9sL,
  tap: I71IO_L0l,
  heading2: E2c8K0pKn = "your first story",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "AyxWLsMP5",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTap1qhws4r = activeVariantCallback(async (...args) => {
    if (I71IO_L0l) {
      const res = await I71IO_L0l(...args);
      if (res === false) return false;
    }
  });
  const isDisplayed1 = () => {
    if (["r_vmqNXfb", "hSjW0ns7B", "RlHqUMvIS"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed2 = () => {
    if (["r_vmqNXfb", "hSjW0ns7B", "yoPaw5baI", "RlHqUMvIS"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed3 = () => {
    if (baseVariant === "yoPaw5baI") return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-oyyBb", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsxs(Image, {
        ...restProps,
        background: {
          alt: "",
          fit: "stretch",
          ...toResponsiveImage_194x2gw(dgTE1s9sL)
        },
        className: cx("framer-18xv3n2", className),
        "data-framer-name": "Default",
        "data-highlight": true,
        layoutDependency: layoutDependency,
        layoutId: "AyxWLsMP5",
        onTap: onTap1qhws4r,
        ref: ref,
        style: {
          borderBottomLeftRadius: 12,
          borderBottomRightRadius: 12,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          cpn7zS1xe: {
            "data-framer-name": "Default - No Link",
            "data-highlight": undefined,
            onTap: undefined
          },
          hSjW0ns7B: {
            "data-framer-name": "No Button - No Link",
            "data-highlight": undefined,
            onTap: undefined
          },
          r_vmqNXfb: {
            "data-framer-name": "No Button"
          },
          RlHqUMvIS: {
            "data-framer-name": "No Button - No Link - Small",
            "data-highlight": undefined,
            onTap: undefined
          },
          yoPaw5baI: {
            "data-framer-name": "Default - No Link - Small",
            "data-highlight": undefined,
            onTap: undefined
          }
        }, baseVariant, gestureVariant),
        children: [isDisplayed1() && /*#__PURE__*/_jsx(Text, {
          __fromCanvasComponent: true,
          alignment: "left",
          className: "framer-1jcmebn",
          "data-framer-name": "Heading",
          fonts: ["GF;Inter-600"],
          layoutDependency: layoutDependency,
          layoutId: "E_IfJoMTs",
          rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='font-size: 0'><span style=''>your first story</span><br></span></span>",
          style: {
            "--framer-font-family": '"Inter", sans-serif',
            "--framer-font-size": "22px",
            "--framer-font-style": "normal",
            "--framer-font-weight": 600,
            "--framer-letter-spacing": "0px",
            "--framer-line-height": "22px",
            "--framer-text-alignment": "left",
            "--framer-text-color": "rgb(255, 255, 255)",
            "--framer-text-decoration": "none",
            "--framer-text-transform": "none"
          },
          text: E2c8K0pKn,
          transition: transition,
          variants: {
            yoPaw5baI: {
              "--framer-font-size": "12px",
              "--framer-line-height": "100%"
            }
          },
          verticalAlignment: "top",
          withExternalLayout: true
        }), /*#__PURE__*/_jsx(Text, {
          __fromCanvasComponent: true,
          alignment: "left",
          className: "framer-590qhw",
          "data-framer-name": "Heading",
          fonts: ["GF;Inter-600"],
          layoutDependency: layoutDependency,
          layoutId: "oQ4fWm2Qo",
          rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='font-size: 0'><span style=''>Write and publish</span><br></span></span>",
          style: {
            "--framer-font-family": '"Inter", sans-serif',
            "--framer-font-size": "22px",
            "--framer-font-style": "normal",
            "--framer-font-weight": 600,
            "--framer-letter-spacing": "0px",
            "--framer-line-height": "22px",
            "--framer-text-alignment": "left",
            "--framer-text-color": "rgb(255, 255, 255)",
            "--framer-text-decoration": "none",
            "--framer-text-transform": "none"
          },
          text: NOrVnDLK7,
          transition: transition,
          variants: {
            hSjW0ns7B: {
              "--framer-font-size": "16px",
              "--framer-line-height": "24px"
            },
            r_vmqNXfb: {
              "--framer-font-size": "16px",
              "--framer-line-height": "24px"
            },
            RlHqUMvIS: {
              "--framer-font-size": "12px",
              "--framer-line-height": "100%"
            },
            yoPaw5baI: {
              "--framer-font-size": "12px",
              "--framer-line-height": "100%"
            }
          },
          verticalAlignment: "top",
          withExternalLayout: true
        }), /*#__PURE__*/_jsx(Text, {
          __fromCanvasComponent: true,
          alignment: "left",
          className: "framer-1apdwzd",
          "data-framer-name": "Sub Heading",
          fonts: ["GF;Inter-regular"],
          layoutDependency: layoutDependency,
          layoutId: "uG6OQbIlh",
          rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='font-size: 0'><span style=''>Collection</span><br></span></span>",
          style: {
            "--framer-font-family": '"Inter", sans-serif',
            "--framer-font-size": "12px",
            "--framer-font-style": "normal",
            "--framer-font-weight": 400,
            "--framer-letter-spacing": "0px",
            "--framer-line-height": "18px",
            "--framer-text-alignment": "left",
            "--framer-text-color": "rgb(255, 255, 255)",
            "--framer-text-decoration": "none",
            "--framer-text-transform": "none"
          },
          text: U7P9U6oXB,
          transition: transition,
          variants: {
            RlHqUMvIS: {
              "--framer-font-size": "8px",
              "--framer-line-height": "1em"
            },
            yoPaw5baI: {
              "--framer-font-size": "8px",
              "--framer-line-height": "1em"
            }
          },
          verticalAlignment: "top",
          withExternalLayout: true
        }), isDisplayed2() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1o8m76h-container",
          layoutDependency: layoutDependency,
          layoutId: "EoGkosJyo-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(ButtonWhite, {
            height: "100%",
            id: "EoGkosJyo",
            layoutId: "EoGkosJyo",
            title: "Show all",
            width: "100%"
          })
        }), isDisplayed3() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1tg0wsk",
          layoutDependency: layoutDependency,
          layoutId: "UJuNbd_3D",
          style: {
            backgroundColor: "rgb(255, 255, 255)",
            borderBottomLeftRadius: 6,
            borderBottomRightRadius: 6,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6
          },
          transition: transition,
          children: /*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--font-selector": "SW50ZXItTWVkaXVt",
                  "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                  "--framer-font-size": "9px",
                  "--framer-font-weight": "500",
                  "--framer-line-height": "1em",
                  "--framer-text-color": "var(--extracted-r6o4lv)"
                },
                children: "Show all"
              })
            }),
            className: "framer-156usz",
            fonts: ["Inter-Medium"],
            layoutDependency: layoutDependency,
            layoutId: "oftmfuvEB",
            style: {
              "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(0, 0, 0)) ",
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-paragraph-spacing": "0px"
            },
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true
          })
        })]
      })
    })
  });
});
const css = ['.framer-oyyBb [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-oyyBb * { box-sizing: border-box; }", ".framer-oyyBb .framer-jc4hkn { display: block; }", ".framer-oyyBb .framer-18xv3n2 { cursor: pointer; height: 250px; overflow: visible; position: relative; width: 450px; }", ".framer-oyyBb .framer-1jcmebn { flex: none; height: 24px; left: 25px; overflow: hidden; position: absolute; top: 67px; white-space: pre-wrap; width: 250px; word-break: break-word; word-wrap: break-word; }", ".framer-oyyBb .framer-590qhw { flex: none; height: 24px; left: 25px; overflow: hidden; position: absolute; top: 43px; white-space: pre-wrap; width: 250px; word-break: break-word; word-wrap: break-word; }", ".framer-oyyBb .framer-1apdwzd { flex: none; height: auto; left: 25px; overflow: visible; position: absolute; top: 25px; white-space: pre; width: auto; }", ".framer-oyyBb .framer-1o8m76h-container { bottom: 25px; flex: none; height: auto; left: 25px; position: absolute; width: auto; }", ".framer-oyyBb .framer-1tg0wsk { align-content: center; align-items: center; bottom: 20px; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; left: 20px; overflow: hidden; padding: 8px 16px 8px 16px; position: absolute; width: min-content; will-change: transform; }", ".framer-oyyBb .framer-156usz { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-oyyBb .framer-1tg0wsk { gap: 0px; } .framer-oyyBb .framer-1tg0wsk > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-oyyBb .framer-1tg0wsk > :first-child { margin-left: 0px; } .framer-oyyBb .framer-1tg0wsk > :last-child { margin-right: 0px; } }", ".framer-oyyBb.framer-v-1090sm9 .framer-590qhw, .framer-oyyBb.framer-v-1li6tw4 .framer-590qhw { bottom: 25px; top: unset; white-space: pre; width: auto; }", ".framer-oyyBb.framer-v-1090sm9 .framer-1apdwzd, .framer-oyyBb.framer-v-1li6tw4 .framer-1apdwzd { bottom: 49px; top: unset; }", ".framer-oyyBb.framer-v-1bnfmlb .framer-18xv3n2, .framer-oyyBb.framer-v-1li6tw4 .framer-18xv3n2 { cursor: unset; }", ".framer-oyyBb.framer-v-1268evn .framer-18xv3n2, .framer-oyyBb.framer-v-1q74l2c .framer-18xv3n2 { cursor: unset; height: 150px; width: 270px; }", ".framer-oyyBb.framer-v-1268evn .framer-1jcmebn { height: auto; left: 20px; overflow: visible; top: 50px; white-space: pre; width: auto; }", ".framer-oyyBb.framer-v-1268evn .framer-590qhw { height: auto; left: 20px; overflow: visible; top: 34px; white-space: pre; width: auto; }", ".framer-oyyBb.framer-v-1268evn .framer-1apdwzd { left: 20px; top: 20px; }", ".framer-oyyBb.framer-v-1q74l2c .framer-590qhw { bottom: 20px; height: auto; left: 20px; overflow: visible; top: unset; white-space: pre; width: auto; }", ".framer-oyyBb.framer-v-1q74l2c .framer-1apdwzd { bottom: 38px; left: 20px; top: unset; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               * @framerIntrinsicHeight 250
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               * @framerIntrinsicWidth 450
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","fixed"]},"r_vmqNXfb":{"layout":["fixed","fixed"]},"cpn7zS1xe":{"layout":["fixed","fixed"]},"hSjW0ns7B":{"layout":["fixed","fixed"]},"yoPaw5baI":{"layout":["fixed","fixed"]},"RlHqUMvIS":{"layout":["fixed","fixed"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               * @framerVariables {"NOrVnDLK7":"heading","U7P9U6oXB":"subHeading","dgTE1s9sL":"hero","I71IO_L0l":"tap","E2c8K0pKn":"heading2"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               */
const FramerIMHsm50Qb = withCSS(Component, css);
export default FramerIMHsm50Qb;
FramerIMHsm50Qb.displayName = "Card";
FramerIMHsm50Qb.defaultProps = {
  height: 250,
  width: 450
};
addPropertyControls(FramerIMHsm50Qb, {
  variant: {
    options: ["AyxWLsMP5", "r_vmqNXfb", "cpn7zS1xe", "hSjW0ns7B", "yoPaw5baI", "RlHqUMvIS"],
    optionTitles: ["Default", "No Button", "Default - No Link", "No Button - No Link", "Default - No Link - Small", "No Button - No Link - Small"],
    title: "Variant",
    type: ControlType.Enum
  },
  NOrVnDLK7: {
    defaultValue: "Write and publish",
    displayTextArea: false,
    placeholder: "",
    title: "Heading",
    type: ControlType.String
  },
  U7P9U6oXB: {
    defaultValue: "Collection",
    displayTextArea: false,
    title: "Sub Heading",
    type: ControlType.String
  },
  dgTE1s9sL: {
    title: "Hero",
    type: ControlType.ResponsiveImage
  },
  I71IO_L0l: {
    title: "Tap",
    type: ControlType.EventHandler
  },
  E2c8K0pKn: {
    defaultValue: "your first story",
    displayTextArea: false,
    title: "Heading 2",
    type: ControlType.String
  }
});
addFonts(FramerIMHsm50Qb, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/IMHsm50Qb:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",
  weight: "600"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/IMHsm50Qb:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...ButtonWhiteFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerIMHsm50Qb",
      "slots": [],
      "annotations": {
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"fixed\"]},\"r_vmqNXfb\":{\"layout\":[\"fixed\",\"fixed\"]},\"cpn7zS1xe\":{\"layout\":[\"fixed\",\"fixed\"]},\"hSjW0ns7B\":{\"layout\":[\"fixed\",\"fixed\"]},\"yoPaw5baI\":{\"layout\":[\"fixed\",\"fixed\"]},\"RlHqUMvIS\":{\"layout\":[\"fixed\",\"fixed\"]}}}",
        "framerIntrinsicHeight": "250",
        "framerVariables": "{\"NOrVnDLK7\":\"heading\",\"U7P9U6oXB\":\"subHeading\",\"dgTE1s9sL\":\"hero\",\"I71IO_L0l\":\"tap\",\"E2c8K0pKn\":\"heading2\"}",
        "framerIntrinsicWidth": "450",
        "framerContractVersion": "1"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./IMHsm50Qb.map