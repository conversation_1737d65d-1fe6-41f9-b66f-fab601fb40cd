var a=t=>({breakpoints:[{hash:"npnv69",mediaQuery:"(min-width: 1280px)"},{hash:"1ppsi1q",mediaQuery:"(min-width: 810px) and (max-width: 1279px)"},{hash:"1ajoynm",mediaQuery:"(max-width: 809px)"}],elements:{},title:"Popless | Contact us",viewport:"width=device-width"}),n=a,o=1,s={exports:{metadataVersion:{type:"variable",annotations:{framerContractVersion:"1"}},default:{type:"variable",annotations:{framerContractVersion:"1"}},__FramerMetadata__:{type:"variable"}}};export{n as a,o as b,s as c};
//# sourceMappingURL=chunk-JMIMJQZK.mjs.map
