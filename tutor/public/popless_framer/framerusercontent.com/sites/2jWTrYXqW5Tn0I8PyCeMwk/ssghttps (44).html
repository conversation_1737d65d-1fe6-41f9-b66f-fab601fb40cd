const metadata = params => ({
  breakpoints: [{
    hash: "npnv69",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "1ppsi1q",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "1ajoynm",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | Contact us",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};