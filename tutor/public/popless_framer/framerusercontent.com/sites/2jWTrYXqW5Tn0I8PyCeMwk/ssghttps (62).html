// Generated by Fr<PERSON>r (b35efa8)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import FAQ from "https://framerusercontent.com/modules/b8AH0plGjvq7M3Qy1hr4/aChrgHtIQdaByuINsrug/hRs2gNE0M.js";
const FAQFonts = getFonts(FAQ);
const cycleOrder = ["Y34AGiolL", "yUUjY8mcV", "ELxxQYuAE", "Bz5EMU0YC", "D1QpKKelm", "T7YlnKrPD", "PVsQ6PkFg", "Zj22AG5Ff", "UxFtFvLpK", "UDNJPF7Pt", "tHmnorkX8", "KNrssAjQG", "w7u8_YP40", "W5eCtIe4C", "aKrRkR4N1", "lcqmKApVF", "oxMaC4Ztr", "lDVTGadZf", "v7fnda6kU", "hfZXhT42s", "G1BIJ_W6z", "qhKlgas8o", "yD4TCl5Uq", "quJVsLC4P", "lQxMMn5Si", "S97euB7vk", "rWUPavnoO", "w5P_qXlam", "PzHEdDByo", "U_dwEDSHM", "cbqwyJZ64", "hLKVhDmW3", "Sc6Py3Cqb", "pFJwCAhaP", "Ven45_IGb", "EuSj6EBj5", "ZsKVlQ2Ef", "c67tCjDMf", "N_ESJQqxE", "g3UpsbSX9", "PNeO4qznx", "Cf0PIGeI_", "YFGIQaQAp", "oWzl85imi"];
const variantClassNames = {
  aKrRkR4N1: "framer-v-mzssuq",
  Bz5EMU0YC: "framer-v-b6lon4",
  c67tCjDMf: "framer-v-xhqwl",
  cbqwyJZ64: "framer-v-1gxhnxz",
  Cf0PIGeI_: "framer-v-ny8tt3",
  D1QpKKelm: "framer-v-qgn552",
  ELxxQYuAE: "framer-v-u9ekm9",
  EuSj6EBj5: "framer-v-14v6kq7",
  G1BIJ_W6z: "framer-v-1purhro",
  g3UpsbSX9: "framer-v-lu0aee",
  hfZXhT42s: "framer-v-13bduyt",
  hLKVhDmW3: "framer-v-1wgfjfb",
  KNrssAjQG: "framer-v-14k2t9p",
  lcqmKApVF: "framer-v-1qeftsr",
  lDVTGadZf: "framer-v-cavvor",
  lQxMMn5Si: "framer-v-b3z8ey",
  N_ESJQqxE: "framer-v-35o6ce",
  oWzl85imi: "framer-v-vbh9w1",
  oxMaC4Ztr: "framer-v-yts22s",
  pFJwCAhaP: "framer-v-uneynz",
  PNeO4qznx: "framer-v-yv5vbl",
  PVsQ6PkFg: "framer-v-1fe0ejk",
  PzHEdDByo: "framer-v-iphu35",
  qhKlgas8o: "framer-v-17l55oa",
  quJVsLC4P: "framer-v-hp29bu",
  rWUPavnoO: "framer-v-1v59jog",
  S97euB7vk: "framer-v-1wh8ytc",
  Sc6Py3Cqb: "framer-v-d7ndli",
  T7YlnKrPD: "framer-v-dm6ld",
  tHmnorkX8: "framer-v-1a0pwyq",
  U_dwEDSHM: "framer-v-grgrko",
  UDNJPF7Pt: "framer-v-1vb95db",
  UxFtFvLpK: "framer-v-1txy65p",
  v7fnda6kU: "framer-v-1u5mohd",
  Ven45_IGb: "framer-v-1ykstih",
  W5eCtIe4C: "framer-v-18ewnlb",
  w5P_qXlam: "framer-v-rjn1hn",
  w7u8_YP40: "framer-v-6cyf2g",
  Y34AGiolL: "framer-v-brfeq6",
  yD4TCl5Uq: "framer-v-1jtebrj",
  YFGIQaQAp: "framer-v-7kjjsy",
  yUUjY8mcV: "framer-v-1tpah9w",
  Zj22AG5Ff: "framer-v-no952",
  ZsKVlQ2Ef: "framer-v-1pm6cq4"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "1 - FAQ Privacy - M": "pFJwCAhaP",
  "1 Stack Medium Mobile": "lDVTGadZf",
  "1 Stack": "oxMaC4Ztr",
  "3 Stack Medium Mobile": "aKrRkR4N1",
  "3 Stack Medium": "tHmnorkX8",
  "3 Stack": "ELxxQYuAE",
  "4 - FAQ General - D": "w5P_qXlam",
  "4 - FAQ General - M": "cbqwyJZ64",
  "4 - FAQ Payments - D": "U_dwEDSHM",
  "4 - FAQ Payments - M": "Sc6Py3Cqb",
  "4 Stack Medium Mobile": "w7u8_YP40",
  "4 Stack Medium": "UxFtFvLpK",
  "4 Stack": "Y34AGiolL",
  "5 - FAQ Getting Started - D": "PzHEdDByo",
  "5 - FAQ Getting Started - M": "hLKVhDmW3",
  "5 - Host M": "rWUPavnoO",
  "5 - Host": "S97euB7vk",
  "5 Stack Medium 2 Links": "yD4TCl5Uq",
  "5 Stack Medium Links": "G1BIJ_W6z",
  "5 Stack Medium Mobile 2 Links": "quJVsLC4P",
  "5 Stack Medium Mobile Links": "qhKlgas8o",
  "5 Stack Medium Mobile": "W5eCtIe4C",
  "5 Stack Medium": "UDNJPF7Pt",
  "5 Stack": "yUUjY8mcV",
  "6 Stack Medium Mobile": "lcqmKApVF",
  "6 Stack Medium": "KNrssAjQG",
  "6 Stack": "PVsQ6PkFg",
  "7 Stack": "v7fnda6kU",
  "9 Stack Medium Mobile": "oWzl85imi",
  "9 Stack Medium": "YFGIQaQAp",
  "Accessibility - Desktop - v2": "PNeO4qznx",
  "Accessibility - Mobile - v2": "Cf0PIGeI_",
  "Group Classes - Desktop - v2": "N_ESJQqxE",
  "Group Classes - Mobile - v2": "g3UpsbSX9",
  "Marketplace - Desktop - v2": "ZsKVlQ2Ef",
  "Marketplace - Mobile - v2": "c67tCjDMf",
  "Mobile 3 Stack": "T7YlnKrPD",
  "Mobile 4 Stack": "Bz5EMU0YC",
  "Mobile 5 Stack": "D1QpKKelm",
  "Mobile 6 Stack": "Zj22AG5Ff",
  "Mobile 7 Stack": "hfZXhT42s",
  "Tutoring - Desktop - v2": "Ven45_IGb",
  "Tutoring - Mobile - v2": "EuSj6EBj5",
  "Variant 25": "lQxMMn5Si"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "Y34AGiolL",
  title1: HIZndhE9f = "Title here",
  title2: wNXNR_Qr2 = "Title here",
  title3: fnT9ikjlW = "Title here",
  title4: jZCPg_4s0 = "Title here",
  title5: DCoUXJ2W0 = "Title here",
  title6: UrJEjiSRm = "Title here",
  title7: v9D2aRikN = "Title here",
  title8: G9ldWBbPm = "Title here",
  title9: tueDYeE3z = "Title here",
  copy1: lUTGPNHkx = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy2: Jx0bbXaW2 = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy3: bijV3LJCT = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy4: FM5IynMUd = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy5: XYlYFlwe_ = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy6: Z_8Ed9Pia = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy7: D_aLowF7q = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy8: UaRCl6228 = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  copy9: PtJLPNgzq = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "Y34AGiolL",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const tap1bmxtq1 = activeVariantCallback(async (...args) => {
    setVariant("yUUjY8mcV");
  });
  const tap1bjgddf = activeVariantCallback(async (...args) => {
    setVariant("UDNJPF7Pt");
  });
  const tap11oisf8 = activeVariantCallback(async (...args) => {
    setVariant("G1BIJ_W6z");
  });
  const tap1bhpm2r = activeVariantCallback(async (...args) => {
    setVariant("yD4TCl5Uq");
  });
  const tapprmaqk = activeVariantCallback(async (...args) => {
    setVariant("S97euB7vk");
  });
  const tap32aom6 = activeVariantCallback(async (...args) => {
    setVariant("PzHEdDByo");
  });
  const isDisplayed = () => {
    if (["oxMaC4Ztr", "lDVTGadZf", "pFJwCAhaP"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed1 = () => {
    if (["ELxxQYuAE", "T7YlnKrPD", "tHmnorkX8", "aKrRkR4N1", "oxMaC4Ztr", "lDVTGadZf", "pFJwCAhaP"].includes(baseVariant)) return false;
    return true;
  };
  const isDisplayed2 = () => {
    if (["yUUjY8mcV", "D1QpKKelm", "PVsQ6PkFg", "Zj22AG5Ff", "UDNJPF7Pt", "KNrssAjQG", "W5eCtIe4C", "lcqmKApVF", "v7fnda6kU", "hfZXhT42s", "G1BIJ_W6z", "qhKlgas8o", "yD4TCl5Uq", "quJVsLC4P", "lQxMMn5Si", "S97euB7vk", "rWUPavnoO", "PzHEdDByo", "hLKVhDmW3", "Ven45_IGb", "EuSj6EBj5", "ZsKVlQ2Ef", "c67tCjDMf", "N_ESJQqxE", "g3UpsbSX9", "PNeO4qznx", "Cf0PIGeI_", "YFGIQaQAp", "oWzl85imi"].includes(baseVariant)) return true;
    return false;
  };
  const isDisplayed3 = () => {
    if (["PVsQ6PkFg", "Zj22AG5Ff", "KNrssAjQG", "lcqmKApVF", "v7fnda6kU", "hfZXhT42s", "Ven45_IGb", "EuSj6EBj5", "ZsKVlQ2Ef", "c67tCjDMf", "YFGIQaQAp", "oWzl85imi"].includes(baseVariant)) return true;
    return false;
  };
  const isDisplayed4 = () => {
    if (["v7fnda6kU", "hfZXhT42s", "YFGIQaQAp", "oWzl85imi"].includes(baseVariant)) return true;
    return false;
  };
  const isDisplayed5 = () => {
    if (["YFGIQaQAp", "oWzl85imi"].includes(baseVariant)) return true;
    return false;
  };
  const defaultLayoutId = React.useId();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-8zmNM", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsxs(motion.div, {
        ...restProps,
        className: cx("framer-brfeq6", className),
        "data-framer-name": "4 Stack",
        layoutDependency: layoutDependency,
        layoutId: "Y34AGiolL",
        ref: ref,
        style: {
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          aKrRkR4N1: {
            "data-framer-name": "3 Stack Medium Mobile"
          },
          Bz5EMU0YC: {
            "data-framer-name": "Mobile 4 Stack"
          },
          c67tCjDMf: {
            "data-framer-name": "Marketplace - Mobile - v2"
          },
          cbqwyJZ64: {
            "data-framer-name": "4 - FAQ General - M"
          },
          Cf0PIGeI_: {
            "data-framer-name": "Accessibility - Mobile - v2"
          },
          D1QpKKelm: {
            "data-framer-name": "Mobile 5 Stack"
          },
          ELxxQYuAE: {
            "data-framer-name": "3 Stack"
          },
          EuSj6EBj5: {
            "data-framer-name": "Tutoring - Mobile - v2"
          },
          G1BIJ_W6z: {
            "data-framer-name": "5 Stack Medium Links"
          },
          g3UpsbSX9: {
            "data-framer-name": "Group Classes - Mobile - v2"
          },
          hfZXhT42s: {
            "data-framer-name": "Mobile 7 Stack"
          },
          hLKVhDmW3: {
            "data-framer-name": "5 - FAQ Getting Started - M"
          },
          KNrssAjQG: {
            "data-framer-name": "6 Stack Medium"
          },
          lcqmKApVF: {
            "data-framer-name": "6 Stack Medium Mobile"
          },
          lDVTGadZf: {
            "data-framer-name": "1 Stack Medium Mobile"
          },
          lQxMMn5Si: {
            "data-framer-name": "Variant 25"
          },
          N_ESJQqxE: {
            "data-framer-name": "Group Classes - Desktop - v2"
          },
          oWzl85imi: {
            "data-framer-name": "9 Stack Medium Mobile"
          },
          oxMaC4Ztr: {
            "data-framer-name": "1 Stack"
          },
          pFJwCAhaP: {
            "data-framer-name": "1 - FAQ Privacy - M"
          },
          PNeO4qznx: {
            "data-framer-name": "Accessibility - Desktop - v2"
          },
          PVsQ6PkFg: {
            "data-framer-name": "6 Stack"
          },
          PzHEdDByo: {
            "data-framer-name": "5 - FAQ Getting Started - D"
          },
          qhKlgas8o: {
            "data-framer-name": "5 Stack Medium Mobile Links"
          },
          quJVsLC4P: {
            "data-framer-name": "5 Stack Medium Mobile 2 Links"
          },
          rWUPavnoO: {
            "data-framer-name": "5 - Host M"
          },
          S97euB7vk: {
            "data-framer-name": "5 - Host"
          },
          Sc6Py3Cqb: {
            "data-framer-name": "4 - FAQ Payments - M"
          },
          T7YlnKrPD: {
            "data-framer-name": "Mobile 3 Stack"
          },
          tHmnorkX8: {
            "data-framer-name": "3 Stack Medium"
          },
          U_dwEDSHM: {
            "data-framer-name": "4 - FAQ Payments - D"
          },
          UDNJPF7Pt: {
            "data-framer-name": "5 Stack Medium"
          },
          UxFtFvLpK: {
            "data-framer-name": "4 Stack Medium"
          },
          v7fnda6kU: {
            "data-framer-name": "7 Stack"
          },
          Ven45_IGb: {
            "data-framer-name": "Tutoring - Desktop - v2"
          },
          W5eCtIe4C: {
            "data-framer-name": "5 Stack Medium Mobile"
          },
          w5P_qXlam: {
            "data-framer-name": "4 - FAQ General - D"
          },
          w7u8_YP40: {
            "data-framer-name": "4 Stack Medium Mobile"
          },
          yD4TCl5Uq: {
            "data-framer-name": "5 Stack Medium 2 Links"
          },
          YFGIQaQAp: {
            "data-framer-name": "9 Stack Medium"
          },
          yUUjY8mcV: {
            "data-framer-name": "5 Stack"
          },
          Zj22AG5Ff: {
            "data-framer-name": "Mobile 6 Stack"
          },
          ZsKVlQ2Ef: {
            "data-framer-name": "Marketplace - Desktop - v2"
          }
        }, baseVariant, gestureVariant),
        children: [/*#__PURE__*/_jsx(motion.div, {
          className: "framer-11mmp6z-container",
          layoutDependency: layoutDependency,
          layoutId: "NfINSMHvQ-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: lUTGPNHkx,
            height: "100%",
            id: "NfINSMHvQ",
            layoutId: "NfINSMHvQ",
            style: {
              width: "100%"
            },
            title: HIZndhE9f,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              aKrRkR4N1: {
                variant: "fj4N9m2ex"
              },
              Bz5EMU0YC: {
                variant: "lciTG5SyQ"
              },
              c67tCjDMf: {
                variant: "fj4N9m2ex"
              },
              cbqwyJZ64: {
                variant: "fj4N9m2ex"
              },
              Cf0PIGeI_: {
                variant: "fj4N9m2ex"
              },
              D1QpKKelm: {
                variant: "lciTG5SyQ"
              },
              EuSj6EBj5: {
                variant: "fj4N9m2ex"
              },
              G1BIJ_W6z: {
                variant: "vAJa31dne"
              },
              g3UpsbSX9: {
                variant: "fj4N9m2ex"
              },
              hfZXhT42s: {
                variant: "lciTG5SyQ"
              },
              hLKVhDmW3: {
                variant: "PJNW1ipBG"
              },
              KNrssAjQG: {
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                variant: "fj4N9m2ex"
              },
              lDVTGadZf: {
                variant: "eAJk7qogp"
              },
              lQxMMn5Si: {
                variant: "B2K0BxN_A"
              },
              N_ESJQqxE: {
                variant: "KP0Nn4KBY"
              },
              oWzl85imi: {
                variant: "fj4N9m2ex"
              },
              oxMaC4Ztr: {
                variant: "NFhYMdmcQ"
              },
              pFJwCAhaP: {
                variant: "eAJk7qogp"
              },
              PNeO4qznx: {
                variant: "KP0Nn4KBY"
              },
              PzHEdDByo: {
                variant: "OqjNtM0yZ"
              },
              qhKlgas8o: {
                variant: "W4Ph5o9ri"
              },
              quJVsLC4P: {
                variant: "B2K0BxN_A"
              },
              rWUPavnoO: {
                variant: "lciTG5SyQ"
              },
              Sc6Py3Cqb: {
                variant: "zuQ6Zt_Fe"
              },
              T7YlnKrPD: {
                variant: "lciTG5SyQ"
              },
              tHmnorkX8: {
                variant: "KP0Nn4KBY"
              },
              U_dwEDSHM: {
                variant: "McS8uAhgX"
              },
              UDNJPF7Pt: {
                variant: "KP0Nn4KBY"
              },
              UxFtFvLpK: {
                variant: "KP0Nn4KBY"
              },
              Ven45_IGb: {
                variant: "KP0Nn4KBY"
              },
              W5eCtIe4C: {
                variant: "fj4N9m2ex"
              },
              w5P_qXlam: {
                variant: "KP0Nn4KBY"
              },
              w7u8_YP40: {
                variant: "fj4N9m2ex"
              },
              yD4TCl5Uq: {
                variant: "T1nDZqrXi"
              },
              YFGIQaQAp: {
                variant: "KP0Nn4KBY"
              },
              Zj22AG5Ff: {
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                variant: "KP0Nn4KBY"
              }
            }, baseVariant, gestureVariant)
          })
        }), /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1vpa8yo",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "JJIwgFrWV",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-veui21-container",
          layoutDependency: layoutDependency,
          layoutId: "mzAgNkHI4-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: Jx0bbXaW2,
            height: "100%",
            id: "mzAgNkHI4",
            layoutId: "mzAgNkHI4",
            style: {
              width: "100%"
            },
            title: wNXNR_Qr2,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              aKrRkR4N1: {
                variant: "fj4N9m2ex"
              },
              Bz5EMU0YC: {
                variant: "lciTG5SyQ"
              },
              c67tCjDMf: {
                variant: "asOsoMiMY"
              },
              cbqwyJZ64: {
                variant: "fj4N9m2ex"
              },
              Cf0PIGeI_: {
                variant: "fj4N9m2ex"
              },
              D1QpKKelm: {
                variant: "lciTG5SyQ"
              },
              EuSj6EBj5: {
                variant: "q_5c2DzRU"
              },
              G1BIJ_W6z: {
                variant: "KP0Nn4KBY"
              },
              g3UpsbSX9: {
                variant: "fj4N9m2ex"
              },
              hfZXhT42s: {
                variant: "lciTG5SyQ"
              },
              hLKVhDmW3: {
                variant: "fj4N9m2ex"
              },
              KNrssAjQG: {
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                variant: "fj4N9m2ex"
              },
              lQxMMn5Si: {
                variant: "fj4N9m2ex"
              },
              N_ESJQqxE: {
                variant: "KP0Nn4KBY"
              },
              oWzl85imi: {
                variant: "fj4N9m2ex"
              },
              PNeO4qznx: {
                variant: "KP0Nn4KBY"
              },
              PzHEdDByo: {
                variant: "KP0Nn4KBY"
              },
              qhKlgas8o: {
                variant: "fj4N9m2ex"
              },
              quJVsLC4P: {
                variant: "fj4N9m2ex"
              },
              rWUPavnoO: {
                variant: "PeW4YDjMI"
              },
              S97euB7vk: {
                variant: "p3sZBctIS"
              },
              Sc6Py3Cqb: {
                variant: "fj4N9m2ex"
              },
              T7YlnKrPD: {
                variant: "lciTG5SyQ"
              },
              tHmnorkX8: {
                variant: "KP0Nn4KBY"
              },
              U_dwEDSHM: {
                variant: "KP0Nn4KBY"
              },
              UDNJPF7Pt: {
                variant: "KP0Nn4KBY"
              },
              UxFtFvLpK: {
                variant: "KP0Nn4KBY"
              },
              Ven45_IGb: {
                variant: "rW1WUIUEr"
              },
              W5eCtIe4C: {
                variant: "fj4N9m2ex"
              },
              w5P_qXlam: {
                variant: "KP0Nn4KBY"
              },
              w7u8_YP40: {
                variant: "fj4N9m2ex"
              },
              yD4TCl5Uq: {
                variant: "KP0Nn4KBY"
              },
              YFGIQaQAp: {
                variant: "KP0Nn4KBY"
              },
              Zj22AG5Ff: {
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                variant: "CbKtCXQ3c"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1xi9x3o",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "MenNVQ7fM",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-16u58oc-container",
          layoutDependency: layoutDependency,
          layoutId: "jLuj0x9BP-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: bijV3LJCT,
            height: "100%",
            id: "jLuj0x9BP",
            layoutId: "jLuj0x9BP",
            style: {
              width: "100%"
            },
            title: fnT9ikjlW,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              aKrRkR4N1: {
                variant: "fj4N9m2ex"
              },
              Bz5EMU0YC: {
                variant: "lciTG5SyQ"
              },
              c67tCjDMf: {
                variant: "fj4N9m2ex"
              },
              cbqwyJZ64: {
                variant: "XcWv6aoQT"
              },
              Cf0PIGeI_: {
                variant: "fj4N9m2ex"
              },
              D1QpKKelm: {
                variant: "lciTG5SyQ"
              },
              EuSj6EBj5: {
                variant: "AW5V70MRF"
              },
              G1BIJ_W6z: {
                tap: tap11oisf8,
                variant: "KP0Nn4KBY"
              },
              g3UpsbSX9: {
                variant: "bh18hddZb"
              },
              hfZXhT42s: {
                variant: "PeW4YDjMI"
              },
              hLKVhDmW3: {
                variant: "fj4N9m2ex"
              },
              KNrssAjQG: {
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                variant: "fj4N9m2ex"
              },
              lQxMMn5Si: {
                variant: "fj4N9m2ex"
              },
              N_ESJQqxE: {
                variant: "Rp2uDkvZW"
              },
              oWzl85imi: {
                variant: "fj4N9m2ex"
              },
              PNeO4qznx: {
                variant: "KP0Nn4KBY"
              },
              PzHEdDByo: {
                tap: tap32aom6,
                variant: "KP0Nn4KBY"
              },
              qhKlgas8o: {
                variant: "fj4N9m2ex"
              },
              quJVsLC4P: {
                variant: "fj4N9m2ex"
              },
              rWUPavnoO: {
                variant: "lciTG5SyQ"
              },
              S97euB7vk: {
                tap: tapprmaqk
              },
              Sc6Py3Cqb: {
                variant: "fj4N9m2ex"
              },
              T7YlnKrPD: {
                variant: "lciTG5SyQ"
              },
              tHmnorkX8: {
                variant: "KP0Nn4KBY"
              },
              U_dwEDSHM: {
                variant: "KP0Nn4KBY"
              },
              UDNJPF7Pt: {
                tap: tap1bjgddf,
                variant: "KP0Nn4KBY"
              },
              UxFtFvLpK: {
                variant: "KP0Nn4KBY"
              },
              v7fnda6kU: {
                variant: "p3sZBctIS"
              },
              Ven45_IGb: {
                variant: "A55XVbgoX"
              },
              W5eCtIe4C: {
                variant: "fj4N9m2ex"
              },
              w5P_qXlam: {
                variant: "G2MN8fkmQ"
              },
              w7u8_YP40: {
                variant: "fj4N9m2ex"
              },
              yD4TCl5Uq: {
                tap: tap1bhpm2r,
                variant: "KP0Nn4KBY"
              },
              YFGIQaQAp: {
                variant: "KP0Nn4KBY"
              },
              yUUjY8mcV: {
                tap: tap1bmxtq1
              },
              Zj22AG5Ff: {
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                variant: "KP0Nn4KBY"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1qzk6xm",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "KScEpGbil",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed1() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-js53b3-container",
          layoutDependency: layoutDependency,
          layoutId: "MbQWmvE89-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: FM5IynMUd,
            height: "100%",
            id: "MbQWmvE89",
            layoutId: "MbQWmvE89",
            style: {
              width: "100%"
            },
            title: jZCPg_4s0,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              Bz5EMU0YC: {
                variant: "lciTG5SyQ"
              },
              c67tCjDMf: {
                variant: "fj4N9m2ex"
              },
              cbqwyJZ64: {
                variant: "fj4N9m2ex"
              },
              Cf0PIGeI_: {
                variant: "fj4N9m2ex"
              },
              D1QpKKelm: {
                variant: "lciTG5SyQ"
              },
              EuSj6EBj5: {
                variant: "fj4N9m2ex"
              },
              G1BIJ_W6z: {
                variant: "KP0Nn4KBY"
              },
              g3UpsbSX9: {
                variant: "fj4N9m2ex"
              },
              hfZXhT42s: {
                variant: "lciTG5SyQ"
              },
              hLKVhDmW3: {
                variant: "fj4N9m2ex"
              },
              KNrssAjQG: {
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                variant: "fj4N9m2ex"
              },
              lQxMMn5Si: {
                variant: "fj4N9m2ex"
              },
              N_ESJQqxE: {
                variant: "KP0Nn4KBY"
              },
              oWzl85imi: {
                variant: "fj4N9m2ex"
              },
              PNeO4qznx: {
                variant: "KP0Nn4KBY"
              },
              PzHEdDByo: {
                variant: "KP0Nn4KBY"
              },
              qhKlgas8o: {
                variant: "fj4N9m2ex"
              },
              quJVsLC4P: {
                variant: "fj4N9m2ex"
              },
              rWUPavnoO: {
                variant: "lciTG5SyQ"
              },
              Sc6Py3Cqb: {
                variant: "j7eUa_b6M"
              },
              U_dwEDSHM: {
                variant: "G4G5pf7f6"
              },
              UDNJPF7Pt: {
                variant: "KP0Nn4KBY"
              },
              UxFtFvLpK: {
                variant: "KP0Nn4KBY"
              },
              Ven45_IGb: {
                variant: "KP0Nn4KBY"
              },
              W5eCtIe4C: {
                variant: "fj4N9m2ex"
              },
              w5P_qXlam: {
                variant: "KP0Nn4KBY"
              },
              w7u8_YP40: {
                variant: "fj4N9m2ex"
              },
              yD4TCl5Uq: {
                variant: "KP0Nn4KBY"
              },
              YFGIQaQAp: {
                variant: "KP0Nn4KBY"
              },
              Zj22AG5Ff: {
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                variant: "KP0Nn4KBY"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed2() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-5nhjsa",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "vplI2DKCJ",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed2() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1itx7o6-container",
          layoutDependency: layoutDependency,
          layoutId: "lZaHkgWjl-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: XYlYFlwe_,
            height: "100%",
            id: "lZaHkgWjl",
            layoutId: "lZaHkgWjl",
            style: {
              width: "100%"
            },
            title: DCoUXJ2W0,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              c67tCjDMf: {
                variant: "PCAgL4Uwz"
              },
              Cf0PIGeI_: {
                variant: "kifH89zFT"
              },
              D1QpKKelm: {
                variant: "lciTG5SyQ"
              },
              EuSj6EBj5: {
                variant: "fj4N9m2ex"
              },
              G1BIJ_W6z: {
                variant: "KP0Nn4KBY"
              },
              g3UpsbSX9: {
                variant: "fj4N9m2ex"
              },
              hfZXhT42s: {
                variant: "lciTG5SyQ"
              },
              hLKVhDmW3: {
                variant: "fj4N9m2ex"
              },
              KNrssAjQG: {
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                variant: "fj4N9m2ex"
              },
              lQxMMn5Si: {
                variant: "oouo9Kw59"
              },
              N_ESJQqxE: {
                variant: "KP0Nn4KBY"
              },
              oWzl85imi: {
                variant: "fj4N9m2ex"
              },
              PNeO4qznx: {
                variant: "tzTxbNo2j"
              },
              PzHEdDByo: {
                variant: "KP0Nn4KBY"
              },
              qhKlgas8o: {
                variant: "fj4N9m2ex"
              },
              quJVsLC4P: {
                variant: "oouo9Kw59"
              },
              rWUPavnoO: {
                variant: "lciTG5SyQ"
              },
              UDNJPF7Pt: {
                variant: "KP0Nn4KBY"
              },
              Ven45_IGb: {
                variant: "KP0Nn4KBY"
              },
              W5eCtIe4C: {
                variant: "fj4N9m2ex"
              },
              yD4TCl5Uq: {
                variant: "NMSDpNUX8"
              },
              YFGIQaQAp: {
                variant: "KP0Nn4KBY"
              },
              Zj22AG5Ff: {
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                variant: "FmvzcLpN0"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed3() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1qab86k",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "wDzx7CJVG",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed3() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1cn2t0z-container",
          layoutDependency: layoutDependency,
          layoutId: "Ols6wvONK-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: XYlYFlwe_,
            height: "100%",
            id: "Ols6wvONK",
            layoutId: "Ols6wvONK",
            style: {
              width: "100%"
            },
            title: DCoUXJ2W0,
            variant: "DBi28tdlr",
            width: "100%",
            ...addPropertyOverrides({
              c67tCjDMf: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "fj4N9m2ex"
              },
              EuSj6EBj5: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "fj4N9m2ex"
              },
              hfZXhT42s: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "lciTG5SyQ"
              },
              KNrssAjQG: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "KP0Nn4KBY"
              },
              lcqmKApVF: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "fj4N9m2ex"
              },
              oWzl85imi: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "fj4N9m2ex"
              },
              PVsQ6PkFg: {
                body: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
                title: UrJEjiSRm,
                variant: "cI4qCwTUS"
              },
              v7fnda6kU: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm
              },
              Ven45_IGb: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "KP0Nn4KBY"
              },
              YFGIQaQAp: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "KP0Nn4KBY"
              },
              Zj22AG5Ff: {
                body: Z_8Ed9Pia,
                variant: "lciTG5SyQ"
              },
              ZsKVlQ2Ef: {
                body: Z_8Ed9Pia,
                title: UrJEjiSRm,
                variant: "KP0Nn4KBY"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed4() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1h68fel",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "HUOZiZ_52",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed5() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-6t3icp",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "k2VCLPg1w",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed4() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-10ne2wy-container",
          layoutDependency: layoutDependency,
          layoutId: "rkVdPDuPd-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
            height: "100%",
            id: "rkVdPDuPd",
            layoutId: "rkVdPDuPd",
            style: {
              width: "100%"
            },
            title: v9D2aRikN,
            variant: "cI4qCwTUS",
            width: "100%",
            ...addPropertyOverrides({
              hfZXhT42s: {
                body: D_aLowF7q,
                variant: "kcwkX_93e"
              },
              oWzl85imi: {
                body: D_aLowF7q,
                variant: "fj4N9m2ex"
              },
              v7fnda6kU: {
                body: D_aLowF7q
              },
              YFGIQaQAp: {
                body: D_aLowF7q,
                variant: "KP0Nn4KBY"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed5() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1we2s7q",
          "data-framer-name": "Divider",
          layoutDependency: layoutDependency,
          layoutId: "RgBQ3vaCS",
          style: {
            backgroundColor: "rgb(238, 238, 238)"
          },
          transition: transition
        }), isDisplayed5() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1nibpyk-container",
          layoutDependency: layoutDependency,
          layoutId: "GlcOnwAVZ-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: UaRCl6228,
            height: "100%",
            id: "GlcOnwAVZ",
            layoutId: "GlcOnwAVZ",
            style: {
              width: "100%"
            },
            title: G9ldWBbPm,
            variant: "KP0Nn4KBY",
            width: "100%",
            ...addPropertyOverrides({
              oWzl85imi: {
                variant: "fj4N9m2ex"
              }
            }, baseVariant, gestureVariant)
          })
        }), isDisplayed5() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-bla0gm-container",
          layoutDependency: layoutDependency,
          layoutId: "v0CQ0r2L2-container",
          transition: transition,
          children: /*#__PURE__*/_jsx(FAQ, {
            body: PtJLPNgzq,
            height: "100%",
            id: "v0CQ0r2L2",
            layoutId: "v0CQ0r2L2",
            style: {
              width: "100%"
            },
            title: tueDYeE3z,
            variant: "KP0Nn4KBY",
            width: "100%",
            ...addPropertyOverrides({
              oWzl85imi: {
                variant: "fj4N9m2ex"
              }
            }, baseVariant, gestureVariant)
          })
        })]
      })
    })
  });
});
const css = ['.framer-8zmNM [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-8zmNM * { box-sizing: border-box; }", ".framer-8zmNM .framer-19zb5jt { display: block; }", ".framer-8zmNM .framer-brfeq6 { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 703px; }", ".framer-8zmNM .framer-11mmp6z-container, .framer-8zmNM .framer-veui21-container, .framer-8zmNM .framer-16u58oc-container, .framer-8zmNM .framer-js53b3-container, .framer-8zmNM .framer-1itx7o6-container, .framer-8zmNM .framer-1cn2t0z-container, .framer-8zmNM .framer-10ne2wy-container, .framer-8zmNM .framer-1nibpyk-container, .framer-8zmNM .framer-bla0gm-container { flex: none; height: auto; position: relative; width: 100%; }", ".framer-8zmNM .framer-1vpa8yo, .framer-8zmNM .framer-1xi9x3o, .framer-8zmNM .framer-1qzk6xm, .framer-8zmNM .framer-5nhjsa, .framer-8zmNM .framer-1qab86k, .framer-8zmNM .framer-1h68fel, .framer-8zmNM .framer-6t3icp, .framer-8zmNM .framer-1we2s7q { flex: none; height: 1px; overflow: visible; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-8zmNM .framer-brfeq6 { gap: 0px; } .framer-8zmNM .framer-brfeq6 > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-8zmNM .framer-brfeq6 > :first-child { margin-top: 0px; } .framer-8zmNM .framer-brfeq6 > :last-child { margin-bottom: 0px; } }", ".framer-8zmNM.framer-v-qgn552 .framer-brfeq6, .framer-8zmNM.framer-v-18ewnlb .framer-brfeq6, .framer-8zmNM.framer-v-17l55oa .framer-brfeq6, .framer-8zmNM.framer-v-hp29bu .framer-brfeq6, .framer-8zmNM.framer-v-b3z8ey .framer-brfeq6, .framer-8zmNM.framer-v-1v59jog .framer-brfeq6, .framer-8zmNM.framer-v-1wgfjfb .framer-brfeq6, .framer-8zmNM.framer-v-14v6kq7 .framer-brfeq6, .framer-8zmNM.framer-v-xhqwl .framer-brfeq6, .framer-8zmNM.framer-v-lu0aee .framer-brfeq6, .framer-8zmNM.framer-v-ny8tt3 .framer-brfeq6 { width: 500px; }", ".framer-8zmNM.framer-v-6cyf2g .framer-brfeq6, .framer-8zmNM.framer-v-1gxhnxz .framer-brfeq6, .framer-8zmNM.framer-v-d7ndli .framer-brfeq6 { width: 390px; }", ".framer-8zmNM.framer-v-cavvor .framer-brfeq6, .framer-8zmNM.framer-v-uneynz .framer-brfeq6 { width: 277px; }", ".framer-8zmNM.framer-v-7kjjsy .framer-11mmp6z-container, .framer-8zmNM.framer-v-vbh9w1 .framer-11mmp6z-container { order: 0; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1vpa8yo, .framer-8zmNM.framer-v-vbh9w1 .framer-1vpa8yo { order: 1; }", ".framer-8zmNM.framer-v-7kjjsy .framer-veui21-container, .framer-8zmNM.framer-v-vbh9w1 .framer-veui21-container { order: 2; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1xi9x3o, .framer-8zmNM.framer-v-vbh9w1 .framer-1xi9x3o { order: 3; }", ".framer-8zmNM.framer-v-7kjjsy .framer-16u58oc-container, .framer-8zmNM.framer-v-vbh9w1 .framer-16u58oc-container { order: 4; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1qzk6xm, .framer-8zmNM.framer-v-vbh9w1 .framer-1qzk6xm { order: 5; }", ".framer-8zmNM.framer-v-7kjjsy .framer-js53b3-container, .framer-8zmNM.framer-v-vbh9w1 .framer-js53b3-container { order: 6; }", ".framer-8zmNM.framer-v-7kjjsy .framer-5nhjsa, .framer-8zmNM.framer-v-vbh9w1 .framer-5nhjsa { order: 7; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1itx7o6-container, .framer-8zmNM.framer-v-vbh9w1 .framer-1itx7o6-container { order: 8; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1qab86k, .framer-8zmNM.framer-v-vbh9w1 .framer-1qab86k { order: 9; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1cn2t0z-container, .framer-8zmNM.framer-v-vbh9w1 .framer-1cn2t0z-container { order: 10; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1h68fel, .framer-8zmNM.framer-v-vbh9w1 .framer-1h68fel { order: 11; }", ".framer-8zmNM.framer-v-7kjjsy .framer-6t3icp, .framer-8zmNM.framer-v-vbh9w1 .framer-6t3icp { order: 13; }", ".framer-8zmNM.framer-v-7kjjsy .framer-10ne2wy-container, .framer-8zmNM.framer-v-vbh9w1 .framer-10ne2wy-container { order: 12; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1we2s7q, .framer-8zmNM.framer-v-vbh9w1 .framer-1we2s7q { order: 15; }", ".framer-8zmNM.framer-v-7kjjsy .framer-1nibpyk-container, .framer-8zmNM.framer-v-vbh9w1 .framer-1nibpyk-container { order: 14; }", ".framer-8zmNM.framer-v-7kjjsy .framer-bla0gm-container, .framer-8zmNM.framer-v-vbh9w1 .framer-bla0gm-container { order: 16; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicHeight 291
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicWidth 703
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"yUUjY8mcV":{"layout":["fixed","auto"]},"ELxxQYuAE":{"layout":["fixed","auto"]},"Bz5EMU0YC":{"layout":["fixed","auto"]},"D1QpKKelm":{"layout":["fixed","auto"]},"T7YlnKrPD":{"layout":["fixed","auto"]},"PVsQ6PkFg":{"layout":["fixed","auto"]},"Zj22AG5Ff":{"layout":["fixed","auto"]},"UxFtFvLpK":{"layout":["fixed","auto"]},"UDNJPF7Pt":{"layout":["fixed","auto"]},"tHmnorkX8":{"layout":["fixed","auto"]},"KNrssAjQG":{"layout":["fixed","auto"]},"w7u8_YP40":{"layout":["fixed","auto"]},"W5eCtIe4C":{"layout":["fixed","auto"]},"aKrRkR4N1":{"layout":["fixed","auto"]},"lcqmKApVF":{"layout":["fixed","auto"]},"oxMaC4Ztr":{"layout":["fixed","auto"]},"lDVTGadZf":{"layout":["fixed","auto"]},"v7fnda6kU":{"layout":["fixed","auto"]},"hfZXhT42s":{"layout":["fixed","auto"]},"G1BIJ_W6z":{"layout":["fixed","auto"]},"qhKlgas8o":{"layout":["fixed","auto"]},"yD4TCl5Uq":{"layout":["fixed","auto"]},"quJVsLC4P":{"layout":["fixed","auto"]},"lQxMMn5Si":{"layout":["fixed","auto"]},"S97euB7vk":{"layout":["fixed","auto"]},"rWUPavnoO":{"layout":["fixed","auto"]},"w5P_qXlam":{"layout":["fixed","auto"]},"PzHEdDByo":{"layout":["fixed","auto"]},"U_dwEDSHM":{"layout":["fixed","auto"]},"cbqwyJZ64":{"layout":["fixed","auto"]},"hLKVhDmW3":{"layout":["fixed","auto"]},"Sc6Py3Cqb":{"layout":["fixed","auto"]},"pFJwCAhaP":{"layout":["fixed","auto"]},"Ven45_IGb":{"layout":["fixed","auto"]},"EuSj6EBj5":{"layout":["fixed","auto"]},"ZsKVlQ2Ef":{"layout":["fixed","auto"]},"c67tCjDMf":{"layout":["fixed","auto"]},"N_ESJQqxE":{"layout":["fixed","auto"]},"g3UpsbSX9":{"layout":["fixed","auto"]},"PNeO4qznx":{"layout":["fixed","auto"]},"Cf0PIGeI_":{"layout":["fixed","auto"]},"YFGIQaQAp":{"layout":["fixed","auto"]},"oWzl85imi":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerVariables {"HIZndhE9f":"title1","wNXNR_Qr2":"title2","fnT9ikjlW":"title3","jZCPg_4s0":"title4","DCoUXJ2W0":"title5","UrJEjiSRm":"title6","v9D2aRikN":"title7","G9ldWBbPm":"title8","tueDYeE3z":"title9","lUTGPNHkx":"copy1","Jx0bbXaW2":"copy2","bijV3LJCT":"copy3","FM5IynMUd":"copy4","XYlYFlwe_":"copy5","Z_8Ed9Pia":"copy6","D_aLowF7q":"copy7","UaRCl6228":"copy8","PtJLPNgzq":"copy9"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       */
const FramerfpaWCS1ZP = withCSS(Component, css);
export default FramerfpaWCS1ZP;
FramerfpaWCS1ZP.displayName = "FAQ Stack";
FramerfpaWCS1ZP.defaultProps = {
  height: 291,
  width: 703
};
addPropertyControls(FramerfpaWCS1ZP, {
  variant: {
    options: ["Y34AGiolL", "yUUjY8mcV", "ELxxQYuAE", "Bz5EMU0YC", "D1QpKKelm", "T7YlnKrPD", "PVsQ6PkFg", "Zj22AG5Ff", "UxFtFvLpK", "UDNJPF7Pt", "tHmnorkX8", "KNrssAjQG", "w7u8_YP40", "W5eCtIe4C", "aKrRkR4N1", "lcqmKApVF", "oxMaC4Ztr", "lDVTGadZf", "v7fnda6kU", "hfZXhT42s", "G1BIJ_W6z", "qhKlgas8o", "yD4TCl5Uq", "quJVsLC4P", "lQxMMn5Si", "S97euB7vk", "rWUPavnoO", "w5P_qXlam", "PzHEdDByo", "U_dwEDSHM", "cbqwyJZ64", "hLKVhDmW3", "Sc6Py3Cqb", "pFJwCAhaP", "Ven45_IGb", "EuSj6EBj5", "ZsKVlQ2Ef", "c67tCjDMf", "N_ESJQqxE", "g3UpsbSX9", "PNeO4qznx", "Cf0PIGeI_", "YFGIQaQAp", "oWzl85imi"],
    optionTitles: ["4 Stack", "5 Stack", "3 Stack", "Mobile 4 Stack", "Mobile 5 Stack", "Mobile 3 Stack", "6 Stack", "Mobile 6 Stack", "4 Stack Medium", "5 Stack Medium", "3 Stack Medium", "6 Stack Medium", "4 Stack Medium Mobile", "5 Stack Medium Mobile", "3 Stack Medium Mobile", "6 Stack Medium Mobile", "1 Stack", "1 Stack Medium Mobile", "7 Stack", "Mobile 7 Stack", "5 Stack Medium Links", "5 Stack Medium Mobile Links", "5 Stack Medium 2 Links", "5 Stack Medium Mobile 2 Links", "Variant 25", "5 - Host", "5 - Host M", "4 - FAQ General - D", "5 - FAQ Getting Started - D", "4 - FAQ Payments - D", "4 - FAQ General - M", "5 - FAQ Getting Started - M", "4 - FAQ Payments - M", "1 - FAQ Privacy - M", "Tutoring - Desktop - v2", "Tutoring - Mobile - v2", "Marketplace - Desktop - v2", "Marketplace - Mobile - v2", "Group Classes - Desktop - v2", "Group Classes - Mobile - v2", "Accessibility - Desktop - v2", "Accessibility - Mobile - v2", "9 Stack Medium", "9 Stack Medium Mobile"],
    title: "Variant",
    type: ControlType.Enum
  },
  HIZndhE9f: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 1",
    type: ControlType.String
  },
  wNXNR_Qr2: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 2",
    type: ControlType.String
  },
  fnT9ikjlW: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 3",
    type: ControlType.String
  },
  jZCPg_4s0: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 4",
    type: ControlType.String
  },
  DCoUXJ2W0: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 5",
    type: ControlType.String
  },
  UrJEjiSRm: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 6",
    type: ControlType.String
  },
  v9D2aRikN: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 7",
    type: ControlType.String
  },
  G9ldWBbPm: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 8",
    type: ControlType.String
  },
  tueDYeE3z: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title 9",
    type: ControlType.String
  },
  lUTGPNHkx: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 1",
    type: ControlType.String
  },
  Jx0bbXaW2: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 2",
    type: ControlType.String
  },
  bijV3LJCT: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 3",
    type: ControlType.String
  },
  FM5IynMUd: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 4",
    type: ControlType.String
  },
  XYlYFlwe_: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 5",
    type: ControlType.String
  },
  Z_8Ed9Pia: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 6",
    type: ControlType.String
  },
  D_aLowF7q: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    placeholder: "",
    title: "Copy 7",
    type: ControlType.String
  },
  UaRCl6228: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 8",
    type: ControlType.String
  },
  PtJLPNgzq: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Copy 9",
    type: ControlType.String
  }
});
addFonts(FramerfpaWCS1ZP, [...FAQFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerfpaWCS1ZP",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "703",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"yUUjY8mcV\":{\"layout\":[\"fixed\",\"auto\"]},\"ELxxQYuAE\":{\"layout\":[\"fixed\",\"auto\"]},\"Bz5EMU0YC\":{\"layout\":[\"fixed\",\"auto\"]},\"D1QpKKelm\":{\"layout\":[\"fixed\",\"auto\"]},\"T7YlnKrPD\":{\"layout\":[\"fixed\",\"auto\"]},\"PVsQ6PkFg\":{\"layout\":[\"fixed\",\"auto\"]},\"Zj22AG5Ff\":{\"layout\":[\"fixed\",\"auto\"]},\"UxFtFvLpK\":{\"layout\":[\"fixed\",\"auto\"]},\"UDNJPF7Pt\":{\"layout\":[\"fixed\",\"auto\"]},\"tHmnorkX8\":{\"layout\":[\"fixed\",\"auto\"]},\"KNrssAjQG\":{\"layout\":[\"fixed\",\"auto\"]},\"w7u8_YP40\":{\"layout\":[\"fixed\",\"auto\"]},\"W5eCtIe4C\":{\"layout\":[\"fixed\",\"auto\"]},\"aKrRkR4N1\":{\"layout\":[\"fixed\",\"auto\"]},\"lcqmKApVF\":{\"layout\":[\"fixed\",\"auto\"]},\"oxMaC4Ztr\":{\"layout\":[\"fixed\",\"auto\"]},\"lDVTGadZf\":{\"layout\":[\"fixed\",\"auto\"]},\"v7fnda6kU\":{\"layout\":[\"fixed\",\"auto\"]},\"hfZXhT42s\":{\"layout\":[\"fixed\",\"auto\"]},\"G1BIJ_W6z\":{\"layout\":[\"fixed\",\"auto\"]},\"qhKlgas8o\":{\"layout\":[\"fixed\",\"auto\"]},\"yD4TCl5Uq\":{\"layout\":[\"fixed\",\"auto\"]},\"quJVsLC4P\":{\"layout\":[\"fixed\",\"auto\"]},\"lQxMMn5Si\":{\"layout\":[\"fixed\",\"auto\"]},\"S97euB7vk\":{\"layout\":[\"fixed\",\"auto\"]},\"rWUPavnoO\":{\"layout\":[\"fixed\",\"auto\"]},\"w5P_qXlam\":{\"layout\":[\"fixed\",\"auto\"]},\"PzHEdDByo\":{\"layout\":[\"fixed\",\"auto\"]},\"U_dwEDSHM\":{\"layout\":[\"fixed\",\"auto\"]},\"cbqwyJZ64\":{\"layout\":[\"fixed\",\"auto\"]},\"hLKVhDmW3\":{\"layout\":[\"fixed\",\"auto\"]},\"Sc6Py3Cqb\":{\"layout\":[\"fixed\",\"auto\"]},\"pFJwCAhaP\":{\"layout\":[\"fixed\",\"auto\"]},\"Ven45_IGb\":{\"layout\":[\"fixed\",\"auto\"]},\"EuSj6EBj5\":{\"layout\":[\"fixed\",\"auto\"]},\"ZsKVlQ2Ef\":{\"layout\":[\"fixed\",\"auto\"]},\"c67tCjDMf\":{\"layout\":[\"fixed\",\"auto\"]},\"N_ESJQqxE\":{\"layout\":[\"fixed\",\"auto\"]},\"g3UpsbSX9\":{\"layout\":[\"fixed\",\"auto\"]},\"PNeO4qznx\":{\"layout\":[\"fixed\",\"auto\"]},\"Cf0PIGeI_\":{\"layout\":[\"fixed\",\"auto\"]},\"YFGIQaQAp\":{\"layout\":[\"fixed\",\"auto\"]},\"oWzl85imi\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerVariables": "{\"HIZndhE9f\":\"title1\",\"wNXNR_Qr2\":\"title2\",\"fnT9ikjlW\":\"title3\",\"jZCPg_4s0\":\"title4\",\"DCoUXJ2W0\":\"title5\",\"UrJEjiSRm\":\"title6\",\"v9D2aRikN\":\"title7\",\"G9ldWBbPm\":\"title8\",\"tueDYeE3z\":\"title9\",\"lUTGPNHkx\":\"copy1\",\"Jx0bbXaW2\":\"copy2\",\"bijV3LJCT\":\"copy3\",\"FM5IynMUd\":\"copy4\",\"XYlYFlwe_\":\"copy5\",\"Z_8Ed9Pia\":\"copy6\",\"D_aLowF7q\":\"copy7\",\"UaRCl6228\":\"copy8\",\"PtJLPNgzq\":\"copy9\"}",
        "framerIntrinsicHeight": "291"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./fpaWCS1ZP.map