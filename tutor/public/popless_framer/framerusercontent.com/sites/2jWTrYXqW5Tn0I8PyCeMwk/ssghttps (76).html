import{containerStyles,emptyStateStyle as defaultEmptyStateStyle}from"https://framer.com/m/framer/default-utils.js@^0.43.0";export const emptyStateStyle={...containerStyles,...defaultEmptyStateStyle,textAlign:"center",padding:15,width:200,height:100,overflow:"hidden"};export const neutralStateStyle={...emptyStateStyle,color:"#09f",background:"rgb(0, 153, 255, 0.1)",borderColor:"#09f"};export const stateTitleStyle={fontSize:12,fontWeight:600,margin:0};export const stateParagraphStyle={fontSize:12,maxWidth:200,lineHeight:1.4,margin:"5px 0 0 0"};
export const __FramerMetadata__ = {"exports":{"neutralStateStyle":{"type":"variable","annotations":{"framerContractVersion":"1"}},"stateTitleStyle":{"type":"variable","annotations":{"framerContractVersion":"1"}},"emptyStateStyle":{"type":"variable","annotations":{"framerContractVersion":"1"}},"stateParagraphStyle":{"type":"variable","annotations":{"framerContractVersion":"1"}},"__FramerMetadata__":{"type":"variable"}}}
//# sourceMappingURL=./styles.map