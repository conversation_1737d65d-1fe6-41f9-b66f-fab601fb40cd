// Generated by <PERSON><PERSON><PERSON> (b997b22)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const cycleOrder = ["ug1m0le6P", "uJSvrvfuH"];
const variantClassNames = {
  ug1m0le6P: "framer-v-4me0bo",
  uJSvrvfuH: "framer-v-6s9mjq"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Close: "uJSvrvfuH",
  Open: "ug1m0le6P"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "ug1m0le6P",
  tap3: EbowFJKgM,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "ug1m0le6P",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTapa9u40r = activeVariantCallback(async (...args) => {
    if (EbowFJKgM) {
      const res = await EbowFJKgM(...args);
      if (res === false) return false;
    }
    setVariant("uJSvrvfuH");
  });
  const onTap101zaj9 = activeVariantCallback(async (...args) => {
    if (EbowFJKgM) {
      const res = await EbowFJKgM(...args);
      if (res === false) return false;
    }
    setVariant("ug1m0le6P");
  });
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-fNuUZ", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsx(motion.div, {
        ...restProps,
        className: cx("framer-4me0bo", className),
        "data-framer-name": "Open",
        "data-highlight": true,
        layoutDependency: layoutDependency,
        layoutId: "ug1m0le6P",
        onTap: onTapa9u40r,
        ref: ref,
        style: {
          backgroundColor: "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))",
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          uJSvrvfuH: {
            "data-framer-name": "Close",
            onTap: onTap101zaj9
          }
        }, baseVariant, gestureVariant),
        children: /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-3gimif",
          layoutDependency: layoutDependency,
          layoutId: "CJWVasfk_",
          style: {
            backgroundColor: "var(--token-a1e13941-d14a-4692-855b-e794eebdfa6f, rgb(229, 232, 232))",
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8
          },
          transition: transition,
          children: [/*#__PURE__*/_jsx(motion.div, {
            className: "framer-15rwfem",
            layoutDependency: layoutDependency,
            layoutId: "xpZQc0zTU",
            style: {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
              borderBottomLeftRadius: 100,
              borderBottomRightRadius: 100,
              borderTopLeftRadius: 100,
              borderTopRightRadius: 100,
              rotate: 0
            },
            transition: transition,
            variants: {
              uJSvrvfuH: {
                rotate: 45
              }
            }
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-l6dh8w",
            layoutDependency: layoutDependency,
            layoutId: "fTsAlvv7z",
            style: {
              backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
              borderBottomLeftRadius: 100,
              borderBottomRightRadius: 100,
              borderTopLeftRadius: 100,
              borderTopRightRadius: 100,
              rotate: 0
            },
            transition: transition,
            variants: {
              uJSvrvfuH: {
                rotate: -45
              }
            }
          })]
        })
      })
    })
  });
});
const css = ['.framer-fNuUZ [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-fNuUZ * { box-sizing: border-box; }", ".framer-fNuUZ .framer-cxd4nt { display: block; }", ".framer-fNuUZ .framer-4me0bo { cursor: pointer; height: 72px; overflow: visible; position: relative; width: 72px; }", ".framer-fNuUZ .framer-3gimif { flex: none; height: 32px; left: calc(50.00000000000002% - 32px / 2); overflow: visible; position: absolute; top: calc(50.00000000000002% - 32px / 2); width: 32px; }", ".framer-fNuUZ .framer-15rwfem { flex: none; height: 2px; left: calc(50.00000000000002% - 18px / 2); overflow: visible; position: absolute; top: 11px; width: 18px; }", ".framer-fNuUZ .framer-l6dh8w { bottom: 11px; flex: none; height: 2px; left: calc(50.00000000000002% - 18px / 2); overflow: visible; position: absolute; width: 18px; }", ".framer-fNuUZ.framer-v-6s9mjq .framer-15rwfem { top: calc(50.00000000000002% - 2px / 2); }", ".framer-fNuUZ.framer-v-6s9mjq .framer-l6dh8w { bottom: unset; top: calc(50.00000000000002% - 2px / 2); }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     * @framerIntrinsicHeight 72
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     * @framerIntrinsicWidth 72
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","fixed"]},"uJSvrvfuH":{"layout":["fixed","fixed"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     * @framerVariables {"EbowFJKgM":"tap3"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     */
const FramerxU0KUKjPH = withCSS(Component, css);
export default FramerxU0KUKjPH;
FramerxU0KUKjPH.displayName = "Assets/Hamburger";
FramerxU0KUKjPH.defaultProps = {
  height: 72,
  width: 72
};
addPropertyControls(FramerxU0KUKjPH, {
  variant: {
    options: ["ug1m0le6P", "uJSvrvfuH"],
    optionTitles: ["Open", "Close"],
    title: "Variant",
    type: ControlType.Enum
  },
  EbowFJKgM: {
    title: "Tap 3",
    type: ControlType.EventHandler
  }
});
addFonts(FramerxU0KUKjPH, []);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerxU0KUKjPH",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "72",
        "framerVariables": "{\"EbowFJKgM\":\"tap3\"}",
        "framerIntrinsicWidth": "72",
        "framerContractVersion": "1",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"fixed\"]},\"uJSvrvfuH\":{\"layout\":[\"fixed\",\"fixed\"]}}}"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./xU0KUKjPH.map