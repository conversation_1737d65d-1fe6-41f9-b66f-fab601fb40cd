import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter-Bold"]);
export const fonts = [];
export const css = ['.framer-HisJT .framer-styles-preset-o3e5h0:not(.rich-text-wrapper), .framer-HisJT .framer-styles-preset-o3e5h0.rich-text-wrapper h1, .framer-HisJT .framer-styles-preset-o3e5h0.rich-text-wrapper [data-preset-tag="h1"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 60px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: -2.4px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; }'];
export const className = "framer-HisJT";
export const __FramerMetadata__ = {
  "exports": {
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};