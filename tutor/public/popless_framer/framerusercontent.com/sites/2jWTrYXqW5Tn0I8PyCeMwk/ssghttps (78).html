// Generated by Framer (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Image, Link, PropertyOverrides, removeHiddenBreakpointLayers, RichText, useActiveVariantCallback, useHydratedBreakpointVariants, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import Typeform from "https://framerusercontent.com/modules/F6IzcnH090BibK8JiJh0/YiH6Irb8csUljprMirUP/Typeform.js";
import Intercom from "https://framerusercontent.com/modules/UIhUTcd796YH7Ndybys8/totj55n8qE3VYpdXhshW/Intercom.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import * as sharedStyle from "https://framerusercontent.com/modules/1EpHnr2nicQOm2hJS1GZ/fX3rD1mDGtZf0YfpuTmU/COkUUCscg.js";
import metadataProvider from "https://framerusercontent.com/modules/z5vJu2GdtjinABhr2eOj/417o249THmOs0Yjg2ybw/gMU7brHGJ.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const IntercomFonts = getFonts(Intercom);
const TypeformFonts = getFonts(Typeform);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["n6_stw_Hi", "frcFRkhPR", "HOZChxxat"];
const breakpoints = {
  frcFRkhPR: "(min-width: 810px) and (max-width: 1279px)",
  HOZChxxat: "(max-width: 809px)",
  n6_stw_Hi: "(min-width: 1280px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  frcFRkhPR: "framer-v-6g857d",
  HOZChxxat: "framer-v-183mh6c",
  n6_stw_Hi: "framer-v-roxc3z"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("n6_stw_Hi", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  Desktop: "n6_stw_Hi",
  Phone: "HOZChxxat",
  Tablet: "frcFRkhPR"
};
const transitions = {
  default: {
    duration: 0
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "n6_stw_Hi",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata = metadataProvider();
    document.title = metadata.title || "";
    if (metadata.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata.viewport);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const poplessLink1hxdeaz = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noreferrer noopener");
  });
  const twitterLink1bj8fo6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noreferrer noopener");
  });
  const isDisplayed1 = () => {
    if (baseVariant === "HOZChxxat") return !isBrowser();
    return true;
  };
  const isDisplayed2 = () => {
    if (baseVariant === "HOZChxxat") return true;
    return !isBrowser();
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "n6_stw_Hi",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        "data-framer-generated": true,
        className: cx("framer-hsemL", sharedStyle.className),
        style: {
          display: "contents",
          pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
        },
        children: [/*#__PURE__*/_jsx(motion.div, {
          ...restProps,
          className: cx("framer-roxc3z", className),
          ref: ref,
          style: {
            ...style
          },
          children: /*#__PURE__*/_jsxs(motion.main, {
            className: "framer-gc2xgh",
            "data-framer-name": "Main",
            name: "Main",
            children: [/*#__PURE__*/_jsx(Container, {
              className: "framer-1myr2tr-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  frcFRkhPR: {
                    variant: "aZMkidfTG"
                  },
                  HOZChxxat: {
                    variant: "aZMkidfTG"
                  }
                },
                children: /*#__PURE__*/_jsx(HeaderNavigation, {
                  height: "100%",
                  id: "bSsIh2z2V",
                  layoutId: "bSsIh2z2V",
                  style: {
                    width: "100%"
                  },
                  variant: "Dg2JgAbsI",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsxs(motion.header, {
              className: "framer-uazonv",
              "data-framer-name": "Stack",
              name: "Stack",
              children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  frcFRkhPR: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("h1", {
                        style: {
                          "--framer-line-height": "60px",
                          "--framer-text-alignment": "center"
                        },
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNzAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "58px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-2px",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Get started."
                        })
                      })
                    })
                  },
                  HOZChxxat: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("h1", {
                        style: {
                          "--framer-text-alignment": "center"
                        },
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNzAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "41px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-2px",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Get started."
                        })
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("h1", {
                      style: {
                        "--framer-line-height": "60px",
                        "--framer-text-alignment": "center"
                      },
                      children: /*#__PURE__*/_jsx("span", {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItNzAw",
                          "--framer-font-family": '"Inter", sans-serif',
                          "--framer-font-size": "60px",
                          "--framer-font-style": "normal",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-2px",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "Get started."
                      })
                    })
                  }),
                  className: "framer-og4any",
                  fonts: ["GF;Inter-700"],
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              }), /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  HOZChxxat: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("h1", {
                        style: {
                          "--framer-line-height": "56px",
                          "--framer-text-alignment": "center"
                        },
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNzAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "39px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-0.9px",
                            "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                          },
                          children: "It takes 20 seconds."
                        })
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichText, {
                  __fromCanvasComponent: true,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("h1", {
                      style: {
                        "--framer-line-height": "56px",
                        "--framer-text-alignment": "center"
                      },
                      children: /*#__PURE__*/_jsx("span", {
                        style: {
                          "--font-selector": "R0Y7SW50ZXItNzAw",
                          "--framer-font-family": '"Inter", sans-serif',
                          "--framer-font-size": "60px",
                          "--framer-font-style": "normal",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-2px",
                          "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))"
                        },
                        children: "It takes 20 seconds."
                      })
                    })
                  }),
                  className: "framer-1fgdjgm",
                  fonts: ["GF;Inter-700"],
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              }), /*#__PURE__*/_jsx(Container, {
                className: "framer-gftnkb-container",
                children: /*#__PURE__*/_jsx(Intercom, {
                  appId: "lsvujawt",
                  height: "100%",
                  id: "n8V9Lrkx0",
                  layoutId: "n8V9Lrkx0",
                  style: {
                    height: "100%",
                    width: "100%"
                  },
                  width: "100%"
                })
              })]
            }), /*#__PURE__*/_jsxs(motion.section, {
              className: "framer-1nd8bih",
              children: [isDisplayed1() && /*#__PURE__*/_jsx(Image, {
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 479,
                  intrinsicWidth: 832,
                  pixelHeight: 479,
                  pixelWidth: 832,
                  src: new URL("https://framerusercontent.com/images/ZRyYZtThcnxvGZQSqDFTZNvtwjE.png").href
                },
                className: "framer-113s7uk hidden-183mh6c",
                children: /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-71j67q",
                  children: /*#__PURE__*/_jsx(motion.div, {
                    className: "framer-3yh45l",
                    transformTemplate: (_, t) => `translateX(-50%) ${t}`,
                    children: /*#__PURE__*/_jsx(Container, {
                      className: "framer-1dkviub-container",
                      children: /*#__PURE__*/_jsx(Typeform, {
                        formId: "chvbrCM3",
                        height: "100%",
                        hideFooter: false,
                        hideHeaders: false,
                        id: "I4VPt3Cwm",
                        layoutId: "I4VPt3Cwm",
                        style: {
                          height: "100%",
                          width: "100%"
                        },
                        width: "100%"
                      })
                    })
                  })
                })
              }), isDisplayed2() && /*#__PURE__*/_jsx(Image, {
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 510,
                  intrinsicWidth: 440,
                  pixelHeight: 510,
                  pixelWidth: 440,
                  src: new URL("https://framerusercontent.com/images/BoBENhrUUU9OHt58etj4KxLzto.png").href
                },
                className: "framer-67xgze hidden-roxc3z hidden-6g857d",
                children: /*#__PURE__*/_jsx(motion.div, {
                  className: "framer-16755m6",
                  children: /*#__PURE__*/_jsx(motion.div, {
                    className: "framer-yqvsky",
                    transformTemplate: (_, t) => `translate(-50%, -50%) ${t}`,
                    children: /*#__PURE__*/_jsx(Container, {
                      className: "framer-2wjeq-container",
                      children: /*#__PURE__*/_jsx(Typeform, {
                        formId: "chvbrCM3",
                        height: "100%",
                        hideFooter: true,
                        hideHeaders: true,
                        id: "m6VLCC5oc",
                        layoutId: "m6VLCC5oc",
                        style: {
                          height: "100%",
                          width: "100%"
                        },
                        width: "100%"
                      })
                    })
                  })
                })
              })]
            }), /*#__PURE__*/_jsx(motion.section, {
              className: "framer-1b5l3up",
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs("p", {
                    style: {
                      "--framer-line-height": "30px",
                      "--framer-text-alignment": "center"
                    },
                    children: [/*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                      },
                      children: "Form not loading? "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx("a", {
                        className: "framer-styles-preset-1utq9v6",
                        "data-styles-preset": "COkUUCscg",
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx("span", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "500",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: "Click here"
                        })
                      })
                    }), /*#__PURE__*/_jsx("span", {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                      },
                      children: "."
                    })]
                  })
                }),
                className: "framer-19ya4uo",
                fonts: ["GF;Inter-500"],
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-1vi4wrj-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  HOZChxxat: {
                    variant: "Zz_9kWOfb"
                  }
                },
                children: /*#__PURE__*/_jsx(FooterNew, {
                  height: "100%",
                  id: "yLSqPWe2r",
                  layoutId: "yLSqPWe2r",
                  poplessLink: poplessLink1hxdeaz,
                  style: {
                    width: "100%"
                  },
                  twitterLink: twitterLink1bj8fo6,
                  variant: "zyTRmFlly",
                  width: "100%"
                })
              })
            })]
          })
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-hsemL [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-hsemL .framer-hhr2pe { display: block; }", ".framer-hsemL .framer-roxc3z { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }", ".framer-hsemL .framer-gc2xgh { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-hsemL .framer-1myr2tr-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-hsemL .framer-uazonv { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 2px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 150px 120px 11px 120px; position: relative; width: 1200px; }", ".framer-hsemL .framer-og4any, .framer-hsemL .framer-1fgdjgm { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 780px; word-break: break-word; word-wrap: break-word; }", ".framer-hsemL .framer-gftnkb-container { flex: none; height: 1px; position: relative; width: 1px; }", ".framer-hsemL .framer-1nd8bih { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: visible; padding: 28px 0px 73px 0px; position: relative; width: 1200px; }", ".framer-hsemL .framer-113s7uk { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 479px; justify-content: flex-start; overflow: visible; padding: 48px 48px 48px 48px; position: relative; width: 69%; }", ".framer-hsemL .framer-71j67q { flex: none; height: 490px; overflow: visible; position: relative; width: 760px; }", ".framer-hsemL .framer-3yh45l { align-content: center; align-items: center; background-color: #ffffff; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; border-top-left-radius: 24px; border-top-right-radius: 24px; bottom: -40px; box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; left: 50%; overflow: visible; padding: 20px 20px 20px 20px; position: absolute; transform: translateX(-50%); width: min-content; }", ".framer-hsemL .framer-1dkviub-container { flex: none; height: 450px; position: relative; width: 720px; }", ".framer-hsemL .framer-67xgze { align-content: center; align-items: center; aspect-ratio: 0.8706624605678234 / 1; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 130%; justify-content: flex-start; overflow: visible; padding: 48px 48px 48px 48px; position: relative; width: var(--framer-aspect-ratio-supported, 174px); }", ".framer-hsemL .framer-16755m6 { flex: none; height: 385px; overflow: visible; position: relative; width: 100%; }", ".framer-hsemL .framer-yqvsky { align-content: center; align-items: center; background-color: #ffffff; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; border-top-left-radius: 24px; border-top-right-radius: 24px; box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; left: 50%; overflow: visible; padding: 20px 20px 20px 20px; position: absolute; top: 57%; transform: translate(-50%, -50%); width: min-content; }", ".framer-hsemL .framer-2wjeq-container { flex: none; height: 400px; position: relative; width: 300px; }", ".framer-hsemL .framer-1b5l3up { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 150px 0px; position: relative; width: 1200px; }", ".framer-hsemL .framer-19ya4uo { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 700px; word-break: break-word; word-wrap: break-word; }", ".framer-hsemL .framer-1vi4wrj-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hsemL .framer-roxc3z, .framer-hsemL .framer-gc2xgh, .framer-hsemL .framer-uazonv, .framer-hsemL .framer-1nd8bih, .framer-hsemL .framer-113s7uk, .framer-hsemL .framer-3yh45l, .framer-hsemL .framer-67xgze, .framer-hsemL .framer-yqvsky, .framer-hsemL .framer-1b5l3up { gap: 0px; } .framer-hsemL .framer-roxc3z > *, .framer-hsemL .framer-3yh45l > *, .framer-hsemL .framer-67xgze > *, .framer-hsemL .framer-yqvsky > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-hsemL .framer-roxc3z > :first-child, .framer-hsemL .framer-gc2xgh > :first-child, .framer-hsemL .framer-uazonv > :first-child, .framer-hsemL .framer-1nd8bih > :first-child, .framer-hsemL .framer-3yh45l > :first-child, .framer-hsemL .framer-67xgze > :first-child, .framer-hsemL .framer-yqvsky > :first-child, .framer-hsemL .framer-1b5l3up > :first-child { margin-top: 0px; } .framer-hsemL .framer-roxc3z > :last-child, .framer-hsemL .framer-gc2xgh > :last-child, .framer-hsemL .framer-uazonv > :last-child, .framer-hsemL .framer-1nd8bih > :last-child, .framer-hsemL .framer-3yh45l > :last-child, .framer-hsemL .framer-67xgze > :last-child, .framer-hsemL .framer-yqvsky > :last-child, .framer-hsemL .framer-1b5l3up > :last-child { margin-bottom: 0px; } .framer-hsemL .framer-gc2xgh > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-hsemL .framer-uazonv > * { margin: 0px; margin-bottom: calc(2px / 2); margin-top: calc(2px / 2); } .framer-hsemL .framer-1nd8bih > *, .framer-hsemL .framer-1b5l3up > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-hsemL .framer-113s7uk > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-hsemL .framer-113s7uk > :first-child { margin-left: 0px; } .framer-hsemL .framer-113s7uk > :last-child { margin-right: 0px; } }", "@media (min-width: 1280px) { .framer-hsemL .hidden-roxc3z { display: none !important; } }", "@media (min-width: 810px) and (max-width: 1279px) { .framer-hsemL .hidden-6g857d { display: none !important; } .framer-hsemL .framer-roxc3z { width: 810px; } .framer-hsemL .framer-uazonv { padding: 150px 24px 11px 24px; width: 100%; } .framer-hsemL .framer-og4any, .framer-hsemL .framer-1b5l3up, .framer-hsemL .framer-19ya4uo { width: 100%; } .framer-hsemL .framer-1nd8bih { justify-content: center; padding: 28px 24px 142px 24px; width: 100%; } .framer-hsemL .framer-113s7uk { height: 439px; width: 762px; } .framer-hsemL .framer-71j67q { width: 666px; } .framer-hsemL .framer-3yh45l { bottom: unset; height: 490px; justify-content: center; top: 61px; } .framer-hsemL .framer-1dkviub-container { width: 650px; }}", "@media (max-width: 809px) { .framer-hsemL .hidden-183mh6c { display: none !important; } .framer-hsemL .framer-roxc3z { width: 390px; } .framer-hsemL .framer-1myr2tr-container { order: 0; } .framer-hsemL .framer-uazonv { gap: 0px; order: 1; padding: 150px 24px 22px 24px; width: 100%; } .framer-hsemL .framer-og4any, .framer-hsemL .framer-19ya4uo { width: 100%; } .framer-hsemL .framer-1nd8bih { gap: 69px; height: 567px; order: 2; padding: 25px 0px 200px 0px; width: 100%; } .framer-hsemL .framer-67xgze { width: var(--framer-aspect-ratio-supported, 388px); } .framer-hsemL .framer-1b5l3up { order: 3; padding: 0px 0px 100px 0px; width: 100%; } .framer-hsemL .framer-1vi4wrj-container { order: 4; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-hsemL .framer-uazonv, .framer-hsemL .framer-1nd8bih { gap: 0px; } .framer-hsemL .framer-uazonv > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-hsemL .framer-uazonv > :first-child, .framer-hsemL .framer-1nd8bih > :first-child { margin-top: 0px; } .framer-hsemL .framer-uazonv > :last-child, .framer-hsemL .framer-1nd8bih > :last-child { margin-bottom: 0px; } .framer-hsemL .framer-1nd8bih > * { margin: 0px; margin-bottom: calc(69px / 2); margin-top: calc(69px / 2); } }}", ...sharedStyle.css]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerIntrinsicHeight 1441
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerIntrinsicWidth 1280
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"frcFRkhPR":{"layout":["fixed","auto"]},"HOZChxxat":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           */
const FramergMU7brHGJ = withCSS(Component, css);
export default FramergMU7brHGJ;
FramergMU7brHGJ.displayName = "Request Access";
FramergMU7brHGJ.defaultProps = {
  height: 1441,
  width: 1280
};
addFonts(FramergMU7brHGJ, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/gMU7brHGJ:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/gMU7brHGJ:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, ...HeaderNavigationFonts, ...IntercomFonts, ...TypeformFonts, ...FooterNewFonts, ...sharedStyle.fonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramergMU7brHGJ",
      "slots": [],
      "annotations": {
        "framerIntrinsicWidth": "1280",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"frcFRkhPR\":{\"layout\":[\"fixed\",\"auto\"]},\"HOZChxxat\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerResponsiveScreen": "",
        "framerIntrinsicHeight": "1441",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};