// Generated by Framer (b964d09)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, Image, PropertyOverrides, removeHiddenBreakpointLayers, RichText, SVG, Text, useActiveVariantCallback, useHydratedBreakpointVariants, withCSS, withFX } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import FormSpark from "https://framerusercontent.com/modules/vkHAj2Yk0mTnbM6ZdN5c/PlLMu0V3HsBupvdXeFrH/FormSpark.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import FAQStack from "https://framerusercontent.com/modules/o88pEDyM5V3BTkcTTtDj/vP5d3bseQIJiN0eHwQPA/fpaWCS1ZP.js";
import AssetsButtonMain from "https://framerusercontent.com/modules/ocbwDwrCSLiVC873hFen/SZ3EEPoJIeHkm0wEe9x3/W7ao3lSRJ.js";
import AssetsLogo from "https://framerusercontent.com/modules/2C84XLlICCTdnOkBT7Qv/UhnyqDy13mV1BKFPdxi9/xp2JJtRrF.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import metadataProvider from "https://framerusercontent.com/modules/xZl3Ds1yOU0NmC7sQwtc/fKC1BkzKgdvRrJggA49u/QXYyFVKW8.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const RichTextWithFX = withFX(RichText);
const FormSparkFonts = getFonts(FormSpark);
const ContainerWithFX = withFX(Container);
const ImageWithFX = withFX(Image);
const MotionDivWithFX = withFX(motion.div);
const TextWithFX = withFX(Text);
const FAQStackFonts = getFonts(FAQStack);
const AssetsLogoFonts = getFonts(AssetsLogo);
const AssetsButtonMainFonts = getFonts(AssetsButtonMain);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["uKYFsJzgy", "pSYmZKfbO", "NTyq1hRL8", "ndrGg8iNq", "AXlhJB8sW"];
const breakpoints = {
  AXlhJB8sW: "(min-width: 1650px) and (max-width: 1919px)",
  ndrGg8iNq: "(min-width: 1920px)",
  NTyq1hRL8: "(min-width: 810px) and (max-width: 1279px)",
  pSYmZKfbO: "(max-width: 809px)",
  uKYFsJzgy: "(min-width: 1280px) and (max-width: 1649px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  AXlhJB8sW: "framer-v-tidzzc",
  ndrGg8iNq: "framer-v-11im4pt",
  NTyq1hRL8: "framer-v-t4h2ks",
  pSYmZKfbO: "framer-v-z0cqn6",
  uKYFsJzgy: "framer-v-xxfe1n"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("uKYFsJzgy", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  "Desktop L": "ndrGg8iNq",
  "Desktop Medium": "AXlhJB8sW",
  Desktop: "uKYFsJzgy",
  Phone: "pSYmZKfbO",
  Tablet: "NTyq1hRL8"
};
const transitions = {
  default: {
    duration: 0
  }
};
const animation = {
  opacity: 0,
  rotate: 0,
  rotateX: 0,
  rotateY: 0,
  scale: 1,
  x: 0,
  y: 20
};
const transition1 = {
  damping: 60,
  delay: .1,
  mass: 1,
  stiffness: 500,
  type: "spring"
};
const animation1 = {
  opacity: 0,
  rotate: 0,
  rotateX: 0,
  rotateY: 0,
  scale: 1,
  transition: transition1,
  x: 0,
  y: 20
};
const transition2 = {
  damping: 60,
  delay: .3,
  mass: 1,
  stiffness: 500,
  type: "spring"
};
const animation2 = {
  opacity: 0,
  rotate: 0,
  rotateX: 0,
  rotateY: 0,
  scale: 1,
  transition: transition2,
  x: 0,
  y: 20
};
const transition3 = {
  damping: 60,
  delay: .4,
  mass: 1,
  stiffness: 500,
  type: "spring"
};
const animation3 = {
  opacity: 0,
  rotate: 0,
  rotateX: 0,
  rotateY: 0,
  scale: 1,
  transition: transition3,
  x: 0,
  y: 20
};
const transition4 = {
  damping: 60,
  delay: .2,
  mass: 1,
  stiffness: 500,
  type: "spring"
};
const animation4 = {
  opacity: 0,
  rotate: 0,
  rotateX: 0,
  rotateY: 0,
  scale: 1,
  transition: transition4,
  x: 0,
  y: 20
};
const metadata = metadataProvider();
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style,
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "uKYFsJzgy",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata1 = metadataProvider();
    document.title = metadata1.title || "";
    if (metadata1.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata1.viewport);
    }
    if (metadata1.bodyClassName) {
      Array.from(document.body.classList).filter(c => c.startsWith("framer-body-")).map(c => document.body.classList.remove(c));
      document.body.classList.add(metadata1.bodyClassName);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const tap904hit = activeVariantCallback(async (...args) => {
    window.open("https://www.popless.com/signup", "_blank", "noopener");
  });
  const poplessLink1xqqrqe = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noopener");
  });
  const twitterLinkzdpqk6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noopener");
  });
  const isDisplayed = () => {
    if (baseVariant === "pSYmZKfbO") return !isBrowser();
    return true;
  };
  const isDisplayed1 = () => {
    if (baseVariant === "pSYmZKfbO") return true;
    return !isBrowser();
  };
  const defaultLayoutId = React.useId();
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "uKYFsJzgy",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        className: cx("framer-4kBfB"),
        style: {
          display: "contents"
        },
        children: [/*#__PURE__*/_jsxs(motion.div, {
          ...restProps,
          className: cx("framer-xxfe1n", className),
          ref: ref,
          style: {
            ...style
          },
          children: [/*#__PURE__*/_jsx(Container, {
            className: "framer-1qdpz30-container",
            "data-framer-name": "Navigation",
            name: "Navigation",
            children: /*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                NTyq1hRL8: {
                  variant: "aZMkidfTG"
                },
                pSYmZKfbO: {
                  variant: "aZMkidfTG"
                }
              },
              children: /*#__PURE__*/_jsx(HeaderNavigation, {
                height: "100%",
                id: "msJdNluBu",
                layoutId: "msJdNluBu",
                name: "Navigation",
                style: {
                  width: "100%"
                },
                variant: "Dg2JgAbsI",
                width: "100%"
              })
            })
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-4by23b",
            children: [/*#__PURE__*/_jsxs(motion.div, {
              className: "framer-zc2tfg",
              "data-framer-name": "hero",
              name: "hero",
              children: [/*#__PURE__*/_jsx(motion.div, {
                className: "framer-1itmnlg",
                children: /*#__PURE__*/_jsx(PropertyOverrides, {
                  breakpoint: baseVariant,
                  overrides: {
                    NTyq1hRL8: {
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--font-selector": "SW50ZXItQm9sZA==",
                            "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                            "--framer-font-size": "20px",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-0.8px",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: [/*#__PURE__*/_jsx("span", {
                            style: {
                              "--framer-font-size": "44px"
                            },
                            children: "Power your classes"
                          }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                            style: {
                              "--framer-font-size": "44px"
                            },
                            children: "from an all-in-one dashboard"
                          })]
                        })
                      })
                    },
                    pSYmZKfbO: {
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--font-selector": "SW50ZXItQm9sZA==",
                            "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                            "--framer-font-size": "20px",
                            "--framer-font-weight": "700",
                            "--framer-letter-spacing": "-0.8px",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                          },
                          children: [/*#__PURE__*/_jsx("span", {
                            style: {
                              "--framer-font-size": "24px"
                            },
                            children: "Power your classes"
                          }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                            style: {
                              "--framer-font-size": "24px"
                            },
                            children: "from an all-in-one dashboard"
                          })]
                        })
                      })
                    }
                  },
                  children: /*#__PURE__*/_jsx(RichTextWithFX, {
                    __framer__animate: {
                      transition: transition1
                    },
                    __framer__animateOnce: true,
                    __framer__enter: animation,
                    __framer__exit: animation1,
                    __framer__styleAppearEffectEnabled: true,
                    __framer__threshold: 0,
                    __fromCanvasComponent: true,
                    __perspectiveFX: false,
                    __targetOpacity: 1,
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsxs("p", {
                        style: {
                          "--font-selector": "SW50ZXItQm9sZA==",
                          "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                          "--framer-font-size": "70px",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-2px",
                          "--framer-line-height": "76px",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: ["Power your classes", /*#__PURE__*/_jsx("br", {}), "from an all-in-one dashboard"]
                      })
                    }),
                    className: "framer-lf16sh",
                    fonts: ["Inter-Bold"],
                    verticalAlignment: "top",
                    withExternalLayout: true
                  })
                })
              }), /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  NTyq1hRL8: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsxs("p", {
                        style: {
                          "--framer-line-height": "1.4em",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: ["Live learning is effective, engaging, and energizing. Discover how Popless can", /*#__PURE__*/_jsx("br", {}), "supercharge your classes and engage your students."]
                      })
                    })
                  },
                  pSYmZKfbO: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("p", {
                        style: {
                          "--framer-line-height": "1.4em",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "Live learning is effective, engaging, and energizing. Discover how Popless can supercharge your classes."
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichTextWithFX, {
                  __framer__animate: {
                    transition: transition2
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation2,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __fromCanvasComponent: true,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--framer-font-size": "24px",
                        "--framer-line-height": "1.4em",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                      },
                      children: "Live learning is effective, engaging, and energizing. Discover how Popless can supercharge your classes and engage your students."
                    })
                  }),
                  className: "framer-q0hqlm",
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              }), /*#__PURE__*/_jsx(ContainerWithFX, {
                __framer__animate: {
                  transition: transition3
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation3,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __perspectiveFX: false,
                __targetOpacity: 1,
                className: "framer-1hzn9h8-container",
                children: /*#__PURE__*/_jsx(PropertyOverrides, {
                  breakpoint: baseVariant,
                  overrides: {
                    pSYmZKfbO: {
                      layout: "vertical"
                    }
                  },
                  children: /*#__PURE__*/_jsx(FormSpark, {
                    borderRadius: 50,
                    bottomLeftRadius: 50,
                    bottomRightRadius: 50,
                    button: {
                      color: "rgb(255, 255, 255)",
                      fill: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                      fontWeight: 600,
                      label: "Join waitlist"
                    },
                    email: {
                      placeholder: "Email address",
                      value: ""
                    },
                    font: true,
                    fontFamily: "Inter",
                    fontSize: 16,
                    fontWeight: 400,
                    formId: "Jj4TgP0M",
                    gap: 10,
                    height: "100%",
                    id: "m5QMpduyt",
                    inputs: {
                      color: "rgb(0, 0, 0)",
                      error: "rgb(238, 68, 68)",
                      fill: 'var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245)) /* {"name":"Light Gray"} */',
                      placeholderColor: "rgba(0, 0, 0, 0.5)"
                    },
                    isMixedBorderRadius: false,
                    layout: "horizontal",
                    layoutId: "m5QMpduyt",
                    message: {
                      placeholder: "Message",
                      value: ""
                    },
                    nameField: {
                      placeholder: "Name",
                      value: ""
                    },
                    padding: 15,
                    paddingBottom: 15,
                    paddingLeft: 24,
                    paddingPerSide: true,
                    paddingRight: 24,
                    paddingTop: 15,
                    style: {
                      height: "100%",
                      width: "100%"
                    },
                    topLeftRadius: 50,
                    topRightRadius: 50,
                    width: "100%",
                    withEmail: true,
                    withMessage: false,
                    withName: false
                  })
                })
              })]
            }), isDisplayed() && /*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                NTyq1hRL8: {
                  background: {
                    alt: "",
                    fit: "fit",
                    intrinsicHeight: 1476,
                    intrinsicWidth: 5760,
                    pixelHeight: 1476,
                    pixelWidth: 5760,
                    sizes: "1620px",
                    src: new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png").href,
                    srcSet: `${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=4096").href} 4096w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png").href} 5760w`
                  }
                }
              },
              children: /*#__PURE__*/_jsx(ImageWithFX, {
                __framer__animate: {
                  transition: transition4
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation4,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __perspectiveFX: false,
                __targetOpacity: 1,
                background: {
                  alt: "",
                  fit: "fit",
                  intrinsicHeight: 1476,
                  intrinsicWidth: 5760,
                  pixelHeight: 1476,
                  pixelWidth: 5760,
                  sizes: "100vw",
                  src: new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png").href,
                  srcSet: `${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=2048").href} 2048w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png?scale-down-to=4096").href} 4096w, ${new URL("https://framerusercontent.com/images/gzSuGrZPzpEZvsUEWRPPspZ7S4.png").href} 5760w`
                },
                className: "framer-ble6ye hidden-z0cqn6",
                "data-framer-name": "tut_hero",
                name: "tut_hero"
              })
            }), isDisplayed1() && /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1r5fquh hidden-xxfe1n hidden-t4h2ks hidden-11im4pt hidden-tidzzc",
              children: isDisplayed1() && /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  pSYmZKfbO: {
                    background: {
                      alt: "",
                      fit: "fill",
                      intrinsicHeight: 800,
                      intrinsicWidth: 1580,
                      pixelHeight: 800,
                      pixelWidth: 1580,
                      sizes: "100vw",
                      src: new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png").href,
                      srcSet: `${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png").href} 1580w`
                    }
                  }
                },
                children: /*#__PURE__*/_jsx(ImageWithFX, {
                  __framer__animate: {
                    transition: transition4
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation4,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  background: {
                    alt: "",
                    fit: "fill",
                    intrinsicHeight: 800,
                    intrinsicWidth: 1580,
                    pixelHeight: 800,
                    pixelWidth: 1580,
                    src: new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png").href,
                    srcSet: `${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/U640zEUUK0jj88FmpcRDtLzXkwA.png").href} 1580w`
                  },
                  className: "framer-10qh9ok hidden-xxfe1n",
                  "data-framer-name": "tut_hero",
                  name: "tut_hero"
                })
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-m6wzej",
            "data-framer-name": "logos",
            name: "logos",
            children: [/*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                NTyq1hRL8: {
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--framer-line-height": "1.4em",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "rgb(255, 255, 255)"
                      },
                      children: "Trusted by the world’s best educators and companies."
                    })
                  })
                },
                pSYmZKfbO: {
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--framer-line-height": "1.4em",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "rgb(255, 255, 255)"
                      },
                      children: "Trusted by the world’s best educators and companies."
                    })
                  })
                }
              },
              children: /*#__PURE__*/_jsx(RichTextWithFX, {
                __framer__animate: {
                  transition: transition1
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation1,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __fromCanvasComponent: true,
                __perspectiveFX: false,
                __targetOpacity: 1,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx("p", {
                    style: {
                      "--framer-font-size": "20px",
                      "--framer-line-height": "1.4em",
                      "--framer-text-alignment": "center",
                      "--framer-text-color": "rgb(255, 255, 255)"
                    },
                    children: "Trusted by the world’s best educators and companies."
                  })
                }),
                className: "framer-akophw",
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
              __framer__animate: {
                transition: transition4
              },
              __framer__animateOnce: true,
              __framer__enter: animation,
              __framer__exit: animation4,
              __framer__styleAppearEffectEnabled: true,
              __framer__threshold: 0,
              __perspectiveFX: false,
              __targetOpacity: 1,
              className: "framer-yimur9",
              children: [/*#__PURE__*/_jsx(SVG, {
                className: "framer-sqozob",
                "data-framer-name": "google",
                fill: "black",
                intrinsicHeight: 101,
                intrinsicWidth: 300,
                name: "google",
                svg: '<svg width="300" height="101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M74.58 35.97H39.63v10.29H64.5c-1.2 14.4-13.2 20.55-24.51 20.55-14.49 0-27.12-11.4-27.12-27.36 0-15.57 12.03-27.54 27.15-27.54 11.64 0 18.51 7.44 18.51 7.44l7.2-7.44S56.49 1.62 39.66 1.62C18.18 1.59 1.59 19.71 1.59 39.24c0 19.17 15.63 37.86 38.61 37.86 20.22 0 35.01-13.86 35.01-34.32 0-4.32-.63-6.81-.63-6.81Z" fill="#4285F4"/><path d="M104.13 28.53c-14.22 0-24.39 11.1-24.39 24.06 0 13.14 9.87 24.48 24.57 24.48 13.29 0 24.18-10.17 24.18-24.18 0-16.11-12.66-24.36-24.36-24.36Zm.15 9.51c6.99 0 13.62 5.64 13.62 14.76 0 8.91-6.6 14.73-13.65 14.73-7.74 0-13.86-6.21-13.86-14.79 0-8.4 6.03-14.7 13.89-14.7Z" fill="#EA4335"/><path d="M156.9 28.53c-14.22 0-24.39 11.1-24.39 24.06 0 13.14 9.87 24.48 24.57 24.48 13.29 0 24.18-10.17 24.18-24.18 0-16.11-12.69-24.36-24.36-24.36Zm.15 9.51c6.99 0 13.62 5.64 13.62 14.76 0 8.91-6.6 14.73-13.65 14.73-7.74 0-13.86-6.21-13.86-14.79 0-8.4 6.03-14.7 13.89-14.7Z" fill="#FBBC05"/><path d="M208.95 28.53c-13.05 0-23.31 11.43-23.31 24.24 0 14.61 11.88 24.3 23.07 24.3 6.93 0 10.59-2.76 13.32-5.91v4.8c0 8.37-5.07 13.38-12.75 13.38-7.41 0-11.13-5.52-12.42-8.64l-9.33 3.9c3.3 6.99 9.96 14.28 21.84 14.28 12.96 0 22.86-8.16 22.86-25.29V29.4h-10.17v4.71c-3.18-3.36-7.44-5.58-13.11-5.58Zm.93 9.54c6.39 0 12.96 5.46 12.96 14.79 0 9.48-6.54 14.7-13.11 14.7-6.96 0-13.41-5.64-13.41-14.61 0-9.33 6.72-14.88 13.56-14.88Z" fill="#4285F4"/><path d="M276.54 28.47c-12.3 0-22.62 9.78-22.62 24.24 0 15.27 11.52 24.36 23.82 24.36 10.26 0 16.56-5.61 20.34-10.65l-8.4-5.58c-2.19 3.39-5.82 6.69-11.88 6.69-6.81 0-9.96-3.75-11.91-7.35l32.55-13.5-1.68-3.96c-3.18-7.77-10.53-14.22-20.22-14.25Zm.42 9.36c4.44 0 7.62 2.37 8.97 5.19l-21.72 9.09c-.93-7.05 5.73-14.28 12.75-14.28Z" fill="#EA4335"/><path d="M248.94 4.11h-9.96v71.52h9.96V4.11Z" fill="#34A853"/></svg>',
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Image, {
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 52.666666666666664,
                  intrinsicWidth: 200,
                  loading: "lazy",
                  pixelHeight: 79,
                  pixelWidth: 300,
                  src: new URL("https://framerusercontent.com/images/k5RqTHPEx4YXwJ4SLrv0Ucrqnuo.svg").href
                },
                className: "framer-56yvsq",
                "data-framer-name": "harvard",
                name: "harvard"
              }), /*#__PURE__*/_jsx(SVG, {
                className: "framer-f5fodc",
                "data-framer-name": "microsoft",
                fill: "black",
                intrinsicHeight: 68,
                intrinsicWidth: 300,
                name: "microsoft",
                svg: '<svg width="300" height="68" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.518 2.338h29.946v29.946H2.518V2.338Z" fill="#F25022"/><path d="M35.522 2.338h29.946v29.946H35.52V2.338Z" fill="#80BA01"/><path d="M272.554 13.741c2.77-1.798 6.331-1.96 9.443-1.115.018 1.853 0 3.705.018 5.557-1.475-.647-3.22-1.079-4.767-.431-1.241.486-1.96 1.762-2.158 3.021-.234 1.601-.072 3.238-.126 4.857h9.479c.018-2.05-.018-4.101.036-6.151 2.14-.612 4.28-1.296 6.403-1.943.018 2.698-.018 5.396.018 8.111 2.14-.035 4.28 0 6.402-.017v5.251c-2.14-.09-4.28-.018-6.438-.036.018 3.022 0 6.044 0 9.065.036 1.673-.09 3.363.108 5.018.125.99.467 2.068 1.402 2.59 1.547.863 3.508.486 4.928-.468v5.306c-1.852.81-3.92 1.043-5.917.9-1.906-.144-3.867-.828-5.108-2.339-1.439-1.708-1.798-4.01-1.834-6.187-.018-4.64 0-9.28 0-13.92h-9.479v21.852h-6.474c0-7.284-.018-14.568 0-21.835-1.493-.036-3.004 0-4.515-.018 0-1.726.018-3.453 0-5.197 1.493-.036 2.986-.018 4.497-.018.107-2.303-.234-4.659.449-6.907.576-1.978 1.871-3.795 3.633-4.946Zm-138.651.378c1.313-.198 2.716.251 3.579 1.277 1.007 1.115 1.205 2.895.45 4.208-.828 1.475-2.698 2.159-4.317 1.799-1.673-.306-3.075-1.87-3.003-3.615-.054-1.817 1.492-3.454 3.291-3.67Zm-49.64.809h9.424c2.932 7.446 5.882 14.874 8.813 22.32.756 1.87 1.457 3.741 2.23 5.594 3.777-9.299 7.59-18.58 11.349-27.896 3.022-.054 6.043-.018 9.065-.018v37.77c-2.176 0-4.353.018-6.529-.018.018-9.28 0-18.58.018-27.878 0-.414-.018-.827-.036-1.259a6.46 6.46 0 0 0-.341.612c-3.742 9.514-7.554 18.992-11.277 28.507-1.547.054-3.094 0-4.659.036-3.867-9.515-7.68-19.047-11.51-28.561-.109-.198-.216-.396-.324-.576-.072 4.065-.018 8.148-.036 12.212V52.68h-6.17c-.017-12.572-.017-25.162-.017-37.752Zm67.464 10.665c3.129-.899 6.493-.863 9.622.037.648.18 1.259.431 1.853.773-.036 2.068 0 4.137-.018 6.187-2.087-1.6-4.731-2.608-7.392-2.266-2.105.198-4.119 1.277-5.378 2.985-1.619 2.105-1.997 4.91-1.709 7.5.216 2.015 1.043 4.047 2.608 5.378 1.637 1.457 3.921 1.96 6.061 1.799 2.105-.252 4.101-1.133 5.81-2.392.018 1.96 0 3.902.018 5.863-2.644 1.583-5.828 1.978-8.849 1.834-3.094-.161-6.187-1.33-8.436-3.525-2.446-2.356-3.759-5.701-3.92-9.065-.162-3.489.557-7.14 2.554-10.053 1.672-2.465 4.298-4.227 7.176-5.055Zm58.795 6.349c-1.116-2.5-3.148-4.586-5.648-5.719-2.878-1.313-6.169-1.493-9.281-1.097-3.273.413-6.438 1.942-8.507 4.55-1.888 2.32-2.805 5.306-2.985 8.274-.27 3.489.323 7.194 2.374 10.107 1.744 2.518 4.496 4.245 7.482 4.875 2.482.521 5.09.521 7.59 0 2.913-.594 5.629-2.213 7.428-4.587 1.762-2.248 2.644-5.054 2.842-7.877.197-2.878-.09-5.864-1.295-8.526Zm-6.241 12.428c-.612 1.385-1.745 2.572-3.184 3.13-1.673.647-3.561.683-5.288.234-1.762-.468-3.255-1.727-4.064-3.346-1.044-2.086-1.205-4.496-.99-6.78.198-2.086.918-4.209 2.5-5.648 1.151-1.079 2.716-1.6 4.263-1.672 1.942-.126 4.011.413 5.414 1.834 1.492 1.475 2.068 3.597 2.23 5.648.144 2.212.054 4.532-.881 6.6Zm18.183-19.172c2.986-.522 6.133-.144 8.957.935v5.953a11.474 11.474 0 0 0-6.799-2.014c-1.205.054-2.536.557-3.093 1.709-.432 1.133-.144 2.625.935 3.309 1.835 1.223 4.029 1.744 5.935 2.86 1.493.845 2.95 1.942 3.705 3.525 1.421 2.967.828 6.906-1.69 9.118-2.392 2.23-5.845 2.86-9.011 2.734-2.248-.144-4.514-.593-6.583-1.51.018-2.087-.018-4.173.018-6.26 1.745 1.26 3.741 2.177 5.882 2.536 1.474.234 3.093.252 4.478-.413 1.313-.648 1.565-2.536.773-3.687-.737-.882-1.816-1.367-2.823-1.853-1.907-.863-3.921-1.583-5.594-2.86-1.187-.917-2.068-2.212-2.428-3.669-.522-2.158-.36-4.586.899-6.456 1.421-2.195 3.903-3.508 6.439-3.957Zm39.928 7.536c-.989-2.68-2.986-5.018-5.558-6.295-2.949-1.493-6.385-1.709-9.604-1.313-2.608.342-5.162 1.33-7.158 3.075-2.428 2.087-3.849 5.162-4.281 8.31-.395 3.147-.27 6.457 1.025 9.406 1.223 2.896 3.651 5.234 6.583 6.385 2.878 1.133 6.079 1.26 9.119.773 3.093-.503 6.043-2.14 7.967-4.658 2.051-2.59 2.932-5.953 2.932-9.226.036-2.177-.234-4.407-1.025-6.457Zm-5.845 9.172c-.252 1.421-.738 2.842-1.673 3.975s-2.338 1.817-3.777 2.033c-1.547.233-3.183.125-4.64-.504-1.565-.665-2.77-2.014-3.418-3.58-.773-1.852-.917-3.92-.773-5.898.144-1.925.683-3.903 1.96-5.396 1.152-1.403 2.932-2.158 4.731-2.266 1.834-.126 3.777.288 5.18 1.529 1.294 1.079 2.014 2.68 2.338 4.316.395 1.907.395 3.885.072 5.791Zm-78.885-15.863c1.781-1.079 4.065-1.169 6.007-.503-.018 2.158 0 4.316 0 6.474-1.277-.827-2.878-1.187-4.37-1.025-1.835.216-3.292 1.619-4.047 3.238-.827 1.708-.971 3.65-.917 5.521v12.95h-6.385v-27.05c2.122-.036 4.262-.018 6.385 0-.018 1.546 0 3.093 0 4.64.683-1.69 1.708-3.328 3.327-4.245Zm-46.457-.413c2.14 0 4.299-.037 6.439.017-.018 9.011 0 18.04-.018 27.05h-6.421V25.63Z" fill="#fff"/><path d="M2.518 35.342h29.946v29.946H2.518V35.342Z" fill="#02A4EF"/><path d="M35.522 35.342h29.946v29.946H35.52V35.342Z" fill="#FFB902"/></svg>',
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(SVG, {
                className: "framer-1kf8grz",
                "data-framer-name": "wharton",
                fill: "black",
                intrinsicHeight: 80,
                intrinsicWidth: 300,
                name: "wharton",
                svg: '<svg width="300" height="80" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M64.87 4.188H4.383v30.195c-.049 13.1 7.256 22.646 13.344 28.295 6.721 6.234 14.124 9.79 16.607 10.374l.293.049.292-.05c2.484-.535 9.886-4.139 16.607-10.373 6.136-5.649 13.393-15.194 13.344-28.295V4.188Z" fill="#fff"/><path d="M61.169 16.85c.097.05.146.147.244.244a.903.903 0 0 1 .097.341c0 .146-.049.244-.097.34-.05.098-.147.147-.244.244-.146.098-.292.195-.292.39v1.072h-12.76v-1.072c0-.146-.146-.292-.292-.39-.098-.048-.146-.146-.244-.243a.903.903 0 0 1-.097-.341c0-.146.049-.243.097-.34.05-.098.146-.147.244-.244.146-.098.292-.195.292-.39v-3.555c0-.146-.146-.292-.292-.39-.098-.049-.146-.146-.244-.243a.903.903 0 0 1-.097-.341c0-.146.049-.244.097-.341.05-.098.146-.146.244-.244.146-.097.292-.194.292-.39v-1.12h.146c.73 0 1.364 0 1.85-.194.147-.049.39-.195.537-.292.39-.244.828-.487 1.51-.487.535 0 1.022.194 1.46.39.293.145.634.243.828.243h.098c.194 0 .487-.098.828-.244.438-.146.925-.39 1.46-.39.634 0 1.12.293 1.51.488.146.097.341.243.536.292.438.195 1.12.195 1.85.195h.147v1.071c0 .146.146.292.292.39l.244.243a.903.903 0 0 1 .097.341c0 .146-.049.244-.097.341-.05.097-.147.146-.244.243-.146.098-.292.195-.292.39v3.507c0 .243.146.34.292.487Zm-15.584 1.803C44.61 20.21 42.71 20.6 42.663 20.6c-.39.048-.731.097-1.17.097-1.947 0-3.798-.828-5.649-1.85a11.283 11.283 0 0 1-.681-.39c-1.17-.633-2.825-1.559-4.237-1.559-.39 0-.78.049-1.17.293l-.048.048v.049c0 .146-.049.39-.097.487-.244.633-.926 1.218-1.315 1.51-.828.584-1.851 1.168-3.069 1.168-.194 0-.39-.048-.486-.048-.244-.049-.39-.195-.536-.39-.049-.097-.146-.195-.146-.292-.049-.097-.05-.146-.05-.292v-.049l-.048-.049c-.487-.146-.779-.487-.779-.828 0-.292.195-.535.39-.779.34-.39.633-.73.146-1.169-.049-.048-.098-.048-.146-.097-.05-.049-.146-.097-.146-.146-.39-.438-.147-1.315-.05-1.85v-.05c.147-.584.39-1.168.731-1.85.439-.877.974-1.9 1.705-2.484 0 0 .049-.049.049 0 .048 0 .048 0 .048.049 0 .048.098.146.195.34l.146.195.049-.243c.195-.633.536-1.071 1.023-1.218.487-.146.974.05 1.217.244l.098.049.048-.098a1.2 1.2 0 0 1 .926-.584c.487-.049.925.34 1.168.536l.05.048.048-.048c.243-.195.828-.634 1.412-.536.585.049.78.584.828.78l.049.145.146-.048c.292-.147.78-.293 1.266 0 .292.194.341.535.341.779v.146l.146-.049c.39-.049 1.072-.097 1.461.292.292.341.341.73.292 1.072l-.048.146.146-.049c.34-.049.73 0 .974.39.146.243.097.633.048.779v.195l.098.048c.195.05.487.147.633.439.049.195.049.535-.049.925l-.048.098.097.048c.146.049.487.244.633.536.146.292.146.73-.048 1.169l-.05.049.05.048c.39.39.827.73 1.266 1.023.828.633 2.142.925 3.068.195.34-.293.487-.78.39-1.267-.098-.39-.39-.73-.78-.827-.682-.147-1.217.487-1.656 1.168 0 .05-.048.05-.048.05l-.049-.05c-.39-.925-.39-1.753-.049-2.386.292-.487.926-.877 1.608-.974h.097v-.097c0-1.17.292-1.948.828-2.484.34-.292.925-.633 1.948-.487.048 0 .048 0 .048.048v.05c-.73 1.85-.584 2.483-.146 3.993.05.146.05.292.098.39.438 1.363.34 2.58-.292 3.555ZM21.283 16.85c.097.048.146.146.243.243a.903.903 0 0 1 .098.341c0 .146-.05.244-.098.34-.049.098-.146.147-.243.244-.146.098-.293.195-.293.39v1.072H8.329v-1.072c0-.146-.146-.292-.292-.39-.097-.048-.146-.146-.244-.243a.903.903 0 0 1-.097-.341c0-.146.049-.243.097-.34.05-.098.147-.147.244-.244.146-.098.292-.195.292-.39v-3.555c0-.146-.146-.292-.292-.39-.097-.049-.146-.146-.244-.243a.903.903 0 0 1-.097-.341c0-.146.049-.244.097-.341.05-.098.147-.146.244-.244.146-.097.292-.194.292-.39v-1.12h.146c.73 0 1.364 0 1.85-.194.147-.049.39-.195.537-.292.39-.244.827-.487 1.51-.487.535 0 1.022.194 1.46.39.293.145.633.243.828.243h.098c.194 0 .487-.098.828-.244.438-.146.925-.39 1.46-.39.634 0 1.12.293 1.51.488.146.097.341.243.536.292.438.195 1.12.195 1.85.195h.147v1.071c0 .146.146.292.292.39l.244.243a.903.903 0 0 1 .097.341c0 .146-.049.244-.097.341-.05.097-.146.146-.244.243-.146.098-.292.195-.292.39v3.507c-.049.243.097.34.195.487ZM5.893 5.795s.049 17.533 0 17.435h57.468V5.796H5.893Z" fill="#900"/><path d="M25.666 16.072c.194 0 .39.048.584.194.049.049.146.098.146.146.146.146.146.341-.049.439-.146.097-.292.243-.39.292-.243.146-.632.438-.925.244-.146-.098.146-.293 0-.78 0-.39.341-.535.634-.535Z" fill="#011F5B"/><path d="M25.763 10.422c-.633.633-1.12 1.51-1.51 2.289a17.517 17.517 0 0 0-.73 1.802v.049c-.098.39-.341 1.266-.049 1.607.049.049.098.049.146.146.049.049.098.049.146.146.634.633.195 1.12-.146 1.51-.146.243-.39.438-.39.633 0 .438.536.633.731.633.049.049.146.049.146.146 0 .146.049.244.049.341.049.097.049.146.146.244.098.146.292.243.438.292.147.048.293.048.439.048 1.169 0 2.143-.584 2.922-1.168.487-.341 1.071-.926 1.217-1.364.049-.146.049-.244.049-.39l-.828-.146h-.049c-.097-.048-.146-.097-.146-.146-.048-.146.146-.292.39-.487.097-.049.146-.146.292-.146.438-.292.78-.584.974-.73-.243-.293-.39-.536-.584-.78-.146-.194-.244-.34-.341-.438-.39-.438-1.12-.73-1.705-.34-.243.145-.438.34-.682.486-.146.146-.39.292-.584.487-.049.049-.146.049-.244-.049-.048-.048-.048-.146 0-.243l.098-.097c.292-.244.877-.828.877-1.267.194-.974-.731-2.532-1.072-3.068Zm5.162 6.331c1.413 0 3.069.877 4.335 1.607.243.146.487.293.682.39 2.142 1.12 4.334 2.094 6.623 1.704 0 0 1.85-.39 2.776-1.85.584-.925.682-2.046.292-3.36-.049-.147-.049-.293-.097-.39-.39-1.364-.633-2.192.048-3.945-.779-.049-1.266.195-1.51.438-.486.487-.73 1.267-.73 2.436 0 .097-.048.146-.146.194-.73.049-1.315.39-1.607.877-.292.487-.292 1.071-.049 1.85.341-.486.926-1.12 1.705-.925.487.098.828.487.925.974.146.585-.048 1.17-.487 1.51-1.023.828-2.435.487-3.36-.195-1.072-.779-2.143-1.802-3.263-3.068-.195-.243-.39-.438-.585-.682-.73-.828-1.46-1.704-2.337-2.386-1.364-1.072-3.02-1.218-4.14-1.169-.78.049-2.289.244-3.312.877.293.633.634 1.412.634 1.996 0 .098-.05.195-.05.293.731-.439 1.608-.146 2.144.39.097.145.243.291.39.486.194.292.389.633.681.877.049.048.049.097.049.146 0 .049-.049.097-.049.146 0 0-.487.34-1.169.828-.097.049-.146.146-.292.195l-.049.049.585.097c.487-.341.925-.39 1.363-.39Zm10.569 4.091c-1.997 0-3.8-.828-5.698-1.85a11.49 11.49 0 0 1-.682-.39c-1.51-.828-3.799-2.046-5.26-1.266 0 .194-.049.39-.097.535-.244.633-.974 1.267-1.364 1.51-.828.633-1.85 1.218-3.117 1.218-.195 0-.39-.049-.536-.098-.243-.049-.438-.243-.633-.438-.048-.098-.146-.195-.194-.341-.05-.097-.05-.195-.05-.292-.535-.098-.876-.487-.876-.925 0-.341.244-.634.439-.828.39-.439.487-.633.146-.974-.05-.05-.05-.05-.146-.098a.656.656 0 0 1-.195-.146c-.439-.487-.195-1.364-.05-1.948v-.049c.147-.584.39-1.168.732-1.85.438-.926.974-1.9 1.753-2.533.048-.048.097-.048.146-.048.048 0 .097.048.146.048.049.049.34.487.633 1.169 1.364-.828 3.263-.877 3.506-.925 1.17-.049 2.923.146 4.384 1.266.925.73 1.655 1.558 2.386 2.386.195.195.39.439.584.682 1.12 1.266 2.143 2.24 3.263 3.02.828.584 2.046.925 2.922.194.293-.243.439-.73.341-1.169-.097-.39-.34-.633-.681-.73-.633-.146-1.12.487-1.51 1.072-.049.048-.098.097-.146.048-.049 0-.146-.049-.146-.097-.39-.974-.439-1.85-.05-2.484.342-.536.926-.925 1.706-1.071 0-1.17.292-2.046.827-2.533.341-.292.974-.682 2.046-.487.049 0 .097.049.146.098.049.048.049.097 0 .146-.73 1.801-.536 2.435-.146 3.944.049.147.049.293.097.39.39 1.461.293 2.679-.39 3.701-1.022 1.607-2.921 1.997-3.067 2.046-.439.049-.78.097-1.17.097Z" fill="#011F5B"/><path d="M24.546 19.578c-.146 0-.341 0-.536-.049-.098 0-.146-.097-.146-.195 0-.097.097-.146.195-.146.828.098 2.045 0 3.116-1.12.05-.048.147-.048.293 0 .048.049.048.146 0 .292-.877.78-1.851 1.218-2.922 1.218Zm14.659-2.728c-.05 0-.05 0 0 0-.147-.048-.195-.146-.147-.243.195-.438.195-.828.05-1.071-.196-.39-.634-.487-.634-.487a.105.105 0 0 1-.097-.098c-.05-.049-.05-.097 0-.146.048-.146.194-.682.097-.925-.049-.195-.487-.292-.633-.292-.049 0-.098-.05-.146-.05-.049 0-.049-.097 0-.145.048-.146.146-.536.048-.73-.292-.39-.974-.196-.974-.196-.048.05-.146 0-.146-.048 0-.049-.048-.098-.048-.146 0 0 .194-.633-.195-1.072-.487-.487-1.461-.195-1.51-.195-.049.05-.146 0-.146-.048 0-.049-.049-.098-.049-.146 0-.05.146-.536-.146-.78-.584-.39-1.217.098-1.217.098-.049.048-.098.048-.146.048-.05-.048-.098-.048-.098-.146 0-.048-.097-.73-.73-.828-.682-.097-1.364.585-1.364.634-.049.048-.049.048-.146.048-.049 0-.098-.048-.146-.048 0 0-.487-.634-1.072-.585-.584.049-.779.633-.779.633-.049.049-.049.098-.146.098-.049 0-.097 0-.146-.049 0 0-.585-.536-1.169-.34-.73.291-.925 1.168-.974 1.46-.049.098-.097.146-.195.146-.097-.049-.146-.097-.146-.195.049-.34.292-1.363 1.218-1.704.535-.195 1.071.049 1.315.292.146-.243.438-.633.974-.633.584-.049 1.022.39 1.217.584.292-.243.877-.633 1.51-.584.536.049.828.487.925.828.293-.146.828-.341 1.364 0 .39.243.39.633.39.925.39-.049 1.12-.097 1.558.341.39.39.39.828.34 1.12.293-.049.829-.049 1.121.39.146.292.146.633.049.877.243.048.584.194.73.486.098.293 0 .731-.048.974.194.05.486.293.681.634.195.39.146.827-.048 1.363-.05-.097-.098-.048-.146-.048Z" fill="#011F5B"/><path d="M38.328 19.188c-.097 0-.146-.097-.146-.194.049-.634-.292-.78-.633-.877-.097-.049-.146-.049-.244-.098-.146-.097-.194-.34-.194-.535 0-.244 0-.487-.195-.73-.292-.342-.925-.147-.925-.147-.05.049-.147 0-.147-.049-.048-.048-.048-.097-.048-.146 0 0 .097-.73-.292-1.071-.341-.292-.975-.195-1.17-.146-.048 0-.097 0-.146-.049-.048-.049-.048-.097-.048-.146.048-.438-.146-.78-.487-1.023-.292-.146-.633-.194-.877-.048-.73.39-.974.39-1.753-.146-.244-.147-.487-.244-.73-.147-.293.05-.536.341-.731.73-.049.05-.146.147-.244.05-.048-.05-.146-.146-.048-.244.243-.487.584-.828.974-.925.34-.049.681 0 .974.195.633.438.779.438 1.363.097.39-.195.828-.146 1.218.049.39.243.633.633.633 1.12.292-.049.877-.049 1.266.292.39.292.439.828.439 1.072.292-.05.779-.05 1.071.292.292.292.292.681.292.925 0 .097 0 .243.049.292.049 0 .097.049.146.049.34.097.974.292.877 1.266-.098.244-.147.292-.244.292Zm-16.9-1.753c0 .049-.048.146-.048.195-.05.049-.098.146-.195.146-.146.146-.39.292-.39.584v.78H8.571v-.78c0-.292-.195-.438-.39-.584-.048-.049-.146-.097-.194-.146-.049-.049-.049-.146-.049-.195 0-.049.049-.146.049-.195.048-.048.097-.146.195-.146.146-.146.39-.292.39-.584v-3.556c0-.292-.196-.438-.39-.584-.05-.049-.147-.097-.195-.146-.049-.049-.049-.146-.049-.195 0-.049.049-.146.049-.195.048-.048.097-.146.195-.146.146-.146.39-.292.39-.584v-.877h.048c.292 0 .633 0 .925-.048v8.035h4.432c.049.293.39.536.73.536a.792.792 0 0 0 .731-.536h4.432V10.13c.292.049.633.049.925.049h.049v.828c0 .292.195.438.39.584.048.049.146.097.194.146.049.049.049.146.049.195 0 .048-.049.146-.049.195-.048.048-.097.146-.195.146-.146.146-.39.292-.39.584v3.507c0 .292.196.438.39.584.05.049.147.098.195.146v.341Zm-10.958-7.5c.195-.097.39-.195.585-.34.39-.244.73-.488 1.315-.488.487 0 .925.195 1.364.341.292.097.535.195.779.244v7.012h-.049a4.952 4.952 0 0 0-1.364.39c-.146.049-.39.098-.535.146-.536.146-1.072.146-1.607.146-.341 0-.682 0-.974.05V9.983c.146.097.34.049.486-.049Zm5.163-.438c.39-.146.877-.341 1.364-.341.584 0 .925.243 1.314.487.147.097.39.243.585.34.146.05.34.147.536.147v7.451c-.293-.049-.633-.049-.974-.049-.536 0-1.072 0-1.608-.146-.146-.048-.39-.097-.535-.146-.536-.146-1.072-.34-1.364-.39h-.049V9.74c.244-.048.487-.146.73-.243Zm5.747 7.256c-.146-.097-.244-.146-.244-.292v-3.506c0-.098.049-.147.244-.293.097-.048.194-.146.292-.292.049-.146.146-.292.146-.39 0-.097-.049-.292-.146-.39-.049-.097-.146-.194-.292-.291-.146-.098-.244-.146-.244-.292V9.74h-.292c-.633 0-1.364 0-1.802-.195a1.297 1.297 0 0 1-.487-.292c-.39-.243-.828-.535-1.558-.535-.536 0-1.072.194-1.51.39-.292.145-.585.243-.78.243h-.097c-.195 0-.487-.098-.78-.244-.437-.146-.924-.39-1.509-.39-.682 0-1.169.293-1.558.536-.146.098-.341.195-.487.292-.439.195-1.17.195-1.802.195h-.195v1.267c0 .097-.049.146-.244.292-.097.048-.194.146-.292.292a.777.777 0 0 0-.146.39c0 .146.049.292.146.39a.836.836 0 0 0 .292.291c.147.098.244.146.244.292v3.507c0 .097-.049.146-.244.292-.097.049-.194.146-.292.293-.048.146-.146.292-.146.39 0 .145.049.291.146.389a.83.83 0 0 0 .292.292c.147.097.244.146.244.292v1.169h12.954v-1.169c0-.097.05-.146.244-.292a.83.83 0 0 0 .292-.292.778.778 0 0 0 .146-.39.553.553 0 0 0-.146-.39c-.195-.146-.292-.243-.39-.292Zm39.886.682c0 .049-.049.146-.049.195-.048.049-.097.146-.194.146-.147.146-.39.292-.39.584v.78H48.458v-.78c0-.292-.195-.438-.39-.584-.049-.049-.146-.097-.195-.146-.049-.049-.049-.146-.049-.195 0-.049.05-.146.05-.195.048-.048.096-.146.194-.146.146-.146.39-.292.39-.584v-3.556c0-.292-.195-.438-.39-.584-.049-.049-.146-.097-.195-.146-.049-.049-.049-.146-.049-.195 0-.049.05-.146.05-.195.048-.048.096-.146.194-.146.146-.146.39-.292.39-.584v-.877h.048c.292 0 .633 0 .926-.048v8.035h4.431c.05.293.39.536.73.536a.791.791 0 0 0 .731-.536h4.432V10.13c.292.049.633.049.926.049h.048v.828c0 .292.195.438.39.584.049.049.146.097.195.146.048.049.048.146.048.195 0 .048-.048.146-.048.195-.049.048-.098.146-.195.146-.146.146-.39.292-.39.584v3.507c0 .292.195.438.39.584.049.049.146.098.195.146-.098.146-.049.244-.049.341Zm-10.958-7.5c.195-.097.39-.195.585-.34.39-.244.73-.488 1.315-.488.487 0 .925.195 1.363.341.292.097.536.195.78.244v7.012h-.05c-.34.05-.827.147-1.363.39-.146.049-.39.098-.536.146-.535.146-1.071.146-1.607.146-.34 0-.682 0-.974.05V9.983c.146.097.292.049.487-.049Zm5.211-.438c.39-.146.877-.341 1.364-.341.584 0 .925.243 1.315.487.146.097.39.243.584.34.146.05.341.147.536.147v7.451c-.292-.049-.633-.049-.974-.049-.536 0-1.072 0-1.607-.146-.146-.048-.39-.097-.536-.146-.536-.146-1.071-.34-1.364-.39h-.048V9.74c.194-.048.438-.146.73-.243Zm5.698 7.256c-.146-.097-.243-.146-.243-.292v-3.506c0-.098.049-.147.243-.293.098-.048.195-.146.293-.292.048-.146.146-.292.146-.39 0-.097-.05-.292-.146-.39a.84.84 0 0 0-.293-.291c-.146-.098-.243-.146-.243-.292V9.74h-.292c-.634 0-1.364 0-1.802-.195a1.297 1.297 0 0 1-.487-.292c-.39-.243-.828-.535-1.559-.535-.536 0-1.071.194-1.51.39-.292.145-.584.243-.779.243h-.049c-.194 0-.486-.098-.779-.244-.438-.146-.925-.39-1.51-.39-.681 0-1.168.293-1.558.536-.146.098-.34.195-.487.292-.438.195-1.169.195-1.802.195h-.292v1.267c0 .097-.049.146-.244.292-.097.048-.194.146-.292.292-.048.146-.146.292-.146.39 0 .146.049.292.146.39a.836.836 0 0 0 .292.291c.147.098.244.146.244.292v3.507c0 .097-.049.146-.244.292-.097.049-.194.146-.292.293-.048.146-.146.292-.146.39 0 .145.049.291.146.389a.83.83 0 0 0 .292.292c.147.097.244.146.244.292v1.169h12.954v-1.169c0-.097.05-.146.244-.292a.83.83 0 0 0 .292-.292c.049-.146.146-.293.146-.39a.553.553 0 0 0-.146-.39c-.146-.146-.292-.243-.34-.292Z" fill="#011F5B"/><path d="m59.708 48.604-25.13-24.205L9.497 48.604c-1.948-4.091-3.263-8.864-3.215-14.22V23.668h56.591v10.714c.049 5.357-1.266 10.13-3.165 14.22Zm-8.036 2.484c-1.948 1.948-5.114 1.948-7.013 0-1.948-1.948-1.948-5.114 0-7.013 1.948-1.948 5.114-1.948 7.013 0 1.948 1.948 1.948 5.113 0 7.013ZM29.61 35.21c0-2.727 2.24-4.967 4.968-4.967 2.727 0 4.967 2.24 4.967 4.967 0 2.727-2.24 4.968-4.967 4.968-2.727.048-4.968-2.192-4.968-4.968Zm20.601 26.007c-6.72 6.233-13.685 9.35-15.536 9.837-1.85-.487-8.815-3.604-15.535-9.837-.147-.147-.341-.293-.488-.487l16.072-17.046L50.795 60.73c-.243.194-.438.34-.584.487Zm-32.679-10.13c-1.948-1.948-1.948-5.114 0-7.013 1.948-1.948 5.114-1.948 7.013 0 1.948 1.948 1.948 5.113 0 7.013-1.899 1.996-5.065 1.996-7.013 0Zm45.39-44.952v16.656H6.332V6.136h56.59Zm.78-.779h-58.2v29.026C5.454 47.094 12.565 56.3 18.506 61.851c6.478 6.039 13.783 9.594 16.072 10.08h.097c2.29-.486 9.594-4.041 16.072-10.08 5.941-5.504 13.003-14.757 13.003-27.468-.049-.39-.049-29.026-.049-29.026Z" fill="#011F5B"/><path d="M296.542 56.688H71.25v-.876h225.34l-.049.876ZM74.221 67.5c0 2.289.633 3.993 3.02 3.993 2.19 0 3.36-1.51 3.36-4.042v-4.48c0-.341-.05-.633-.098-.877-.049-.584-.633-.828-1.364-.877v-.438h3.8v.39c-.78.049-1.316.292-1.364.877-.05.292-.098.535-.098.876V67.5c0 3.507-2.191 4.773-4.286 4.773-3.457 0-4.48-1.754-4.48-4.676v-4.821c0-1.51.049-1.412-1.364-1.607v-.438h4.383v.39c-1.46.145-1.363.096-1.363 1.606L74.22 67.5Zm17.824 4.53c-1.996-2.338-4.042-4.822-6.087-7.209v5.504c0 .292.048.487.048.681.05.439.487.634 1.315.682v.341h-3.068v-.34c.633-.05 1.023-.244 1.072-.682.048-.195.048-.39.048-.682v-5.893c-.34-.439-.73-.828-1.46-.828v-.341H86.2c1.85 2.289 3.799 4.578 5.65 6.818h.048V64.92c0-.292-.049-.487-.049-.682-.048-.438-.486-.633-1.314-.682v-.34h3.068v.34c-.585.049-1.023.244-1.072.682-.048.195-.048.39-.048.682v7.013h-.439v.097Zm6.429-1.56c0 1.17-.049 1.072 1.071 1.267v.34h-3.36v-.34c1.12-.146 1.071-.049 1.071-1.266v-5.6c0-1.17.05-1.072-1.071-1.267v-.341h3.36v.34c-1.12.147-1.071.05-1.071 1.267v5.6Zm6.428-6.866c-.681 0-1.168.195-.779 1.364.487 1.51 1.218 3.603 1.851 5.503.828-1.9 1.558-3.799 2.338-5.747.292-.78 0-1.169-.828-1.169v-.34h2.727v.34c-.39 0-.779.146-1.072.73-1.071 2.582-2.094 5.114-3.165 7.647h-.633a367.484 367.484 0 0 0-2.387-6.721c-.487-1.315-.828-1.704-1.461-1.704v-.341h3.361l.048.438Zm7.16 8.084c1.12-.146 1.071-.048 1.071-1.266v-5.6c0-1.17.049-1.072-1.071-1.267v-.34h5.6v1.996h-.341c-.146-.925-.389-1.607-1.266-1.607h-1.704v3.555h1.071c.779 0 1.12-.292 1.169-1.071h.341v2.678h-.341c-.049-.73-.292-1.169-1.169-1.169h-1.071v2.825c0 1.072.487 1.169 1.607 1.169 1.802 0 1.704-.487 2.289-1.85h.341l-.39 2.24h-6.136v-.293Zm8.425-8.376h3.604c1.266 0 2.532.681 2.532 2.143 0 1.266-.779 2.142-1.753 2.532l1.218 1.85c.633.926 1.314 1.803 1.753 2.046v.146h-1.266c-.731 0-1.169-1.363-2.63-3.847h-1.218v2.289c0 1.169-.049 1.071 1.072 1.266v.34h-3.361v-.34c1.12-.146 1.072-.049 1.072-1.266v-5.6c0-1.17.048-1.072-1.072-1.267l.049-.292Zm2.24 4.48h1.072c1.071 0 1.607-.828 1.607-2.24 0-1.364-.731-1.85-1.656-1.85h-.974v4.09h-.049Zm11.299-2.63c-.098-.876-.585-1.558-1.559-1.558-.73 0-1.363.39-1.363 1.266 0 .877.876 1.364 2.143 2.338 1.315.974 1.753 1.607 1.753 2.678 0 1.51-1.51 2.29-3.02 2.29a5.793 5.793 0 0 1-2.142-.39c-.049-.049-.098-.049-.098-.146v-1.802h.341c.146 1.363 1.071 1.948 1.997 1.948a1.59 1.59 0 0 0 1.607-1.607c0-.828-.438-1.51-2.289-2.728-.682-.438-1.51-.925-1.51-2.289 0-1.12 1.266-1.996 2.679-1.996.536 0 1.266.048 1.753.243.049.049.097.049.097.146v1.607h-.389Zm5.454 5.309c0 1.169-.048 1.071 1.072 1.266v.34h-3.361v-.34c1.121-.146 1.072-.049 1.072-1.266v-5.6c0-1.17.049-1.072-1.072-1.267v-.341h3.361v.34c-1.12.147-1.072.05-1.072 1.267v5.6Zm7.257 0c0 1.169-.049 1.071 1.071 1.266v.34h-3.36v-.34c1.12-.146 1.071-.049 1.071-1.266V63.7h-1.168c-1.072 0-1.315.682-1.754 1.51h-.292l.341-1.9h6.916l.34 1.9h-.292c-.389-.828-.682-1.51-1.753-1.51h-1.12v6.77Zm6.769 1.217c1.121-.146 1.072-.048 1.072-1.266v-1.948l-1.948-3.799c-.39-.828-.731-1.12-1.315-1.12v-.34h3.165v.34c-.925 0-.828.487-.535 1.12l1.509 3.166 1.705-3.166c.438-.73.049-1.12-.731-1.12v-.34h2.582v.34c-.341 0-.682.146-1.023.73l-2.094 3.8c-.146.243-.146.633-.146.925v1.363c0 1.17-.049 1.072 1.071 1.267v.34h-3.409v-.292h.097Zm11.25-2.63c0-2.142 1.851-5.26 4.53-5.26 2.142 0 2.581 1.462 2.581 2.923 0 1.802-1.413 5.552-4.481 5.552-2.094-.049-2.63-1.705-2.63-3.215Zm5.65-3.165c0-1.705-1.023-1.753-1.267-1.753-1.753 0-2.922 3.165-2.922 5.26 0 .827.147 2.386 1.364 2.386 1.851-.049 2.825-3.069 2.825-5.893ZM180 64.286l-.39.34h-2.143c-.779 3.848-2.094 8.962-3.019 11.445h-.731l2.289-11.444h-1.509l.146-.682h1.509c.828-3.458 3.702-3.799 4.384-3.799.389 0 1.168.049 1.363.292l-.195 1.072-.389.195c-.244-.439-.731-.926-1.413-.926-.974 0-1.704.682-2.094 2.387l-.146.779h2.387l-.049.34Zm9.886 5.746c0 1.51-.048 1.413 1.364 1.608v.39h-4.383v-.44c1.461-.145 1.363-.097 1.363-1.606v-7.257c0-1.51.049-1.412-1.363-1.607v-.438h4.237c2.678 0 4.042 1.071 4.042 3.02 0 2.337-2.094 3.603-4.578 3.457v-.34c2.046.048 2.922-1.17 2.922-2.923 0-1.85-.876-2.63-2.678-2.63h-.974l.048 8.767Zm7.5 1.656c1.12-.146 1.072-.048 1.072-1.266v-5.6c0-1.17.048-1.072-1.072-1.267v-.34h5.601v1.996h-.341c-.146-.925-.39-1.607-1.266-1.607h-1.705v3.555h1.072c.779 0 1.12-.292 1.168-1.071h.341v2.678h-.341c-.048-.73-.292-1.169-1.168-1.169h-1.072v2.825c0 1.072.487 1.169 1.607 1.169 1.802 0 1.705-.487 2.289-1.85h.341l-.389 2.24h-6.137v-.293Zm16.461.341c-1.996-2.337-4.042-4.821-6.087-7.208v5.504c0 .292.048.487.048.681.049.439.487.634 1.315.682v.341h-3.068v-.34c.633-.05 1.023-.244 1.071-.682.049-.195.049-.39.049-.682v-5.893c-.341-.439-.73-.828-1.461-.828v-.341h2.289c1.851 2.289 3.799 4.578 5.649 6.818h.049V64.92c0-.292-.049-.487-.049-.682-.048-.438-.487-.633-1.314-.682v-.34h3.068v.34c-.585.049-1.023.244-1.072.682-.048.195-.048.39-.048.682v7.013h-.39v.097h-.049Zm11.153 0c-1.997-2.337-4.042-4.821-6.088-7.208v5.504c0 .292.049.487.049.681.049.439.487.634 1.315.682v.341h-3.068v-.34c.633-.05 1.022-.244 1.071-.682.049-.195.049-.39.049-.682v-5.893c-.341-.439-.731-.828-1.461-.828v-.341h2.289c1.85 2.289 3.798 4.578 5.649 6.818h.049V64.92c0-.292-.049-.487-.049-.682-.049-.438-.487-.633-1.315-.682v-.34h3.068v.34c-.584.049-1.022.244-1.071.682-.049.195-.049.39-.049.682v7.013H225v.097Zm8.133-6.867c-.097-.876-.584-1.558-1.558-1.558-.731 0-1.364.39-1.364 1.266 0 .877.877 1.364 2.143 2.338 1.315.974 1.753 1.607 1.753 2.678 0 1.51-1.51 2.29-3.019 2.29-.828 0-1.51-.147-2.143-.39-.049-.049-.098-.049-.098-.146v-1.802h.341c.146 1.363 1.072 1.948 1.997 1.948a1.59 1.59 0 0 0 1.607-1.607c0-.828-.438-1.51-2.289-2.728-.682-.438-1.51-.925-1.51-2.289 0-1.12 1.267-1.996 2.679-1.996.536 0 1.266.048 1.753.243.049.049.098.049.098.146v1.607h-.39Zm5.016 6.526c1.12-.146 1.072-.048 1.072-1.266v-1.948l-1.948-3.799c-.39-.828-.731-1.12-1.315-1.12v-.34h3.165v.34c-.925 0-.876.487-.535 1.12l1.509 3.166 1.705-3.166c.438-.73.049-1.12-.731-1.12v-.34h2.581v.34c-.34 0-.681.146-1.022.73l-2.094 3.8c-.147.243-.147.633-.147.925v1.363c0 1.17-.048 1.072 1.072 1.267v.34h-3.36v-.34h.048v.048Zm7.598 0c1.12-.146 1.071-.048 1.071-1.266v-5.6c0-1.17.049-1.072-1.071-1.267v-.34h3.36v.34c-1.12.146-1.071.049-1.071 1.266v5.796c0 .876.487.925 1.607.925 1.802 0 1.704-.487 2.289-1.85h.292l-.39 2.24h-6.136l.049-.244Zm10.86-8.084c-.682 0-1.169.195-.779 1.364.487 1.51 1.217 3.603 1.85 5.503.828-1.9 1.559-3.799 2.338-5.747.292-.78 0-1.169-.828-1.169v-.34h2.727v.34c-.389 0-.779.146-1.071.73-1.071 2.582-2.094 5.114-3.166 7.647h-.633c-.779-2.143-1.607-4.578-2.386-6.721-.487-1.315-.828-1.704-1.461-1.704v-.341h3.36v.438h.049Zm7.305 5.552-.584 1.656c-.146.487.048.925.633.925v.34h-2.289v-.34c.341 0 .73-.195.925-.633.536-1.072 1.997-5.162 3.068-7.938h.731c.828 2.483 2.143 6.38 2.532 7.45.293.731.487 1.072 1.023 1.072v.341h-2.922v-.34c.779 0 .828-.39.682-.926l-.536-1.607h-3.263Zm3.02-.487-1.315-4.042h-.049l-1.51 4.042h2.874Zm12.565 3.36c-1.997-2.337-4.043-4.821-6.088-7.208v5.504c0 .292.049.487.049.681.048.439.487.634 1.315.682v.341h-3.02v-.34c.633-.05 1.023-.244 1.072-.682.048-.195.048-.39.048-.682v-5.893c-.341-.439-.73-.828-1.461-.828v-.341h2.289c1.851 2.289 3.799 4.578 5.65 6.818h.048V64.92c0-.292-.048-.487-.048-.682-.049-.438-.487-.633-1.315-.682v-.34h3.068v.34c-.585.049-1.023.244-1.072.682-.048.195-.048.39-.048.682v7.013h-.39v.097h-.097Zm5.892-1.558c0 1.169-.048 1.071 1.072 1.266v.34h-3.36v-.34c1.12-.146 1.071-.049 1.071-1.266v-5.6c0-1.17.049-1.072-1.071-1.267v-.341h3.36v.34c-1.12.147-1.072.05-1.072 1.267v5.6Zm5.212-1.315-.585 1.656c-.146.487.049.925.633.925v.34h-2.289v-.34c.341 0 .731-.195.926-.633.535-1.072 1.996-5.162 3.068-7.938h.73c.828 2.483 2.143 6.38 2.533 7.45.292.731.487 1.072 1.022 1.072v.341h-2.922v-.34c.78 0 .828-.39.634-.926l-.536-1.607h-3.214Zm3.019-.487-1.315-4.042h-.049l-1.509 4.042h2.873ZM182.143 45.34v-9.35c-3.068 1.314-10.861 3.214-10.861 7.304 0 2.728 2.046 4.92 4.87 4.92 2.533 0 4.043-1.413 5.991-2.874Zm.048-10.958v-7.159c0-2.63-1.704-3.312-4.188-3.312-2.435 0-5.941 1.608-5.941 3.069 0 .39.146.876.146 1.51 0 2.483-.633 4.188-3.507 4.188-1.217 0-2.045-1.17-2.045-2.24 0-1.462 1.022-2.825 2.435-4.043 3.068-2.484 7.889-4.53 11.883-4.53 4.14 0 6.331 2.436 6.331 6.38v17.63c0 1.12 0 3.069 1.559 3.069 1.071 0 1.704-1.364 1.899-2.24h1.071c-.341 3.165-1.704 5.016-4.919 5.016-2.483 0-4.577-2.24-4.675-4.53-3.165 2.29-5.308 4.53-9.497 4.53-3.116 0-5.99-2.435-5.99-5.6.049-7.209 9.984-9.449 15.438-11.738Zm58.588 2.386c0 5.065 1.899 13.442 8.474 13.442 6.38 0 8.62-6.672 8.62-11.834 0-5.601-.828-15.147-8.571-15.147-6.526-.048-8.523 8.62-8.523 13.54Zm-5.698-.292c0-7.938 5.698-14.756 14.123-14.756 8.815 0 14.513 6.623 14.513 15 0 8.571-5.552 14.902-14.464 14.902-9.058 0-14.172-6.77-14.172-15.146ZM71.201 6.916V5.357h15.341v1.559c-2.825.048-3.945.048-3.945 1.753 0 .049.049.487.293 1.169l9.253 31.412 13.052-37.354h1.071l11.348 36.916s8.961-26.98 8.961-30.78c0-3.019-1.9-2.824-4.335-3.068V5.357h9.399c4.14 0 8.377-1.412 10.763-2.825v23.864c2.923-2.581 5.358-4.675 9.595-4.675 3.117 0 5.99 1.363 7.548 4.042 1.413 2.289 1.705 4.48 1.705 7.013v13.247c0 3.409.876 3.409 3.847 3.604v1.266h-12.808v-1.266c2.776-.195 3.652-.195 3.652-3.604V32.776c0-2.094 0-3.02-.925-5.016-1.071-1.9-3.019-3.75-5.454-3.36-3.702.633-5.163 2.337-7.062 4.188v17.386c0 3.41.877 3.41 3.701 3.604v1.266h-12.954v-1.266c3.068-.195 3.944-.195 3.944-3.604V7.305c.049-.048-1.071 0-2.435 0-5.162.341-5.308 4.286-6.964 8.961l-11.64 35.357h-1.217L103.441 16.17 90.146 51.672h-1.169L76.753 12.711c-.73-2.045-1.071-3.75-2.143-4.87-.682-.536-.73-.925-3.409-.925Zm206.007 43.977v-1.218c-2.922-.194-3.799-.194-3.799-3.604V30.487c0-3.068 4.042-5.795 6.916-5.795 4.383 0 6.72 2.776 6.72 6.915v14.464c0 3.41-.876 3.41-3.944 3.604v1.267h13.344v-1.267c-2.289-.194-4.14-.194-4.14-3.604V30.487c0-4.919-4.967-8.669-9.838-8.669-4.285 0-6.379 1.948-9.155 4.724v-4.724c-2.923 1.072-5.991 2.581-9.156 3.02v1.071c2.824.292 3.798 1.169 3.798 3.701v16.56c0 3.409-.876 3.409-3.944 3.604v1.266h13.198v-.146Zm-70.422 0v-1.218c-3.458-.194-4.335-.194-4.335-3.604V28.636c0-1.071 1.753-2.727 3.653-2.727 1.753-.049 2.045 2.046 4.285 2.046 2.728 0 3.361-1.461 3.361-3.41 0-2.045-2.192-3.214-4.675-3.214-2.923 0-5.163 2.484-6.721 4.724v-4.48c-3.068 1.363-6.38 2.727-9.4 3.165v1.072c3.02.292 4.189 1.022 4.189 3.604V46.07c0 3.41-.877 3.41-3.799 3.604v1.267h13.442v-.05Zm28.1-27.516.974-3.604h-11.591v-5.844h-.681c-2.728 3.214-5.309 5.941-8.474 8.717v.78h3.701v19.82c0 4.725 2.143 8.62 7.208 8.62 4.383 0 7.694-3.165 9.496-5.308l-.828-1.12c-1.607 1.802-3.701 2.825-5.6 2.825-3.896 0-4.87-2.094-4.87-5.698V23.377h10.665Z" fill="#fff"/></svg>',
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Image, {
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 68.66666666666667,
                  intrinsicWidth: 200,
                  loading: "lazy",
                  pixelHeight: 103,
                  pixelWidth: 300,
                  src: new URL("https://framerusercontent.com/images/NXdh3pIjRuyiIVb3ZsOhyQnrCDo.svg").href
                },
                className: "framer-192z5q0",
                "data-framer-name": "stanford",
                name: "stanford"
              }), /*#__PURE__*/_jsx(SVG, {
                className: "framer-rjll25",
                "data-framer-name": "michigan",
                fill: "black",
                intrinsicHeight: 64,
                intrinsicWidth: 300,
                name: "michigan",
                svg: '<svg width="300" height="64" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m42.474 50.347-16.107-22.24v16.176h6.443v15.974H3.013V44.283h6.04V19.412h-6.04V3.437H26.42L42.553 25.88 58.72 3.437h23.404v15.975h-6.04v24.871h6.04v15.974H52.33V44.283h6.441V28.107l-16.297 22.24Z" fill="#FFCB05"/><path d="M201.768 4.218c-3.126 0-5.818 1.91-5.818 5.32 0 3.539 2.819 4.7 4.319 5.242.724.284 1.499.516 2.227.798.877.366 2.867 1.19 2.867 3.254 0 1.965-1.704 2.79-3.646 2.79-2.739 0-4.423-1.602-5.432-4.57l-.93.1.265 3.593 1.52 1.111c.722.464 2.248 1.445 4.731 1.445 3.852 0 6.438-2.605 6.438-5.756 0-2.195-1.191-3.796-2.638-4.65-.748-.438-1.599-.725-2.634-1.057-2.043-.674-4.19-1.4-4.19-3.568 0-1.446 1.136-2.375 3.075-2.375 1.449 0 2.69.528 3.568 1.664.724.959.959 1.511 1.269 2.699l.9-.179-.592-5.423-1.109.798c-1.55-.982-2.973-1.236-4.19-1.236Zm71.113.127c-5.863-.028-9.199 4.467-9.199 9.45 0 5.814 4.146 9.636 9.445 9.636 4.836 0 9.127-3.305 9.127-9.608 0-5.528-3.852-9.452-9.373-9.478Zm-35.803.232v1.006c1.757.104 1.783.155 3.1 2.118l4.856 7.517v3.2c-.025 1.991-.025 2.456-.439 2.867-.363.362-.905.416-2.326.571v.982h8.988v-.982c-2.711-.155-2.737-.413-2.737-3.719v-3.8l2.84-4.75c2.093-3.409 2.634-3.95 4.469-4.004V4.577h-7.128v1.006c1.266.078 2.013.105 2.013.982 0 .416-.255.958-.722 1.861l-2.581 4.54-2.791-4.567c-.464-.775-.798-1.344-.798-1.834 0-.877.85-.928 2.761-.982V4.577h-9.505Zm-58.854.02v.984c1.498.126 1.99.309 2.118 1.314.078.594.103 1.367.103 3.56v6.597a70.7 70.7 0 0 1-.027 2.348c-.026 1.96-.23 2.273-2.194 2.424v1.014h7.615v-1.014c-2.196-.047-2.198-.61-2.224-4.793l.003-2.37c1.731.028 2.092-.115 3.175 2.18l1.01 2.145c1.447 3.072 2.867 4.26 6.302 4.26.414 0 .798-.023 1.188-.05v-.62c-1.474-.203-2.091-.697-3.254-2.84l-3.23-5.616c2.505-.777 3.589-3.063 3.589-4.82 0-2.53-1.419-4.671-5.913-4.671l-8.26-.033h-.001Zm-16.032.002v.982c1.318.131 1.885.207 2.067 1.057.128.568.154 2.582.154 3.306v6.41c0 .49-.025 2.687-.051 3.125-.052 1.99-.284 2.169-2.17 2.35v1.01h13.898c0-1.316.231-3.488.387-4.726h-.879c-.622 3.255-2.454 3.094-5.091 3.094-2.427 0-2.916-.644-2.916-1.834v-5.098h1.964c2.247 0 2.892.192 2.892 2.237h.879V10.49h-.879c-.18 1.628-.517 2.159-2.868 2.159h-1.988V6.225h2.248c3.097 0 4.208.234 4.62 2.713h.828l-.106-4.34h-12.989Zm56.774 0-.154 5.115h.957c.155-3.255 1.52-3.178 3.587-3.178h2.275v13.432c0 1.45-.464 2.07-2.145 2.097v.773h7.544l-.003-.773c-1.679-.026-2.142-.648-2.142-2.097V6.536h2.272c2.067 0 3.436-.077 3.59 3.178h.957l-.157-5.115h-16.581Zm-127.567.005v.98c1.37.077 1.937.312 2.09 1.45.053.463.053 1.337.08 3.302v4.13c0 2.947.026 5.687 2.737 7.52 1.705 1.138 3.77 1.288 4.78 1.288 1.263 0 6.044-.255 7.284-5.161.31-1.318.384-2.69.435-4.082.026-.774.234-5.837.26-6.278.104-1.804.466-2.194 2.169-2.17v-.979h-6.949v.98c1.629.131 2.48.18 2.584 1.833.026.596.027 2.765.027 3.438 0 2.118-.025 5.319-.541 7.022-.62 2.143-2.054 3.5-4.661 3.5-.801 0-2.389-.174-3.322-1.533-1.17-1.712-1.211-3.619-1.211-5.84V9.717c.026-3.875.023-3.952 2.45-4.134v-.979H91.4Zm21.214 0v.98c1.216.103 1.628.286 2.638 1.214-.026 1.887-.105 12.706-.363 13.92-.206 1.033-.775 1.06-2.275 1.139v.982h7.439v-.982c-2.789-.282-2.994-.386-2.994-3.333V9.068l7.203 8.888a304.27 304.27 0 0 1 2.48 3.076l1.626 2.04h1.087l.26-14.077c.026-2.97.204-3.385 2.194-3.411v-.98h-7.182v.98c2.895.104 2.971.26 2.997 3.154v8.16l-5.088-6.304c-2.685-3.331-3.022-3.822-4.547-5.99h-5.475Zm21.259 0v.98c2.143.311 2.143.411 2.169 4.211v9.71c-.026 1.914-.389 2.118-2.169 2.351v.982h7.571v-.982c-1.418-.104-1.936-.361-2.064-1.471-.052-.544-.078-2.585-.078-3.233V9.796c0-3.9-.001-4.002 2.142-4.212v-.979h-7.571Zm9.308 0v.98c1.833.287 1.987.7 3.227 4.16l4.985 13.457h.982l5.553-13.254c1.498-3.613 1.731-4.104 3.487-4.364v-.979h-6.381v.98c1.136.026 2.429.076 2.429 1.395 0 .464-.31 1.29-.544 1.934l-3.716 9.584-3.179-9.27c-.489-1.525-.622-1.965-.622-2.48 0-1.06.958-1.11 2.429-1.164v-.979h-8.65Zm66.322 0v.98c2.145.311 2.148.411 2.175 4.211v9.71c-.027 1.914-.391 2.118-2.175 2.351v.982h7.572v-.982c-1.418-.104-1.936-.361-2.064-1.471-.052-.544-.079-2.585-.079-3.233V9.796c0-3.9 0-4.002 2.143-4.212v-.979h-7.572Zm73.643.13v.98c1.317.127 1.883.207 2.063 1.057.132.57.157 2.583.157 3.308l-.002 6.408c0 .49-.027 2.684-.054 3.122-.049 1.985-.284 2.169-2.167 2.35v1.007h7.552v-1.007c-1.885-.181-2.117-.365-2.169-2.35l.011-5.205h2.018c2.037 0 3.121.195 3.251 2.235h.882V10.62h-.882c-.104 1.965-.643 2.159-2.865 2.159h-2.404l.003-6.314c.647-.08 1.008-.105 2.172-.105 3.564 0 4.713-.016 5.19 2.84h.907l-.287-4.466h-13.376Zm-10.239 1.288c1.52-.047 2.678.878 3.265 1.506 1.34 1.43 1.997 3.447 2.069 5.8.073 2.373-.192 4.19-1.482 6.17-1.127 1.71-2.579 2.223-3.611 2.253-1.03 0-2.494-.465-3.673-2.142-1.349-1.94-1.669-3.743-1.669-6.122 0-2.35.591-4.394 1.888-5.864.57-.644 1.693-1.601 3.213-1.601Zm-88.164.19c3.357 0 4.422 1.83 4.312 3.948-.077 1.46-1.646 3.088-3.72 3.081a9.56 9.56 0 0 1-1.704-.18V6.316a4.752 4.752 0 0 1 1.112-.106Zm71.987 24.247c-1.368 1.02-2.267 1.487-5.469 2.54l.427.665-7.46 20.473c-1.056 2.888-1.994 3.477-4.885 3.595v1.485h11.371v-1.485c-1.679-.078-3.751-.194-3.751-2.15 0-.582.156-1.132.351-1.72l.855-2.384h9.727l.828 2.816c.155.507.313 1.095.313 1.642 0 1.523-1.447 1.678-3.321 1.796v1.485h23.338v-1.485c-4.221-.431-4.533-.587-4.533-5.04V38.385l10.9 13.446c1.252 1.527 2.5 3.087 3.749 4.65l2.465 3.089h1.642l.389-21.293c.038-4.491.313-5.122 3.322-5.156v-1.488h-10.863v1.488c4.376.156 4.493.389 4.533 4.763V50.23l-7.698-9.535c-4.063-5.04-4.572-5.78-6.876-9.062H267.8v1.488c1.837.156 2.461.428 3.985 1.834-.04 2.851-.157 19.22-.547 21.06-.283 1.414-1.07 1.574-2.883 1.68-2.691-.34-3.22-1.199-4.279-4.417l-6.094-18.756c-.625-1.953-.704-2.186-1.253-4.063Zm-29.909.467c-7.492 0-13.47 5.743-13.47 14.571 0 7.226 4.537 14.42 14.03 14.42 5.415 0 8.475-1.917 10.62-3.243v-2.072c.071-5.235.07-5.938 2.567-6.094v-1.485h-11.112v1.485c3.061.315 3.535.39 3.66 3.903.041 1.215-.071 1.878-.563 2.621-.142.156-1.726 2.305-4.785 2.305-5.38 0-8.507-5.43-8.507-11.759 0-7.934 4.009-11.996 8.826-11.996 2.777 0 4.537 1.152 5.451 2.05 2.074 2.073 2.92 3.967 3.481 5.608l1.125-.195-.211-8.637-1.439 1.642c-2.25-1.368-4.961-3.124-9.673-3.124Zm-70.79.041c-9.316 0-13.882 7.348-13.882 14.81 0 8.049 5.385 14.18 13.698 14.18 7.61 0 10.543-4.884 11.767-6.875l-1.642-1.244c-1.447 2.07-3.258 4.763-8.082 4.763-6.387 0-9.617-5.705-9.617-11.764 0-7.151 3.937-11.209 8.91-11.209 2.895 0 4.937 1.794 5.792 2.694 1.781 1.953 2.486 3.24 2.967 4.845l1.225-.239-.148-8.36-1.894 1.72c-1.594-1.246-4.195-3.322-9.094-3.322v.001Zm-63.542.666v1.487c2.46.035 3.008.233 3.008 2.965 0 .978.002 2.307-.038 3.205l-.39 11.564c-.041 1.523-.157 3.83-.235 4.571-.158 1.877-1.018 2.19-3.246 2.307v1.486h9.575v-1.486c-2.93-.233-3.243-.742-3.243-3.438l.206-16.221 8.859 21.495h2.423l9.649-21.217v13.874c-.04 5.12-.039 5.274-3.517 5.508v1.485h12.072v-1.485c-2.188-.196-2.968-.586-3.124-2.619-.118-1.407-.157-3.75-.157-5.626V38.12c-.038-4.298-.001-4.65 3.281-4.844v-1.642h-8.399l-9.453 20.941-8.6-20.941h-8.672Zm37.828 0v1.487c3.242.468 3.241.623 3.281 6.365v14.688c-.04 2.894-.584 3.208-3.281 3.56v1.485h11.452v-1.486c-2.148-.155-2.931-.55-3.127-2.228-.077-.82-.116-3.907-.116-4.886V39.486c0-5.898.001-6.053 3.243-6.365v-1.487h-11.452Zm39.334 0v1.487c2.149.192 2.928.427 3.084 2.067.118.86.119 2.658.119 4.455v11.566c0 .975 0 1.952-.038 2.93 0 2.653-.04 3.32-3.165 3.594v1.486h11.807v-1.486c-3.555-.39-3.633-.389-3.673-6.134v-5.059c2.384-.12 4.242-.194 6.625-.194 2.266 0 4.084.117 6.272.194v5.059c0 1.994 0 3.048-.078 3.827-.237 1.918-.978 1.99-3.752 2.307v1.486h11.961v-1.486c-2.032-.195-2.852-.467-3.048-1.917-.118-.816-.157-4.294-.157-5.389V38.782c0-5.234-.001-5.272 3.205-5.662v-1.487h-11.961v1.487c3.752.35 3.791.544 3.83 5.583v4.634c-2.346.116-4.202.194-6.546.194-2.304 0-3.967-.078-6.351-.194v-3.812c0-1.642.04-3.674.235-4.571.274-1.523 1.132-1.6 3.438-1.834v-1.488H169.65Zm31.445 0v1.487c3.246.468 3.247.623 3.287 6.365v14.688c-.04 2.894-.588 3.208-3.287 3.56v1.485h11.466v-1.486c-2.146-.155-2.925-.55-3.121-2.228-.08-.82-.122-3.907-.122-4.886V39.486c0-5.898.001-6.053 3.243-6.365v-1.487h-11.466Zm52.148 5.442 3.871 11.434h-7.926l4.055-11.434Z" fill="#fff"/></svg>',
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(SVG, {
                className: "framer-oefu9z",
                "data-framer-name": "ucla",
                fill: "black",
                intrinsicHeight: 144,
                intrinsicWidth: 300,
                name: "ucla",
                svg: '<svg width="300" height="144" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M300 0H0v143.5h300V0Z" fill="#0076B5"/><path d="M47.95 85.55c-.3-2.3-.15-5.6-.15-7.25l3.4-39.1h12.55L60.3 78.65c-.75 8.55 2.55 14.65 10.9 14.65 8.55 0 13.05-6.2 13.8-13.75l3.55-40.3h12.55l-3.45 39.8c-1.4 15.35-11.35 26.8-27.5 26.8-12.55-.05-20.65-8.3-22.2-20.3Zm56.1-13.25c1.65-18.75 14.05-34.35 35.5-34.35 5.65 0 11.05 1.3 16.5 4.85l-1.45 15.5c-4.95-6.65-10.85-7.7-16.25-7.7-13.35 0-20.7 9.45-21.75 21.45-1 11.65 4.9 21.15 17.8 21.15 5.65 0 11.65-1.65 17.9-8.05l-1.4 15.95c-6.1 3.35-11.8 4.9-17.5 4.9-21.2-.05-30.9-16.1-29.35-33.7Zm62-33.1h12.55l-4.55 53.1h17.9c1.25 0 5.55-.05 6.7-.15-.05.65-1.15 12.25-1.15 12.25h-37.15l5.7-65.2Zm65.25 24.35c.4-.95.8-2.1.95-2.75h.2c.05.8.2 1.8.5 2.75.7 2.95 5.55 19.5 5.55 19.5h-15.8c-.05 0 8.45-19.05 8.6-19.5Zm3.2-27.35h-.85l-34.3 68.2h13.3l4.8-10.35h24.15l3 10.35h13.35L234.5 36.2Z" fill="#fff"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h300v143.5H0z"/></clipPath></defs></svg>',
                withExternalLayout: true
              })]
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1pxzj3t",
            "data-framer-name": "impact",
            name: "impact",
            children: [/*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1u7ory6",
              children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  NTyq1hRL8: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsxs("p", {
                        style: {
                          "--font-selector": "SW50ZXItQm9sZA==",
                          "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                          "--framer-font-size": "20px",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-0.8px",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: [/*#__PURE__*/_jsx("span", {
                          style: {
                            "--framer-font-size": "30px"
                          },
                          children: "Make an impact"
                        }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                          style: {
                            "--framer-font-size": "30px"
                          },
                          children: "teaching what you love."
                        })]
                      })
                    })
                  },
                  pSYmZKfbO: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsxs("p", {
                        style: {
                          "--font-selector": "SW50ZXItQm9sZA==",
                          "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                          "--framer-font-size": "20px",
                          "--framer-font-weight": "700",
                          "--framer-letter-spacing": "-0.8px",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: [/*#__PURE__*/_jsx("span", {
                          style: {
                            "--framer-font-size": "26px"
                          },
                          children: "Make an impact"
                        }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                          style: {
                            "--framer-font-size": "26px"
                          },
                          children: "teaching what you love."
                        })]
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichTextWithFX, {
                  __framer__animate: {
                    transition: transition1
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation1,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __fromCanvasComponent: true,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsxs("p", {
                      style: {
                        "--font-selector": "SW50ZXItQm9sZA==",
                        "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
                        "--framer-font-size": "58px",
                        "--framer-font-weight": "700",
                        "--framer-letter-spacing": "-1px",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                      },
                      children: ["Make an impact", /*#__PURE__*/_jsx("br", {}), "teaching what you love."]
                    })
                  }),
                  className: "framer-yt9mvi",
                  fonts: ["Inter-Bold"],
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              }), /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  NTyq1hRL8: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("p", {
                        style: {
                          "--framer-line-height": "1.4em",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "What's coming to classes."
                      })
                    })
                  },
                  pSYmZKfbO: {
                    children: /*#__PURE__*/_jsx(React.Fragment, {
                      children: /*#__PURE__*/_jsx("p", {
                        style: {
                          "--framer-line-height": "1.4em",
                          "--framer-text-alignment": "center",
                          "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                        },
                        children: "What's coming to classes."
                      })
                    })
                  }
                },
                children: /*#__PURE__*/_jsx(RichTextWithFX, {
                  __framer__animate: {
                    transition: transition4
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation4,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __fromCanvasComponent: true,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--framer-font-size": "24px",
                        "--framer-line-height": "1.4em",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
                      },
                      children: "What's coming to classes."
                    })
                  }),
                  className: "framer-1e9jrj2",
                  verticalAlignment: "top",
                  withExternalLayout: true
                })
              })]
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1tkhtyu",
              children: /*#__PURE__*/_jsxs(motion.div, {
                className: "framer-zqsogw",
                children: [/*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition1
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation1,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-1g6k6hl",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-1bdx59c",
                    "data-framer-name": "class",
                    fill: "black",
                    intrinsicHeight: 34,
                    intrinsicWidth: 34,
                    name: "class",
                    svg: '<svg width="34" height="34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.846 21.923h12.308m-9.846 3.692h7.384M1 17h32" stroke="#202124" stroke-width="2" stroke-linecap="round"/><path stroke="#202124" stroke-width="2" d="M3.462 17h27.077v13.539H3.462z"/><path d="M3.462 17v16m27.076-16v16" stroke="#202124" stroke-width="2" stroke-linecap="round"/><path d="M24.384 15.945c0-3.065-1.97-5.58-4.055-6.654-1.14-.588-1.639-.896-.65-1.695.689-.557 1.382-1.399 1.382-2.728C21.061 2.732 19.243 1 17 1s-4.062 1.732-4.062 3.868c0 1.33.694 2.171 1.383 2.728.989.799.49 1.107-.65 1.695-2.086 1.075-4.056 3.589-4.056 6.654" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-to3z9c",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(PropertyOverrides, {
                      breakpoint: baseVariant,
                      overrides: {
                        ndrGg8iNq: {
                          children: /*#__PURE__*/_jsx(React.Fragment, {
                            children: /*#__PURE__*/_jsx("p", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-size": "18px",
                                "--framer-font-weight": "500",
                                "--framer-line-height": "135%",
                                "--framer-text-alignment": "center",
                                "--framer-text-color": "rgb(32, 33, 36)"
                              },
                              children: "Set your price"
                            })
                          })
                        }
                      },
                      children: /*#__PURE__*/_jsx(RichText, {
                        __fromCanvasComponent: true,
                        children: /*#__PURE__*/_jsx(React.Fragment, {
                          children: /*#__PURE__*/_jsx("p", {
                            style: {
                              "--framer-line-height": "135%",
                              "--framer-text-alignment": "center"
                            },
                            children: /*#__PURE__*/_jsx("span", {
                              style: {
                                "--font-selector": "R0Y7SW50ZXItNTAw",
                                "--framer-font-size": "18px",
                                "--framer-font-weight": "500",
                                "--framer-text-color": "rgb(32, 33, 36)"
                              },
                              children: "Create a class"
                            })
                          })
                        }),
                        className: "framer-recjih",
                        "data-framer-name": "All in one solution",
                        fonts: ["GF;Inter-500"],
                        name: "All in one solution",
                        verticalAlignment: "top",
                        withExternalLayout: true
                      })
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsxs(React.Fragment, {
                        children: [/*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "Teach online from wherever "
                        }), /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "and set your own prices."
                        })]
                      }),
                      className: "framer-ohece8",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition4
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation4,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-1h8elkr",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-1k3c9wk",
                    "data-framer-name": "dates",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "dates",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 7a1 1 0 0 1 1-1h28a1 1 0 0 1 1 1v25a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7Z" stroke="#202124" stroke-width="2"/><path d="M11 2v3.048M25 2v3.048M3 12.662h30m-23 7.626h4m8 0h4m-16 6.089h4m8 0h4" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-1ti9dip",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Set dates and times"
                        })
                      }),
                      className: "framer-1024zp4",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "Design a curriculum you love and teach the way you want."
                        })
                      }),
                      className: "framer-2oh1cw",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition2
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation2,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-q9p97",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-6d0vo1",
                    "data-framer-name": "enroll",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "enroll",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M33 27.778v-5.673c0-.648-.368-1.297-.987-1.503a3.134 3.134 0 0 1 0-5.953c.619-.206.987-.84.987-1.503V7.473C33 6.663 32.337 6 31.526 6H4.473C3.663 6 3 6.663 3 7.473v5.673c0 .649.368 1.297.987 1.503a3.134 3.134 0 0 1 0 5.953c-.619.206-.987.84-.987 1.503v5.673c0 .81.663 1.474 1.473 1.474h27.053A1.48 1.48 0 0 0 33 27.778Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="m18.782 11.82.972 2.992a.81.81 0 0 0 .78.56h3.14c.795 0 1.12 1.016.486 1.473l-2.55 1.842a.811.811 0 0 0-.294.913l.972 2.992c.25.751-.619 1.385-1.252.913l-2.55-1.842a.819.819 0 0 0-.957 0l-2.55 1.842c-.633.472-1.502-.162-1.252-.913L14.7 19.6c.103-.338 0-.707-.295-.913l-2.549-1.842c-.633-.471-.31-1.473.486-1.473h3.139a.823.823 0 0 0 .78-.56l.973-2.992c.25-.751 1.312-.751 1.562 0h-.014Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-8x516c",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Enroll students"
                        })
                      }),
                      className: "framer-n7140l",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: ["Work with people who have", /*#__PURE__*/_jsx("br", {}), "chosen to be in your class."]
                        })
                      }),
                      className: "framer-2zr2k8",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition1
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation1,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-hrs2co",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-1iy0s9s",
                    "data-framer-name": "world",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "world",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="18" cy="18" r="15" stroke="#202124" stroke-width="2"/><path d="M3 18.002h30" stroke="#202124" stroke-width="2"/><path d="M17.998 3c.482.876.483.876.483.875h.002l.002-.001.003-.002a5.37 5.37 0 0 0-.666.521c-.446.409-1.066 1.086-1.696 2.125C14.87 8.591 13.543 12.157 13.543 18h-2c0-6.157 1.402-10.091 2.873-12.518.733-1.21 1.477-2.034 2.054-2.563.288-.264.535-.455.716-.583a4.62 4.62 0 0 1 .323-.208l.004-.002.002-.001s.001-.001.483.875Zm-4.455 15c0 5.843 1.327 9.409 2.583 11.482.63 1.04 1.25 1.716 1.695 2.125a5.276 5.276 0 0 0 .645.508l.022.013c-.001 0-.002 0-.003-.002h-.002l-.002-.002-.483.876c-.482.876-.483.876-.483.875h-.002a.825.825 0 0 0-.004-.002l-.008-.005a1.712 1.712 0 0 1-.093-.056 4.563 4.563 0 0 1-.222-.148 7.35 7.35 0 0 1-.716-.583c-.577-.53-1.32-1.352-2.054-2.563-1.472-2.427-2.873-6.361-2.873-12.518h2Z" fill="#202124"/><path d="M17.998 3c-.482.876-.483.876-.483.875h-.002l-.002-.001-.002-.002c-.002 0-.002 0 0 0l.021.013a5.276 5.276 0 0 1 .645.508c.445.409 1.065 1.086 1.695 2.125 1.256 2.073 2.583 5.639 2.583 11.482h2c0-6.157-1.401-10.091-2.872-12.518-.734-1.21-1.477-2.034-2.055-2.563a7.353 7.353 0 0 0-.716-.583 4.62 4.62 0 0 0-.291-.19l-.023-.014a.776.776 0 0 0-.01-.004l-.003-.002-.002-.001s-.001-.001-.483.875Zm4.455 15c0 5.843-1.327 9.409-2.583 11.482-.63 1.04-1.25 1.716-1.695 2.125a5.373 5.373 0 0 1-.52.424c-.058.041-.1.069-.125.084l-.022.013c.001 0 .002 0 .003-.002h.003v-.002c.001 0 .002 0 .484.876s.483.876.483.875h.002a.825.825 0 0 1 .004-.002 1.769 1.769 0 0 0 .102-.06c.055-.036.13-.085.22-.149.182-.128.429-.319.717-.583.578-.53 1.32-1.352 2.055-2.563 1.47-2.427 2.872-6.361 2.872-12.518h-2Z" fill="#202124"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-gknktf",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Your own landing page"
                        })
                      }),
                      className: "framer-1jhgx05",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "The only platform designed for tutoring professionals."
                        })
                      }),
                      className: "framer-1153nvc",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition4
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation4,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-1b5yy9s",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-t4290p",
                    "data-framer-name": "testimonials",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "testimonials",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M31.526 8.669H4.473C3.66 8.669 3 9.329 3 10.142v18.89c0 .814.66 1.474 1.473 1.474h27.053c.814 0 1.474-.66 1.474-1.474v-18.89c0-.813-.66-1.473-1.474-1.473Zm-17.254 9.744H8.039m3.109 5.003h-3.11" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="m18.78 19.605 3.182 3.404 5.437-6.808M10.5 6v5.334M26.654 6v5.334M18.574 6v5.334" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-q21cng",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Collect testimonials"
                        })
                      }),
                      className: "framer-r60y",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "Learn why students love your class and collect positive reviews."
                        })
                      }),
                      className: "framer-1bkxb53",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition2
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation2,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-s215s",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-s3mi4p",
                    "data-framer-name": "message",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "message",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="7.5" width="30" height="21" rx="1" stroke="#202124" stroke-width="2"/><path d="M4.5 9 18 18l13.5-9" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-1rl229r",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Class messaging and chat"
                        })
                      }),
                      className: "framer-mcmwzr",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: ["Securely send messages,", /*#__PURE__*/_jsx("br", {}), "photos, videos, and documents."]
                        })
                      }),
                      className: "framer-57psee",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition1
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation1,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-yyimdj",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-hy2lcq",
                    "data-framer-name": "cancelation",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "cancelation",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m14.002 18.71 8.572 8.571m-.002-8.571-8.571 8.571" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3 7a1 1 0 0 1 1-1h28a1 1 0 0 1 1 1v25a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7Z" stroke="#202124" stroke-width="2"/><path d="M11 2v3.048M25 2v3.048M3 12.662h30" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-6p0kw7",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Cancelation policies"
                        })
                      }),
                      className: "framer-18fatc8",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsxs("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: ["Choose a cancelation policy", /*#__PURE__*/_jsx("br", {}), "that works for you."]
                        })
                      }),
                      className: "framer-139vqv6",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition4
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation4,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-1nwu40o",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-1ooe3u0",
                    "data-framer-name": "video",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "video",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.2 8.857h15.886c1.767 0 3.2 1.206 3.2 2.694v3.906L33 10.43v15.714l-7.714-5.029v3.906c0 1.488-1.433 2.694-3.2 2.694H6.2c-1.767 0-3.2-1.206-3.2-2.694v-13.47c0-1.488 1.433-2.694 3.2-2.694Z" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M11.143 8v18.857" stroke="#202124" stroke-width="2"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-fxhn4m",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Large video calls"
                        })
                      }),
                      className: "framer-1u6asnk",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "Up to 200 people with breakout rooms and whiteboards."
                        })
                      }),
                      className: "framer-lkt6cu",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                }), /*#__PURE__*/_jsxs(MotionDivWithFX, {
                  __framer__animate: {
                    transition: transition2
                  },
                  __framer__animateOnce: true,
                  __framer__enter: animation,
                  __framer__exit: animation2,
                  __framer__styleAppearEffectEnabled: true,
                  __framer__threshold: 0,
                  __perspectiveFX: false,
                  __targetOpacity: 1,
                  className: "framer-1y4va38",
                  "data-framer-name": "Features",
                  name: "Features",
                  children: [/*#__PURE__*/_jsx(SVG, {
                    className: "framer-8zk6zh",
                    "data-framer-name": "analytics",
                    fill: "black",
                    intrinsicHeight: 36,
                    intrinsicWidth: 36,
                    name: "analytics",
                    svg: '<svg width="36" height="36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 5v23.531c0 .87.707 1.592 1.591 1.592H33M8.244 24.877v-6.07m6.293 6.071V12.47m6.307 12.407v-9.239m6.291 9.239V9.287" stroke="#202124" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                    withExternalLayout: true
                  }), /*#__PURE__*/_jsxs(motion.div, {
                    className: "framer-ru75sy",
                    "data-framer-name": "text",
                    name: "text",
                    children: [/*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItNTAw",
                            "--framer-font-size": "18px",
                            "--framer-font-weight": "500",
                            "--framer-line-height": "135%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(32, 33, 36)"
                          },
                          children: "Analytics"
                        })
                      }),
                      className: "framer-17hwhbf",
                      "data-framer-name": "All in one solution",
                      fonts: ["GF;Inter-500"],
                      name: "All in one solution",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    }), /*#__PURE__*/_jsx(RichText, {
                      __fromCanvasComponent: true,
                      children: /*#__PURE__*/_jsx(React.Fragment, {
                        children: /*#__PURE__*/_jsx("p", {
                          style: {
                            "--font-selector": "R0Y7SW50ZXItcmVndWxhcg==",
                            "--framer-line-height": "125%",
                            "--framer-text-alignment": "center",
                            "--framer-text-color": "rgb(95, 99, 104)"
                          },
                          children: "Track your earnings, bookings, minutes tutored, and more."
                        })
                      }),
                      className: "framer-11bny6h",
                      "data-framer-name": "Teach online from wherever \u2028and set your own prices.",
                      fonts: ["GF;Inter-regular"],
                      name: "Teach online from wherever \u2028and set your own prices.",
                      verticalAlignment: "top",
                      withExternalLayout: true
                    })]
                  })]
                })]
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.section, {
            className: "framer-12qfrzd",
            children: [/*#__PURE__*/_jsx(motion.header, {
              className: "framer-x1tk5h",
              "data-framer-name": "Stack",
              name: "Stack",
              children: /*#__PURE__*/_jsx(TextWithFX, {
                __framer__animate: {
                  transition: transition1
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation1,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __fromCanvasComponent: true,
                __perspectiveFX: false,
                __targetOpacity: 1,
                alignment: "center",
                className: "framer-1v5tyn",
                fonts: ["GF;Inter-700"],
                rawHTML: "<h1 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>In case you</span><br></span><span style='direction: ltr; font-size: 0'><span style=''>missed anything.</span><br></span></h1>",
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(ContainerWithFX, {
              __framer__animate: {
                transition: transition4
              },
              __framer__animateOnce: true,
              __framer__enter: animation,
              __framer__exit: animation4,
              __framer__styleAppearEffectEnabled: true,
              __framer__threshold: 0,
              __perspectiveFX: false,
              __targetOpacity: 1,
              className: "framer-1n47f2b-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  ndrGg8iNq: {
                    copy5: "No fees are charged for classes when tutors invite students they already work with to Popless. A 10% platform fee is charged to tutors who find students to tutor on Popless. The platform fee helps cover the costs of running Popless, building a highly-skilled community of tutors, and the 24/7 support Popless provides. The platform fee is calculated as a percentage of the price a tutor sets for their services."
                  },
                  pSYmZKfbO: {
                    variant: "g3UpsbSX9"
                  }
                },
                children: /*#__PURE__*/_jsx(FAQStack, {
                  copy1: "Popless is the first all-in-one teaching platform. It’s designed for private tutoring group-class teaching. Popless supercharges teaching by helping manage conversations, payments, scheduling, reporting, and more in one place. With build student engagement features, teachers can offer discounts to students when they purchase multiple meetings, and build a growing business on a trusted platform. By using Popless, teachers are able to opt into the Popless marketplace so they can explore new opportunities and be found by students looking for their next tutor.",
                  copy2: "Teachers can customize their class page. There will be class descriptions, FAQ’s, and testimonials teachers can add to their class page so you can be sure students can learn more about your class.",
                  copy3: "",
                  copy4: "There will initially be a maximum of 20 attendees per class. While we will look to increase this in the future, we will start with this limit.",
                  copy5: "Popless charges a 5% booking fee to students who enroll in a class. This helps cover the costs of running Popless, building a highly-skilled community of teachers, and the 24/7 support Popless provides.",
                  copy6: "",
                  copy7: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
                  copy8: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
                  copy9: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
                  height: "100%",
                  id: "T8rzLrZBQ",
                  layoutId: "T8rzLrZBQ",
                  style: {
                    width: "100%"
                  },
                  title1: "What is Popless?",
                  title2: "What will classes look like?",
                  title3: "How will payments work?",
                  title4: "How many students can enroll in a class?",
                  title5: "What is the pricing for classes?",
                  title6: "",
                  title7: "",
                  title8: "Title here",
                  title9: "Title here",
                  variant: "N_ESJQqxE",
                  width: "100%"
                })
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-nmzv98",
            "data-framer-name": "teach",
            name: "teach",
            children: [/*#__PURE__*/_jsx(MotionDivWithFX, {
              __framer__animate: {
                transition: transition1
              },
              __framer__animateOnce: true,
              __framer__enter: animation,
              __framer__exit: animation1,
              __framer__styleAppearEffectEnabled: true,
              __framer__threshold: 0,
              __perspectiveFX: false,
              __targetOpacity: 1,
              className: "framer-148cze9",
              children: /*#__PURE__*/_jsx(Container, {
                className: "framer-a4i21i-container",
                children: /*#__PURE__*/_jsx(AssetsLogo, {
                  color: "rgb(255, 255, 255)",
                  height: "100%",
                  id: "d9IAjBFeb",
                  layoutId: "d9IAjBFeb",
                  style: {
                    height: "100%",
                    width: "100%"
                  },
                  title: "Popless",
                  variant: "vOkJ3G6Tc",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                NTyq1hRL8: {
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                        "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                        "--framer-font-size": "34px",
                        "--framer-font-weight": "600",
                        "--framer-letter-spacing": "-0.8px",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "rgb(255, 255, 255)"
                      },
                      children: /*#__PURE__*/_jsxs("span", {
                        "data-text-fill": "true",
                        style: {
                          backgroundImage: "linear-gradient(0deg, rgba(255, 255, 255, 0.6) 0%, rgb(255, 255, 255) 100%)"
                        },
                        children: ["Teach what you love on the", /*#__PURE__*/_jsx("br", {}), "leading teaching platform."]
                      })
                    })
                  })
                },
                pSYmZKfbO: {
                  children: /*#__PURE__*/_jsx(React.Fragment, {
                    children: /*#__PURE__*/_jsx("p", {
                      style: {
                        "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                        "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                        "--framer-font-size": "34px",
                        "--framer-font-weight": "600",
                        "--framer-letter-spacing": "-0.8px",
                        "--framer-text-alignment": "center",
                        "--framer-text-color": "rgb(255, 255, 255)"
                      },
                      children: /*#__PURE__*/_jsxs("span", {
                        "data-text-fill": "true",
                        style: {
                          backgroundImage: "linear-gradient(0deg, rgba(255, 255, 255, 0.6) 0%, rgb(255, 255, 255) 100%)"
                        },
                        children: ["Teach what you love on the", /*#__PURE__*/_jsx("br", {}), "leading teaching platform."]
                      })
                    })
                  })
                }
              },
              children: /*#__PURE__*/_jsx(RichTextWithFX, {
                __framer__animate: {
                  transition: transition4
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation4,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __fromCanvasComponent: true,
                __perspectiveFX: false,
                __targetOpacity: 1,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx("p", {
                    style: {
                      "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                      "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                      "--framer-font-size": "58px",
                      "--framer-font-weight": "600",
                      "--framer-letter-spacing": "-1px",
                      "--framer-text-alignment": "center",
                      "--framer-text-color": "rgb(255, 255, 255)"
                    },
                    children: /*#__PURE__*/_jsxs("span", {
                      "data-text-fill": "true",
                      style: {
                        backgroundImage: "linear-gradient(0deg, rgba(255, 255, 255, 0.6) 0%, rgb(255, 255, 255) 100%)"
                      },
                      children: [/*#__PURE__*/_jsx("span", {
                        style: {
                          "--framer-font-size": "70px"
                        },
                        children: "Teach what you love on the"
                      }), /*#__PURE__*/_jsx("br", {}), /*#__PURE__*/_jsx("span", {
                        style: {
                          "--framer-font-size": "70px"
                        },
                        children: "leading teaching platform."
                      })]
                    })
                  })
                }),
                className: "framer-kaz680",
                fonts: ["Inter-SemiBold"],
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1ygvuwz",
              children: [/*#__PURE__*/_jsx(ContainerWithFX, {
                __framer__animate: {
                  transition: transition2
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation2,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __perspectiveFX: false,
                __targetOpacity: 1,
                className: "framer-n6jesi-container",
                children: /*#__PURE__*/_jsx(PropertyOverrides, {
                  breakpoint: baseVariant,
                  overrides: {
                    pSYmZKfbO: {
                      style: {
                        height: "100%",
                        width: "100%"
                      }
                    }
                  },
                  children: /*#__PURE__*/_jsx(AssetsButtonMain, {
                    background: "rgb(3, 104, 224)",
                    buttonBG: "rgb(0, 0, 0)",
                    height: "100%",
                    id: "pl4BuTCb1",
                    layoutId: "pl4BuTCb1",
                    style: {
                      height: "100%"
                    },
                    tap: tap904hit,
                    textColour: "rgb(255, 255, 255)",
                    title: "Sign up for free",
                    variant: "biGcS_1Nr",
                    width: "100%"
                  })
                })
              }), /*#__PURE__*/_jsx(ContainerWithFX, {
                __framer__animate: {
                  transition: transition3
                },
                __framer__animateOnce: true,
                __framer__enter: animation,
                __framer__exit: animation3,
                __framer__styleAppearEffectEnabled: true,
                __framer__threshold: 0,
                __perspectiveFX: false,
                __targetOpacity: 1,
                className: "framer-1bbknoc-container",
                children: /*#__PURE__*/_jsx(PropertyOverrides, {
                  breakpoint: baseVariant,
                  overrides: {
                    pSYmZKfbO: {
                      style: {
                        height: "100%",
                        width: "100%"
                      }
                    }
                  },
                  children: /*#__PURE__*/_jsx(AssetsButtonMain, {
                    background: "rgb(3, 104, 224)",
                    buttonBG: "rgb(0, 0, 0)",
                    height: "100%",
                    id: "iBJvkdteA",
                    layoutId: "iBJvkdteA",
                    style: {
                      height: "100%"
                    },
                    tap: tap904hit,
                    textColour: "rgb(255, 255, 255)",
                    title: "Request a demo",
                    variant: "m7ICNTpLf",
                    width: "100%"
                  })
                })
              })]
            })]
          }), /*#__PURE__*/_jsx(Container, {
            className: "framer-143sc3s-container",
            children: /*#__PURE__*/_jsx(PropertyOverrides, {
              breakpoint: baseVariant,
              overrides: {
                pSYmZKfbO: {
                  variant: "Zz_9kWOfb"
                }
              },
              children: /*#__PURE__*/_jsx(FooterNew, {
                height: "100%",
                id: "RIYWZ6tMR",
                layoutId: "RIYWZ6tMR",
                poplessLink: poplessLink1xqqrqe,
                style: {
                  width: "100%"
                },
                twitterLink: twitterLinkzdpqk6,
                variant: "zyTRmFlly",
                width: "100%"
              })
            })
          })]
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-4kBfB [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", `.${metadata.bodyClassName} { background: white; }`, ".framer-4kBfB .framer-1js9w0v { display: block; }", ".framer-4kBfB .framer-xxfe1n { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }", ".framer-4kBfB .framer-1qdpz30-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 3; }", ".framer-4kBfB .framer-4by23b { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; height: 100vh; justify-content: space-between; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-4kBfB .framer-zc2tfg { align-content: center; align-items: center; display: flex; flex: 0.5 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: 1px; justify-content: center; overflow: visible; padding: 80px 40px 20px 40px; position: relative; width: 100%; z-index: 2; }", ".framer-4kBfB .framer-1itmnlg { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 5px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-4kBfB .framer-lf16sh, .framer-4kBfB .framer-yt9mvi, .framer-4kBfB .framer-kaz680 { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-4kBfB .framer-q0hqlm, .framer-4kBfB .framer-akophw { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 885px; word-break: break-word; word-wrap: break-word; }", ".framer-4kBfB .framer-1hzn9h8-container { flex: none; height: 52px; position: relative; width: 480px; }", ".framer-4kBfB .framer-ble6ye { aspect-ratio: 3.914373088685015 / 1; bottom: 0px; flex: none; height: var(--framer-aspect-ratio-supported, 327px); left: calc(50.00000000000002% - 100% / 2); max-height: 40%; overflow: visible; position: absolute; width: 100%; z-index: 1; }", ".framer-4kBfB .framer-1r5fquh { align-content: center; align-items: center; bottom: 0px; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 294px; justify-content: center; left: 0px; overflow: visible; padding: 0px 0px 0px 0px; position: absolute; right: 0px; }", ".framer-4kBfB .framer-10qh9ok { aspect-ratio: 1.3265306122448979 / 1; bottom: 0px; flex: none; height: var(--framer-aspect-ratio-supported, 294px); left: 0px; overflow: visible; position: absolute; right: 0px; z-index: 1; }", ".framer-4kBfB .framer-m6wzej { align-content: center; align-items: center; background-color: #000000; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: 330px; justify-content: center; overflow: hidden; padding: 0px 40px 0px 40px; position: relative; width: 100%; }", ".framer-4kBfB .framer-yimur9 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: wrap; gap: 50px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }", ".framer-4kBfB .framer-sqozob { aspect-ratio: 2.9702970297029703 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 42px); position: relative; width: 125px; }", ".framer-4kBfB .framer-56yvsq { aspect-ratio: 3.79746835443038 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 40px); overflow: visible; position: relative; width: 150px; }", ".framer-4kBfB .framer-f5fodc { aspect-ratio: 4.411764705882353 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 34px); position: relative; width: 150px; }", ".framer-4kBfB .framer-1kf8grz { aspect-ratio: 3.75 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 37px); position: relative; width: 140px; }", ".framer-4kBfB .framer-192z5q0 { aspect-ratio: 2.9126213592233006 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 43px); overflow: visible; position: relative; width: 125px; }", ".framer-4kBfB .framer-rjll25 { aspect-ratio: 4.6875 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 32px); position: relative; width: 150px; }", ".framer-4kBfB .framer-oefu9z { aspect-ratio: 2.0833333333333335 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 48px); position: relative; width: 100px; }", ".framer-4kBfB .framer-1pxzj3t { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 90px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 220px 40px 220px 40px; position: relative; width: 100%; }", ".framer-4kBfB .framer-1u7ory6 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 885px; }", ".framer-4kBfB .framer-1e9jrj2 { --framer-link-text-color: #0099ff; --framer-link-text-decoration: underline; --framer-paragraph-spacing: 0px; flex: none; height: auto; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-4kBfB .framer-1tkhtyu { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-4kBfB .framer-zqsogw { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: wrap; gap: 20px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 1000px; }", ".framer-4kBfB .framer-1g6k6hl, .framer-4kBfB .framer-1h8elkr, .framer-4kBfB .framer-q9p97, .framer-4kBfB .framer-hrs2co, .framer-4kBfB .framer-1b5yy9s, .framer-4kBfB .framer-s215s, .framer-4kBfB .framer-yyimdj, .framer-4kBfB .framer-1nwu40o, .framer-4kBfB .framer-1y4va38 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 8px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-4kBfB .framer-1bdx59c, .framer-4kBfB .framer-1k3c9wk, .framer-4kBfB .framer-6d0vo1, .framer-4kBfB .framer-1iy0s9s, .framer-4kBfB .framer-t4290p, .framer-4kBfB .framer-s3mi4p, .framer-4kBfB .framer-hy2lcq, .framer-4kBfB .framer-1ooe3u0, .framer-4kBfB .framer-8zk6zh { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 30px); position: relative; width: 30px; }", ".framer-4kBfB .framer-to3z9c, .framer-4kBfB .framer-1ti9dip, .framer-4kBfB .framer-8x516c, .framer-4kBfB .framer-gknktf, .framer-4kBfB .framer-q21cng, .framer-4kBfB .framer-1rl229r, .framer-4kBfB .framer-6p0kw7, .framer-4kBfB .framer-fxhn4m, .framer-4kBfB .framer-ru75sy { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 4px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 282px; }", ".framer-4kBfB .framer-recjih, .framer-4kBfB .framer-ohece8, .framer-4kBfB .framer-1024zp4, .framer-4kBfB .framer-2oh1cw, .framer-4kBfB .framer-n7140l, .framer-4kBfB .framer-2zr2k8, .framer-4kBfB .framer-1jhgx05, .framer-4kBfB .framer-1153nvc, .framer-4kBfB .framer-r60y, .framer-4kBfB .framer-1bkxb53, .framer-4kBfB .framer-mcmwzr, .framer-4kBfB .framer-57psee, .framer-4kBfB .framer-18fatc8, .framer-4kBfB .framer-139vqv6, .framer-4kBfB .framer-1u6asnk, .framer-4kBfB .framer-lkt6cu, .framer-4kBfB .framer-17hwhbf, .framer-4kBfB .framer-11bny6h { --framer-paragraph-spacing: 0px; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-4kBfB .framer-12qfrzd { align-content: center; align-items: center; background-color: var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, #f5f5f5); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: center; overflow: visible; padding: 200px 0px 200px 0px; position: relative; width: 100%; }", ".framer-4kBfB .framer-x1tk5h { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 40px 0px; position: relative; width: 700px; }", '.framer-4kBfB .framer-1v5tyn { --framer-font-family: "Inter", "Inter Placeholder", sans-serif; --framer-font-size: 60px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-line-height: 73px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 700px; word-break: break-word; word-wrap: break-word; }', ".framer-4kBfB .framer-1n47f2b-container { flex: none; height: auto; position: relative; width: 700px; }", ".framer-4kBfB .framer-nmzv98 { align-content: center; align-items: center; background-color: #000000; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 50px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 220px 40px 94px 40px; position: relative; width: 100%; }", ".framer-4kBfB .framer-148cze9 { align-content: center; align-items: center; background-color: #ffffff; border-bottom-left-radius: 20px; border-bottom-right-radius: 20px; border-top-left-radius: 20px; border-top-right-radius: 20px; box-shadow: 0px 0px 187px 52px rgba(255, 255, 255, 0.25); display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 48px; justify-content: center; overflow: hidden; padding: 45px 45px 45px 45px; position: relative; width: 48px; will-change: transform; }", ".framer-4kBfB .framer-a4i21i-container { flex: none; height: 48px; position: relative; width: 48px; }", ".framer-4kBfB .framer-1ygvuwz { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-4kBfB .framer-n6jesi-container, .framer-4kBfB .framer-1bbknoc-container { flex: none; height: 50px; position: relative; width: auto; }", ".framer-4kBfB .framer-143sc3s-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-4kBfB .framer-xxfe1n, .framer-4kBfB .framer-zc2tfg, .framer-4kBfB .framer-1itmnlg, .framer-4kBfB .framer-1r5fquh, .framer-4kBfB .framer-m6wzej, .framer-4kBfB .framer-yimur9, .framer-4kBfB .framer-1pxzj3t, .framer-4kBfB .framer-1u7ory6, .framer-4kBfB .framer-1tkhtyu, .framer-4kBfB .framer-zqsogw, .framer-4kBfB .framer-1g6k6hl, .framer-4kBfB .framer-to3z9c, .framer-4kBfB .framer-1h8elkr, .framer-4kBfB .framer-1ti9dip, .framer-4kBfB .framer-q9p97, .framer-4kBfB .framer-8x516c, .framer-4kBfB .framer-hrs2co, .framer-4kBfB .framer-gknktf, .framer-4kBfB .framer-1b5yy9s, .framer-4kBfB .framer-q21cng, .framer-4kBfB .framer-s215s, .framer-4kBfB .framer-1rl229r, .framer-4kBfB .framer-yyimdj, .framer-4kBfB .framer-6p0kw7, .framer-4kBfB .framer-1nwu40o, .framer-4kBfB .framer-fxhn4m, .framer-4kBfB .framer-1y4va38, .framer-4kBfB .framer-ru75sy, .framer-4kBfB .framer-12qfrzd, .framer-4kBfB .framer-x1tk5h, .framer-4kBfB .framer-nmzv98, .framer-4kBfB .framer-148cze9, .framer-4kBfB .framer-1ygvuwz { gap: 0px; } .framer-4kBfB .framer-xxfe1n > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-4kBfB .framer-xxfe1n > :first-child, .framer-4kBfB .framer-zc2tfg > :first-child, .framer-4kBfB .framer-1itmnlg > :first-child, .framer-4kBfB .framer-1r5fquh > :first-child, .framer-4kBfB .framer-m6wzej > :first-child, .framer-4kBfB .framer-1pxzj3t > :first-child, .framer-4kBfB .framer-1u7ory6 > :first-child, .framer-4kBfB .framer-1tkhtyu > :first-child, .framer-4kBfB .framer-1g6k6hl > :first-child, .framer-4kBfB .framer-to3z9c > :first-child, .framer-4kBfB .framer-1h8elkr > :first-child, .framer-4kBfB .framer-1ti9dip > :first-child, .framer-4kBfB .framer-q9p97 > :first-child, .framer-4kBfB .framer-8x516c > :first-child, .framer-4kBfB .framer-hrs2co > :first-child, .framer-4kBfB .framer-gknktf > :first-child, .framer-4kBfB .framer-1b5yy9s > :first-child, .framer-4kBfB .framer-q21cng > :first-child, .framer-4kBfB .framer-s215s > :first-child, .framer-4kBfB .framer-1rl229r > :first-child, .framer-4kBfB .framer-yyimdj > :first-child, .framer-4kBfB .framer-6p0kw7 > :first-child, .framer-4kBfB .framer-1nwu40o > :first-child, .framer-4kBfB .framer-fxhn4m > :first-child, .framer-4kBfB .framer-1y4va38 > :first-child, .framer-4kBfB .framer-ru75sy > :first-child, .framer-4kBfB .framer-12qfrzd > :first-child, .framer-4kBfB .framer-x1tk5h > :first-child, .framer-4kBfB .framer-nmzv98 > :first-child { margin-top: 0px; } .framer-4kBfB .framer-xxfe1n > :last-child, .framer-4kBfB .framer-zc2tfg > :last-child, .framer-4kBfB .framer-1itmnlg > :last-child, .framer-4kBfB .framer-1r5fquh > :last-child, .framer-4kBfB .framer-m6wzej > :last-child, .framer-4kBfB .framer-1pxzj3t > :last-child, .framer-4kBfB .framer-1u7ory6 > :last-child, .framer-4kBfB .framer-1tkhtyu > :last-child, .framer-4kBfB .framer-1g6k6hl > :last-child, .framer-4kBfB .framer-to3z9c > :last-child, .framer-4kBfB .framer-1h8elkr > :last-child, .framer-4kBfB .framer-1ti9dip > :last-child, .framer-4kBfB .framer-q9p97 > :last-child, .framer-4kBfB .framer-8x516c > :last-child, .framer-4kBfB .framer-hrs2co > :last-child, .framer-4kBfB .framer-gknktf > :last-child, .framer-4kBfB .framer-1b5yy9s > :last-child, .framer-4kBfB .framer-q21cng > :last-child, .framer-4kBfB .framer-s215s > :last-child, .framer-4kBfB .framer-1rl229r > :last-child, .framer-4kBfB .framer-yyimdj > :last-child, .framer-4kBfB .framer-6p0kw7 > :last-child, .framer-4kBfB .framer-1nwu40o > :last-child, .framer-4kBfB .framer-fxhn4m > :last-child, .framer-4kBfB .framer-1y4va38 > :last-child, .framer-4kBfB .framer-ru75sy > :last-child, .framer-4kBfB .framer-12qfrzd > :last-child, .framer-4kBfB .framer-x1tk5h > :last-child, .framer-4kBfB .framer-nmzv98 > :last-child { margin-bottom: 0px; } .framer-4kBfB .framer-zc2tfg > *, .framer-4kBfB .framer-1u7ory6 > *, .framer-4kBfB .framer-12qfrzd > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-4kBfB .framer-1itmnlg > * { margin: 0px; margin-bottom: calc(5px / 2); margin-top: calc(5px / 2); } .framer-4kBfB .framer-1r5fquh > *, .framer-4kBfB .framer-m6wzej > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-4kBfB .framer-yimur9 > * { margin: 0px; margin-left: calc(50px / 2); margin-right: calc(50px / 2); } .framer-4kBfB .framer-yimur9 > :first-child, .framer-4kBfB .framer-zqsogw > :first-child, .framer-4kBfB .framer-148cze9 > :first-child, .framer-4kBfB .framer-1ygvuwz > :first-child { margin-left: 0px; } .framer-4kBfB .framer-yimur9 > :last-child, .framer-4kBfB .framer-zqsogw > :last-child, .framer-4kBfB .framer-148cze9 > :last-child, .framer-4kBfB .framer-1ygvuwz > :last-child { margin-right: 0px; } .framer-4kBfB .framer-1pxzj3t > * { margin: 0px; margin-bottom: calc(90px / 2); margin-top: calc(90px / 2); } .framer-4kBfB .framer-1tkhtyu > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-4kBfB .framer-zqsogw > *, .framer-4kBfB .framer-1ygvuwz > * { margin: 0px; margin-left: calc(20px / 2); margin-right: calc(20px / 2); } .framer-4kBfB .framer-1g6k6hl > *, .framer-4kBfB .framer-1h8elkr > *, .framer-4kBfB .framer-q9p97 > *, .framer-4kBfB .framer-hrs2co > *, .framer-4kBfB .framer-1b5yy9s > *, .framer-4kBfB .framer-s215s > *, .framer-4kBfB .framer-yyimdj > *, .framer-4kBfB .framer-1nwu40o > *, .framer-4kBfB .framer-1y4va38 > * { margin: 0px; margin-bottom: calc(8px / 2); margin-top: calc(8px / 2); } .framer-4kBfB .framer-to3z9c > *, .framer-4kBfB .framer-1ti9dip > *, .framer-4kBfB .framer-8x516c > *, .framer-4kBfB .framer-gknktf > *, .framer-4kBfB .framer-q21cng > *, .framer-4kBfB .framer-1rl229r > *, .framer-4kBfB .framer-6p0kw7 > *, .framer-4kBfB .framer-fxhn4m > *, .framer-4kBfB .framer-ru75sy > * { margin: 0px; margin-bottom: calc(4px / 2); margin-top: calc(4px / 2); } .framer-4kBfB .framer-x1tk5h > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } .framer-4kBfB .framer-nmzv98 > * { margin: 0px; margin-bottom: calc(50px / 2); margin-top: calc(50px / 2); } .framer-4kBfB .framer-148cze9 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }", "@media (min-width: 1280px) and (max-width: 1649px) { .framer-4kBfB .hidden-xxfe1n { display: none !important; } }", `@media (max-width: 809px) { .framer-4kBfB .hidden-z0cqn6 { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-4kBfB .framer-xxfe1n { width: 390px; } .framer-4kBfB .framer-1qdpz30-container { order: 0; } .framer-4kBfB .framer-4by23b { order: 1; } .framer-4kBfB .framer-zc2tfg { order: 0; padding: 140px 24px 20px 24px; width: min-content; } .framer-4kBfB .framer-1itmnlg, .framer-4kBfB .framer-q0hqlm { width: 342px; } .framer-4kBfB .framer-lf16sh, .framer-4kBfB .framer-yt9mvi, .framer-4kBfB .framer-kaz680 { white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; } .framer-4kBfB .framer-1hzn9h8-container { height: 114px; width: 342px; } .framer-4kBfB .framer-1r5fquh { flex-direction: row; order: 2; } .framer-4kBfB .framer-m6wzej { gap: 40px; height: min-content; order: 2; padding: 100px 24px 100px 24px; } .framer-4kBfB .framer-akophw, .framer-4kBfB .framer-yimur9, .framer-4kBfB .framer-1n47f2b-container, .framer-4kBfB .framer-n6jesi-container, .framer-4kBfB .framer-1bbknoc-container { width: 100%; } .framer-4kBfB .framer-1pxzj3t { gap: 50px; order: 3; padding: 100px 24px 180px 24px; } .framer-4kBfB .framer-zqsogw { flex-direction: column; gap: 30px; width: 100%; } .framer-4kBfB .framer-12qfrzd { order: 4; padding: 200px 24px 150px 24px; } .framer-4kBfB .framer-x1tk5h { padding: 0px 24px 40px 20px; width: 100%; } .framer-4kBfB .framer-1v5tyn { --framer-font-size: 34px; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; width: 100%; } .framer-4kBfB .framer-nmzv98 { order: 5; padding: 125px 24px 59px 24px; } .framer-4kBfB .framer-1ygvuwz { flex-direction: column; width: 100%; } .framer-4kBfB .framer-143sc3s-container { order: 6; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-4kBfB .framer-1r5fquh, .framer-4kBfB .framer-m6wzej, .framer-4kBfB .framer-1pxzj3t, .framer-4kBfB .framer-zqsogw, .framer-4kBfB .framer-1ygvuwz { gap: 0px; } .framer-4kBfB .framer-1r5fquh > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-4kBfB .framer-1r5fquh > :first-child { margin-left: 0px; } .framer-4kBfB .framer-1r5fquh > :last-child { margin-right: 0px; } .framer-4kBfB .framer-m6wzej > * { margin: 0px; margin-bottom: calc(40px / 2); margin-top: calc(40px / 2); } .framer-4kBfB .framer-m6wzej > :first-child, .framer-4kBfB .framer-1pxzj3t > :first-child, .framer-4kBfB .framer-zqsogw > :first-child, .framer-4kBfB .framer-1ygvuwz > :first-child { margin-top: 0px; } .framer-4kBfB .framer-m6wzej > :last-child, .framer-4kBfB .framer-1pxzj3t > :last-child, .framer-4kBfB .framer-zqsogw > :last-child, .framer-4kBfB .framer-1ygvuwz > :last-child { margin-bottom: 0px; } .framer-4kBfB .framer-1pxzj3t > * { margin: 0px; margin-bottom: calc(50px / 2); margin-top: calc(50px / 2); } .framer-4kBfB .framer-zqsogw > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-4kBfB .framer-1ygvuwz > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } }}`, `@media (min-width: 810px) and (max-width: 1279px) { .framer-4kBfB .hidden-t4h2ks { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-4kBfB .framer-xxfe1n { width: 810px; } .framer-4kBfB .framer-1qdpz30-container { order: 0; } .framer-4kBfB .framer-4by23b { order: 1; } .framer-4kBfB .framer-zc2tfg { order: 0; padding: 80px 60px 20px 60px; } .framer-4kBfB .framer-q0hqlm { white-space: pre; width: auto; } .framer-4kBfB .framer-ble6ye { aspect-ratio: 3.9130434782608696 / 1; height: var(--framer-aspect-ratio-supported, 414px); left: calc(50.00000000000002% - 1620px / 2); max-height: 35%; order: 1; width: 1620px; } .framer-4kBfB .framer-m6wzej { gap: 30px; height: min-content; order: 2; padding: 100px 60px 100px 60px; } .framer-4kBfB .framer-akophw, .framer-4kBfB .framer-yimur9, .framer-4kBfB .framer-1ygvuwz { width: 100%; } .framer-4kBfB .framer-1pxzj3t { gap: 50px; order: 3; padding: 107px 60px 107px 60px; } .framer-4kBfB .framer-yt9mvi, .framer-4kBfB .framer-kaz680 { white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; } .framer-4kBfB .framer-zqsogw { gap: 30px; width: 100%; } .framer-4kBfB .framer-12qfrzd { order: 4; padding: 200px 60px 150px 60px; } .framer-4kBfB .framer-x1tk5h { padding: 0px 24px 40px 20px; width: 100%; } .framer-4kBfB .framer-1v5tyn { --framer-font-size: 34px; --framer-letter-spacing: -0.8px; --framer-line-height: 1.2em; width: 100%; } .framer-4kBfB .framer-nmzv98 { order: 5; padding: 105px 60px 33px 60px; } .framer-4kBfB .framer-143sc3s-container { order: 6; } @supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-4kBfB .framer-m6wzej, .framer-4kBfB .framer-1pxzj3t, .framer-4kBfB .framer-zqsogw { gap: 0px; } .framer-4kBfB .framer-m6wzej > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-4kBfB .framer-m6wzej > :first-child, .framer-4kBfB .framer-1pxzj3t > :first-child { margin-top: 0px; } .framer-4kBfB .framer-m6wzej > :last-child, .framer-4kBfB .framer-1pxzj3t > :last-child { margin-bottom: 0px; } .framer-4kBfB .framer-1pxzj3t > * { margin: 0px; margin-bottom: calc(50px / 2); margin-top: calc(50px / 2); } .framer-4kBfB .framer-zqsogw > * { margin: 0px; margin-left: calc(30px / 2); margin-right: calc(30px / 2); } .framer-4kBfB .framer-zqsogw > :first-child { margin-left: 0px; } .framer-4kBfB .framer-zqsogw > :last-child { margin-right: 0px; } }}`, `@media (min-width: 1920px) { .framer-4kBfB .hidden-11im4pt { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-4kBfB .framer-xxfe1n { width: 1920px; } .framer-4kBfB .framer-1qdpz30-container, .framer-4kBfB .framer-zc2tfg { order: 0; } .framer-4kBfB .framer-4by23b { order: 1; } .framer-4kBfB .framer-ble6ye { aspect-ratio: 4 / 1; height: var(--framer-aspect-ratio-supported, 480px); max-height: 100%; order: 1; } .framer-4kBfB .framer-m6wzej { order: 2; } .framer-4kBfB .framer-1pxzj3t { order: 3; } .framer-4kBfB .framer-12qfrzd { order: 4; } .framer-4kBfB .framer-nmzv98 { order: 5; } .framer-4kBfB .framer-143sc3s-container { order: 6; }}`, `@media (min-width: 1650px) and (max-width: 1919px) { .framer-4kBfB .hidden-tidzzc { display: none !important; } .${metadata.bodyClassName} { background: white; } .framer-4kBfB .framer-xxfe1n { width: 1650px; } .framer-4kBfB .framer-ble6ye { aspect-ratio: 3.9192399049881237 / 1; height: var(--framer-aspect-ratio-supported, 421px); max-height: 45%; }}`]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerIntrinsicHeight 4550.5
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerIntrinsicWidth 1280
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"pSYmZKfbO":{"layout":["fixed","auto"]},"NTyq1hRL8":{"layout":["fixed","auto"]},"ndrGg8iNq":{"layout":["fixed","auto"]},"AXlhJB8sW":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         */
const FramerQXYyFVKW8 = withCSS(Component, css, "framer-4kBfB");
export default FramerQXYyFVKW8;
FramerQXYyFVKW8.displayName = "Group Classes";
FramerQXYyFVKW8.defaultProps = {
  height: 4550.5,
  width: 1280
};
addFonts(FramerQXYyFVKW8, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/QXYyFVKW8:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/QXYyFVKW8:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/QXYyFVKW8:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, ...HeaderNavigationFonts, ...FormSparkFonts, ...FAQStackFonts, ...AssetsLogoFonts, ...AssetsButtonMainFonts, ...FooterNewFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerQXYyFVKW8",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "4550.5",
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "1280",
        "framerResponsiveScreen": "",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"pSYmZKfbO\":{\"layout\":[\"fixed\",\"auto\"]},\"NTyq1hRL8\":{\"layout\":[\"fixed\",\"auto\"]},\"ndrGg8iNq\":{\"layout\":[\"fixed\",\"auto\"]},\"AXlhJB8sW\":{\"layout\":[\"fixed\",\"auto\"]}}}"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};