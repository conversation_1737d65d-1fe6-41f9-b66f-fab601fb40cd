// Generated by <PERSON><PERSON><PERSON> (f6797a0)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, RichText, SVG, Text, useActiveVariantCallback, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const cycleOrder = ["mP6Uk6KLc", "DBi28tdlr", "npjyeAwW4", "lciTG5SyQ", "NjZMl6qGc", "cI4qCwTUS", "p5az3EAOD", "kcwkX_93e", "TDKX03PC0", "KP0Nn4KBY", "H3AxSKURN", "fj4N9m2ex", "U1Zzy3sUR", "p3sZBctIS", "wWJrUritw", "PeW4YDj<PERSON>", "k4t1JhpNv", "vAJa31dne", "Rl13oSms6", "W4Ph5o9ri", "YNxda2hxn", "B2K0BxN_A", "zlLOcEGNu", "oouo9Kw59", "RyRZSaOaD", "T1nDZqrXi", "nrWBJlIE2", "NMSDpNUX8", "WBIgMLM0W", "eAJk7qogp", "WbjBf2ur5", "NFhYMdmcQ", "Smi_CQgRS", "EStJZWZse", "COIcFGgNo", "G2MN8fkmQ", "HPTJD0Rv9", "OqjNtM0yZ", "H4edc_XhS", "McS8uAhgX", "RVUUVvMba", "G4G5pf7f6", "qDO1yGoPe", "B74KceKeh", "eaRTV3HIf", "XcWv6aoQT", "Xy_ubmDwR", "PJNW1ipBG", "PA5JYpjvF", "zuQ6Zt_Fe", "RHYp4nkhp", "j7eUa_b6M", "qaIyC4wwa", "rW1WUIUEr", "gKVnIhHry", "A55XVbgoX", "oaW21IMXv", "q_5c2DzRU", "KABOPEvu8", "AW5V70MRF", "i8cHpCK9w", "CbKtCXQ3c", "Od32uvUF9", "asOsoMiMY", "ZkojTxbnD", "FmvzcLpN0", "kPnSUhY7l", "PCAgL4Uwz", "HX4O89hRP", "Rp2uDkvZW", "yNbTadh9B", "bh18hddZb", "PkFyJhg3t", "tzTxbNo2j", "gL4XzKRyu", "kifH89zFT"];
const variantClassNames = {
  A55XVbgoX: "framer-v-zu1i8i",
  asOsoMiMY: "framer-v-1y89zao",
  AW5V70MRF: "framer-v-n55on0",
  B2K0BxN_A: "framer-v-2cxk5b",
  B74KceKeh: "framer-v-1tjs4jj",
  bh18hddZb: "framer-v-1lwqlgn",
  CbKtCXQ3c: "framer-v-pt4iyt",
  cI4qCwTUS: "framer-v-1aotjkh",
  COIcFGgNo: "framer-v-kxfgiv",
  DBi28tdlr: "framer-v-1pwcf4u",
  eAJk7qogp: "framer-v-10qo6kh",
  eaRTV3HIf: "framer-v-74kgcc",
  EStJZWZse: "framer-v-ss5vja",
  fj4N9m2ex: "framer-v-3o1ys4",
  FmvzcLpN0: "framer-v-12l304f",
  G2MN8fkmQ: "framer-v-1q807a1",
  G4G5pf7f6: "framer-v-1qygpzp",
  gKVnIhHry: "framer-v-m2c7zi",
  gL4XzKRyu: "framer-v-9mfkra",
  H3AxSKURN: "framer-v-16woqmx",
  H4edc_XhS: "framer-v-114o6bm",
  HPTJD0Rv9: "framer-v-a4t95j",
  HX4O89hRP: "framer-v-1hlk05t",
  i8cHpCK9w: "framer-v-pe0mib",
  j7eUa_b6M: "framer-v-1vyc3ha",
  k4t1JhpNv: "framer-v-1an1t3",
  KABOPEvu8: "framer-v-1ftwmiz",
  kcwkX_93e: "framer-v-3hgee",
  kifH89zFT: "framer-v-1g8kld5",
  KP0Nn4KBY: "framer-v-1k5sum",
  kPnSUhY7l: "framer-v-1jgyqgu",
  lciTG5SyQ: "framer-v-1w9hzn5",
  McS8uAhgX: "framer-v-w9y7sk",
  mP6Uk6KLc: "framer-v-1bbqg76",
  NFhYMdmcQ: "framer-v-1ntth35",
  NjZMl6qGc: "framer-v-15xwdbb",
  NMSDpNUX8: "framer-v-z3a7s7",
  npjyeAwW4: "framer-v-146rjha",
  nrWBJlIE2: "framer-v-p098dc",
  oaW21IMXv: "framer-v-1kjzvy7",
  Od32uvUF9: "framer-v-i95l16",
  oouo9Kw59: "framer-v-1tfcokz",
  OqjNtM0yZ: "framer-v-1ys87g1",
  p3sZBctIS: "framer-v-1nrx05c",
  p5az3EAOD: "framer-v-111wq93",
  PA5JYpjvF: "framer-v-dvte5c",
  PCAgL4Uwz: "framer-v-llduxw",
  PeW4YDjMI: "framer-v-x19ee9",
  PJNW1ipBG: "framer-v-1ml9a5v",
  PkFyJhg3t: "framer-v-1mhpln2",
  q_5c2DzRU: "framer-v-1at6szb",
  qaIyC4wwa: "framer-v-12l1484",
  qDO1yGoPe: "framer-v-18h6r7n",
  RHYp4nkhp: "framer-v-1ii2fxs",
  Rl13oSms6: "framer-v-1k988nh",
  Rp2uDkvZW: "framer-v-azw17c",
  RVUUVvMba: "framer-v-u6r1gh",
  rW1WUIUEr: "framer-v-1c4jtpn",
  RyRZSaOaD: "framer-v-3p29jg",
  Smi_CQgRS: "framer-v-fzf5ow",
  T1nDZqrXi: "framer-v-100cs2o",
  TDKX03PC0: "framer-v-hwph2m",
  tzTxbNo2j: "framer-v-zbha0v",
  U1Zzy3sUR: "framer-v-rgjav",
  vAJa31dne: "framer-v-qts5kf",
  W4Ph5o9ri: "framer-v-125mnc2",
  WBIgMLM0W: "framer-v-1w70yuc",
  WbjBf2ur5: "framer-v-va76t0",
  wWJrUritw: "framer-v-nxnihh",
  XcWv6aoQT: "framer-v-127hjxv",
  Xy_ubmDwR: "framer-v-r0yplu",
  yNbTadh9B: "framer-v-yiulq1",
  YNxda2hxn: "framer-v-65af76",
  ZkojTxbnD: "framer-v-738qch",
  zlLOcEGNu: "framer-v-bncuwh",
  zuQ6Zt_Fe: "framer-v-ufi401"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Accessibility - Q5 - Closed D - v2": "tzTxbNo2j",
  "Accessibility - Q5 - Closed M - v2": "kifH89zFT",
  "Accessibility - Q5 - Open D - v2": "PkFyJhg3t",
  "Accessibility - Q5 - Open M - v2": "gL4XzKRyu",
  "Closed - Desktop - Early Access": "cI4qCwTUS",
  "Closed - Desktop - Payments": "p3sZBctIS",
  "FAQ - General 1 D - C": "EStJZWZse",
  "FAQ - General 1 D - O": "Smi_CQgRS",
  "FAQ - General 1 M - C": "B74KceKeh",
  "FAQ - General 1 M - O": "qDO1yGoPe",
  "FAQ - General 2 D - C": "G2MN8fkmQ",
  "FAQ - General 2 D - O": "COIcFGgNo",
  "FAQ - General 2 M - C": "XcWv6aoQT",
  "FAQ - General 2 M - O": "eaRTV3HIf",
  "FAQ - Getting Started D - C": "OqjNtM0yZ",
  "FAQ - Getting Started D - O": "HPTJD0Rv9",
  "FAQ - Getting Started M - C": "PJNW1ipBG",
  "FAQ - Getting Started M - O": "Xy_ubmDwR",
  "FAQ - Payments 1 D - C": "McS8uAhgX",
  "FAQ - Payments 1 D - O": "H4edc_XhS",
  "FAQ - Payments 1 M - C": "zuQ6Zt_Fe",
  "FAQ - Payments 1 M - O": "PA5JYpjvF",
  "FAQ - Payments 2 D - C": "G4G5pf7f6",
  "FAQ - Payments 2 D - O": "RVUUVvMba",
  "FAQ - Payments 2 M - C": "j7eUa_b6M",
  "FAQ - Payments 2 M - O": "RHYp4nkhp",
  "FAQ - Privacy D - C": "NFhYMdmcQ",
  "FAQ - Privacy D - O": "WbjBf2ur5",
  "FAQ - Privacy M - C": "eAJk7qogp",
  "FAQ - Privacy M - O": "WBIgMLM0W",
  "Group Classes - Q3 - Closed D - v2": "Rp2uDkvZW",
  "Group Classes - Q3 - Closed M - v2": "bh18hddZb",
  "Group Classes - Q3 - Open D - v2": "HX4O89hRP",
  "Group Classes - Q3 - Open M - v2": "yNbTadh9B",
  "Marketplace - Q2 - Closed D - v2": "CbKtCXQ3c",
  "Marketplace - Q2 - Closed M - v2": "asOsoMiMY",
  "Marketplace - Q2 - Open D - v2": "i8cHpCK9w",
  "Marketplace - Q2 - Open M - v2": "Od32uvUF9",
  "Marketplace - Q5 - Closed D - v2": "FmvzcLpN0",
  "Marketplace - Q5 - Closed M - v2": "PCAgL4Uwz",
  "Marketplace - Q5 - Open D - v2": "ZkojTxbnD",
  "Marketplace - Q5 - Open M - v2": "kPnSUhY7l",
  "Medium - Desktop - Closed - Payments 2": "NMSDpNUX8",
  "Medium - Desktop - Closed - Payments": "T1nDZqrXi",
  "Medium - Desktop - Closed - Register": "vAJa31dne",
  "Medium - Desktop - Closed": "KP0Nn4KBY",
  "Medium - Desktop - Opened - Payments 2": "nrWBJlIE2",
  "Medium - Desktop - Opened - Payments": "RyRZSaOaD",
  "Medium - Desktop - Opened - Register": "k4t1JhpNv",
  "Medium - Desktop - Opened": "TDKX03PC0",
  "Medium - Mobile - Closed - Payments 2": "oouo9Kw59",
  "Medium - Mobile - Closed - Payments": "B2K0BxN_A",
  "Medium - Mobile - Closed - Register": "W4Ph5o9ri",
  "Medium - Mobile - Closed": "fj4N9m2ex",
  "Medium - Mobile - Open - Payments 2": "zlLOcEGNu",
  "Medium - Mobile - Open - Payments": "YNxda2hxn",
  "Medium - Mobile - Open - Register": "Rl13oSms6",
  "Medium - Mobile - Open": "H3AxSKURN",
  "Mobile Closed - Early Access": "kcwkX_93e",
  "Mobile Closed - Payments": "PeW4YDjMI",
  "Mobile Closed": "lciTG5SyQ",
  "Mobile Open - Early Access": "p5az3EAOD",
  "Mobile Open - Payments": "wWJrUritw",
  "Mobile Open": "npjyeAwW4",
  "Open - Desktop - Early Access": "NjZMl6qGc",
  "Open - Desktop - Payments": "U1Zzy3sUR",
  "Tutoring - Q2 - Closed D - v2": "rW1WUIUEr",
  "Tutoring - Q2 - Closed M - v2": "q_5c2DzRU",
  "Tutoring - Q2 - Open D - v2": "qaIyC4wwa",
  "Tutoring - Q2 - Open M - v2": "oaW21IMXv",
  "Tutoring - Q3 - Closed D - v2": "A55XVbgoX",
  "Tutoring - Q3 - Closed M - v2": "AW5V70MRF",
  "Tutoring - Q3 - Open D - v2": "gKVnIhHry",
  "Tutoring - Q3 - Open M - v2": "KABOPEvu8",
  Closed: "DBi28tdlr",
  Open: "mP6Uk6KLc"
};
const transitions = {
  DBi28tdlr: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  },
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "mP6Uk6KLc",
  title: gc3ZLybTk = "Title here",
  body: Vfd0fVZpI = "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
  tap: mruQcQHaJ,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "mP6Uk6KLc",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onTap19wovrf = activeVariantCallback(async (...args) => {
    if (mruQcQHaJ) {
      const res = await mruQcQHaJ(...args);
      if (res === false) return false;
    }
  });
  const onTap1hae7jc = activeVariantCallback(async (...args) => {
    setVariant("DBi28tdlr");
  });
  const onTapebosm2 = activeVariantCallback(async (...args) => {
    setVariant("mP6Uk6KLc");
  });
  const onTapf5jt3q = activeVariantCallback(async (...args) => {
    setVariant("lciTG5SyQ");
  });
  const onTapjh3ur = activeVariantCallback(async (...args) => {
    setVariant("npjyeAwW4");
  });
  const onTap1r0lytt = activeVariantCallback(async (...args) => {
    setVariant("cI4qCwTUS");
  });
  const onTapqoe0hu = activeVariantCallback(async (...args) => {
    setVariant("NjZMl6qGc");
  });
  const onTapgbnpgn = activeVariantCallback(async (...args) => {
    setVariant("kcwkX_93e");
  });
  const onTap1rmzeqq = activeVariantCallback(async (...args) => {
    setVariant("p5az3EAOD");
  });
  const onTapsm0im7 = activeVariantCallback(async (...args) => {
    setVariant("KP0Nn4KBY");
  });
  const onTap1ibzzjl = activeVariantCallback(async (...args) => {
    setVariant("TDKX03PC0");
  });
  const onTap1vcxykp = activeVariantCallback(async (...args) => {
    setVariant("fj4N9m2ex");
  });
  const onTap11utsk8 = activeVariantCallback(async (...args) => {
    setVariant("H3AxSKURN");
  });
  const onTap19e1vo5 = activeVariantCallback(async (...args) => {
    setVariant("p3sZBctIS");
  });
  const onTap12d4479 = activeVariantCallback(async (...args) => {
    setVariant("U1Zzy3sUR");
  });
  const onTap1kbwbf6 = activeVariantCallback(async (...args) => {
    setVariant("PeW4YDjMI");
  });
  const onTapkt2vv7 = activeVariantCallback(async (...args) => {
    setVariant("wWJrUritw");
  });
  const onTap1ts4q1n = activeVariantCallback(async (...args) => {
    setVariant("vAJa31dne");
  });
  const onTap1u6egex = activeVariantCallback(async (...args) => {
    setVariant("k4t1JhpNv");
  });
  const onTap1h9c1bn = activeVariantCallback(async (...args) => {
    setVariant("W4Ph5o9ri");
  });
  const onTap1s8y4gx = activeVariantCallback(async (...args) => {
    setVariant("Rl13oSms6");
  });
  const onTap18eob5z = activeVariantCallback(async (...args) => {
    setVariant("B2K0BxN_A");
  });
  const onTap14hkm0q = activeVariantCallback(async (...args) => {
    setVariant("YNxda2hxn");
  });
  const onTap1pdtscr = activeVariantCallback(async (...args) => {
    setVariant("oouo9Kw59");
  });
  const onTap19zog62 = activeVariantCallback(async (...args) => {
    setVariant("zlLOcEGNu");
  });
  const onTap19s65t0 = activeVariantCallback(async (...args) => {
    setVariant("T1nDZqrXi");
  });
  const onTap15indix = activeVariantCallback(async (...args) => {
    setVariant("RyRZSaOaD");
  });
  const onTapktwlsu = activeVariantCallback(async (...args) => {
    setVariant("NMSDpNUX8");
  });
  const onTap1be43ef = activeVariantCallback(async (...args) => {
    setVariant("nrWBJlIE2");
  });
  const onTapsu1jwe = activeVariantCallback(async (...args) => {
    setVariant("eAJk7qogp");
  });
  const onTap13si4gm = activeVariantCallback(async (...args) => {
    setVariant("WBIgMLM0W");
  });
  const onTapk7kmig = activeVariantCallback(async (...args) => {
    setVariant("NFhYMdmcQ");
  });
  const onTap1twev2y = activeVariantCallback(async (...args) => {
    setVariant("WbjBf2ur5");
  });
  const onTap1b3tamj = activeVariantCallback(async (...args) => {
    setVariant("EStJZWZse");
  });
  const onTap4gqpm8 = activeVariantCallback(async (...args) => {
    setVariant("Smi_CQgRS");
  });
  const onTap1gbj0sz = activeVariantCallback(async (...args) => {
    setVariant("G2MN8fkmQ");
  });
  const onTap1xpeshe = activeVariantCallback(async (...args) => {
    setVariant("COIcFGgNo");
  });
  const onTapaywcle = activeVariantCallback(async (...args) => {
    setVariant("OqjNtM0yZ");
  });
  const onTapuo5u2q = activeVariantCallback(async (...args) => {
    setVariant("HPTJD0Rv9");
  });
  const onTapt6nohn = activeVariantCallback(async (...args) => {
    setVariant("McS8uAhgX");
  });
  const onTap1m9fo1 = activeVariantCallback(async (...args) => {
    setVariant("H4edc_XhS");
  });
  const onTapoihzad = activeVariantCallback(async (...args) => {
    setVariant("G4G5pf7f6");
  });
  const onTaprucwyr = activeVariantCallback(async (...args) => {
    setVariant("RVUUVvMba");
  });
  const onTap1jlppdv = activeVariantCallback(async (...args) => {
    setVariant("B74KceKeh");
  });
  const onTapjjphf9 = activeVariantCallback(async (...args) => {
    setVariant("qDO1yGoPe");
  });
  const onTap1u77now = activeVariantCallback(async (...args) => {
    setVariant("XcWv6aoQT");
  });
  const onTap10t56p2 = activeVariantCallback(async (...args) => {
    setVariant("eaRTV3HIf");
  });
  const onTap1vrp28v = activeVariantCallback(async (...args) => {
    setVariant("PJNW1ipBG");
  });
  const onTap1f4nlfc = activeVariantCallback(async (...args) => {
    setVariant("Xy_ubmDwR");
  });
  const onTap1qv17km = activeVariantCallback(async (...args) => {
    setVariant("zuQ6Zt_Fe");
  });
  const onTap1c4jkpk = activeVariantCallback(async (...args) => {
    setVariant("PA5JYpjvF");
  });
  const onTap1grxdwz = activeVariantCallback(async (...args) => {
    setVariant("j7eUa_b6M");
  });
  const onTap1oujws6 = activeVariantCallback(async (...args) => {
    setVariant("RHYp4nkhp");
  });
  const onTap1we8pcq = activeVariantCallback(async (...args) => {
    setVariant("rW1WUIUEr");
  });
  const onTap106rbu7 = activeVariantCallback(async (...args) => {
    setVariant("qaIyC4wwa");
  });
  const onTap12644l2 = activeVariantCallback(async (...args) => {
    setVariant("A55XVbgoX");
  });
  const onTapr2v8zo = activeVariantCallback(async (...args) => {
    setVariant("gKVnIhHry");
  });
  const onTap1y09ldf = activeVariantCallback(async (...args) => {
    setVariant("q_5c2DzRU");
  });
  const onTap1uolbh5 = activeVariantCallback(async (...args) => {
    setVariant("oaW21IMXv");
  });
  const onTap14itdqi = activeVariantCallback(async (...args) => {
    setVariant("AW5V70MRF");
  });
  const onTap15gg5d5 = activeVariantCallback(async (...args) => {
    setVariant("KABOPEvu8");
  });
  const onTap1a3nvk1 = activeVariantCallback(async (...args) => {
    setVariant("CbKtCXQ3c");
  });
  const onTap9zyyki = activeVariantCallback(async (...args) => {
    setVariant("i8cHpCK9w");
  });
  const onTap1lc85qm = activeVariantCallback(async (...args) => {
    setVariant("asOsoMiMY");
  });
  const onTap1cgqcj5 = activeVariantCallback(async (...args) => {
    setVariant("Od32uvUF9");
  });
  const onTap3ffibg = activeVariantCallback(async (...args) => {
    setVariant("FmvzcLpN0");
  });
  const onTap8sesu3 = activeVariantCallback(async (...args) => {
    setVariant("ZkojTxbnD");
  });
  const onTap1ar4xwg = activeVariantCallback(async (...args) => {
    setVariant("PCAgL4Uwz");
  });
  const onTap1sbvdb0 = activeVariantCallback(async (...args) => {
    setVariant("kPnSUhY7l");
  });
  const onTapig5dxz = activeVariantCallback(async (...args) => {
    setVariant("Rp2uDkvZW");
  });
  const onTap1md31ua = activeVariantCallback(async (...args) => {
    setVariant("HX4O89hRP");
  });
  const onTap1bbfkez = activeVariantCallback(async (...args) => {
    setVariant("bh18hddZb");
  });
  const onTap1qjdel0 = activeVariantCallback(async (...args) => {
    setVariant("yNbTadh9B");
  });
  const onTaphauhm2 = activeVariantCallback(async (...args) => {
    setVariant("tzTxbNo2j");
  });
  const onTap1v3wiuq = activeVariantCallback(async (...args) => {
    setVariant("PkFyJhg3t");
  });
  const onTaprkj54f = activeVariantCallback(async (...args) => {
    setVariant("kifH89zFT");
  });
  const onTap4imdy2 = activeVariantCallback(async (...args) => {
    setVariant("gL4XzKRyu");
  });
  const isDisplayed = () => {
    if (["DBi28tdlr", "lciTG5SyQ", "cI4qCwTUS", "kcwkX_93e", "KP0Nn4KBY", "fj4N9m2ex", "p3sZBctIS", "PeW4YDjMI", "vAJa31dne", "W4Ph5o9ri", "B2K0BxN_A", "oouo9Kw59", "T1nDZqrXi", "NMSDpNUX8", "eAJk7qogp", "NFhYMdmcQ", "EStJZWZse", "G2MN8fkmQ", "OqjNtM0yZ", "McS8uAhgX", "G4G5pf7f6", "B74KceKeh", "XcWv6aoQT", "PJNW1ipBG", "zuQ6Zt_Fe", "j7eUa_b6M", "rW1WUIUEr", "A55XVbgoX", "q_5c2DzRU", "AW5V70MRF", "CbKtCXQ3c", "asOsoMiMY", "FmvzcLpN0", "PCAgL4Uwz", "Rp2uDkvZW", "bh18hddZb", "tzTxbNo2j", "kifH89zFT"].includes(baseVariant)) return false;
    return true;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-v2yJr", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsxs(motion.div, {
        ...restProps,
        className: cx("framer-1bbqg76", className),
        "data-framer-name": "Open",
        "data-highlight": true,
        layoutDependency: layoutDependency,
        layoutId: "mP6Uk6KLc",
        onTap: onTap19wovrf,
        ref: ref,
        style: {
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          A55XVbgoX: {
            "data-framer-name": "Tutoring - Q3 - Closed D - v2"
          },
          asOsoMiMY: {
            "data-framer-name": "Marketplace - Q2 - Closed M - v2"
          },
          AW5V70MRF: {
            "data-framer-name": "Tutoring - Q3 - Closed M - v2"
          },
          B2K0BxN_A: {
            "data-framer-name": "Medium - Mobile - Closed - Payments"
          },
          B74KceKeh: {
            "data-framer-name": "FAQ - General 1 M - C"
          },
          bh18hddZb: {
            "data-framer-name": "Group Classes - Q3 - Closed M - v2"
          },
          CbKtCXQ3c: {
            "data-framer-name": "Marketplace - Q2 - Closed D - v2"
          },
          cI4qCwTUS: {
            "data-framer-name": "Closed - Desktop - Early Access"
          },
          COIcFGgNo: {
            "data-framer-name": "FAQ - General 2 D - O"
          },
          DBi28tdlr: {
            "data-framer-name": "Closed"
          },
          eAJk7qogp: {
            "data-framer-name": "FAQ - Privacy M - C"
          },
          eaRTV3HIf: {
            "data-framer-name": "FAQ - General 2 M - O"
          },
          EStJZWZse: {
            "data-framer-name": "FAQ - General 1 D - C"
          },
          fj4N9m2ex: {
            "data-framer-name": "Medium - Mobile - Closed"
          },
          FmvzcLpN0: {
            "data-framer-name": "Marketplace - Q5 - Closed D - v2"
          },
          G2MN8fkmQ: {
            "data-framer-name": "FAQ - General 2 D - C"
          },
          G4G5pf7f6: {
            "data-framer-name": "FAQ - Payments 2 D - C"
          },
          gKVnIhHry: {
            "data-framer-name": "Tutoring - Q3 - Open D - v2"
          },
          gL4XzKRyu: {
            "data-framer-name": "Accessibility - Q5 - Open M - v2"
          },
          H3AxSKURN: {
            "data-framer-name": "Medium - Mobile - Open"
          },
          H4edc_XhS: {
            "data-framer-name": "FAQ - Payments 1 D - O"
          },
          HPTJD0Rv9: {
            "data-framer-name": "FAQ - Getting Started D - O"
          },
          HX4O89hRP: {
            "data-framer-name": "Group Classes - Q3 - Open D - v2"
          },
          i8cHpCK9w: {
            "data-framer-name": "Marketplace - Q2 - Open D - v2"
          },
          j7eUa_b6M: {
            "data-framer-name": "FAQ - Payments 2 M - C"
          },
          k4t1JhpNv: {
            "data-framer-name": "Medium - Desktop - Opened - Register"
          },
          KABOPEvu8: {
            "data-framer-name": "Tutoring - Q3 - Open M - v2"
          },
          kcwkX_93e: {
            "data-framer-name": "Mobile Closed - Early Access"
          },
          kifH89zFT: {
            "data-framer-name": "Accessibility - Q5 - Closed M - v2"
          },
          KP0Nn4KBY: {
            "data-framer-name": "Medium - Desktop - Closed"
          },
          kPnSUhY7l: {
            "data-framer-name": "Marketplace - Q5 - Open M - v2"
          },
          lciTG5SyQ: {
            "data-framer-name": "Mobile Closed"
          },
          McS8uAhgX: {
            "data-framer-name": "FAQ - Payments 1 D - C"
          },
          NFhYMdmcQ: {
            "data-framer-name": "FAQ - Privacy D - C"
          },
          NjZMl6qGc: {
            "data-framer-name": "Open - Desktop - Early Access"
          },
          NMSDpNUX8: {
            "data-framer-name": "Medium - Desktop - Closed - Payments 2"
          },
          npjyeAwW4: {
            "data-framer-name": "Mobile Open"
          },
          nrWBJlIE2: {
            "data-framer-name": "Medium - Desktop - Opened - Payments 2"
          },
          oaW21IMXv: {
            "data-framer-name": "Tutoring - Q2 - Open M - v2"
          },
          Od32uvUF9: {
            "data-framer-name": "Marketplace - Q2 - Open M - v2"
          },
          oouo9Kw59: {
            "data-framer-name": "Medium - Mobile - Closed - Payments 2"
          },
          OqjNtM0yZ: {
            "data-framer-name": "FAQ - Getting Started D - C"
          },
          p3sZBctIS: {
            "data-framer-name": "Closed - Desktop - Payments"
          },
          p5az3EAOD: {
            "data-framer-name": "Mobile Open - Early Access"
          },
          PA5JYpjvF: {
            "data-framer-name": "FAQ - Payments 1 M - O"
          },
          PCAgL4Uwz: {
            "data-framer-name": "Marketplace - Q5 - Closed M - v2"
          },
          PeW4YDjMI: {
            "data-framer-name": "Mobile Closed - Payments"
          },
          PJNW1ipBG: {
            "data-framer-name": "FAQ - Getting Started M - C"
          },
          PkFyJhg3t: {
            "data-framer-name": "Accessibility - Q5 - Open D - v2"
          },
          q_5c2DzRU: {
            "data-framer-name": "Tutoring - Q2 - Closed M - v2"
          },
          qaIyC4wwa: {
            "data-framer-name": "Tutoring - Q2 - Open D - v2"
          },
          qDO1yGoPe: {
            "data-framer-name": "FAQ - General 1 M - O"
          },
          RHYp4nkhp: {
            "data-framer-name": "FAQ - Payments 2 M - O"
          },
          Rl13oSms6: {
            "data-framer-name": "Medium - Mobile - Open - Register"
          },
          Rp2uDkvZW: {
            "data-framer-name": "Group Classes - Q3 - Closed D - v2"
          },
          RVUUVvMba: {
            "data-framer-name": "FAQ - Payments 2 D - O"
          },
          rW1WUIUEr: {
            "data-framer-name": "Tutoring - Q2 - Closed D - v2"
          },
          RyRZSaOaD: {
            "data-framer-name": "Medium - Desktop - Opened - Payments"
          },
          Smi_CQgRS: {
            "data-framer-name": "FAQ - General 1 D - O"
          },
          T1nDZqrXi: {
            "data-framer-name": "Medium - Desktop - Closed - Payments"
          },
          TDKX03PC0: {
            "data-framer-name": "Medium - Desktop - Opened"
          },
          tzTxbNo2j: {
            "data-framer-name": "Accessibility - Q5 - Closed D - v2"
          },
          U1Zzy3sUR: {
            "data-framer-name": "Open - Desktop - Payments"
          },
          vAJa31dne: {
            "data-framer-name": "Medium - Desktop - Closed - Register"
          },
          W4Ph5o9ri: {
            "data-framer-name": "Medium - Mobile - Closed - Register"
          },
          WBIgMLM0W: {
            "data-framer-name": "FAQ - Privacy M - O"
          },
          WbjBf2ur5: {
            "data-framer-name": "FAQ - Privacy D - O"
          },
          wWJrUritw: {
            "data-framer-name": "Mobile Open - Payments"
          },
          XcWv6aoQT: {
            "data-framer-name": "FAQ - General 2 M - C"
          },
          Xy_ubmDwR: {
            "data-framer-name": "FAQ - Getting Started M - O"
          },
          yNbTadh9B: {
            "data-framer-name": "Group Classes - Q3 - Open M - v2"
          },
          YNxda2hxn: {
            "data-framer-name": "Medium - Mobile - Open - Payments"
          },
          ZkojTxbnD: {
            "data-framer-name": "Marketplace - Q5 - Open D - v2"
          },
          zlLOcEGNu: {
            "data-framer-name": "Medium - Mobile - Open - Payments 2"
          },
          zuQ6Zt_Fe: {
            "data-framer-name": "FAQ - Payments 1 M - C"
          }
        }, baseVariant, gestureVariant),
        children: [/*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1owhqhr",
          "data-framer-name": "Title",
          "data-highlight": true,
          layoutDependency: layoutDependency,
          layoutId: "GYWTobVhZ",
          onTap: onTap1hae7jc,
          transition: transition,
          ...addPropertyOverrides({
            A55XVbgoX: {
              onTap: onTapr2v8zo
            },
            asOsoMiMY: {
              onTap: onTap1cgqcj5
            },
            AW5V70MRF: {
              onTap: onTap15gg5d5
            },
            B2K0BxN_A: {
              onTap: onTap14hkm0q
            },
            B74KceKeh: {
              onTap: onTapjjphf9
            },
            bh18hddZb: {
              onTap: onTap1qjdel0
            },
            CbKtCXQ3c: {
              onTap: onTap9zyyki
            },
            cI4qCwTUS: {
              onTap: onTapqoe0hu
            },
            COIcFGgNo: {
              onTap: onTap1gbj0sz
            },
            DBi28tdlr: {
              onTap: onTapebosm2
            },
            eAJk7qogp: {
              onTap: onTap13si4gm
            },
            eaRTV3HIf: {
              onTap: onTap1u77now
            },
            EStJZWZse: {
              onTap: onTap4gqpm8
            },
            fj4N9m2ex: {
              onTap: onTap11utsk8
            },
            FmvzcLpN0: {
              onTap: onTap8sesu3
            },
            G2MN8fkmQ: {
              onTap: onTap1xpeshe
            },
            G4G5pf7f6: {
              onTap: onTaprucwyr
            },
            gKVnIhHry: {
              onTap: onTap12644l2
            },
            gL4XzKRyu: {
              onTap: onTaprkj54f
            },
            H3AxSKURN: {
              onTap: onTap1vcxykp
            },
            H4edc_XhS: {
              onTap: onTapt6nohn
            },
            HPTJD0Rv9: {
              onTap: onTapaywcle
            },
            HX4O89hRP: {
              onTap: onTapig5dxz
            },
            i8cHpCK9w: {
              onTap: onTap1a3nvk1
            },
            j7eUa_b6M: {
              onTap: onTap1oujws6
            },
            k4t1JhpNv: {
              onTap: onTap1ts4q1n
            },
            KABOPEvu8: {
              onTap: onTap14itdqi
            },
            kcwkX_93e: {
              onTap: onTap1rmzeqq
            },
            kifH89zFT: {
              onTap: onTap4imdy2
            },
            KP0Nn4KBY: {
              onTap: onTap1ibzzjl
            },
            kPnSUhY7l: {
              onTap: onTap1ar4xwg
            },
            lciTG5SyQ: {
              onTap: onTapjh3ur
            },
            McS8uAhgX: {
              onTap: onTap1m9fo1
            },
            NFhYMdmcQ: {
              onTap: onTap1twev2y
            },
            NjZMl6qGc: {
              onTap: onTap1r0lytt
            },
            NMSDpNUX8: {
              onTap: onTap1be43ef
            },
            npjyeAwW4: {
              onTap: onTapf5jt3q
            },
            nrWBJlIE2: {
              onTap: onTapktwlsu
            },
            oaW21IMXv: {
              onTap: onTap1y09ldf
            },
            Od32uvUF9: {
              onTap: onTap1lc85qm
            },
            oouo9Kw59: {
              onTap: onTap19zog62
            },
            OqjNtM0yZ: {
              onTap: onTapuo5u2q
            },
            p3sZBctIS: {
              onTap: onTap12d4479
            },
            p5az3EAOD: {
              onTap: onTapgbnpgn
            },
            PA5JYpjvF: {
              onTap: onTap1qv17km
            },
            PCAgL4Uwz: {
              onTap: onTap1sbvdb0
            },
            PeW4YDjMI: {
              onTap: onTapkt2vv7
            },
            PJNW1ipBG: {
              onTap: onTap1f4nlfc
            },
            PkFyJhg3t: {
              onTap: onTaphauhm2
            },
            q_5c2DzRU: {
              onTap: onTap1uolbh5
            },
            qaIyC4wwa: {
              onTap: onTap1we8pcq
            },
            qDO1yGoPe: {
              onTap: onTap1jlppdv
            },
            RHYp4nkhp: {
              onTap: onTap1grxdwz
            },
            Rl13oSms6: {
              onTap: onTap1h9c1bn
            },
            Rp2uDkvZW: {
              onTap: onTap1md31ua
            },
            RVUUVvMba: {
              onTap: onTapoihzad
            },
            rW1WUIUEr: {
              onTap: onTap106rbu7
            },
            RyRZSaOaD: {
              onTap: onTap19s65t0
            },
            Smi_CQgRS: {
              onTap: onTap1b3tamj
            },
            T1nDZqrXi: {
              onTap: onTap15indix
            },
            TDKX03PC0: {
              onTap: onTapsm0im7
            },
            tzTxbNo2j: {
              onTap: onTap1v3wiuq
            },
            U1Zzy3sUR: {
              onTap: onTap19e1vo5
            },
            vAJa31dne: {
              onTap: onTap1u6egex
            },
            W4Ph5o9ri: {
              onTap: onTap1s8y4gx
            },
            WBIgMLM0W: {
              onTap: onTapsu1jwe
            },
            WbjBf2ur5: {
              onTap: onTapk7kmig
            },
            wWJrUritw: {
              onTap: onTap1kbwbf6
            },
            XcWv6aoQT: {
              onTap: onTap10t56p2
            },
            Xy_ubmDwR: {
              onTap: onTap1vrp28v
            },
            yNbTadh9B: {
              onTap: onTap1bbfkez
            },
            YNxda2hxn: {
              onTap: onTap18eob5z
            },
            ZkojTxbnD: {
              onTap: onTap3ffibg
            },
            zlLOcEGNu: {
              onTap: onTap1pdtscr
            },
            zuQ6Zt_Fe: {
              onTap: onTap1c4jkpk
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(Text, {
            __fromCanvasComponent: true,
            className: "framer-1y30chy",
            fonts: ["Inter-Bold"],
            layoutDependency: layoutDependency,
            layoutId: "kB0hGWWVB",
            rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Title here</span><br></span></span>",
            style: {
              "--framer-font-family": '"Inter-Bold", "Inter", sans-serif',
              "--framer-font-size": "20px",
              "--framer-font-style": "normal",
              "--framer-font-weight": 700,
              "--framer-letter-spacing": "-0.8px",
              "--framer-line-height": "1.2em",
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-text-alignment": "start",
              "--framer-text-color": "rgb(0, 0, 0)",
              "--framer-text-decoration": "none",
              "--framer-text-transform": "none"
            },
            text: gc3ZLybTk,
            transition: transition,
            variants: {
              A55XVbgoX: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              asOsoMiMY: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              AW5V70MRF: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              B2K0BxN_A: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              B74KceKeh: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              bh18hddZb: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              CbKtCXQ3c: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              COIcFGgNo: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              eAJk7qogp: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              eaRTV3HIf: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              EStJZWZse: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              fj4N9m2ex: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              FmvzcLpN0: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              G2MN8fkmQ: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              G4G5pf7f6: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              gKVnIhHry: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              gL4XzKRyu: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              H3AxSKURN: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              H4edc_XhS: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              HPTJD0Rv9: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              HX4O89hRP: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              i8cHpCK9w: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              j7eUa_b6M: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              k4t1JhpNv: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              KABOPEvu8: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              kcwkX_93e: {
                "--framer-font-size": "18px"
              },
              kifH89zFT: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              KP0Nn4KBY: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              kPnSUhY7l: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              lciTG5SyQ: {
                "--framer-font-size": "18px"
              },
              McS8uAhgX: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              NFhYMdmcQ: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              NMSDpNUX8: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              npjyeAwW4: {
                "--framer-font-size": "18px"
              },
              nrWBJlIE2: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              oaW21IMXv: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              Od32uvUF9: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              oouo9Kw59: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              OqjNtM0yZ: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              p5az3EAOD: {
                "--framer-font-size": "18px"
              },
              PA5JYpjvF: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              PCAgL4Uwz: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              PeW4YDjMI: {
                "--framer-font-size": "18px"
              },
              PJNW1ipBG: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              PkFyJhg3t: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              q_5c2DzRU: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              qaIyC4wwa: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              qDO1yGoPe: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              RHYp4nkhp: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              Rl13oSms6: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              Rp2uDkvZW: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              RVUUVvMba: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              rW1WUIUEr: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              RyRZSaOaD: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              Smi_CQgRS: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              T1nDZqrXi: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              TDKX03PC0: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              tzTxbNo2j: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              vAJa31dne: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              W4Ph5o9ri: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              WBIgMLM0W: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              WbjBf2ur5: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              wWJrUritw: {
                "--framer-font-size": "18px"
              },
              XcWv6aoQT: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              Xy_ubmDwR: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              yNbTadh9B: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              YNxda2hxn: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              ZkojTxbnD: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-weight": 500
              },
              zlLOcEGNu: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              },
              zuQ6Zt_Fe: {
                "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                "--framer-font-size": "18px",
                "--framer-font-weight": 500
              }
            },
            verticalAlignment: "top",
            withExternalLayout: true,
            ...addPropertyOverrides({
              A55XVbgoX: {
                fonts: ["Inter-Medium"]
              },
              asOsoMiMY: {
                fonts: ["Inter-Medium"]
              },
              AW5V70MRF: {
                fonts: ["Inter-Medium"]
              },
              B2K0BxN_A: {
                fonts: ["Inter-Medium"]
              },
              B74KceKeh: {
                fonts: ["Inter-Medium"]
              },
              bh18hddZb: {
                fonts: ["Inter-Medium"]
              },
              CbKtCXQ3c: {
                fonts: ["Inter-Medium"]
              },
              COIcFGgNo: {
                fonts: ["Inter-Medium"]
              },
              eAJk7qogp: {
                fonts: ["Inter-Medium"]
              },
              eaRTV3HIf: {
                fonts: ["Inter-Medium"]
              },
              EStJZWZse: {
                fonts: ["Inter-Medium"]
              },
              fj4N9m2ex: {
                fonts: ["Inter-Medium"]
              },
              FmvzcLpN0: {
                fonts: ["Inter-Medium"]
              },
              G2MN8fkmQ: {
                fonts: ["Inter-Medium"]
              },
              G4G5pf7f6: {
                fonts: ["Inter-Medium"]
              },
              gKVnIhHry: {
                fonts: ["Inter-Medium"]
              },
              gL4XzKRyu: {
                fonts: ["Inter-Medium"]
              },
              H3AxSKURN: {
                fonts: ["Inter-Medium"]
              },
              H4edc_XhS: {
                fonts: ["Inter-Medium"]
              },
              HPTJD0Rv9: {
                fonts: ["Inter-Medium"]
              },
              HX4O89hRP: {
                fonts: ["Inter-Medium"]
              },
              i8cHpCK9w: {
                fonts: ["Inter-Medium"]
              },
              j7eUa_b6M: {
                fonts: ["Inter-Medium"]
              },
              k4t1JhpNv: {
                fonts: ["Inter-Medium"]
              },
              KABOPEvu8: {
                fonts: ["Inter-Medium"]
              },
              kifH89zFT: {
                fonts: ["Inter-Medium"]
              },
              KP0Nn4KBY: {
                fonts: ["Inter-Medium"]
              },
              kPnSUhY7l: {
                fonts: ["Inter-Medium"]
              },
              McS8uAhgX: {
                fonts: ["Inter-Medium"]
              },
              NFhYMdmcQ: {
                fonts: ["Inter-Medium"]
              },
              NMSDpNUX8: {
                fonts: ["Inter-Medium"]
              },
              nrWBJlIE2: {
                fonts: ["Inter-Medium"]
              },
              oaW21IMXv: {
                fonts: ["Inter-Medium"]
              },
              Od32uvUF9: {
                fonts: ["Inter-Medium"]
              },
              oouo9Kw59: {
                fonts: ["Inter-Medium"]
              },
              OqjNtM0yZ: {
                fonts: ["Inter-Medium"]
              },
              PA5JYpjvF: {
                fonts: ["Inter-Medium"]
              },
              PCAgL4Uwz: {
                fonts: ["Inter-Medium"]
              },
              PJNW1ipBG: {
                fonts: ["Inter-Medium"]
              },
              PkFyJhg3t: {
                fonts: ["Inter-Medium"]
              },
              q_5c2DzRU: {
                fonts: ["Inter-Medium"]
              },
              qaIyC4wwa: {
                fonts: ["Inter-Medium"]
              },
              qDO1yGoPe: {
                fonts: ["Inter-Medium"]
              },
              RHYp4nkhp: {
                fonts: ["Inter-Medium"]
              },
              Rl13oSms6: {
                fonts: ["Inter-Medium"]
              },
              Rp2uDkvZW: {
                fonts: ["Inter-Medium"]
              },
              RVUUVvMba: {
                fonts: ["Inter-Medium"]
              },
              rW1WUIUEr: {
                fonts: ["Inter-Medium"]
              },
              RyRZSaOaD: {
                fonts: ["Inter-Medium"]
              },
              Smi_CQgRS: {
                fonts: ["Inter-Medium"]
              },
              T1nDZqrXi: {
                fonts: ["Inter-Medium"]
              },
              TDKX03PC0: {
                fonts: ["Inter-Medium"]
              },
              tzTxbNo2j: {
                fonts: ["Inter-Medium"]
              },
              vAJa31dne: {
                fonts: ["Inter-Medium"]
              },
              W4Ph5o9ri: {
                fonts: ["Inter-Medium"]
              },
              WBIgMLM0W: {
                fonts: ["Inter-Medium"]
              },
              WbjBf2ur5: {
                fonts: ["Inter-Medium"]
              },
              XcWv6aoQT: {
                fonts: ["Inter-Medium"]
              },
              Xy_ubmDwR: {
                fonts: ["Inter-Medium"]
              },
              yNbTadh9B: {
                fonts: ["Inter-Medium"]
              },
              YNxda2hxn: {
                fonts: ["Inter-Medium"]
              },
              ZkojTxbnD: {
                fonts: ["Inter-Medium"]
              },
              zlLOcEGNu: {
                fonts: ["Inter-Medium"]
              },
              zuQ6Zt_Fe: {
                fonts: ["Inter-Medium"]
              }
            }, baseVariant, gestureVariant)
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-zuog2c",
            layoutDependency: layoutDependency,
            layoutId: "nZh4Wpk_T",
            transition: transition,
            variants: {
              A55XVbgoX: {
                rotate: 180
              },
              CbKtCXQ3c: {
                rotate: 180
              },
              cI4qCwTUS: {
                rotate: 180
              },
              eAJk7qogp: {
                rotate: 180
              },
              EStJZWZse: {
                rotate: 180
              },
              FmvzcLpN0: {
                rotate: 180
              },
              G2MN8fkmQ: {
                rotate: 180
              },
              G4G5pf7f6: {
                rotate: 180
              },
              kcwkX_93e: {
                rotate: 180
              },
              KP0Nn4KBY: {
                rotate: 180
              },
              McS8uAhgX: {
                rotate: 180
              },
              NFhYMdmcQ: {
                rotate: 180
              },
              NMSDpNUX8: {
                rotate: 180
              },
              OqjNtM0yZ: {
                rotate: 180
              },
              p3sZBctIS: {
                rotate: 180
              },
              PeW4YDjMI: {
                rotate: 180
              },
              Rp2uDkvZW: {
                rotate: 180
              },
              rW1WUIUEr: {
                rotate: 180
              },
              T1nDZqrXi: {
                rotate: 180
              },
              tzTxbNo2j: {
                rotate: 180
              },
              vAJa31dne: {
                rotate: 180
              }
            },
            children: /*#__PURE__*/_jsx(motion.div, {
              className: "framer-99gyaw",
              "data-framer-name": "Chevron",
              layoutDependency: layoutDependency,
              layoutId: "Gm3PIaA0o",
              transition: transition,
              variants: {
                asOsoMiMY: {
                  rotate: 180
                },
                AW5V70MRF: {
                  rotate: 180
                },
                B2K0BxN_A: {
                  rotate: 180
                },
                B74KceKeh: {
                  rotate: 180
                },
                bh18hddZb: {
                  rotate: 180
                },
                DBi28tdlr: {
                  rotate: 180
                },
                fj4N9m2ex: {
                  rotate: 180
                },
                j7eUa_b6M: {
                  rotate: 180
                },
                kifH89zFT: {
                  rotate: 180
                },
                lciTG5SyQ: {
                  rotate: 180
                },
                oouo9Kw59: {
                  rotate: 180
                },
                PCAgL4Uwz: {
                  rotate: 180
                },
                PJNW1ipBG: {
                  rotate: 180
                },
                q_5c2DzRU: {
                  rotate: 180
                },
                W4Ph5o9ri: {
                  rotate: 180
                },
                XcWv6aoQT: {
                  rotate: 180
                },
                zuQ6Zt_Fe: {
                  rotate: 180
                }
              },
              children: /*#__PURE__*/_jsx(SVG, {
                className: "framer-14ycicj",
                layout: "position",
                layoutDependency: layoutDependency,
                layoutId: "FWEyl2xwv",
                opacity: 1,
                radius: 0,
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 19 12"><path d="M 1.226 1.839 L 9.5 10.697 L 17.774 1.839" fill="transparent" stroke-width="2.26" stroke="var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168)) /* {&quot;name&quot;:&quot;Medium Gray&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
                svgContentId: 2378593587,
                transition: transition,
                withExternalLayout: true,
                ...addPropertyOverrides({
                  DBi28tdlr: {
                    svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 19 12"><path d="M 1.226 0.839 L 9.5 9.697 L 17.774 0.839" fill="transparent" stroke-width="2.26" stroke="var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168)) /* {&quot;name&quot;:&quot;Medium Gray&quot;} */" stroke-linecap="round" stroke-linejoin="round"></path></svg>',
                    svgContentId: 273687419
                  }
                }, baseVariant, gestureVariant)
              })
            })
          })]
        }), isDisplayed() && /*#__PURE__*/_jsx(motion.div, {
          className: "framer-1rqpg6j",
          "data-framer-name": "Body",
          layoutDependency: layoutDependency,
          layoutId: "PKTDLK1qF",
          transition: transition,
          children: /*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--framer-line-height": "1.6em",
                  "--framer-text-alignment": "start"
                },
                children: /*#__PURE__*/_jsx(motion.span, {
                  style: {
                    "--font-selector": "SW50ZXI=",
                    "--framer-font-family": '"Inter", sans-serif',
                    "--framer-font-size": "16px",
                    "--framer-font-style": "normal",
                    "--framer-font-weight": "400",
                    "--framer-letter-spacing": "0px",
                    "--framer-text-color": "var(--extracted-1w3ko1f)",
                    "--framer-text-decoration": "none",
                    "--framer-text-transform": "none"
                  },
                  children: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here."
                })
              })
            }),
            className: "framer-o4yh5n",
            fonts: ["Inter"],
            layoutDependency: layoutDependency,
            layoutId: "XDEQrPWTe",
            style: {
              "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-paragraph-spacing": "0px"
            },
            text: Vfd0fVZpI,
            transition: transition,
            variants: {
              COIcFGgNo: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              eaRTV3HIf: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              gKVnIhHry: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              gL4XzKRyu: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              H4edc_XhS: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              HPTJD0Rv9: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              HX4O89hRP: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              i8cHpCK9w: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              k4t1JhpNv: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              KABOPEvu8: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              kPnSUhY7l: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              NjZMl6qGc: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-csatvk": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-dfbufw": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              nrWBJlIE2: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              oaW21IMXv: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              Od32uvUF9: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              p5az3EAOD: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-csatvk": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-dfbufw": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              PA5JYpjvF: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              PkFyJhg3t: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              qaIyC4wwa: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              qDO1yGoPe: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              RHYp4nkhp: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              Rl13oSms6: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              RVUUVvMba: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              RyRZSaOaD: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              Smi_CQgRS: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              U1Zzy3sUR: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              WBIgMLM0W: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              WbjBf2ur5: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              wWJrUritw: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              Xy_ubmDwR: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              yNbTadh9B: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              YNxda2hxn: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              },
              ZkojTxbnD: {
                "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) "
              },
              zlLOcEGNu: {
                "--extracted-c9yw3e": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--extracted-zp3slp": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                "--framer-link-text-color": "var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, rgb(3, 104, 224))",
                "--framer-link-text-decoration": "none"
              }
            },
            verticalAlignment: "top",
            withExternalLayout: true,
            ...addPropertyOverrides({
              COIcFGgNo: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              eaRTV3HIf: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              gKVnIhHry: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              gL4XzKRyu: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors may provide more accessible features than they think. They can edit their profile, and confirm each accessibility feature they offer via a written description to ", /*#__PURE__*/_jsx(Link, {
                      href: "mailto:<EMAIL>",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "mailto:<EMAIL>",
                        rel: "noreferrer noopener",
                        children: "<EMAIL>"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              H4edc_XhS: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors can securely receive payments from over 45 countries in 120 currencies across the world. When students book time with a tutor, they will be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              HPTJD0Rv9: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              HX4O89hRP: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Students can pay for classes in over 120 currencies. When students enroll in a class, there will be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), " during checkout."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              i8cHpCK9w: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors can securely receive payments from over 45 countries in over 120 currencies. When students book time with a tutor, they’ll be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), " during checkout."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              k4t1JhpNv: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Invites are rolling out daily. To become a host, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/to/chvbrCM3",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/to/chvbrCM3",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "request early access using this form link"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              KABOPEvu8: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              kPnSUhY7l: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              NjZMl6qGc: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Join a community of learnings bringing the world together in a whole new way. To get early access, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://www.popless.com/request-access/",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://www.popless.com/request-access/",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "fill in this form"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: ". Once we’ve received your details, we’ll reach out with instructions on how to get started. Also, we’re always looking for community advocates to help participate in our we make Popless the best platform for personalized learning. To help shape the future of Popless, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://form.typeform.com/to/ezoF4TrB",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://form.typeform.com/to/ezoF4TrB",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-csatvk)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "get in touch"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-dfbufw)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              nrWBJlIE2: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Our third-party payment processor, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/en-gb-us",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/en-gb-us",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: ", has been audited by a PCI-certified auditor and is certified to PCR service provider level 1. This is the most stringent level of certification available in the payments industry. To accomplish this, Stripe uses best-in-class security tools and practices to maintain a high level of security."
                    })]
                  })
                })
              },
              oaW21IMXv: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Tutors can securely receive payments from over 45 countries in over 120 currencies. When students book time with a tutor, they’ll be charged and earnings will be paid to the tutor instantly after the meeting."
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              Od32uvUF9: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors can securely receive payments from over 45 countries in over 120 currencies. When students book time with a tutor, they’ll be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), " during checkout."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              p5az3EAOD: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Join a community of learnings bringing the world together in a whole new way. To get early access, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://www.popless.com/request-access/",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://www.popless.com/request-access/",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "fill in this form"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: ". Once we’ve received your details, we’ll reach out with instructions on how to get started. Also, we’re always looking for community advocates to help participate in our we make Popless the best platform for personalized learning. To help shape the future of Popless, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://form.typeform.com/to/ezoF4TrB",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://form.typeform.com/to/ezoF4TrB",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-csatvk)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "get in touch"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-dfbufw)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              PA5JYpjvF: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors can securely receive payments from over 45 countries in 120 currencies across the world. When students book time with a tutor, they will be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              PkFyJhg3t: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors may provide more accessible features than they think. They can edit their profile, and confirm each accessibility feature they offer via a written description to ", /*#__PURE__*/_jsx(Link, {
                      href: "mailto:<EMAIL>",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "mailto:<EMAIL>",
                        rel: "noreferrer noopener",
                        children: "<EMAIL>"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              qaIyC4wwa: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Tutors can securely receive payments from over 45 countries in over 120 currencies. When students book time with a tutor, they’ll be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), " during checkout."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              qDO1yGoPe: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "What makes Popless unique is our focus on building the most simple and flexible product for tutors and mentors. Existing tutoring tools charge expensive subscription fees while tutoring marketplaces charge excessive booking fees -\xa0 between 20% and 40% for each tutoring session! Instead, Popless: 1) charges a flat 5% fee to students for each tutoring session, 2) helps tutors manage conversations, payments, scheduling, reporting, and more in one place, 3) allows tutors to work with people they already do through the "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://www.popless.com/refer",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://www.popless.com/refer",
                        rel: "noreferrer noopener",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--framer-text-color": "var(--extracted-zp3slp)"
                          },
                          children: "Refer rogram"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-c9yw3e)"
                      },
                      children: "."
                    })]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              RHYp4nkhp: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Our third-party payment processor, ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), ", has been audited by a PCI-certified auditor and is certified to PCR service provider Level 1. This is the most stringent level of certification available in the payments industry. To accomplish this, Stripe uses best-in-class security tools and practices to maintain a high level of security."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              Rl13oSms6: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Invites are rolling out daily. To become a host, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/to/chvbrCM3",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/to/chvbrCM3",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "request early access using this form link"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              RVUUVvMba: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Our third-party payment processor, ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), ", has been audited by a PCI-certified auditor and is certified to PCR service provider Level 1. This is the most stringent level of certification available in the payments industry. To accomplish this, Stripe uses best-in-class security tools and practices to maintain a high level of security."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              RyRZSaOaD: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Securely receive payments from over 45 countries in 120 currencies across the world. We also plan to support cryptocurrencies payments such as Bitcoin, Ethereum, USDC, and Solana. When people schedule time with you, they will be charged a 3% payment processing fee by "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              Smi_CQgRS: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "What makes Popless unique is our focus on building the most simple and flexible product for tutors and mentors. Existing tutoring tools charge expensive subscription fees while tutoring marketplaces charge excessive booking fees -\xa0 between 20% and 40% for each tutoring session! Instead, Popless: 1) charges a flat 5% fee to students for each tutoring session, 2) helps tutors manage conversations, payments, scheduling, reporting, and more in one place, 3) allows tutors to work with people they already do through the "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://www.popless.com/refer",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://www.popless.com/refer",
                        rel: "noreferrer noopener",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--framer-text-color": "var(--extracted-zp3slp)"
                          },
                          children: "refer program"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-c9yw3e)"
                      },
                      children: "."
                    })]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              U1Zzy3sUR: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Tutors and mentors can securely receive payments from over 45 countries in 120 currencies across the world. When students book time with a tutor or mentor, they will be charged a 3% payment processing fee by "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--framer-text-color": "var(--extracted-zp3slp)"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-c9yw3e)"
                      },
                      children: "."
                    })]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              WBIgMLM0W: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Popless only collects data needed to enable website functionality. Prioritizing the privacy of our customers means protecting the data you trust us with. We will never sell your data and have integrated the strongest security technologies using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://cloud.google.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://cloud.google.com/",
                        rel: "noreferrer noopener",
                        children: "Google’s Cloud Platform"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              WbjBf2ur5: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Popless only collects data needed to enable website functionality. Prioritizing the privacy of our customers means protecting the data you trust us with. We will never sell your data and have integrated the strongest security technologies using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://cloud.google.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://cloud.google.com/",
                        rel: "noreferrer noopener",
                        children: "Google’s Cloud Platform"
                      })
                    }), "."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              wWJrUritw: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Tutors and mentors can securely receive payments from over 45 countries in 120 currencies across the world. When students book time with a tutor or mentor, they will be charged a 3% payment processing fee by "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--framer-text-color": "var(--extracted-zp3slp)"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--framer-text-color": "var(--extracted-c9yw3e)"
                      },
                      children: "."
                    })]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              Xy_ubmDwR: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              yNbTadh9B: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Students can pay for classes in over 120 currencies. When students enroll in a class, there will be charged a 3% payment processing fee by ", /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        children: "Stripe"
                      })
                    }), " during checkout."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              YNxda2hxn: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Securely receive payments from over 45 countries in 120 currencies across the world. We also plan to support cryptocurrencies payments such as Bitcoin, Ethereum, USDC, and Solana. When people schedule time with you, they will be charged a 3% payment processing fee by "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "."
                    })]
                  })
                })
              },
              ZkojTxbnD: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: ["Less than 5 minutes from start to finish. Get started as a tutor using ", /*#__PURE__*/_jsx(Link, {
                      href: "https://popless.typeform.com/request-access",
                      openInNewTab: false,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://popless.typeform.com/request-access",
                        rel: "noreferrer noopener",
                        children: "this form link"
                      })
                    }), ". We ask people to sign-up using a form so we can ensure you have a smooth onboarding and have the help you need."]
                  })
                }),
                fonts: undefined,
                text: undefined
              },
              zlLOcEGNu: {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsxs(motion.p, {
                    style: {
                      "--framer-line-height": "1.6em",
                      "--framer-text-alignment": "start"
                    },
                    children: [/*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: "Our third-party payment processor, "
                    }), /*#__PURE__*/_jsx(Link, {
                      href: "https://stripe.com/en-gb-us",
                      openInNewTab: true,
                      smoothScroll: false,
                      children: /*#__PURE__*/_jsx(motion.a, {
                        href: "https://stripe.com/en-gb-us",
                        rel: "noreferrer noopener",
                        target: "_blank",
                        children: /*#__PURE__*/_jsx(motion.span, {
                          style: {
                            "--font-selector": "SW50ZXI=",
                            "--framer-font-family": '"Inter", sans-serif',
                            "--framer-font-size": "16px",
                            "--framer-font-style": "normal",
                            "--framer-font-weight": "400",
                            "--framer-letter-spacing": "0px",
                            "--framer-text-color": "var(--extracted-zp3slp)",
                            "--framer-text-decoration": "none",
                            "--framer-text-transform": "none"
                          },
                          children: "Stripe"
                        })
                      })
                    }), /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "SW50ZXI=",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "16px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "400",
                        "--framer-letter-spacing": "0px",
                        "--framer-text-color": "var(--extracted-c9yw3e)",
                        "--framer-text-decoration": "none",
                        "--framer-text-transform": "none"
                      },
                      children: ", has been audited by a PCI-certified auditor and is certified to PCR service provider level 1. This is the most stringent level of certification available in the payments industry. To accomplish this, Stripe uses best-in-class security tools and practices to maintain a high level of security."
                    })]
                  })
                })
              }
            }, baseVariant, gestureVariant)
          })
        })]
      })
    })
  });
});
const css = ['.framer-v2yJr [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-v2yJr * { box-sizing: border-box; }", ".framer-v2yJr .framer-13xt28l { display: block; }", ".framer-v2yJr .framer-1bbqg76 { align-content: flex-start; align-items: flex-start; cursor: pointer; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 500px; }", ".framer-v2yJr .framer-1owhqhr { align-content: center; align-items: center; cursor: pointer; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 24px 0px 24px 0px; position: relative; width: 100%; }", ".framer-v2yJr .framer-1y30chy { -webkit-user-select: none; align-self: stretch; flex: 1 0 0px; height: auto; overflow: hidden; position: relative; user-select: none; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", ".framer-v2yJr .framer-zuog2c { flex: none; height: 24px; overflow: visible; position: relative; width: 24px; }", ".framer-v2yJr .framer-99gyaw { aspect-ratio: 1 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 20px); left: calc(50.00000000000002% - 20px / 2); overflow: hidden; position: absolute; top: calc(50.00000000000002% - 20px / 2); width: 20px; }", ".framer-v2yJr .framer-14ycicj { flex: none; height: 12px; left: calc(50.00000000000002% - 19px / 2); position: absolute; top: calc(50.00000000000002% - 12px / 2); width: 19px; }", ".framer-v2yJr .framer-1rqpg6j { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 0px 0px 40px 0px; position: relative; width: 100%; }", ".framer-v2yJr .framer-o4yh5n { flex: 1 0 0px; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 1px; word-break: break-word; word-wrap: break-word; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-v2yJr .framer-1bbqg76, .framer-v2yJr .framer-1owhqhr { gap: 0px; } .framer-v2yJr .framer-1bbqg76 > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-v2yJr .framer-1bbqg76 > :first-child { margin-top: 0px; } .framer-v2yJr .framer-1bbqg76 > :last-child { margin-bottom: 0px; } .framer-v2yJr .framer-1owhqhr > * { margin: 0px; margin-left: calc(0px / 2); margin-right: calc(0px / 2); } .framer-v2yJr .framer-1owhqhr > :first-child { margin-left: 0px; } .framer-v2yJr .framer-1owhqhr > :last-child { margin-right: 0px; } }", ".framer-v2yJr.framer-v-1pwcf4u .framer-14ycicj { bottom: 3px; left: 0px; top: unset; }", ".framer-v2yJr.framer-v-146rjha .framer-1owhqhr, .framer-v2yJr.framer-v-1w9hzn5 .framer-1owhqhr, .framer-v2yJr.framer-v-111wq93 .framer-1owhqhr, .framer-v2yJr.framer-v-3hgee .framer-1owhqhr, .framer-v2yJr.framer-v-16woqmx .framer-1owhqhr, .framer-v2yJr.framer-v-3o1ys4 .framer-1owhqhr, .framer-v2yJr.framer-v-nxnihh .framer-1owhqhr, .framer-v2yJr.framer-v-x19ee9 .framer-1owhqhr, .framer-v2yJr.framer-v-1k988nh .framer-1owhqhr, .framer-v2yJr.framer-v-125mnc2 .framer-1owhqhr, .framer-v2yJr.framer-v-65af76 .framer-1owhqhr, .framer-v2yJr.framer-v-2cxk5b .framer-1owhqhr, .framer-v2yJr.framer-v-bncuwh .framer-1owhqhr, .framer-v2yJr.framer-v-1tfcokz .framer-1owhqhr, .framer-v2yJr.framer-v-1w70yuc .framer-1owhqhr, .framer-v2yJr.framer-v-10qo6kh .framer-1owhqhr, .framer-v2yJr.framer-v-18h6r7n .framer-1owhqhr, .framer-v2yJr.framer-v-1tjs4jj .framer-1owhqhr, .framer-v2yJr.framer-v-74kgcc .framer-1owhqhr, .framer-v2yJr.framer-v-127hjxv .framer-1owhqhr, .framer-v2yJr.framer-v-r0yplu .framer-1owhqhr, .framer-v2yJr.framer-v-1ml9a5v .framer-1owhqhr, .framer-v2yJr.framer-v-dvte5c .framer-1owhqhr, .framer-v2yJr.framer-v-ufi401 .framer-1owhqhr, .framer-v2yJr.framer-v-1ii2fxs .framer-1owhqhr, .framer-v2yJr.framer-v-1vyc3ha .framer-1owhqhr, .framer-v2yJr.framer-v-1kjzvy7 .framer-1owhqhr, .framer-v2yJr.framer-v-1at6szb .framer-1owhqhr, .framer-v2yJr.framer-v-1ftwmiz .framer-1owhqhr, .framer-v2yJr.framer-v-n55on0 .framer-1owhqhr, .framer-v2yJr.framer-v-i95l16 .framer-1owhqhr, .framer-v2yJr.framer-v-1y89zao .framer-1owhqhr, .framer-v2yJr.framer-v-1jgyqgu .framer-1owhqhr, .framer-v2yJr.framer-v-llduxw .framer-1owhqhr, .framer-v2yJr.framer-v-yiulq1 .framer-1owhqhr, .framer-v2yJr.framer-v-1lwqlgn .framer-1owhqhr, .framer-v2yJr.framer-v-9mfkra .framer-1owhqhr, .framer-v2yJr.framer-v-1g8kld5 .framer-1owhqhr { padding: 18px 0px 18px 0px; }", ".framer-v2yJr.framer-v-146rjha .framer-99gyaw, .framer-v2yJr.framer-v-1w9hzn5 .framer-99gyaw, .framer-v2yJr.framer-v-16woqmx .framer-99gyaw, .framer-v2yJr.framer-v-3o1ys4 .framer-99gyaw, .framer-v2yJr.framer-v-1k988nh .framer-99gyaw, .framer-v2yJr.framer-v-125mnc2 .framer-99gyaw, .framer-v2yJr.framer-v-65af76 .framer-99gyaw, .framer-v2yJr.framer-v-2cxk5b .framer-99gyaw, .framer-v2yJr.framer-v-bncuwh .framer-99gyaw, .framer-v2yJr.framer-v-1tfcokz .framer-99gyaw, .framer-v2yJr.framer-v-18h6r7n .framer-99gyaw, .framer-v2yJr.framer-v-1tjs4jj .framer-99gyaw, .framer-v2yJr.framer-v-74kgcc .framer-99gyaw, .framer-v2yJr.framer-v-127hjxv .framer-99gyaw, .framer-v2yJr.framer-v-r0yplu .framer-99gyaw, .framer-v2yJr.framer-v-1ml9a5v .framer-99gyaw, .framer-v2yJr.framer-v-dvte5c .framer-99gyaw, .framer-v2yJr.framer-v-ufi401 .framer-99gyaw, .framer-v2yJr.framer-v-1ii2fxs .framer-99gyaw, .framer-v2yJr.framer-v-1vyc3ha .framer-99gyaw, .framer-v2yJr.framer-v-1kjzvy7 .framer-99gyaw, .framer-v2yJr.framer-v-1at6szb .framer-99gyaw, .framer-v2yJr.framer-v-1ftwmiz .framer-99gyaw, .framer-v2yJr.framer-v-n55on0 .framer-99gyaw, .framer-v2yJr.framer-v-i95l16 .framer-99gyaw, .framer-v2yJr.framer-v-1y89zao .framer-99gyaw, .framer-v2yJr.framer-v-1jgyqgu .framer-99gyaw, .framer-v2yJr.framer-v-llduxw .framer-99gyaw, .framer-v2yJr.framer-v-yiulq1 .framer-99gyaw, .framer-v2yJr.framer-v-1lwqlgn .framer-99gyaw, .framer-v2yJr.framer-v-9mfkra .framer-99gyaw, .framer-v2yJr.framer-v-1g8kld5 .framer-99gyaw { height: var(--framer-aspect-ratio-supported, 22px); left: calc(50.00000000000002% - 22px / 2); top: calc(50.00000000000002% - 22px / 2); width: 22px; }", ".framer-v2yJr.framer-v-146rjha .framer-1rqpg6j, .framer-v2yJr.framer-v-16woqmx .framer-1rqpg6j, .framer-v2yJr.framer-v-1k988nh .framer-1rqpg6j, .framer-v2yJr.framer-v-65af76 .framer-1rqpg6j, .framer-v2yJr.framer-v-bncuwh .framer-1rqpg6j, .framer-v2yJr.framer-v-18h6r7n .framer-1rqpg6j, .framer-v2yJr.framer-v-74kgcc .framer-1rqpg6j, .framer-v2yJr.framer-v-r0yplu .framer-1rqpg6j, .framer-v2yJr.framer-v-dvte5c .framer-1rqpg6j, .framer-v2yJr.framer-v-1ii2fxs .framer-1rqpg6j, .framer-v2yJr.framer-v-1kjzvy7 .framer-1rqpg6j, .framer-v2yJr.framer-v-1ftwmiz .framer-1rqpg6j, .framer-v2yJr.framer-v-i95l16 .framer-1rqpg6j, .framer-v2yJr.framer-v-1jgyqgu .framer-1rqpg6j, .framer-v2yJr.framer-v-yiulq1 .framer-1rqpg6j, .framer-v2yJr.framer-v-9mfkra .framer-1rqpg6j { padding: 0px 0px 24px 0px; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerIntrinsicHeight 163
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerIntrinsicWidth 500
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"DBi28tdlr":{"layout":["fixed","auto"]},"npjyeAwW4":{"layout":["fixed","auto"]},"lciTG5SyQ":{"layout":["fixed","auto"]},"NjZMl6qGc":{"layout":["fixed","auto"]},"cI4qCwTUS":{"layout":["fixed","auto"]},"p5az3EAOD":{"layout":["fixed","auto"]},"kcwkX_93e":{"layout":["fixed","auto"]},"TDKX03PC0":{"layout":["fixed","auto"]},"KP0Nn4KBY":{"layout":["fixed","auto"]},"H3AxSKURN":{"layout":["fixed","auto"]},"fj4N9m2ex":{"layout":["fixed","auto"]},"U1Zzy3sUR":{"layout":["fixed","auto"]},"p3sZBctIS":{"layout":["fixed","auto"]},"wWJrUritw":{"layout":["fixed","auto"]},"PeW4YDjMI":{"layout":["fixed","auto"]},"k4t1JhpNv":{"layout":["fixed","auto"]},"vAJa31dne":{"layout":["fixed","auto"]},"Rl13oSms6":{"layout":["fixed","auto"]},"W4Ph5o9ri":{"layout":["fixed","auto"]},"YNxda2hxn":{"layout":["fixed","auto"]},"B2K0BxN_A":{"layout":["fixed","auto"]},"zlLOcEGNu":{"layout":["fixed","auto"]},"oouo9Kw59":{"layout":["fixed","auto"]},"RyRZSaOaD":{"layout":["fixed","auto"]},"T1nDZqrXi":{"layout":["fixed","auto"]},"nrWBJlIE2":{"layout":["fixed","auto"]},"NMSDpNUX8":{"layout":["fixed","auto"]},"WBIgMLM0W":{"layout":["fixed","auto"]},"eAJk7qogp":{"layout":["fixed","auto"]},"WbjBf2ur5":{"layout":["fixed","auto"]},"NFhYMdmcQ":{"layout":["fixed","auto"]},"Smi_CQgRS":{"layout":["fixed","auto"]},"EStJZWZse":{"layout":["fixed","auto"]},"COIcFGgNo":{"layout":["fixed","auto"]},"G2MN8fkmQ":{"layout":["fixed","auto"]},"HPTJD0Rv9":{"layout":["fixed","auto"]},"OqjNtM0yZ":{"layout":["fixed","auto"]},"H4edc_XhS":{"layout":["fixed","auto"]},"McS8uAhgX":{"layout":["fixed","auto"]},"RVUUVvMba":{"layout":["fixed","auto"]},"G4G5pf7f6":{"layout":["fixed","auto"]},"qDO1yGoPe":{"layout":["fixed","auto"]},"B74KceKeh":{"layout":["fixed","auto"]},"eaRTV3HIf":{"layout":["fixed","auto"]},"XcWv6aoQT":{"layout":["fixed","auto"]},"Xy_ubmDwR":{"layout":["fixed","auto"]},"PJNW1ipBG":{"layout":["fixed","auto"]},"PA5JYpjvF":{"layout":["fixed","auto"]},"zuQ6Zt_Fe":{"layout":["fixed","auto"]},"RHYp4nkhp":{"layout":["fixed","auto"]},"j7eUa_b6M":{"layout":["fixed","auto"]},"qaIyC4wwa":{"layout":["fixed","auto"]},"rW1WUIUEr":{"layout":["fixed","auto"]},"gKVnIhHry":{"layout":["fixed","auto"]},"A55XVbgoX":{"layout":["fixed","auto"]},"oaW21IMXv":{"layout":["fixed","auto"]},"q_5c2DzRU":{"layout":["fixed","auto"]},"KABOPEvu8":{"layout":["fixed","auto"]},"AW5V70MRF":{"layout":["fixed","auto"]},"i8cHpCK9w":{"layout":["fixed","auto"]},"CbKtCXQ3c":{"layout":["fixed","auto"]},"Od32uvUF9":{"layout":["fixed","auto"]},"asOsoMiMY":{"layout":["fixed","auto"]},"ZkojTxbnD":{"layout":["fixed","auto"]},"FmvzcLpN0":{"layout":["fixed","auto"]},"kPnSUhY7l":{"layout":["fixed","auto"]},"PCAgL4Uwz":{"layout":["fixed","auto"]},"HX4O89hRP":{"layout":["fixed","auto"]},"Rp2uDkvZW":{"layout":["fixed","auto"]},"yNbTadh9B":{"layout":["fixed","auto"]},"bh18hddZb":{"layout":["fixed","auto"]},"PkFyJhg3t":{"layout":["fixed","auto"]},"tzTxbNo2j":{"layout":["fixed","auto"]},"gL4XzKRyu":{"layout":["fixed","auto"]},"kifH89zFT":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerVariables {"gc3ZLybTk":"title","Vfd0fVZpI":"body","mruQcQHaJ":"tap"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   */
const FramerhRs2gNE0M = withCSS(Component, css);
export default FramerhRs2gNE0M;
FramerhRs2gNE0M.displayName = "FAQ";
FramerhRs2gNE0M.defaultProps = {
  height: 163,
  width: 500
};
addPropertyControls(FramerhRs2gNE0M, {
  variant: {
    options: ["mP6Uk6KLc", "DBi28tdlr", "npjyeAwW4", "lciTG5SyQ", "NjZMl6qGc", "cI4qCwTUS", "p5az3EAOD", "kcwkX_93e", "TDKX03PC0", "KP0Nn4KBY", "H3AxSKURN", "fj4N9m2ex", "U1Zzy3sUR", "p3sZBctIS", "wWJrUritw", "PeW4YDjMI", "k4t1JhpNv", "vAJa31dne", "Rl13oSms6", "W4Ph5o9ri", "YNxda2hxn", "B2K0BxN_A", "zlLOcEGNu", "oouo9Kw59", "RyRZSaOaD", "T1nDZqrXi", "nrWBJlIE2", "NMSDpNUX8", "WBIgMLM0W", "eAJk7qogp", "WbjBf2ur5", "NFhYMdmcQ", "Smi_CQgRS", "EStJZWZse", "COIcFGgNo", "G2MN8fkmQ", "HPTJD0Rv9", "OqjNtM0yZ", "H4edc_XhS", "McS8uAhgX", "RVUUVvMba", "G4G5pf7f6", "qDO1yGoPe", "B74KceKeh", "eaRTV3HIf", "XcWv6aoQT", "Xy_ubmDwR", "PJNW1ipBG", "PA5JYpjvF", "zuQ6Zt_Fe", "RHYp4nkhp", "j7eUa_b6M", "qaIyC4wwa", "rW1WUIUEr", "gKVnIhHry", "A55XVbgoX", "oaW21IMXv", "q_5c2DzRU", "KABOPEvu8", "AW5V70MRF", "i8cHpCK9w", "CbKtCXQ3c", "Od32uvUF9", "asOsoMiMY", "ZkojTxbnD", "FmvzcLpN0", "kPnSUhY7l", "PCAgL4Uwz", "HX4O89hRP", "Rp2uDkvZW", "yNbTadh9B", "bh18hddZb", "PkFyJhg3t", "tzTxbNo2j", "gL4XzKRyu", "kifH89zFT"],
    optionTitles: ["Open", "Closed", "Mobile Open", "Mobile Closed", "Open - Desktop - Early Access", "Closed - Desktop - Early Access", "Mobile Open - Early Access", "Mobile Closed - Early Access", "Medium - Desktop - Opened", "Medium - Desktop - Closed", "Medium - Mobile - Open", "Medium - Mobile - Closed", "Open - Desktop - Payments", "Closed - Desktop - Payments", "Mobile Open - Payments", "Mobile Closed - Payments", "Medium - Desktop - Opened - Register", "Medium - Desktop - Closed - Register", "Medium - Mobile - Open - Register", "Medium - Mobile - Closed - Register", "Medium - Mobile - Open - Payments", "Medium - Mobile - Closed - Payments", "Medium - Mobile - Open - Payments 2", "Medium - Mobile - Closed - Payments 2", "Medium - Desktop - Opened - Payments", "Medium - Desktop - Closed - Payments", "Medium - Desktop - Opened - Payments 2", "Medium - Desktop - Closed - Payments 2", "FAQ - Privacy M - O", "FAQ - Privacy M - C", "FAQ - Privacy D - O", "FAQ - Privacy D - C", "FAQ - General 1 D - O", "FAQ - General 1 D - C", "FAQ - General 2 D - O", "FAQ - General 2 D - C", "FAQ - Getting Started D - O", "FAQ - Getting Started D - C", "FAQ - Payments 1 D - O", "FAQ - Payments 1 D - C", "FAQ - Payments 2 D - O", "FAQ - Payments 2 D - C", "FAQ - General 1 M - O", "FAQ - General 1 M - C", "FAQ - General 2 M - O", "FAQ - General 2 M - C", "FAQ - Getting Started M - O", "FAQ - Getting Started M - C", "FAQ - Payments 1 M - O", "FAQ - Payments 1 M - C", "FAQ - Payments 2 M - O", "FAQ - Payments 2 M - C", "Tutoring - Q2 - Open D - v2", "Tutoring - Q2 - Closed D - v2", "Tutoring - Q3 - Open D - v2", "Tutoring - Q3 - Closed D - v2", "Tutoring - Q2 - Open M - v2", "Tutoring - Q2 - Closed M - v2", "Tutoring - Q3 - Open M - v2", "Tutoring - Q3 - Closed M - v2", "Marketplace - Q2 - Open D - v2", "Marketplace - Q2 - Closed D - v2", "Marketplace - Q2 - Open M - v2", "Marketplace - Q2 - Closed M - v2", "Marketplace - Q5 - Open D - v2", "Marketplace - Q5 - Closed D - v2", "Marketplace - Q5 - Open M - v2", "Marketplace - Q5 - Closed M - v2", "Group Classes - Q3 - Open D - v2", "Group Classes - Q3 - Closed D - v2", "Group Classes - Q3 - Open M - v2", "Group Classes - Q3 - Closed M - v2", "Accessibility - Q5 - Open D - v2", "Accessibility - Q5 - Closed D - v2", "Accessibility - Q5 - Open M - v2", "Accessibility - Q5 - Closed M - v2"],
    title: "Variant",
    type: ControlType.Enum
  },
  gc3ZLybTk: {
    defaultValue: "Title here",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  Vfd0fVZpI: {
    defaultValue: "Body copy here. Body copy here. Body copy here. Body copy here. Body copy here. Body copy here.",
    displayTextArea: true,
    title: "Body",
    type: ControlType.String
  },
  mruQcQHaJ: {
    title: "Tap",
    type: ControlType.EventHandler
  }
});
addFonts(FramerhRs2gNE0M, []);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerhRs2gNE0M",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "163",
        "framerVariables": "{\"gc3ZLybTk\":\"title\",\"Vfd0fVZpI\":\"body\",\"mruQcQHaJ\":\"tap\"}",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"DBi28tdlr\":{\"layout\":[\"fixed\",\"auto\"]},\"npjyeAwW4\":{\"layout\":[\"fixed\",\"auto\"]},\"lciTG5SyQ\":{\"layout\":[\"fixed\",\"auto\"]},\"NjZMl6qGc\":{\"layout\":[\"fixed\",\"auto\"]},\"cI4qCwTUS\":{\"layout\":[\"fixed\",\"auto\"]},\"p5az3EAOD\":{\"layout\":[\"fixed\",\"auto\"]},\"kcwkX_93e\":{\"layout\":[\"fixed\",\"auto\"]},\"TDKX03PC0\":{\"layout\":[\"fixed\",\"auto\"]},\"KP0Nn4KBY\":{\"layout\":[\"fixed\",\"auto\"]},\"H3AxSKURN\":{\"layout\":[\"fixed\",\"auto\"]},\"fj4N9m2ex\":{\"layout\":[\"fixed\",\"auto\"]},\"U1Zzy3sUR\":{\"layout\":[\"fixed\",\"auto\"]},\"p3sZBctIS\":{\"layout\":[\"fixed\",\"auto\"]},\"wWJrUritw\":{\"layout\":[\"fixed\",\"auto\"]},\"PeW4YDjMI\":{\"layout\":[\"fixed\",\"auto\"]},\"k4t1JhpNv\":{\"layout\":[\"fixed\",\"auto\"]},\"vAJa31dne\":{\"layout\":[\"fixed\",\"auto\"]},\"Rl13oSms6\":{\"layout\":[\"fixed\",\"auto\"]},\"W4Ph5o9ri\":{\"layout\":[\"fixed\",\"auto\"]},\"YNxda2hxn\":{\"layout\":[\"fixed\",\"auto\"]},\"B2K0BxN_A\":{\"layout\":[\"fixed\",\"auto\"]},\"zlLOcEGNu\":{\"layout\":[\"fixed\",\"auto\"]},\"oouo9Kw59\":{\"layout\":[\"fixed\",\"auto\"]},\"RyRZSaOaD\":{\"layout\":[\"fixed\",\"auto\"]},\"T1nDZqrXi\":{\"layout\":[\"fixed\",\"auto\"]},\"nrWBJlIE2\":{\"layout\":[\"fixed\",\"auto\"]},\"NMSDpNUX8\":{\"layout\":[\"fixed\",\"auto\"]},\"WBIgMLM0W\":{\"layout\":[\"fixed\",\"auto\"]},\"eAJk7qogp\":{\"layout\":[\"fixed\",\"auto\"]},\"WbjBf2ur5\":{\"layout\":[\"fixed\",\"auto\"]},\"NFhYMdmcQ\":{\"layout\":[\"fixed\",\"auto\"]},\"Smi_CQgRS\":{\"layout\":[\"fixed\",\"auto\"]},\"EStJZWZse\":{\"layout\":[\"fixed\",\"auto\"]},\"COIcFGgNo\":{\"layout\":[\"fixed\",\"auto\"]},\"G2MN8fkmQ\":{\"layout\":[\"fixed\",\"auto\"]},\"HPTJD0Rv9\":{\"layout\":[\"fixed\",\"auto\"]},\"OqjNtM0yZ\":{\"layout\":[\"fixed\",\"auto\"]},\"H4edc_XhS\":{\"layout\":[\"fixed\",\"auto\"]},\"McS8uAhgX\":{\"layout\":[\"fixed\",\"auto\"]},\"RVUUVvMba\":{\"layout\":[\"fixed\",\"auto\"]},\"G4G5pf7f6\":{\"layout\":[\"fixed\",\"auto\"]},\"qDO1yGoPe\":{\"layout\":[\"fixed\",\"auto\"]},\"B74KceKeh\":{\"layout\":[\"fixed\",\"auto\"]},\"eaRTV3HIf\":{\"layout\":[\"fixed\",\"auto\"]},\"XcWv6aoQT\":{\"layout\":[\"fixed\",\"auto\"]},\"Xy_ubmDwR\":{\"layout\":[\"fixed\",\"auto\"]},\"PJNW1ipBG\":{\"layout\":[\"fixed\",\"auto\"]},\"PA5JYpjvF\":{\"layout\":[\"fixed\",\"auto\"]},\"zuQ6Zt_Fe\":{\"layout\":[\"fixed\",\"auto\"]},\"RHYp4nkhp\":{\"layout\":[\"fixed\",\"auto\"]},\"j7eUa_b6M\":{\"layout\":[\"fixed\",\"auto\"]},\"qaIyC4wwa\":{\"layout\":[\"fixed\",\"auto\"]},\"rW1WUIUEr\":{\"layout\":[\"fixed\",\"auto\"]},\"gKVnIhHry\":{\"layout\":[\"fixed\",\"auto\"]},\"A55XVbgoX\":{\"layout\":[\"fixed\",\"auto\"]},\"oaW21IMXv\":{\"layout\":[\"fixed\",\"auto\"]},\"q_5c2DzRU\":{\"layout\":[\"fixed\",\"auto\"]},\"KABOPEvu8\":{\"layout\":[\"fixed\",\"auto\"]},\"AW5V70MRF\":{\"layout\":[\"fixed\",\"auto\"]},\"i8cHpCK9w\":{\"layout\":[\"fixed\",\"auto\"]},\"CbKtCXQ3c\":{\"layout\":[\"fixed\",\"auto\"]},\"Od32uvUF9\":{\"layout\":[\"fixed\",\"auto\"]},\"asOsoMiMY\":{\"layout\":[\"fixed\",\"auto\"]},\"ZkojTxbnD\":{\"layout\":[\"fixed\",\"auto\"]},\"FmvzcLpN0\":{\"layout\":[\"fixed\",\"auto\"]},\"kPnSUhY7l\":{\"layout\":[\"fixed\",\"auto\"]},\"PCAgL4Uwz\":{\"layout\":[\"fixed\",\"auto\"]},\"HX4O89hRP\":{\"layout\":[\"fixed\",\"auto\"]},\"Rp2uDkvZW\":{\"layout\":[\"fixed\",\"auto\"]},\"yNbTadh9B\":{\"layout\":[\"fixed\",\"auto\"]},\"bh18hddZb\":{\"layout\":[\"fixed\",\"auto\"]},\"PkFyJhg3t\":{\"layout\":[\"fixed\",\"auto\"]},\"tzTxbNo2j\":{\"layout\":[\"fixed\",\"auto\"]},\"gL4XzKRyu\":{\"layout\":[\"fixed\",\"auto\"]},\"kifH89zFT\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "500"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./hRs2gNE0M.map