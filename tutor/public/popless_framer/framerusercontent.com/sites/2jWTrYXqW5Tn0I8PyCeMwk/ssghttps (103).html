// Generated by Fr<PERSON>r (1b71f6a)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, RichText, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import PricingTableRow from "https://framerusercontent.com/modules/yFmpMEO504HYTaxSGYJa/uAMNVF9vPmrBUpyXtMXq/ZCoG6G2JF.js";
const PricingTableRowFonts = getFonts(PricingTableRow);
const cycleOrder = ["pVapogY4C", "UIpdM25NP", "TzdWaDWrs"];
const variantClassNames = {
  pVapogY4C: "framer-v-42w0j9",
  TzdWaDWrs: "framer-v-58883u",
  UIpdM25NP: "framer-v-9va7rw"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Desktop: "pVapogY4C",
  Mobile: "TzdWaDWrs",
  Tablet: "UIpdM25NP"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "pVapogY4C",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "pVapogY4C",
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-CxcIw", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsx(motion.section, {
        ...restProps,
        className: cx("framer-42w0j9", className),
        "data-framer-name": "Desktop",
        layoutDependency: layoutDependency,
        layoutId: "pVapogY4C",
        ref: ref,
        style: {
          backgroundColor: "var(--token-d76e7120-d27d-4da8-b15f-04234125e89d, rgb(255, 255, 255))",
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          TzdWaDWrs: {
            "data-framer-name": "Mobile"
          },
          UIpdM25NP: {
            "data-framer-name": "Tablet"
          }
        }, baseVariant, gestureVariant),
        children: /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1w4zm2b",
          "data-framer-name": "Container",
          layoutDependency: layoutDependency,
          layoutId: "t6l3e39UN",
          transition: transition,
          children: [/*#__PURE__*/_jsxs(motion.div, {
            className: "framer-173mv6h",
            layoutDependency: layoutDependency,
            layoutId: "ivR7k7IoY",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1iojrs1",
              layoutDependency: layoutDependency,
              layoutId: "TDPQnUBwv",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Overview"
                    })
                  })
                }),
                className: "framer-7q2vd",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "lI2T9kR5R",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-12l3uyb",
              layoutDependency: layoutDependency,
              layoutId: "Ivq24H8PE",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-rfelyc-container",
              layoutDependency: layoutDependency,
              layoutId: "jN8zkhJDi-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "Complicated. Expensive.     30% - 40% booking fees.",
                amount3: "Simple. Built for tutors building a business.",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "A platform for you",
                height: "100%",
                id: "jN8zkhJDi",
                layoutId: "jN8zkhJDi",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1xng9xy",
              layoutDependency: layoutDependency,
              layoutId: "vVcKPafFJ",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-mgrvem",
            layoutDependency: layoutDependency,
            layoutId: "B58godgeV",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1b1uq82",
              layoutDependency: layoutDependency,
              layoutId: "jvOfB0QLu",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Search and discovery"
                    })
                  })
                }),
                className: "framer-ue9t2s",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "dllJOeHAf",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-55gafh",
              layoutDependency: layoutDependency,
              layoutId: "z8ArNxSaS",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-78suh6-container",
              layoutDependency: layoutDependency,
              layoutId: "qtuxeCGFb-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Bookmark favorite tutors",
                height: "100%",
                id: "qtuxeCGFb",
                layoutId: "qtuxeCGFb",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-vai4dq",
              layoutDependency: layoutDependency,
              layoutId: "lAhAyTa9E",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1rjmac4-container",
              layoutDependency: layoutDependency,
              layoutId: "zBQU5AEl5-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Tutor and mentor across all categories",
                height: "100%",
                id: "zBQU5AEl5",
                layoutId: "zBQU5AEl5",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-4ccz3u",
              layoutDependency: layoutDependency,
              layoutId: "I7JtswzbO",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-xzn3yl-container",
              layoutDependency: layoutDependency,
              layoutId: "FfLODpQCh-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Find tutors from across the world",
                height: "100%",
                id: "FfLODpQCh",
                layoutId: "FfLODpQCh",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1f84pce",
              layoutDependency: layoutDependency,
              layoutId: "ybOJjy60f",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1124b0z-container",
              layoutDependency: layoutDependency,
              layoutId: "K5cPigMqJ-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Filter tutors by their availability",
                height: "100%",
                id: "K5cPigMqJ",
                layoutId: "K5cPigMqJ",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1pevy6u",
              layoutDependency: layoutDependency,
              layoutId: "rYmfd_cXf",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1smac97",
            layoutDependency: layoutDependency,
            layoutId: "enOTBZYdH",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1h3v63",
              layoutDependency: layoutDependency,
              layoutId: "jKiM7SrO7",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Manage availability"
                    })
                  })
                }),
                className: "framer-zx1dfr",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "hv42dUjIK",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-14r4fe9",
              layoutDependency: layoutDependency,
              layoutId: "fGOLmrbvo",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-11xc9lc-container",
              layoutDependency: layoutDependency,
              layoutId: "ptlcJIWs_-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Choose when you work",
                height: "100%",
                id: "ptlcJIWs_",
                layoutId: "ptlcJIWs_",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-pu4mje",
              layoutDependency: layoutDependency,
              layoutId: "u4_dM_Yu5",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-oyqzjq-container",
              layoutDependency: layoutDependency,
              layoutId: "eW1CQ2QXL-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Set the price you want to charge",
                height: "100%",
                id: "eW1CQ2QXL",
                layoutId: "eW1CQ2QXL",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1jdjy2",
              layoutDependency: layoutDependency,
              layoutId: "q55TTE52w",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-jwgyw9-container",
              layoutDependency: layoutDependency,
              layoutId: "NSygS6_z1-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Settings to prevent last-minute bookings",
                height: "100%",
                id: "NSygS6_z1",
                layoutId: "NSygS6_z1",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-8k62ov",
              layoutDependency: layoutDependency,
              layoutId: "EV5CKjw8Z",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1ukobvc-container",
              layoutDependency: layoutDependency,
              layoutId: "TYCXxg312-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Block time between meetings",
                height: "100%",
                id: "TYCXxg312",
                layoutId: "TYCXxg312",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1l0cbaa",
              layoutDependency: layoutDependency,
              layoutId: "Tq5VRsmO7",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-b90tja-container",
              layoutDependency: layoutDependency,
              layoutId: "hYeVwlx65-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Automatic time zone detection",
                height: "100%",
                id: "hYeVwlx65",
                layoutId: "hYeVwlx65",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-60qx1i",
              layoutDependency: layoutDependency,
              layoutId: "k8I1oGLk3",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-i63hw8-container",
              layoutDependency: layoutDependency,
              layoutId: "UlH40cExi-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Accept group bookings",
                height: "100%",
                id: "UlH40cExi",
                layoutId: "UlH40cExi",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-va7s7v",
              layoutDependency: layoutDependency,
              layoutId: "hH7cmwCNc",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-rmshn0-container",
              layoutDependency: layoutDependency,
              layoutId: "ST0dHie0O-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "‌Daily limits for meetings ",
                height: "100%",
                id: "ST0dHie0O",
                layoutId: "ST0dHie0O",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-oetuxm",
              layoutDependency: layoutDependency,
              layoutId: "rFm2AIF48",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-j3j1lh-container",
              layoutDependency: layoutDependency,
              layoutId: "yqv6XOwJw-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Offer discounts for pre-bought sessions",
                height: "100%",
                id: "yqv6XOwJw",
                layoutId: "yqv6XOwJw",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1mlnf3",
              layoutDependency: layoutDependency,
              layoutId: "CdsnwXGEl",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-8pvgon",
            layoutDependency: layoutDependency,
            layoutId: "TO4QsyTL3",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-8rsf8y",
              layoutDependency: layoutDependency,
              layoutId: "IdHG_Vi9Q",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Bookings"
                    })
                  })
                }),
                className: "framer-ma3aal",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "cCUhQwJnv",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1fjgqep",
              layoutDependency: layoutDependency,
              layoutId: "u4ATAE9j0",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1jgvb26-container",
              layoutDependency: layoutDependency,
              layoutId: "hZXzn9ewF-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Manage reschedules",
                height: "100%",
                id: "hZXzn9ewF",
                layoutId: "hZXzn9ewF",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-wfiz3f",
              layoutDependency: layoutDependency,
              layoutId: "Z2l85nqy8",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-nsr1qb-container",
              layoutDependency: layoutDependency,
              layoutId: "T5Z5c5r2s-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Create and accept coupons",
                height: "100%",
                id: "T5Z5c5r2s",
                layoutId: "T5Z5c5r2s",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1ld9s0x",
              layoutDependency: layoutDependency,
              layoutId: "uygL32yAa",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1s37ew6-container",
              layoutDependency: layoutDependency,
              layoutId: "MjqV_XIMA-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Transparent review system",
                height: "100%",
                id: "MjqV_XIMA",
                layoutId: "MjqV_XIMA",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1suczy9",
              layoutDependency: layoutDependency,
              layoutId: "Wme_iRhAw",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-erojj6-container",
              layoutDependency: layoutDependency,
              layoutId: "mMdvTtaC7-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Custom cancellation policies",
                height: "100%",
                id: "mMdvTtaC7",
                layoutId: "mMdvTtaC7",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1d381mp",
              layoutDependency: layoutDependency,
              layoutId: "GCZxrTrQM",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1dn4j39",
            layoutDependency: layoutDependency,
            layoutId: "vGQjRGR65",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1c5q62n",
              layoutDependency: layoutDependency,
              layoutId: "AWVmoN9ot",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Messaging"
                    })
                  })
                }),
                className: "framer-106wa9t",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "LUBUyFI5g",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1saci1e",
              layoutDependency: layoutDependency,
              layoutId: "zuZ2MzRcx",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-14o0f51-container",
              layoutDependency: layoutDependency,
              layoutId: "AlR8Pj2ub-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Manage contacts",
                height: "100%",
                id: "AlR8Pj2ub",
                layoutId: "AlR8Pj2ub",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1ign4tc",
              layoutDependency: layoutDependency,
              layoutId: "AzjzLWIhw",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1lfkg3a-container",
              layoutDependency: layoutDependency,
              layoutId: "LYbi3AyCd-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Secure encrypted messages",
                height: "100%",
                id: "LYbi3AyCd",
                layoutId: "LYbi3AyCd",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-aikn6r",
              layoutDependency: layoutDependency,
              layoutId: "Al5VZn81A",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-rexcme-container",
              layoutDependency: layoutDependency,
              layoutId: "rmyg2aGr_-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Audio, video, images, GIFs, files",
                height: "100%",
                id: "rmyg2aGr_",
                layoutId: "rmyg2aGr_",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-nj65vc",
              layoutDependency: layoutDependency,
              layoutId: "cdyTfMA66",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-w6uiun-container",
              layoutDependency: layoutDependency,
              layoutId: "GK9jXWusV-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Typing indicators",
                height: "100%",
                id: "GK9jXWusV",
                layoutId: "GK9jXWusV",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-2zv92o",
              layoutDependency: layoutDependency,
              layoutId: "HcI07cvwk",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-g1ugc4-container",
              layoutDependency: layoutDependency,
              layoutId: "TViW1C9d7-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Emoticon reactions",
                height: "100%",
                id: "TViW1C9d7",
                layoutId: "TViW1C9d7",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-ivh0oc",
              layoutDependency: layoutDependency,
              layoutId: "GUtjHh2T7",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1v4urjk-container",
              layoutDependency: layoutDependency,
              layoutId: "MvpxzlaKx-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Online Offline indicators",
                height: "100%",
                id: "MvpxzlaKx",
                layoutId: "MvpxzlaKx",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-eqke9y",
              layoutDependency: layoutDependency,
              layoutId: "jVvIulmrf",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-f7yjaq-container",
              layoutDependency: layoutDependency,
              layoutId: "vFirlXRfO-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "On demand translation",
                height: "100%",
                id: "vFirlXRfO",
                layoutId: "vFirlXRfO",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1b9ov9",
              layoutDependency: layoutDependency,
              layoutId: "gowkz1s_E",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-howlzr-container",
              layoutDependency: layoutDependency,
              layoutId: "MgD1LOpO5-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "User-to-User blockings",
                height: "100%",
                id: "MgD1LOpO5",
                layoutId: "MgD1LOpO5",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1x4fp0x",
              layoutDependency: layoutDependency,
              layoutId: "bwkIlXdzR",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-ydc9pk-container",
              layoutDependency: layoutDependency,
              layoutId: "TrZhVVfXZ-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Message threads and replies",
                height: "100%",
                id: "TrZhVVfXZ",
                layoutId: "TrZhVVfXZ",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-q999kt",
              layoutDependency: layoutDependency,
              layoutId: "zrJRrS3Ws",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-p81zl6",
            layoutDependency: layoutDependency,
            layoutId: "wCxRKuqQe",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-l8eb85",
              layoutDependency: layoutDependency,
              layoutId: "CiyFkihs7",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Payments"
                    })
                  })
                }),
                className: "framer-appjmp",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "akouNU3S7",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-n6in16",
              layoutDependency: layoutDependency,
              layoutId: "kN9dDwYVf",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-r0qcpm-container",
              layoutDependency: layoutDependency,
              layoutId: "bn3hEYwrs-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Accept payments in 45 currencies",
                height: "100%",
                id: "bn3hEYwrs",
                layoutId: "bn3hEYwrs",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-hst396",
              layoutDependency: layoutDependency,
              layoutId: "jl6fhgQCb",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1xbgjrj-container",
              layoutDependency: layoutDependency,
              layoutId: "yO1bK1EAS-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Choose your payout currency",
                height: "100%",
                id: "yO1bK1EAS",
                layoutId: "yO1bK1EAS",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-63pv0j",
              layoutDependency: layoutDependency,
              layoutId: "KULDs8ZgP",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-f5p3f4-container",
              layoutDependency: layoutDependency,
              layoutId: "V74zuZXa3-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Create and accept coupons ",
                height: "100%",
                id: "V74zuZXa3",
                layoutId: "V74zuZXa3",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1mcs3a2",
              layoutDependency: layoutDependency,
              layoutId: "I_3NnL0t3",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-viui5f-container",
              layoutDependency: layoutDependency,
              layoutId: "UvpEyHA8B-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "4-7 days",
                amount3: "Instant",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Payout timing",
                height: "100%",
                id: "UvpEyHA8B",
                layoutId: "UvpEyHA8B",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-5or1z7",
              layoutDependency: layoutDependency,
              layoutId: "iotmsgi2u",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1ta66k8-container",
              layoutDependency: layoutDependency,
              layoutId: "IqBuSXslN-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "20% - 30%",
                amount3: "5%",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Popless service fee",
                height: "100%",
                id: "IqBuSXslN",
                layoutId: "IqBuSXslN",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1sxsmvi",
              layoutDependency: layoutDependency,
              layoutId: "lh_r0l07c",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1oq3uq7-container",
              layoutDependency: layoutDependency,
              layoutId: "axzQIsGGA-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "20% - 30%",
                amount3: "0%",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Service fee for students you refer",
                height: "100%",
                id: "axzQIsGGA",
                layoutId: "axzQIsGGA",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  },
                  UIpdM25NP: {
                    feature: "Popless service fee - Referred students"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-19jnpon",
              layoutDependency: layoutDependency,
              layoutId: "KDIGqSkXf",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-jfiwpn-container",
              layoutDependency: layoutDependency,
              layoutId: "HUVd2AL_P-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Recieve crypto payments",
                height: "100%",
                id: "HUVd2AL_P",
                layoutId: "HUVd2AL_P",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-htq4ke",
              layoutDependency: layoutDependency,
              layoutId: "xDDcsM1yo",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-x1dm4y-container",
              layoutDependency: layoutDependency,
              layoutId: "ow7BlhKC7-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Be paid out in crypto",
                height: "100%",
                id: "ow7BlhKC7",
                layoutId: "ow7BlhKC7",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1p0sio4",
              layoutDependency: layoutDependency,
              layoutId: "n2AG9ryA3",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-10lqkbv",
            layoutDependency: layoutDependency,
            layoutId: "vQv5ciIsl",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1auem3m",
              layoutDependency: layoutDependency,
              layoutId: "kM3g63Rgu",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Notifications"
                    })
                  })
                }),
                className: "framer-1wy7g6n",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "sYTq8IlxK",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-czpdc9",
              layoutDependency: layoutDependency,
              layoutId: "taJ1l6QaB",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-o62lbw-container",
              layoutDependency: layoutDependency,
              layoutId: "k7X8_mTKd-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Email",
                height: "100%",
                id: "k7X8_mTKd",
                layoutId: "k7X8_mTKd",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-jg3bf4",
              layoutDependency: layoutDependency,
              layoutId: "eA4l6Lpmk",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-hja7ei-container",
              layoutDependency: layoutDependency,
              layoutId: "Wu0GSFjR2-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: true,
                check3: false,
                close3: false,
                cross2: false,
                feature: "SMS",
                height: "100%",
                id: "Wu0GSFjR2",
                layoutId: "Wu0GSFjR2",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-8ixsk8",
              layoutDependency: layoutDependency,
              layoutId: "SjbBDcrBg",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-hf25k8-container",
              layoutDependency: layoutDependency,
              layoutId: "dVu6czghs-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Slack",
                height: "100%",
                id: "dVu6czghs",
                layoutId: "dVu6czghs",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-4tqew4",
              layoutDependency: layoutDependency,
              layoutId: "k2uF5omhY",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1xwj1b6-container",
              layoutDependency: layoutDependency,
              layoutId: "rIM9TzRAQ-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Discord",
                height: "100%",
                id: "rIM9TzRAQ",
                layoutId: "rIM9TzRAQ",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-7hyghk",
              layoutDependency: layoutDependency,
              layoutId: "DGbCxN8c1",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-12a6426",
            layoutDependency: layoutDependency,
            layoutId: "fYUs2Mm5i",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-1cs4a4o",
              layoutDependency: layoutDependency,
              layoutId: "iH710TRUf",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Analytics"
                    })
                  })
                }),
                className: "framer-1dvlm1d",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "XjIx9e5N4",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-17fsqjv",
              layoutDependency: layoutDependency,
              layoutId: "My5xmKZBx",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-3oaien-container",
              layoutDependency: layoutDependency,
              layoutId: "ddiVaN8MA-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Earnings dashboard",
                height: "100%",
                id: "ddiVaN8MA",
                layoutId: "ddiVaN8MA",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-apcgzz",
              layoutDependency: layoutDependency,
              layoutId: "ROP08CVy4",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-lpfkor-container",
              layoutDependency: layoutDependency,
              layoutId: "p_mbzrln3-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Booking analytics",
                height: "100%",
                id: "p_mbzrln3",
                layoutId: "p_mbzrln3",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-rnuxw4",
              layoutDependency: layoutDependency,
              layoutId: "KKGN20wBF",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1nkyyil-container",
              layoutDependency: layoutDependency,
              layoutId: "t88ymt79L-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Page view metrics",
                height: "100%",
                id: "t88ymt79L",
                layoutId: "t88ymt79L",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1aw79ny",
              layoutDependency: layoutDependency,
              layoutId: "uCUKS99uR",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-gxvgi8-container",
              layoutDependency: layoutDependency,
              layoutId: "LMdYUUjsr-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Google Analytics",
                height: "100%",
                id: "LMdYUUjsr",
                layoutId: "LMdYUUjsr",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1947pv2",
              layoutDependency: layoutDependency,
              layoutId: "Y4OUwVcB6",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-5nhpql",
            layoutDependency: layoutDependency,
            layoutId: "xxS6jEY7g",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-6yuku3",
              layoutDependency: layoutDependency,
              layoutId: "KUOWqbpYk",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Integrations"
                    })
                  })
                }),
                className: "framer-1o7z1lr",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "nOM4JbRo5",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1dgie7q",
              layoutDependency: layoutDependency,
              layoutId: "e6heOFrLb",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-cculhm-container",
              layoutDependency: layoutDependency,
              layoutId: "oOnKkXhvR-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: true,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Google Calendar",
                height: "100%",
                id: "oOnKkXhvR",
                layoutId: "oOnKkXhvR",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1bzbawc",
              layoutDependency: layoutDependency,
              layoutId: "NPrvNvbTK",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-vov2p-container",
              layoutDependency: layoutDependency,
              layoutId: "nqIbh0OFY-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Zoom",
                height: "100%",
                id: "nqIbh0OFY",
                layoutId: "nqIbh0OFY",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-182h6xj",
              layoutDependency: layoutDependency,
              layoutId: "zTVuuIlnt",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-32fx73-container",
              layoutDependency: layoutDependency,
              layoutId: "NjzE0JY_K-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Whereby",
                height: "100%",
                id: "NjzE0JY_K",
                layoutId: "NjzE0JY_K",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1maeob9",
              layoutDependency: layoutDependency,
              layoutId: "f30qtOl6_",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1xrn0ti-container",
              layoutDependency: layoutDependency,
              layoutId: "ptU7IVemY-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Outlook",
                height: "100%",
                id: "ptU7IVemY",
                layoutId: "ptU7IVemY",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-10lfion",
              layoutDependency: layoutDependency,
              layoutId: "PZyD3QJH2",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1lq5t0n-container",
              layoutDependency: layoutDependency,
              layoutId: "jDiwzxcmA-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: false,
                check3: false,
                close3: false,
                cross2: true,
                feature: "Webhooks",
                height: "100%",
                id: "jDiwzxcmA",
                layoutId: "jDiwzxcmA",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-ivbhz2",
              layoutDependency: layoutDependency,
              layoutId: "AOfX2ZnHz",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-gnzkrc",
            layoutDependency: layoutDependency,
            layoutId: "nP7JnLCKg",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-10v8w9g",
              layoutDependency: layoutDependency,
              layoutId: "fdN5ocf2G",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Support"
                    })
                  })
                }),
                className: "framer-j8edns",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "VNRSdRYBY",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-v9458",
              layoutDependency: layoutDependency,
              layoutId: "bB9qNQC5L",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1911xpa-container",
              layoutDependency: layoutDependency,
              layoutId: "sUZlf2L0J-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "Minimal",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: false,
                feature: "Support center",
                height: "100%",
                id: "sUZlf2L0J",
                layoutId: "sUZlf2L0J",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-xlu9la",
              layoutDependency: layoutDependency,
              layoutId: "EE0dhSmM5",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-wxfw1t-container",
              layoutDependency: layoutDependency,
              layoutId: "OTcr4M5qj-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "24/7 Customer support",
                height: "100%",
                id: "OTcr4M5qj",
                layoutId: "OTcr4M5qj",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1szqamv",
              layoutDependency: layoutDependency,
              layoutId: "deggbH22T",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1xla8p7-container",
              layoutDependency: layoutDependency,
              layoutId: "dW1MQDJAZ-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Real-time chat",
                height: "100%",
                id: "dW1MQDJAZ",
                layoutId: "dW1MQDJAZ",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-191et4c",
              layoutDependency: layoutDependency,
              layoutId: "SjnvXH_iZ",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-qe71k7-container",
              layoutDependency: layoutDependency,
              layoutId: "sNe3_CCyf-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Discord community",
                height: "100%",
                id: "sNe3_CCyf",
                layoutId: "sNe3_CCyf",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-124tfu6",
              layoutDependency: layoutDependency,
              layoutId: "xeoGf33JH",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-klgonh",
            layoutDependency: layoutDependency,
            layoutId: "vQUMVmuDl",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-rlz6gh",
              layoutDependency: layoutDependency,
              layoutId: "yRYgPIg41",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Security and Privacy"
                    })
                  })
                }),
                className: "framer-ac93cj",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "XrS9PmHJ4",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-wevq19",
              layoutDependency: layoutDependency,
              layoutId: "GothY84p8",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-10t97dl-container",
              layoutDependency: layoutDependency,
              layoutId: "S4C0ziYpc-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Privacy preserving analytics",
                height: "100%",
                id: "S4C0ziYpc",
                layoutId: "S4C0ziYpc",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-qezy3v",
              layoutDependency: layoutDependency,
              layoutId: "RFEnzsJYu",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1teohuv-container",
              layoutDependency: layoutDependency,
              layoutId: "FThyGGnu7-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Choose to remain private",
                height: "100%",
                id: "FThyGGnu7",
                layoutId: "FThyGGnu7",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-poqp7v",
              layoutDependency: layoutDependency,
              layoutId: "sY8o_eQWN",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-w26ezt",
            layoutDependency: layoutDependency,
            layoutId: "Dj_y07vTx",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-ojmfx1",
              layoutDependency: layoutDependency,
              layoutId: "GAI7rSN1b",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Performance"
                    })
                  })
                }),
                className: "framer-whg3k2",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "sZ4q30Err",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-l7ca6a",
              layoutDependency: layoutDependency,
              layoutId: "EgisOMa_h",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-pn87k-container",
              layoutDependency: layoutDependency,
              layoutId: "T0C8hY0_a-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Statically generated",
                height: "100%",
                id: "T0C8hY0_a",
                layoutId: "T0C8hY0_a",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-ka1mou",
              layoutDependency: layoutDependency,
              layoutId: "T58APyfnE",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-ys06fh-container",
              layoutDependency: layoutDependency,
              layoutId: "RFyaQ0KoM-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "500ms",
                amount3: "200ms",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Avg page load time",
                height: "100%",
                id: "RFyaQ0KoM",
                layoutId: "RFyaQ0KoM",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1x1fowl",
              layoutDependency: layoutDependency,
              layoutId: "ewufJ6sHV",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-kud8p6-container",
              layoutDependency: layoutDependency,
              layoutId: "I9Tex_vbU-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "Accessible without Javascript",
                height: "100%",
                id: "I9Tex_vbU",
                layoutId: "I9Tex_vbU",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-dqla5v",
              layoutDependency: layoutDependency,
              layoutId: "uzNXkFOpm",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1cld2ui-container",
              layoutDependency: layoutDependency,
              layoutId: "J4Ga8HtJK-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "",
                check2: false,
                check3: true,
                close3: false,
                cross2: true,
                feature: "SEO optimized",
                height: "100%",
                id: "J4Ga8HtJK",
                layoutId: "J4Ga8HtJK",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-14ppyrv",
              layoutDependency: layoutDependency,
              layoutId: "wOFsS1rzE",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-e04nb5-container",
              layoutDependency: layoutDependency,
              layoutId: "UNusWpND2-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "98%",
                amount3: "99.999%",
                check2: false,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Uptime",
                height: "100%",
                id: "UNusWpND2",
                layoutId: "UNusWpND2",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1n7l3ir",
              layoutDependency: layoutDependency,
              layoutId: "OsdnDMKJ6",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-17mqjxs",
            layoutDependency: layoutDependency,
            layoutId: "tKfxQwo4g",
            transition: transition,
            children: [/*#__PURE__*/_jsx(motion.div, {
              className: "framer-dhwqeh",
              layoutDependency: layoutDependency,
              layoutId: "DN44zxiCd",
              transition: transition,
              children: /*#__PURE__*/_jsx(RichText, {
                __fromCanvasComponent: true,
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-line-height": "1.1em",
                      "--framer-text-alignment": "left"
                    },
                    children: /*#__PURE__*/_jsx(motion.span, {
                      style: {
                        "--font-selector": "R0Y7SW50ZXItNTAw",
                        "--framer-font-family": '"Inter", sans-serif',
                        "--framer-font-size": "18px",
                        "--framer-font-style": "normal",
                        "--framer-font-weight": "500",
                        "--framer-letter-spacing": "-0.5px",
                        "--framer-text-color": "var(--extracted-1w3ko1f)"
                      },
                      children: "Mobile"
                    })
                  })
                }),
                className: "framer-1pyp2ql",
                fonts: ["GF;Inter-500"],
                layoutDependency: layoutDependency,
                layoutId: "A4nBesNwQ",
                style: {
                  "--extracted-1w3ko1f": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
                  "--framer-link-text-color": "rgb(0, 153, 255)",
                  "--framer-link-text-decoration": "underline",
                  "--framer-paragraph-spacing": "0px"
                },
                transition: transition,
                verticalAlignment: "top",
                withExternalLayout: true
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1fuiegq",
              layoutDependency: layoutDependency,
              layoutId: "TAmbJtB5H",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1o7bpsq-container",
              layoutDependency: layoutDependency,
              layoutId: "h8ihl_tyk-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: true,
                check3: false,
                close3: false,
                cross2: false,
                feature: "iOS app",
                height: "100%",
                id: "h8ihl_tyk",
                layoutId: "h8ihl_tyk",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-gfac51",
              layoutDependency: layoutDependency,
              layoutId: "SGSC94Clz",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-1vrv35t-container",
              layoutDependency: layoutDependency,
              layoutId: "BT4Z6QLIL-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(PricingTableRow, {
                amount2: "",
                amount3: "Coming soon",
                check2: true,
                check3: false,
                close3: false,
                cross2: false,
                feature: "Android app",
                height: "100%",
                id: "BT4Z6QLIL",
                layoutId: "BT4Z6QLIL",
                style: {
                  width: "100%"
                },
                variant: "E8fyzpKQN",
                width: "100%",
                ...addPropertyOverrides({
                  TzdWaDWrs: {
                    variant: "QDi0tDYwF"
                  }
                }, baseVariant, gestureVariant)
              })
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-eqrjgk",
              layoutDependency: layoutDependency,
              layoutId: "QYk9suaeb",
              style: {
                backgroundColor: "var(--token-6cca7a5e-8aef-48dd-8852-18702fb7a7e7, rgb(240, 240, 240))"
              },
              transition: transition
            })]
          })]
        })
      })
    })
  });
});
const css = ['.framer-CxcIw [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-CxcIw * { box-sizing: border-box; }", ".framer-CxcIw .framer-17ocq26 { display: block; }", ".framer-CxcIw .framer-42w0j9 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1200px; }", ".framer-CxcIw .framer-1w4zm2b { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 130px; height: min-content; justify-content: center; max-width: 1000px; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-CxcIw .framer-173mv6h, .framer-CxcIw .framer-mgrvem { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-CxcIw .framer-1iojrs1, .framer-CxcIw .framer-1b1uq82 { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 200px; }", ".framer-CxcIw .framer-7q2vd, .framer-CxcIw .framer-ue9t2s, .framer-CxcIw .framer-zx1dfr, .framer-CxcIw .framer-ma3aal, .framer-CxcIw .framer-106wa9t, .framer-CxcIw .framer-appjmp, .framer-CxcIw .framer-1wy7g6n, .framer-CxcIw .framer-1dvlm1d, .framer-CxcIw .framer-1o7z1lr, .framer-CxcIw .framer-j8edns, .framer-CxcIw .framer-ac93cj, .framer-CxcIw .framer-whg3k2, .framer-CxcIw .framer-1pyp2ql { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-CxcIw .framer-12l3uyb, .framer-CxcIw .framer-1xng9xy, .framer-CxcIw .framer-55gafh, .framer-CxcIw .framer-vai4dq, .framer-CxcIw .framer-4ccz3u, .framer-CxcIw .framer-1f84pce, .framer-CxcIw .framer-1pevy6u, .framer-CxcIw .framer-14r4fe9, .framer-CxcIw .framer-pu4mje, .framer-CxcIw .framer-1jdjy2, .framer-CxcIw .framer-8k62ov, .framer-CxcIw .framer-1l0cbaa, .framer-CxcIw .framer-60qx1i, .framer-CxcIw .framer-va7s7v, .framer-CxcIw .framer-oetuxm, .framer-CxcIw .framer-1mlnf3, .framer-CxcIw .framer-1fjgqep, .framer-CxcIw .framer-wfiz3f, .framer-CxcIw .framer-1ld9s0x, .framer-CxcIw .framer-1suczy9, .framer-CxcIw .framer-1d381mp, .framer-CxcIw .framer-1saci1e, .framer-CxcIw .framer-1ign4tc, .framer-CxcIw .framer-aikn6r, .framer-CxcIw .framer-nj65vc, .framer-CxcIw .framer-2zv92o, .framer-CxcIw .framer-ivh0oc, .framer-CxcIw .framer-eqke9y, .framer-CxcIw .framer-1b9ov9, .framer-CxcIw .framer-1x4fp0x, .framer-CxcIw .framer-q999kt, .framer-CxcIw .framer-n6in16, .framer-CxcIw .framer-hst396, .framer-CxcIw .framer-63pv0j, .framer-CxcIw .framer-1mcs3a2, .framer-CxcIw .framer-5or1z7, .framer-CxcIw .framer-1sxsmvi, .framer-CxcIw .framer-19jnpon, .framer-CxcIw .framer-htq4ke, .framer-CxcIw .framer-1p0sio4, .framer-CxcIw .framer-czpdc9, .framer-CxcIw .framer-jg3bf4, .framer-CxcIw .framer-8ixsk8, .framer-CxcIw .framer-4tqew4, .framer-CxcIw .framer-7hyghk, .framer-CxcIw .framer-17fsqjv, .framer-CxcIw .framer-apcgzz, .framer-CxcIw .framer-rnuxw4, .framer-CxcIw .framer-1aw79ny, .framer-CxcIw .framer-1947pv2, .framer-CxcIw .framer-1dgie7q, .framer-CxcIw .framer-1bzbawc, .framer-CxcIw .framer-182h6xj, .framer-CxcIw .framer-1maeob9, .framer-CxcIw .framer-10lfion, .framer-CxcIw .framer-ivbhz2, .framer-CxcIw .framer-v9458, .framer-CxcIw .framer-xlu9la, .framer-CxcIw .framer-1szqamv, .framer-CxcIw .framer-191et4c, .framer-CxcIw .framer-124tfu6, .framer-CxcIw .framer-wevq19, .framer-CxcIw .framer-qezy3v, .framer-CxcIw .framer-poqp7v, .framer-CxcIw .framer-l7ca6a, .framer-CxcIw .framer-ka1mou, .framer-CxcIw .framer-1x1fowl, .framer-CxcIw .framer-dqla5v, .framer-CxcIw .framer-14ppyrv, .framer-CxcIw .framer-1n7l3ir, .framer-CxcIw .framer-1fuiegq, .framer-CxcIw .framer-gfac51, .framer-CxcIw .framer-eqrjgk { flex: none; height: 1px; overflow: visible; position: relative; width: 100%; }", ".framer-CxcIw .framer-rfelyc-container, .framer-CxcIw .framer-78suh6-container, .framer-CxcIw .framer-1rjmac4-container, .framer-CxcIw .framer-xzn3yl-container, .framer-CxcIw .framer-1124b0z-container, .framer-CxcIw .framer-11xc9lc-container, .framer-CxcIw .framer-oyqzjq-container, .framer-CxcIw .framer-jwgyw9-container, .framer-CxcIw .framer-1ukobvc-container, .framer-CxcIw .framer-b90tja-container, .framer-CxcIw .framer-i63hw8-container, .framer-CxcIw .framer-rmshn0-container, .framer-CxcIw .framer-j3j1lh-container, .framer-CxcIw .framer-1jgvb26-container, .framer-CxcIw .framer-nsr1qb-container, .framer-CxcIw .framer-1s37ew6-container, .framer-CxcIw .framer-erojj6-container, .framer-CxcIw .framer-14o0f51-container, .framer-CxcIw .framer-1lfkg3a-container, .framer-CxcIw .framer-rexcme-container, .framer-CxcIw .framer-w6uiun-container, .framer-CxcIw .framer-g1ugc4-container, .framer-CxcIw .framer-1v4urjk-container, .framer-CxcIw .framer-f7yjaq-container, .framer-CxcIw .framer-howlzr-container, .framer-CxcIw .framer-ydc9pk-container, .framer-CxcIw .framer-r0qcpm-container, .framer-CxcIw .framer-1xbgjrj-container, .framer-CxcIw .framer-f5p3f4-container, .framer-CxcIw .framer-viui5f-container, .framer-CxcIw .framer-1ta66k8-container, .framer-CxcIw .framer-1oq3uq7-container, .framer-CxcIw .framer-jfiwpn-container, .framer-CxcIw .framer-x1dm4y-container, .framer-CxcIw .framer-o62lbw-container, .framer-CxcIw .framer-hja7ei-container, .framer-CxcIw .framer-hf25k8-container, .framer-CxcIw .framer-1xwj1b6-container, .framer-CxcIw .framer-3oaien-container, .framer-CxcIw .framer-lpfkor-container, .framer-CxcIw .framer-1nkyyil-container, .framer-CxcIw .framer-gxvgi8-container, .framer-CxcIw .framer-cculhm-container, .framer-CxcIw .framer-vov2p-container, .framer-CxcIw .framer-32fx73-container, .framer-CxcIw .framer-1xrn0ti-container, .framer-CxcIw .framer-1lq5t0n-container, .framer-CxcIw .framer-1911xpa-container, .framer-CxcIw .framer-wxfw1t-container, .framer-CxcIw .framer-1xla8p7-container, .framer-CxcIw .framer-qe71k7-container, .framer-CxcIw .framer-10t97dl-container, .framer-CxcIw .framer-1teohuv-container, .framer-CxcIw .framer-pn87k-container, .framer-CxcIw .framer-ys06fh-container, .framer-CxcIw .framer-kud8p6-container, .framer-CxcIw .framer-1cld2ui-container, .framer-CxcIw .framer-e04nb5-container, .framer-CxcIw .framer-1o7bpsq-container, .framer-CxcIw .framer-1vrv35t-container { flex: none; height: auto; position: relative; width: 100%; }", ".framer-CxcIw .framer-1smac97, .framer-CxcIw .framer-8pvgon, .framer-CxcIw .framer-1dn4j39, .framer-CxcIw .framer-p81zl6, .framer-CxcIw .framer-10lqkbv, .framer-CxcIw .framer-12a6426, .framer-CxcIw .framer-5nhpql, .framer-CxcIw .framer-gnzkrc, .framer-CxcIw .framer-klgonh, .framer-CxcIw .framer-w26ezt, .framer-CxcIw .framer-17mqjxs { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-CxcIw .framer-1h3v63, .framer-CxcIw .framer-8rsf8y, .framer-CxcIw .framer-1c5q62n, .framer-CxcIw .framer-l8eb85, .framer-CxcIw .framer-1auem3m, .framer-CxcIw .framer-1cs4a4o, .framer-CxcIw .framer-6yuku3, .framer-CxcIw .framer-10v8w9g, .framer-CxcIw .framer-rlz6gh, .framer-CxcIw .framer-ojmfx1, .framer-CxcIw .framer-dhwqeh { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-CxcIw .framer-42w0j9, .framer-CxcIw .framer-1w4zm2b, .framer-CxcIw .framer-173mv6h, .framer-CxcIw .framer-1iojrs1, .framer-CxcIw .framer-mgrvem, .framer-CxcIw .framer-1b1uq82, .framer-CxcIw .framer-1smac97, .framer-CxcIw .framer-1h3v63, .framer-CxcIw .framer-8pvgon, .framer-CxcIw .framer-8rsf8y, .framer-CxcIw .framer-1dn4j39, .framer-CxcIw .framer-1c5q62n, .framer-CxcIw .framer-p81zl6, .framer-CxcIw .framer-l8eb85, .framer-CxcIw .framer-10lqkbv, .framer-CxcIw .framer-1auem3m, .framer-CxcIw .framer-12a6426, .framer-CxcIw .framer-1cs4a4o, .framer-CxcIw .framer-5nhpql, .framer-CxcIw .framer-6yuku3, .framer-CxcIw .framer-gnzkrc, .framer-CxcIw .framer-10v8w9g, .framer-CxcIw .framer-klgonh, .framer-CxcIw .framer-rlz6gh, .framer-CxcIw .framer-w26ezt, .framer-CxcIw .framer-ojmfx1, .framer-CxcIw .framer-17mqjxs, .framer-CxcIw .framer-dhwqeh { gap: 0px; } .framer-CxcIw .framer-42w0j9 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-CxcIw .framer-42w0j9 > :first-child { margin-left: 0px; } .framer-CxcIw .framer-42w0j9 > :last-child { margin-right: 0px; } .framer-CxcIw .framer-1w4zm2b > * { margin: 0px; margin-bottom: calc(130px / 2); margin-top: calc(130px / 2); } .framer-CxcIw .framer-1w4zm2b > :first-child, .framer-CxcIw .framer-173mv6h > :first-child, .framer-CxcIw .framer-1iojrs1 > :first-child, .framer-CxcIw .framer-mgrvem > :first-child, .framer-CxcIw .framer-1b1uq82 > :first-child, .framer-CxcIw .framer-1smac97 > :first-child, .framer-CxcIw .framer-1h3v63 > :first-child, .framer-CxcIw .framer-8pvgon > :first-child, .framer-CxcIw .framer-8rsf8y > :first-child, .framer-CxcIw .framer-1dn4j39 > :first-child, .framer-CxcIw .framer-1c5q62n > :first-child, .framer-CxcIw .framer-p81zl6 > :first-child, .framer-CxcIw .framer-l8eb85 > :first-child, .framer-CxcIw .framer-10lqkbv > :first-child, .framer-CxcIw .framer-1auem3m > :first-child, .framer-CxcIw .framer-12a6426 > :first-child, .framer-CxcIw .framer-1cs4a4o > :first-child, .framer-CxcIw .framer-5nhpql > :first-child, .framer-CxcIw .framer-6yuku3 > :first-child, .framer-CxcIw .framer-gnzkrc > :first-child, .framer-CxcIw .framer-10v8w9g > :first-child, .framer-CxcIw .framer-klgonh > :first-child, .framer-CxcIw .framer-rlz6gh > :first-child, .framer-CxcIw .framer-w26ezt > :first-child, .framer-CxcIw .framer-ojmfx1 > :first-child, .framer-CxcIw .framer-17mqjxs > :first-child, .framer-CxcIw .framer-dhwqeh > :first-child { margin-top: 0px; } .framer-CxcIw .framer-1w4zm2b > :last-child, .framer-CxcIw .framer-173mv6h > :last-child, .framer-CxcIw .framer-1iojrs1 > :last-child, .framer-CxcIw .framer-mgrvem > :last-child, .framer-CxcIw .framer-1b1uq82 > :last-child, .framer-CxcIw .framer-1smac97 > :last-child, .framer-CxcIw .framer-1h3v63 > :last-child, .framer-CxcIw .framer-8pvgon > :last-child, .framer-CxcIw .framer-8rsf8y > :last-child, .framer-CxcIw .framer-1dn4j39 > :last-child, .framer-CxcIw .framer-1c5q62n > :last-child, .framer-CxcIw .framer-p81zl6 > :last-child, .framer-CxcIw .framer-l8eb85 > :last-child, .framer-CxcIw .framer-10lqkbv > :last-child, .framer-CxcIw .framer-1auem3m > :last-child, .framer-CxcIw .framer-12a6426 > :last-child, .framer-CxcIw .framer-1cs4a4o > :last-child, .framer-CxcIw .framer-5nhpql > :last-child, .framer-CxcIw .framer-6yuku3 > :last-child, .framer-CxcIw .framer-gnzkrc > :last-child, .framer-CxcIw .framer-10v8w9g > :last-child, .framer-CxcIw .framer-klgonh > :last-child, .framer-CxcIw .framer-rlz6gh > :last-child, .framer-CxcIw .framer-w26ezt > :last-child, .framer-CxcIw .framer-ojmfx1 > :last-child, .framer-CxcIw .framer-17mqjxs > :last-child, .framer-CxcIw .framer-dhwqeh > :last-child { margin-bottom: 0px; } .framer-CxcIw .framer-173mv6h > *, .framer-CxcIw .framer-mgrvem > *, .framer-CxcIw .framer-1smac97 > *, .framer-CxcIw .framer-8pvgon > *, .framer-CxcIw .framer-1dn4j39 > *, .framer-CxcIw .framer-p81zl6 > *, .framer-CxcIw .framer-10lqkbv > *, .framer-CxcIw .framer-12a6426 > *, .framer-CxcIw .framer-5nhpql > *, .framer-CxcIw .framer-gnzkrc > *, .framer-CxcIw .framer-klgonh > *, .framer-CxcIw .framer-w26ezt > *, .framer-CxcIw .framer-17mqjxs > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-CxcIw .framer-1iojrs1 > *, .framer-CxcIw .framer-1b1uq82 > *, .framer-CxcIw .framer-1h3v63 > *, .framer-CxcIw .framer-8rsf8y > *, .framer-CxcIw .framer-1c5q62n > *, .framer-CxcIw .framer-l8eb85 > *, .framer-CxcIw .framer-1auem3m > *, .framer-CxcIw .framer-1cs4a4o > *, .framer-CxcIw .framer-6yuku3 > *, .framer-CxcIw .framer-10v8w9g > *, .framer-CxcIw .framer-rlz6gh > *, .framer-CxcIw .framer-ojmfx1 > *, .framer-CxcIw .framer-dhwqeh > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } }", ".framer-CxcIw.framer-v-9va7rw .framer-42w0j9 { width: 810px; }", ".framer-CxcIw.framer-v-58883u .framer-42w0j9 { width: 390px; }", ".framer-CxcIw.framer-v-58883u .framer-1w4zm2b { gap: 117px; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-CxcIw.framer-v-58883u .framer-1w4zm2b { gap: 0px; } .framer-CxcIw.framer-v-58883u .framer-1w4zm2b > * { margin: 0px; margin-bottom: calc(117px / 2); margin-top: calc(117px / 2); } .framer-CxcIw.framer-v-58883u .framer-1w4zm2b > :first-child { margin-top: 0px; } .framer-CxcIw.framer-v-58883u .framer-1w4zm2b > :last-child { margin-bottom: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicHeight 6197.5
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicWidth 1200
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"UIpdM25NP":{"layout":["fixed","auto"]},"TzdWaDWrs":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                */
const FramerOp0sX5onA = withCSS(Component, css);
export default FramerOp0sX5onA;
FramerOp0sX5onA.displayName = "Compare - Desktop";
FramerOp0sX5onA.defaultProps = {
  height: 6197.5,
  width: 1200
};
addPropertyControls(FramerOp0sX5onA, {
  variant: {
    options: ["pVapogY4C", "UIpdM25NP", "TzdWaDWrs"],
    optionTitles: ["Desktop", "Tablet", "Mobile"],
    title: "Variant",
    type: ControlType.Enum
  }
});
addFonts(FramerOp0sX5onA, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/Op0sX5onA:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, ...PricingTableRowFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerOp0sX5onA",
      "slots": [],
      "annotations": {
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"UIpdM25NP\":{\"layout\":[\"fixed\",\"auto\"]},\"TzdWaDWrs\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerIntrinsicWidth": "1200",
        "framerContractVersion": "1",
        "framerIntrinsicHeight": "6197.5"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./Op0sX5onA.map