// Generated by Fr<PERSON>r (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, Image, Link, resolveLink, RichText, SVG, useActiveVariantCallback, useRouter, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import HeaderLink from "https://framerusercontent.com/modules/3a5ypyWJemZDhFlR04nh/IzEOMmjqDBbQ58ggaRlP/bEbqHaZIR.js";
const HeaderLinkFonts = getFonts(HeaderLink);
const enabledGestures = {
  Ww9rXClsF: {
    hover: true
  }
};
const cycleOrder = ["Ww9rXClsF", "KYtWevcMb", "C0Y6XBtwC", "hpgTZmhZp", "FQfuR9HWa"];
const variantClassNames = {
  C0Y6XBtwC: "framer-v-iw7pb5",
  FQfuR9HWa: "framer-v-ijgsvd",
  hpgTZmhZp: "framer-v-1it8qt",
  KYtWevcMb: "framer-v-1gaqst8",
  Ww9rXClsF: "framer-v-13iaam4"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Features - click": "FQfuR9HWa",
  "Use cases - click": "hpgTZmhZp",
  "Use cases": "KYtWevcMb",
  Default: "Ww9rXClsF",
  Features: "C0Y6XBtwC"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "Ww9rXClsF",
  enter: kWfkMXjSU,
  title: QmkfTDQ0u = "Hosting",
  leave: BK1jQbG1J,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "Ww9rXClsF",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const router = useRouter();
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(baseVariant);
  const onMouseEnter124cfc6 = activeVariantCallback(async (...args) => {
    if (kWfkMXjSU) {
      const res = await kWfkMXjSU(...args);
      if (res === false) return false;
    }
  });
  const onMouseLeaveso37ii = activeVariantCallback(async (...args) => {
    if (BK1jQbG1J) {
      const res = await BK1jQbG1J(...args);
      if (res === false) return false;
    }
    setVariant("Ww9rXClsF");
  });
  const onTapuuqp83 = activeVariantCallback(async (...args) => {
    setVariant("hpgTZmhZp");
  });
  const onTap1h836u8 = activeVariantCallback(async (...args) => {
    setVariant("FQfuR9HWa");
  });
  const onTap1dah25d = activeVariantCallback(async (...args) => {
    setVariant("KYtWevcMb");
  });
  const onTapuut37e = activeVariantCallback(async (...args) => {
    setVariant("C0Y6XBtwC");
  });
  const isDisplayed1 = () => {
    if (baseVariant === "hpgTZmhZp") return true;
    return false;
  };
  const isDisplayed2 = () => {
    if (baseVariant === "hpgTZmhZp") return true;
    return false;
  };
  const isDisplayed3 = () => {
    if (baseVariant === "FQfuR9HWa") return true;
    return false;
  };
  const isDisplayed4 = () => {
    if (baseVariant === "FQfuR9HWa") return true;
    return false;
  };
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-NxZit", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsxs(motion.div, {
        ...restProps,
        className: cx("framer-13iaam4", className),
        "data-framer-name": "Default",
        "data-highlight": true,
        layoutDependency: layoutDependency,
        layoutId: "Ww9rXClsF",
        onMouseEnter: onMouseEnter124cfc6,
        onMouseLeave: onMouseLeaveso37ii,
        ref: ref,
        style: {
          backgroundColor: "rgba(0, 0, 0, 0)",
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          ...style
        },
        transition: transition,
        variants: {
          "Ww9rXClsF-hover": {
            backgroundColor: "rgb(255, 255, 255)"
          },
          C0Y6XBtwC: {
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0
          },
          FQfuR9HWa: {
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0
          },
          hpgTZmhZp: {
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0
          },
          KYtWevcMb: {
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0
          }
        },
        ...addPropertyOverrides({
          "Ww9rXClsF-hover": {
            "data-framer-name": undefined
          },
          C0Y6XBtwC: {
            "data-framer-name": "Features",
            onMouseEnter: undefined,
            onMouseLeave: undefined,
            onTap: onTap1h836u8
          },
          FQfuR9HWa: {
            "data-framer-name": "Features - click",
            "data-highlight": undefined,
            onMouseEnter: undefined,
            onMouseLeave: undefined
          },
          hpgTZmhZp: {
            "data-framer-name": "Use cases - click",
            "data-highlight": undefined,
            onMouseEnter: undefined,
            onMouseLeave: undefined
          },
          KYtWevcMb: {
            "data-framer-name": "Use cases",
            onMouseEnter: undefined,
            onMouseLeave: undefined,
            onTap: onTapuuqp83
          }
        }, baseVariant, gestureVariant),
        children: [/*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1sjhzl9",
          layoutDependency: layoutDependency,
          layoutId: "oeoHPWFGG",
          style: {
            backgroundColor: "rgb(255, 255, 255)",
            borderBottomLeftRadius: 6,
            borderBottomRightRadius: 6,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6
          },
          transition: transition,
          variants: {
            "Ww9rXClsF-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))"
            },
            C0Y6XBtwC: {
              backgroundColor: "rgba(0, 0, 0, 0)"
            },
            FQfuR9HWa: {
              backgroundColor: "rgba(0, 0, 0, 0)",
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0
            },
            hpgTZmhZp: {
              backgroundColor: "rgba(0, 0, 0, 0)",
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0
            },
            KYtWevcMb: {
              backgroundColor: "rgba(0, 0, 0, 0)",
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0
            }
          },
          ...addPropertyOverrides({
            FQfuR9HWa: {
              "data-highlight": true,
              onTap: onTapuut37e
            },
            hpgTZmhZp: {
              "data-highlight": true,
              onTap: onTap1dah25d
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--framer-letter-spacing": "-0.2px",
                  "--framer-line-height": "2.4em",
                  "--framer-text-alignment": "center",
                  "--framer-text-color": "var(--extracted-r6o4lv)"
                },
                children: "Hosting"
              })
            }),
            className: "framer-tmd0b8",
            layoutDependency: layoutDependency,
            layoutId: "owTS6M05_",
            style: {
              "--extracted-r6o4lv": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) ",
              "--framer-paragraph-spacing": "0px"
            },
            text: QmkfTDQ0u,
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-p22jzm",
            layoutDependency: layoutDependency,
            layoutId: "PlY_SYpy6",
            transition: transition,
            children: /*#__PURE__*/_jsx(SVG, {
              className: "framer-15gfcyx",
              "data-framer-name": "Forward",
              fill: "black",
              intrinsicHeight: 15,
              intrinsicWidth: 15,
              layoutDependency: layoutDependency,
              layoutId: "vr94sNMFJ",
              svg: '<svg width="15" height="15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.834 5.833 7.501 9.167 4.167 5.833" stroke="#202124" stroke-linecap="round" stroke-linejoin="round"/></svg>',
              transition: transition,
              variants: {
                FQfuR9HWa: {
                  rotate: -180
                },
                hpgTZmhZp: {
                  rotate: -180
                }
              },
              withExternalLayout: true
            })
          })]
        }), isDisplayed1() && /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-c4no4b",
          layoutDependency: layoutDependency,
          layoutId: "RWHiYCFy2",
          transition: transition,
          children: [/*#__PURE__*/_jsxs(motion.div, {
            className: "framer-vodn05",
            "data-framer-name": "private-tutoring",
            layoutDependency: layoutDependency,
            layoutId: "OMjWmU6ew",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Private tutoring"
                })
              }),
              className: "framer-ilr5m3",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "uyYgdTjqW",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Link, {
              href: "data:framer/page-link,eAoNDui3w",
              openInNewTab: false,
              children: /*#__PURE__*/_jsx(Image, {
                as: "a",
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 340,
                  intrinsicWidth: 543,
                  pixelHeight: 340,
                  pixelWidth: 543,
                  sizes: "270px",
                  src: new URL("https://framerusercontent.com/images/gBSJAos3NTrZIY9qLuoejWB84U.png").href,
                  srcSet: `${new URL("https://framerusercontent.com/images/gBSJAos3NTrZIY9qLuoejWB84U.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/gBSJAos3NTrZIY9qLuoejWB84U.png").href} 543w`
                },
                className: "framer-1lfss9b framer-jkq1mi",
                "data-border": true,
                "data-framer-name": "private-tutoring-image",
                layoutDependency: layoutDependency,
                layoutId: "IpXMhxx7l",
                style: {
                  "--border-bottom-width": "1px",
                  "--border-color": "rgb(218, 220, 224)",
                  "--border-left-width": "1px",
                  "--border-right-width": "1px",
                  "--border-style": "solid",
                  "--border-top-width": "1px",
                  borderBottomLeftRadius: 14,
                  borderBottomRightRadius: 14,
                  borderTopLeftRadius: 14,
                  borderTopRightRadius: 14
                },
                transition: transition
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-18h24fl",
            "data-framer-name": "group-classes",
            layoutDependency: layoutDependency,
            layoutId: "LXEJ9zpCz",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Group classes"
                })
              }),
              className: "framer-dke677",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "ynRnYbOj7",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Link, {
              href: "data:framer/page-link,QXYyFVKW8",
              children: /*#__PURE__*/_jsx(Image, {
                as: "a",
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 510,
                  intrinsicWidth: 813,
                  pixelHeight: 510,
                  pixelWidth: 813,
                  sizes: "270px",
                  src: new URL("https://framerusercontent.com/images/8NL2QEATHCTbAScCWZ4Nkp3pq4.png").href,
                  srcSet: `${new URL("https://framerusercontent.com/images/8NL2QEATHCTbAScCWZ4Nkp3pq4.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/8NL2QEATHCTbAScCWZ4Nkp3pq4.png").href} 813w`
                },
                className: "framer-1m1t19b framer-jkq1mi",
                "data-border": true,
                "data-framer-name": "group-classes-image",
                layoutDependency: layoutDependency,
                layoutId: "a6vgJOaLL",
                style: {
                  "--border-bottom-width": "1px",
                  "--border-color": "rgb(218, 220, 224)",
                  "--border-left-width": "1px",
                  "--border-right-width": "1px",
                  "--border-style": "solid",
                  "--border-top-width": "1px",
                  borderBottomLeftRadius: 14,
                  borderBottomRightRadius: 14,
                  borderTopLeftRadius: 14,
                  borderTopRightRadius: 14
                },
                transition: transition
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1m4ynyf",
            "data-framer-name": "marketplace",
            layoutDependency: layoutDependency,
            layoutId: "uAmD1vf2c",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Marketplace"
                })
              }),
              className: "framer-oexc91",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "YD2DGPNjU",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Link, {
              href: "/marketplace",
              openInNewTab: false,
              children: /*#__PURE__*/_jsx(Image, {
                as: "a",
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 340,
                  intrinsicWidth: 543,
                  pixelHeight: 340,
                  pixelWidth: 543,
                  sizes: "270px",
                  src: new URL("https://framerusercontent.com/images/RgQYE7n9Oecbef8E8PqKO2YGFhk.png").href,
                  srcSet: `${new URL("https://framerusercontent.com/images/RgQYE7n9Oecbef8E8PqKO2YGFhk.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/RgQYE7n9Oecbef8E8PqKO2YGFhk.png").href} 543w`
                },
                className: "framer-2v54d0 framer-jkq1mi",
                "data-border": true,
                "data-framer-name": "marketplace-image",
                layoutDependency: layoutDependency,
                layoutId: "KHvFVEmjd",
                style: {
                  "--border-bottom-width": "1px",
                  "--border-color": "rgb(218, 220, 224)",
                  "--border-left-width": "1px",
                  "--border-right-width": "1px",
                  "--border-style": "solid",
                  "--border-top-width": "1px",
                  borderBottomLeftRadius: 14,
                  borderBottomRightRadius: 14,
                  borderTopLeftRadius: 14,
                  borderTopRightRadius: 14
                },
                transition: transition
              })
            })]
          })]
        }), isDisplayed2() && /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1bo3vg3",
          "data-framer-name": "resources",
          layoutDependency: layoutDependency,
          layoutId: "AsjnajNBQ",
          transition: transition,
          children: [/*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                  "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                  "--framer-font-weight": "600"
                },
                children: "Resources"
              })
            }),
            className: "framer-7uah6o",
            fonts: ["Inter-SemiBold"],
            layoutDependency: layoutDependency,
            layoutId: "PfMXy7v0B",
            style: {
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-paragraph-spacing": "0px"
            },
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-1o2u2ul",
            "data-framer-name": "use-cases-resources",
            layoutDependency: layoutDependency,
            layoutId: "H2BZ1zAz4",
            transition: transition,
            children: /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1t1ljik",
              "data-framer-name": "use-cases-links-1",
              layoutDependency: layoutDependency,
              layoutId: "tu6wdXhUq",
              transition: transition,
              children: [/*#__PURE__*/_jsx(motion.div, {
                className: "framer-16lipo7-container",
                layoutDependency: layoutDependency,
                layoutId: "SvY4sgNbU-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "Get started with the only all-in-one tutoring platform.",
                  heading: "Become a tutor",
                  height: "100%",
                  id: "SvY4sgNbU",
                  layoutId: "SvY4sgNbU",
                  link: "https://popless.typeform.com/request-access",
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-t88zqw-container",
                layoutDependency: layoutDependency,
                layoutId: "A1wjOWSvV-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "Get answers to questions and a personalized demo.",
                  heading: "Request a demo",
                  height: "100%",
                  id: "A1wjOWSvV",
                  layoutId: "A1wjOWSvV",
                  link: "https://calendly.com/popless/intro",
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-pvymhr-container",
                layoutDependency: layoutDependency,
                layoutId: "IL2Fe8BjO-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "Learn more about popular topics and find resources.",
                  heading: "FAQ's",
                  height: "100%",
                  id: "IL2Fe8BjO",
                  layoutId: "IL2Fe8BjO",
                  link: resolveLink("data:framer/page-link,ijFjMFkqN", router),
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              })]
            })
          })]
        }), isDisplayed3() && /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-10pq8ee",
          layoutDependency: layoutDependency,
          layoutId: "QGjx97x0h",
          transition: transition,
          children: [/*#__PURE__*/_jsxs(motion.div, {
            className: "framer-1ud52b0",
            "data-framer-name": "feature-releases",
            layoutDependency: layoutDependency,
            layoutId: "rOxntK74T",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Feature releases"
                })
              }),
              className: "framer-1x1ijhj",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "lE6VthmFi",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Link, {
              href: "data:framer/page-link,qFQZWHHvr",
              children: /*#__PURE__*/_jsx(motion.a, {
                className: "framer-aw48to framer-jkq1mi",
                "data-border": true,
                "data-framer-name": "feature-releases-image",
                layoutDependency: layoutDependency,
                layoutId: "bVd_XjdP5",
                style: {
                  "--border-bottom-width": "1px",
                  "--border-color": "rgb(218, 220, 224)",
                  "--border-left-width": "1px",
                  "--border-right-width": "1px",
                  "--border-style": "solid",
                  "--border-top-width": "1px",
                  backgroundColor: "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  borderBottomLeftRadius: 14,
                  borderBottomRightRadius: 14,
                  borderTopLeftRadius: 14,
                  borderTopRightRadius: 14
                },
                transition: transition,
                children: /*#__PURE__*/_jsx(Image, {
                  background: {
                    alt: "",
                    fit: "fill",
                    intrinsicHeight: 866,
                    intrinsicWidth: 1914,
                    pixelHeight: 866,
                    pixelWidth: 1914,
                    sizes: "248px",
                    src: new URL("https://framerusercontent.com/images/E6jIayRQgiXA0hftkTFvm8gRz80.png").href,
                    srcSet: `${new URL("https://framerusercontent.com/images/E6jIayRQgiXA0hftkTFvm8gRz80.png?scale-down-to=512").href} 512w, ${new URL("https://framerusercontent.com/images/E6jIayRQgiXA0hftkTFvm8gRz80.png?scale-down-to=1024").href} 1024w, ${new URL("https://framerusercontent.com/images/E6jIayRQgiXA0hftkTFvm8gRz80.png").href} 1914w`
                  },
                  className: "framer-sbz1ht",
                  layoutDependency: layoutDependency,
                  layoutId: "gwomiW7l3",
                  style: {
                    borderBottomLeftRadius: 14,
                    borderBottomRightRadius: 14,
                    borderTopLeftRadius: 14,
                    borderTopRightRadius: 14
                  },
                  transition: transition
                })
              })
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-k33qt5",
            "data-framer-name": "ask-a-question",
            layoutDependency: layoutDependency,
            layoutId: "npQHiI1iC",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Ask a question"
                })
              }),
              className: "framer-ifbpjv",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "w5aGweJYb",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Image, {
              background: {
                alt: "",
                fit: "fill",
                intrinsicHeight: 340,
                intrinsicWidth: 440,
                pixelHeight: 340,
                pixelWidth: 440,
                src: new URL("https://framerusercontent.com/images/cmTmfpRnmVdUha2HVaiSPmgIfM.png").href
              },
              className: "framer-19mr09q",
              "data-border": true,
              "data-framer-name": "ask-a-question-image",
              layoutDependency: layoutDependency,
              layoutId: "kEW1Qhxmu",
              style: {
                "--border-bottom-width": "1px",
                "--border-color": "rgb(218, 220, 224)",
                "--border-left-width": "1px",
                "--border-right-width": "1px",
                "--border-style": "solid",
                "--border-top-width": "1px",
                borderBottomLeftRadius: 14,
                borderBottomRightRadius: 14,
                borderTopLeftRadius: 14,
                borderTopRightRadius: 14
              },
              transition: transition
            })]
          }), /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-60bpoe",
            "data-framer-name": "request-a-demo",
            layoutDependency: layoutDependency,
            layoutId: "L5qCn8yKp",
            transition: transition,
            children: [/*#__PURE__*/_jsx(RichText, {
              __fromCanvasComponent: true,
              children: /*#__PURE__*/_jsx(React.Fragment, {
                children: /*#__PURE__*/_jsx(motion.p, {
                  style: {
                    "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                    "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                    "--framer-font-weight": "600"
                  },
                  children: "Request a demo"
                })
              }),
              className: "framer-6qzle2",
              fonts: ["Inter-SemiBold"],
              layoutDependency: layoutDependency,
              layoutId: "g24y8KvbH",
              style: {
                "--framer-link-text-color": "rgb(0, 153, 255)",
                "--framer-link-text-decoration": "underline",
                "--framer-paragraph-spacing": "0px"
              },
              transition: transition,
              verticalAlignment: "top",
              withExternalLayout: true
            }), /*#__PURE__*/_jsx(Link, {
              href: "https://calendly.com/popless/intro",
              openInNewTab: false,
              children: /*#__PURE__*/_jsx(Image, {
                as: "a",
                background: {
                  alt: "",
                  fit: "fill",
                  intrinsicHeight: 340,
                  intrinsicWidth: 440,
                  pixelHeight: 340,
                  pixelWidth: 440,
                  src: new URL("https://framerusercontent.com/images/vZDLsckP0wKRscHV2Hq5IMDScU.png").href
                },
                className: "framer-434nab framer-jkq1mi",
                "data-border": true,
                "data-framer-name": "request-a-demo-image",
                layoutDependency: layoutDependency,
                layoutId: "WIn4aN1Ji",
                style: {
                  "--border-bottom-width": "1px",
                  "--border-color": "rgb(218, 220, 224)",
                  "--border-left-width": "1px",
                  "--border-right-width": "1px",
                  "--border-style": "solid",
                  "--border-top-width": "1px",
                  borderBottomLeftRadius: 14,
                  borderBottomRightRadius: 14,
                  borderTopLeftRadius: 14,
                  borderTopRightRadius: 14
                },
                transition: transition
              })
            })]
          })]
        }), isDisplayed4() && /*#__PURE__*/_jsxs(motion.div, {
          className: "framer-1enzmzm",
          "data-framer-name": "resources",
          layoutDependency: layoutDependency,
          layoutId: "Jr4aXcQHL",
          transition: transition,
          children: [/*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--font-selector": "SW50ZXItU2VtaUJvbGQ=",
                  "--framer-font-family": '"Inter-SemiBold", "Inter", sans-serif',
                  "--framer-font-weight": "600"
                },
                children: "Resources"
              })
            }),
            className: "framer-tb1ucn",
            fonts: ["Inter-SemiBold"],
            layoutDependency: layoutDependency,
            layoutId: "wvZEuepjF",
            style: {
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-paragraph-spacing": "0px"
            },
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true
          }), /*#__PURE__*/_jsx(motion.div, {
            className: "framer-14omi3l",
            "data-framer-name": "features-resources",
            layoutDependency: layoutDependency,
            layoutId: "fvMLIZmBT",
            transition: transition,
            children: /*#__PURE__*/_jsxs(motion.div, {
              className: "framer-1cpo9ik",
              "data-framer-name": "features-links-1",
              layoutDependency: layoutDependency,
              layoutId: "BexFIApwI",
              transition: transition,
              children: [/*#__PURE__*/_jsx(motion.div, {
                className: "framer-1b6956-container",
                layoutDependency: layoutDependency,
                layoutId: "FsZfeOhj4-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "We’re here to help. Learn more about popular topics.",
                  heading: "Support center",
                  height: "100%",
                  id: "FsZfeOhj4",
                  layoutId: "FsZfeOhj4",
                  link: "https://support.popless.com/",
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-uzxg7w-container",
                layoutDependency: layoutDependency,
                layoutId: "jLxFbaL7P-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "Get started with the only all-in-one tutoring platform.",
                  heading: "Become a tutor",
                  height: "100%",
                  id: "jLxFbaL7P",
                  layoutId: "jLxFbaL7P",
                  link: "https://popless.typeform.com/request-access",
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              }), /*#__PURE__*/_jsx(motion.div, {
                className: "framer-x5vku2-container",
                layoutDependency: layoutDependency,
                layoutId: "yaPMsYvww-container",
                transition: transition,
                children: /*#__PURE__*/_jsx(HeaderLink, {
                  body: "See how Popless compares to tutoring marketplaces.",
                  heading: "Compare features",
                  height: "100%",
                  id: "yaPMsYvww",
                  layoutId: "yaPMsYvww",
                  link: resolveLink("data:framer/page-link,LycHsr6mA", router),
                  style: {
                    width: "100%"
                  },
                  width: "100%"
                })
              })]
            })
          })]
        })]
      })
    })
  });
});
const css = ['.framer-NxZit [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-NxZit * { box-sizing: border-box; }", ".framer-NxZit .framer-jkq1mi { display: block; }", ".framer-NxZit .framer-13iaam4 { align-content: center; align-items: center; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-1sjhzl9 { align-content: center; align-items: center; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 4px; height: 42px; justify-content: center; overflow: visible; padding: 0px 10px 0px 10px; position: relative; width: min-content; }", ".framer-NxZit .framer-tmd0b8 { flex: none; height: 38px; overflow: hidden; position: relative; white-space: pre; width: auto; }", ".framer-NxZit .framer-p22jzm { flex: none; height: 15px; overflow: visible; position: relative; width: 15px; }", ".framer-NxZit .framer-15gfcyx { aspect-ratio: 1 / 1; bottom: -1px; flex: none; height: var(--framer-aspect-ratio-supported, 15px); left: 0px; position: absolute; right: 0px; }", ".framer-NxZit .framer-1mrugai, .framer-NxZit .framer-c4no4b { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: center; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-780v0f { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 200px; }", ".framer-NxZit .framer-8acdos, .framer-NxZit .framer-10pq8ee { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-gwnjsr { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: 460px; }", ".framer-NxZit .framer-vodn05, .framer-NxZit .framer-18h24fl, .framer-NxZit .framer-1m4ynyf, .framer-NxZit .framer-1bo3vg3, .framer-NxZit .framer-1ud52b0, .framer-NxZit .framer-k33qt5, .framer-NxZit .framer-60bpoe, .framer-NxZit .framer-1enzmzm { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-ilr5m3, .framer-NxZit .framer-dke677, .framer-NxZit .framer-oexc91, .framer-NxZit .framer-7uah6o, .framer-NxZit .framer-1x1ijhj, .framer-NxZit .framer-ifbpjv, .framer-NxZit .framer-6qzle2, .framer-NxZit .framer-tb1ucn { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-NxZit .framer-1lfss9b, .framer-NxZit .framer-1m1t19b, .framer-NxZit .framer-2v54d0 { aspect-ratio: 1.588235294117647 / 1; flex: none; height: var(--framer-aspect-ratio-supported, 170px); overflow: hidden; position: relative; text-decoration: none; width: 270px; will-change: transform; }", ".framer-NxZit .framer-1o2u2ul, .framer-NxZit .framer-14omi3l { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-1t1ljik, .framer-NxZit .framer-1cpo9ik { align-content: flex-start; align-items: flex-start; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: min-content; justify-content: center; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; width: min-content; }", ".framer-NxZit .framer-16lipo7-container, .framer-NxZit .framer-t88zqw-container, .framer-NxZit .framer-pvymhr-container, .framer-NxZit .framer-1b6956-container, .framer-NxZit .framer-uzxg7w-container, .framer-NxZit .framer-x5vku2-container { flex: none; height: auto; position: relative; width: 270px; }", ".framer-NxZit .framer-aw48to, .framer-NxZit .framer-434nab { flex: none; height: 170px; overflow: hidden; position: relative; text-decoration: none; width: 270px; will-change: transform; }", ".framer-NxZit .framer-sbz1ht { bottom: 0px; flex: none; height: 114px; left: calc(50.00000000000002% - 248px / 2); overflow: visible; position: absolute; width: 248px; }", ".framer-NxZit .framer-19mr09q { flex: none; height: 170px; overflow: hidden; position: relative; width: 270px; will-change: transform; }", ".framer-NxZit .framer-v-13iaam4 .framer-13iaam4 { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NxZit .framer-13iaam4, .framer-NxZit .framer-1sjhzl9, .framer-NxZit .framer-1mrugai, .framer-NxZit .framer-780v0f, .framer-NxZit .framer-8acdos, .framer-NxZit .framer-gwnjsr, .framer-NxZit .framer-c4no4b, .framer-NxZit .framer-vodn05, .framer-NxZit .framer-18h24fl, .framer-NxZit .framer-1m4ynyf, .framer-NxZit .framer-1bo3vg3, .framer-NxZit .framer-1o2u2ul, .framer-NxZit .framer-1t1ljik, .framer-NxZit .framer-10pq8ee, .framer-NxZit .framer-1ud52b0, .framer-NxZit .framer-k33qt5, .framer-NxZit .framer-60bpoe, .framer-NxZit .framer-1enzmzm, .framer-NxZit .framer-14omi3l, .framer-NxZit .framer-1cpo9ik { gap: 0px; } .framer-NxZit .framer-13iaam4 > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-NxZit .framer-13iaam4 > :first-child, .framer-NxZit .framer-1mrugai > :first-child, .framer-NxZit .framer-780v0f > :first-child, .framer-NxZit .framer-8acdos > :first-child, .framer-NxZit .framer-gwnjsr > :first-child, .framer-NxZit .framer-c4no4b > :first-child, .framer-NxZit .framer-vodn05 > :first-child, .framer-NxZit .framer-18h24fl > :first-child, .framer-NxZit .framer-1m4ynyf > :first-child, .framer-NxZit .framer-1bo3vg3 > :first-child, .framer-NxZit .framer-1t1ljik > :first-child, .framer-NxZit .framer-10pq8ee > :first-child, .framer-NxZit .framer-1ud52b0 > :first-child, .framer-NxZit .framer-k33qt5 > :first-child, .framer-NxZit .framer-60bpoe > :first-child, .framer-NxZit .framer-1enzmzm > :first-child, .framer-NxZit .framer-1cpo9ik > :first-child { margin-top: 0px; } .framer-NxZit .framer-13iaam4 > :last-child, .framer-NxZit .framer-1mrugai > :last-child, .framer-NxZit .framer-780v0f > :last-child, .framer-NxZit .framer-8acdos > :last-child, .framer-NxZit .framer-gwnjsr > :last-child, .framer-NxZit .framer-c4no4b > :last-child, .framer-NxZit .framer-vodn05 > :last-child, .framer-NxZit .framer-18h24fl > :last-child, .framer-NxZit .framer-1m4ynyf > :last-child, .framer-NxZit .framer-1bo3vg3 > :last-child, .framer-NxZit .framer-1t1ljik > :last-child, .framer-NxZit .framer-10pq8ee > :last-child, .framer-NxZit .framer-1ud52b0 > :last-child, .framer-NxZit .framer-k33qt5 > :last-child, .framer-NxZit .framer-60bpoe > :last-child, .framer-NxZit .framer-1enzmzm > :last-child, .framer-NxZit .framer-1cpo9ik > :last-child { margin-bottom: 0px; } .framer-NxZit .framer-1sjhzl9 > * { margin: 0px; margin-left: calc(4px / 2); margin-right: calc(4px / 2); } .framer-NxZit .framer-1sjhzl9 > :first-child, .framer-NxZit .framer-1o2u2ul > :first-child, .framer-NxZit .framer-14omi3l > :first-child { margin-left: 0px; } .framer-NxZit .framer-1sjhzl9 > :last-child, .framer-NxZit .framer-1o2u2ul > :last-child, .framer-NxZit .framer-14omi3l > :last-child { margin-right: 0px; } .framer-NxZit .framer-1mrugai > *, .framer-NxZit .framer-8acdos > *, .framer-NxZit .framer-c4no4b > *, .framer-NxZit .framer-1t1ljik > *, .framer-NxZit .framer-10pq8ee > *, .framer-NxZit .framer-1cpo9ik > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-NxZit .framer-780v0f > *, .framer-NxZit .framer-gwnjsr > *, .framer-NxZit .framer-vodn05 > *, .framer-NxZit .framer-18h24fl > *, .framer-NxZit .framer-1m4ynyf > *, .framer-NxZit .framer-1bo3vg3 > *, .framer-NxZit .framer-1ud52b0 > *, .framer-NxZit .framer-k33qt5 > *, .framer-NxZit .framer-60bpoe > *, .framer-NxZit .framer-1enzmzm > * { margin: 0px; margin-bottom: calc(20px / 2); margin-top: calc(20px / 2); } .framer-NxZit .framer-1o2u2ul > *, .framer-NxZit .framer-14omi3l > * { margin: 0px; margin-left: calc(40px / 2); margin-right: calc(40px / 2); } }", ".framer-NxZit.framer-v-1gaqst8 .framer-13iaam4, .framer-NxZit.framer-v-iw7pb5 .framer-13iaam4 { align-content: flex-start; align-items: flex-start; cursor: pointer; width: 150px; }", ".framer-NxZit.framer-v-1gaqst8 .framer-1sjhzl9, .framer-NxZit.framer-v-iw7pb5 .framer-1sjhzl9 { gap: unset; justify-content: space-between; padding: 0px 0px 0px 0px; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NxZit.framer-v-1gaqst8 .framer-1sjhzl9 { gap: 0px; } .framer-NxZit.framer-v-1gaqst8 .framer-1sjhzl9 > *, .framer-NxZit.framer-v-1gaqst8 .framer-1sjhzl9 > :first-child, .framer-NxZit.framer-v-1gaqst8 .framer-1sjhzl9 > :last-child { margin: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NxZit.framer-v-iw7pb5 .framer-1sjhzl9 { gap: 0px; } .framer-NxZit.framer-v-iw7pb5 .framer-1sjhzl9 > *, .framer-NxZit.framer-v-iw7pb5 .framer-1sjhzl9 > :first-child, .framer-NxZit.framer-v-iw7pb5 .framer-1sjhzl9 > :last-child { margin: 0px; } }", ".framer-NxZit.framer-v-1it8qt .framer-13iaam4, .framer-NxZit.framer-v-ijgsvd .framer-13iaam4 { align-content: flex-start; align-items: flex-start; gap: 30px; justify-content: flex-start; padding: 0px 0px 30px 0px; }", ".framer-NxZit.framer-v-1it8qt .framer-1sjhzl9, .framer-NxZit.framer-v-ijgsvd .framer-1sjhzl9 { align-self: stretch; cursor: pointer; gap: unset; justify-content: space-between; padding: 0px 0px 0px 0px; width: auto; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NxZit.framer-v-1it8qt .framer-13iaam4, .framer-NxZit.framer-v-1it8qt .framer-1sjhzl9 { gap: 0px; } .framer-NxZit.framer-v-1it8qt .framer-13iaam4 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-NxZit.framer-v-1it8qt .framer-13iaam4 > :first-child { margin-top: 0px; } .framer-NxZit.framer-v-1it8qt .framer-13iaam4 > :last-child { margin-bottom: 0px; } .framer-NxZit.framer-v-1it8qt .framer-1sjhzl9 > *, .framer-NxZit.framer-v-1it8qt .framer-1sjhzl9 > :first-child, .framer-NxZit.framer-v-1it8qt .framer-1sjhzl9 > :last-child { margin: 0px; } }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-NxZit.framer-v-ijgsvd .framer-13iaam4, .framer-NxZit.framer-v-ijgsvd .framer-1sjhzl9 { gap: 0px; } .framer-NxZit.framer-v-ijgsvd .framer-13iaam4 > * { margin: 0px; margin-bottom: calc(30px / 2); margin-top: calc(30px / 2); } .framer-NxZit.framer-v-ijgsvd .framer-13iaam4 > :first-child { margin-top: 0px; } .framer-NxZit.framer-v-ijgsvd .framer-13iaam4 > :last-child { margin-bottom: 0px; } .framer-NxZit.framer-v-ijgsvd .framer-1sjhzl9 > *, .framer-NxZit.framer-v-ijgsvd .framer-1sjhzl9 > :first-child, .framer-NxZit.framer-v-ijgsvd .framer-1sjhzl9 > :last-child { margin: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        * @framerIntrinsicHeight 42
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        * @framerIntrinsicWidth 96
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]},"KYtWevcMb":{"layout":["fixed","auto"]},"C0Y6XBtwC":{"layout":["fixed","auto"]},"hpgTZmhZp":{"layout":["auto","auto"]},"FQfuR9HWa":{"layout":["auto","auto"]},"QXnQX5_M9":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        * @framerVariables {"kWfkMXjSU":"enter","QmkfTDQ0u":"title","BK1jQbG1J":"leave"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        */
const FramerrAZkTz6LY = withCSS(Component, css);
export default FramerrAZkTz6LY;
FramerrAZkTz6LY.displayName = "Menu Item - v2";
FramerrAZkTz6LY.defaultProps = {
  height: 42,
  width: 96
};
addPropertyControls(FramerrAZkTz6LY, {
  variant: {
    options: ["Ww9rXClsF", "KYtWevcMb", "C0Y6XBtwC", "hpgTZmhZp", "FQfuR9HWa"],
    optionTitles: ["Default", "Use cases", "Features", "Use cases - click", "Features - click"],
    title: "Variant",
    type: ControlType.Enum
  },
  kWfkMXjSU: {
    title: "Enter",
    type: ControlType.EventHandler
  },
  QmkfTDQ0u: {
    defaultValue: "Hosting",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  BK1jQbG1J: {
    title: "Leave",
    type: ControlType.EventHandler
  }
});
addFonts(FramerrAZkTz6LY, [...HeaderLinkFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerrAZkTz6LY",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "42",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]},\"KYtWevcMb\":{\"layout\":[\"fixed\",\"auto\"]},\"C0Y6XBtwC\":{\"layout\":[\"fixed\",\"auto\"]},\"hpgTZmhZp\":{\"layout\":[\"auto\",\"auto\"]},\"FQfuR9HWa\":{\"layout\":[\"auto\",\"auto\"]},\"QXnQX5_M9\":{\"layout\":[\"auto\",\"auto\"]}}}",
        "framerIntrinsicWidth": "96",
        "framerContractVersion": "1",
        "framerVariables": "{\"kWfkMXjSU\":\"enter\",\"QmkfTDQ0u\":\"title\",\"BK1jQbG1J\":\"leave\"}"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./rAZkTz6LY.map