import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter-Bold"]);
export const fonts = [];
export const css = ['.framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; }', '@media (max-width: 1279px) and (min-width: 810px) { .framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; } }'];
export const className = "framer-sRzif";
export const __FramerMetadata__ = {
  "exports": {
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};