import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors([]);
export const fonts = [];
export const css = [".framer-qPr25 .framer-styles-preset-1vs0812:not(.rich-text-wrapper), .framer-qPr25 .framer-styles-preset-1vs0812.rich-text-wrapper a { --framer-link-text-color: #ffffff; --framer-link-text-decoration: none; --framer-link-hover-text-color: #A8A8A8; --framer-link-hover-text-decoration: none; }"];
export const className = "framer-qPr25";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};