import { jsx as _jsx } from "react/jsx-runtime";
import { useRef, useEffect, useMemo, useCallback, memo } from "react";
import { ControlType, RenderTarget, addPropertyControls, useIsInCurrentNavigationTarget } from "framer";
import { cachedResponse, corsProxy, hashCode, checkForCachedData } from "https://framer.com/m/framer/default-video-utils.js";
import { useOnEnter, useOnExit, defaultEvents, useAutoMotionValue, useIsBrowserSafari } from "https://framer.com/m/framer/default-utils.js@^0.45.0";
var ObjectFitType;
(function (ObjectFitType) {
  ObjectFitType["Fill"] = "fill";
  ObjectFitType["Contain"] = "contain";
  ObjectFitType["Cover"] = "cover";
  ObjectFitType["None"] = "none";
  ObjectFitType["ScaleDown"] = "scale-down";
})(ObjectFitType || (ObjectFitType = {}));
var LoopType;
(function (LoopType) {
  LoopType["StartTime"] = "startTime";
  LoopType["Beginning"] = "beginning";
  LoopType["NoLoop"] = "noLoop";
})(LoopType || (LoopType = {}));
var PreloadType;
(function (PreloadType) {
  PreloadType["None"] = "none";
  PreloadType["MetaData"] = "metadata";
  PreloadType["Auto"] = "auto";
  PreloadType["ForceCache"] = "force";
})(PreloadType || (PreloadType = {}));
var SrcType;
(function (SrcType) {
  SrcType["Video"] = "Upload";
  SrcType["Url"] = "URL";
})(SrcType || (SrcType = {})); // Reduce renders
function getProps(props) {
  const {
    width,
    height,
    topLeft,
    topRight,
    bottomRight,
    bottomLeft,
    id,
    children,
    ...rest
  } = props;
  return rest;
} /**
  * VIDEO
  *
  * @framerIntrinsicWidth 200
  * @framerIntrinsicHeight 200
  *
  * @framerSupportedLayoutWidth fixed
  * @framerSupportedLayoutHeight any
  */
export function Video(props) {
  const newProps = getProps(props);
  return /*#__PURE__*/_jsx(VideoMemo, {
    ...newProps
  });
}
const VideoMemo = /*#__PURE__*/memo(function VideoInner(props) {
  const {
    srcType,
    srcFile,
    srcUrl,
    playing,
    canvasPlay,
    loopType,
    muted,
    playsinline,
    controls,
    preload,
    progress,
    objectFit,
    backgroundColor,
    radius,
    topLeft,
    topRight,
    bottomRight,
    bottomLeft,
    isMixed,
    onSeeked,
    onPause,
    onPlay,
    onEnd,
    onClick,
    onMouseEnter,
    onMouseLeave,
    onMouseDown,
    onMouseUp,
    poster,
    restartOnEnter,
    posterEnabled,
    startTime: startTimeProp,
    volume
  } = props;
  const isInCurrentNavigationTarget = useIsInCurrentNavigationTarget(); // video elements behave oddly at 100% duration
  const startTime = startTimeProp === 100 ? 99.9 : startTimeProp;
  const videoRef = useRef();
  const isLoaded = useRef(false);
  const isSafari = useIsBrowserSafari();
  const wasPausedOnLeave = useRef(null);
  const wasEndedOnLeave = useRef(null);
  const loop = loopType !== LoopType.NoLoop;
  const fullLoop = loopType === LoopType.Beginning;
  const isCanvas = useMemo(() => RenderTarget.current() !== RenderTarget.preview, []);
  const isForcedCache = preload === PreloadType.ForceCache;
  const isAutoCache = preload === PreloadType.Auto;
  const isMuted = useMemo(() => isCanvas ? true : muted, [isCanvas, muted]);
  const shouldPlay = !isCanvas || canvasPlay;
  const autoPlay = useMemo(() => playing, []);
  const play = useCallback(() => {
    var ref;
    if (isInCurrentNavigationTarget) (ref = videoRef.current) === null || ref === void 0 ? void 0 : ref.play();
  }, []);
  const pause = useCallback(() => {
    var ref;
    return (ref = videoRef.current) === null || ref === void 0 ? void 0 : ref.pause();
  }, []);
  const restartVideo = useCallback((playAfter = true) => {
    if (!fullLoop) setProgress(startTime, playAfter);else play();
  }, [startTime, fullLoop]);
  const setProgress = (newProgress, playAfter = false) => {
    if (videoRef.current) {
      const isAlreadySet = Math.abs(videoRef.current.currentTime - newProgress * 0.01 * videoRef.current.duration) < 0.3;
      if (videoRef.current.duration > 0 && !isAlreadySet) videoRef.current.currentTime = newProgress * 0.01 * videoRef.current.duration;
      if (autoPlay && shouldPlay && playAfter) play();
    }
  }; // Change progress via prop
  useEffect(() => {
    setProgress(startTime);
  }, [startTimeProp, srcFile, srcUrl]);
  const videoProgress = useAutoMotionValue(progress, {
    transform: value => value * 0.01,
    onChange: (newValue, value) => {
      setProgress(newValue);
    }
  }); // Checking if we need to play on navigation enter
  useOnEnter(() => {
    if (wasPausedOnLeave.current === null) return;
    if (videoRef.current) {
      if (restartOnEnter) restartVideo(!wasPausedOnLeave.current || wasEndedOnLeave.current);else if (!wasEndedOnLeave && loop || !wasPausedOnLeave.current) play();
    }
  }); // Pausing & saving playing state on navigation exit
  useOnExit(() => {
    if (videoRef.current) {
      wasEndedOnLeave.current = videoRef.current.ended;
      wasPausedOnLeave.current = videoRef.current.paused;
      pause();
    }
  });
  const getUrl = useCallback((cors = false) => {
    if (srcType === SrcType.Url) {
      return cors ? corsProxy(srcUrl) : srcUrl;
    }
    if (srcType === SrcType.Video) {
      return srcFile;
    }
  }, [srcType, srcFile, srcUrl]); // Logic for cache options
  const setVideoRef = useCallback(async element => {
    if (!element) return;
    videoRef.current = element;
    if (isSafari) {
      videoRef.current["src"] = getUrl();
      return;
    }
    if (preload === PreloadType.ForceCache) {
      if (isLoaded.current) return;
      const url = getUrl(true);
      const response = await cachedResponse(url);
      if (response && videoRef.current) {
        videoRef.current["src"] = URL.createObjectURL(response) // IE10+
        ;
        isLoaded.current = true;
      }
    } else if (preload === PreloadType.Auto) {
      if (isLoaded.current) return;
      const url = getUrl(true);
      const response = await checkForCachedData(url);
      if (response && videoRef.current) videoRef.current["src"] = URL.createObjectURL(response);else videoRef.current["src"] = getUrl();
      isLoaded.current = true;
    }
  }, [preload]); // Trigger rerender & reload when key props change
  useEffect(() => {
    if (isCanvas) isLoaded.current = false;
    setVideoRef(videoRef.current);
  }, [srcFile, srcUrl, srcType, posterEnabled, canvasPlay, preload, loop, autoPlay]); // Pause/play via props
  useEffect(() => {
    if (playing && shouldPlay) play();else pause();
  }, [playing]); // Autoplay via JS to work in Safari
  useEffect(() => {
    if (isSafari && videoRef.current && autoPlay) {
      setTimeout(() => {
        play();
      }, 50);
    }
  }, []); // Volume Control
  useEffect(() => {
    if (videoRef.current && !muted) videoRef.current.volume = volume / 100;
  }, [volume]);
  const key = useMemo(() => hashCode(JSON.stringify({
    srcType,
    srcUrl,
    srcFile,
    autoPlay,
    canvasPlay,
    isForcedCache
  })), [srcType, srcUrl, srcFile, autoPlay, canvasPlay, isForcedCache]);
  const borderRadius = isMixed ? `${topLeft}px ${topRight}px ${bottomRight}px ${bottomLeft}px` : `${radius}px`;
  const src = useMemo(() => (isForcedCache || isAutoCache) && !isSafari ? null : getUrl(), [isSafari, isAutoCache, isForcedCache]);
  return /*#__PURE__*/_jsx("video", {
    autoPlay: autoPlay && shouldPlay,
    ref: setVideoRef,
    onClick,
    onMouseEnter,
    onMouseLeave,
    onMouseDown,
    onMouseUp,
    poster: posterEnabled ? poster : undefined,
    style: {
      width: "100%",
      height: "100%",
      borderRadius,
      display: "block",
      objectFit: objectFit,
      backgroundColor: backgroundColor,
      objectPosition: "50% 50%"
    },
    onSeeked: e => {
      if (onSeeked) onSeeked(e);
    },
    onPause: e => {
      if (onPause) onPause(e);
    },
    onPlay: e => {
      if (onPlay) onPlay(e);
    },
    onEnded: e => {
      if (onEnd) onEnd(e);
      if (loop && shouldPlay && videoRef.current) restartVideo();
    },
    onCanPlay: () => {
      if (shouldPlay && videoRef.current && autoPlay) play();else pause();
      if (videoRef.current && videoRef.current.currentTime < 0.3) setProgress(startTime);
    },
    src: src,
    controls: isCanvas ? false : controls,
    muted: isMuted,
    playsInline: playsinline
  }, key);
});
Video.defaultProps = {
  srcType: SrcType.Url,
  srcUrl: "https://assets.mixkit.co/videos/preview/mixkit-ice-cream-glass-of-red-soda-5094-small.mp4",
  srcFile: "",
  posterEnabled: true,
  poster: "https://misc.framerstatic.com/components/video-poster.jpg",
  controls: false,
  autoPlay: true,
  canvasPlay: false,
  fullLoop: false,
  muted: true,
  playsinline: true,
  restartOnEnter: false,
  preload: PreloadType.Auto,
  objectFit: ObjectFitType.Cover,
  backgroundColor: "rgba(0,0,0,0)",
  radius: 0,
  volume: 25,
  startTime: 0
};
addPropertyControls(Video, {
  srcType: {
    type: ControlType.Enum,
    displaySegmentedControl: true,
    title: "Source",
    options: [SrcType.Url, SrcType.Video]
  },
  srcUrl: {
    type: ControlType.String,
    title: " ",
    placeholder: "../example.mp4",
    hidden(props) {
      return props.srcType === SrcType.Video;
    }
  },
  srcFile: {
    type: ControlType.File,
    title: " ",
    allowedFileTypes: ["mp4"],
    hidden(props) {
      return props.srcType === SrcType.Url;
    }
  },
  playing: {
    type: ControlType.Boolean,
    title: "Playing",
    enabledTitle: "Yes",
    disabledTitle: "No"
  },
  posterEnabled: {
    type: ControlType.Boolean,
    title: "Poster",
    enabledTitle: "Yes",
    disabledTitle: "No"
  },
  poster: {
    type: ControlType.Image,
    title: " ",
    defaultValue: Video.defaultProps.poster,
    hidden: ({
      posterEnabled
    }) => !posterEnabled
  },
  backgroundColor: {
    type: ControlType.Color,
    title: "Background"
  },
  radius: {
    title: "Radius",
    type: ControlType.FusedNumber,
    toggleKey: "isMixed",
    toggleTitles: ["Radius", "Radius per corner"],
    valueKeys: ["topLeft", "topRight", "bottomRight", "bottomLeft"],
    valueLabels: ["TL", "TR", "BR", "BL"],
    min: 0
  },
  startTime: {
    title: "Start Time",
    type: ControlType.Number,
    min: 0,
    max: 100,
    step: 0.1,
    unit: "%"
  },
  loopType: {
    type: ControlType.Enum,
    title: "Loop",
    optionTitles: ["From Start Time", "From Beginning", "Don't Loop"],
    options: [LoopType.StartTime, LoopType.Beginning, LoopType.NoLoop]
  },
  objectFit: {
    type: ControlType.Enum,
    title: "Fit",
    options: [ObjectFitType.Cover, ObjectFitType.Fill, ObjectFitType.Contain, ObjectFitType.ScaleDown, ObjectFitType.None]
  },
  canvasPlay: {
    type: ControlType.Boolean,
    title: "On Canvas",
    enabledTitle: "Play",
    disabledTitle: "Pause",
    hidden(props) {
      return props.autoPlay === false;
    }
  },
  restartOnEnter: {
    type: ControlType.Boolean,
    title: "On ReEnter",
    enabledTitle: "Restart",
    disabledTitle: "Resume"
  },
  controls: {
    type: ControlType.Boolean,
    title: "Controls",
    enabledTitle: "Show",
    disabledTitle: "Hide"
  },
  muted: {
    type: ControlType.Boolean,
    title: "Muted",
    enabledTitle: "Yes",
    disabledTitle: "No"
  },
  volume: {
    type: ControlType.Number,
    max: 100,
    min: 0,
    unit: "%",
    hidden: ({
      muted
    }) => muted
  },
  // playsinline: { type: ControlType.Boolean, title: "Inline", enabledTitle: "Yes", disabledTitle: "No" },
  preload: {
    type: ControlType.Enum,
    title: "Cache",
    options: [PreloadType.Auto, PreloadType.None, PreloadType.ForceCache]
  },
  onEnd: {
    type: ControlType.EventHandler
  },
  onSeeked: {
    type: ControlType.EventHandler
  },
  onPause: {
    type: ControlType.EventHandler
  },
  onPlay: {
    type: ControlType.EventHandler
  },
  ...defaultEvents
});
export const __FramerMetadata__ = {
  "exports": {
    "Video": {
      "type": "reactComponent",
      "name": "Video",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerIntrinsicHeight": "200",
        "framerSupportedLayoutHeight": "any",
        "framerIntrinsicWidth": "200",
        "framerSupportedLayoutWidth": "fixed"
      }
    },
    "VideoProps": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./Video.map