import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["GF;Meow Script-regular"]);
export const fonts = [{
  url: "https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ESch0_0CfDf1.ttf",
  family: "Meow Script",
  style: "normal",
  weight: "400",
  moduleAsset: {
    url: "https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ESch0_0CfDf1.ttf",
    localModuleIdentifier: "local-module:css/EciIwMErV:default"
  }
}];
export const css = ['.framer-Iyk50 .framer-styles-preset-24blvi:not(.rich-text-wrapper), .framer-Iyk50 .framer-styles-preset-24blvi.rich-text-wrapper h3 { --framer-font-family: "Meow Script", serif; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, #ffffff); --framer-font-size: 28px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 1.4em; --framer-text-alignment: right; --framer-paragraph-spacing: 0px; }', '@media (max-width: 1279px) and (min-width: 810px) { .framer-Iyk50 .framer-styles-preset-24blvi:not(.rich-text-wrapper), .framer-Iyk50 .framer-styles-preset-24blvi.rich-text-wrapper h3 { --framer-font-family: "Meow Script", serif; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, #ffffff); --framer-font-size: 28px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 1.4em; --framer-text-alignment: right; --framer-paragraph-spacing: 0px; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-Iyk50 .framer-styles-preset-24blvi:not(.rich-text-wrapper), .framer-Iyk50 .framer-styles-preset-24blvi.rich-text-wrapper h3 { --framer-font-family: "Meow Script", serif; --framer-font-style: normal; --framer-font-weight: 400; --framer-text-color: var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, #ffffff); --framer-font-size: 28px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 1.4em; --framer-text-alignment: right; --framer-paragraph-spacing: 0px; } }'];
export const className = "framer-Iyk50";
export const __FramerMetadata__ = {
  "exports": {
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};