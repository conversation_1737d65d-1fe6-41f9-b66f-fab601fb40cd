import { jsx as _jsx } from "react/jsx-runtime"; // Generated by Framer (1e4a7f1)
import { addFonts, addPropertyControls, ControlType, cx, RichText, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  RkuQdhlTZ: {
    hover: true
  },
  TfJ77UFlF: {
    hover: true
  }
};
const cycleOrder = ["TfJ77UFlF", "RkuQdhlTZ"];
const variantClassNames = {
  RkuQdhlTZ: "framer-v-1uvdq",
  TfJ77UFlF: "framer-v-y5w3le"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  "Ask a Question - Footer": "TfJ77UFlF",
  "Request a demo": "RkuQdhlTZ"
};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .1,
    ease: [0, 0, 1, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "TfJ77UFlF",
  title: TtVIfkkob = "",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "TfJ77UFlF",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-1BGCh", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(motion.div, {
        ...restProps,
        background: null,
        className: cx("framer-y5w3le", className),
        "data-framer-name": "Ask a Question - Footer",
        layoutDependency: layoutDependency,
        layoutId: "TfJ77UFlF",
        ref: ref,
        style: {
          ...style
        },
        transition: transition,
        ...addPropertyOverrides({
          RkuQdhlTZ: {
            "data-framer-name": "Request a demo",
            background: null
          }
        }, baseVariant, gestureVariant),
        children: /*#__PURE__*/_jsx(RichText, {
          __fromCanvasComponent: true,
          __htmlStructure: '<p style="--framer-font-size:14px; --framer-line-height:22px;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:400; --font-selector:R0Y7SW50ZXItcmVndWxhcg==; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:14px;">{{ text-placeholder }}</span></p>',
          className: "framer-139vnnw",
          fonts: ["GF;Inter-regular"],
          htmlFromDesign: '<p style="--framer-font-size:14px; --framer-line-height:22px;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:400; --font-selector:R0Y7SW50ZXItcmVndWxhcg==; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:14px;">Ask a question</span></p>',
          layoutDependency: layoutDependency,
          layoutId: "zKHbiV98D",
          style: {
            "--extracted-1w3ko1f": "rgb(255, 255, 255)",
            "--framer-paragraph-spacing": "0px"
          },
          textFromDesign: TtVIfkkob,
          transformTemplate: (_, t) => `translate(-50%, -50%) ${t}`,
          transition: transition,
          variants: {
            "RkuQdhlTZ-hover": {
              "--extracted-1w3ko1f": "rgb(168, 168, 168)"
            },
            "TfJ77UFlF-hover": {
              "--extracted-1w3ko1f": "rgb(168, 168, 168)"
            }
          },
          verticalAlignment: "top",
          withExternalLayout: true,
          ...addPropertyOverrides({
            "RkuQdhlTZ-hover": {
              __htmlStructure: '<p style="--framer-font-size:14px; --framer-line-height:22px;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:400; --font-selector:R0Y7SW50ZXItcmVndWxhcg==; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:14px;">{{ text-placeholder }}</span></p>',
              fonts: ["GF;Inter-regular"]
            },
            "TfJ77UFlF-hover": {
              __htmlStructure: '<p style="--framer-font-size:14px; --framer-line-height:22px;"><span style="--framer-font-family:&quot;Inter&quot;, sans-serif; --framer-font-style:normal; --framer-font-weight:400; --font-selector:R0Y7SW50ZXItcmVndWxhcg==; --framer-text-color:var(--extracted-1w3ko1f); --framer-font-size:14px;">{{ text-placeholder }}</span></p>',
              fonts: ["GF;Inter-regular"]
            }
          }, baseVariant, gestureVariant)
        })
      })
    })
  });
});
const css = ['.framer-1BGCh [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-1BGCh * { box-sizing: border-box; }", ".framer-1BGCh .framer-y5w3le { height: 22px; overflow: visible; position: relative; width: 98px; }", ".framer-1BGCh .framer-139vnnw { flex: none; height: auto; left: 50%; overflow: visible; position: absolute; top: 50%; white-space: pre; width: auto; }", ".framer-1BGCh.framer-v-y5w3le .framer-y5w3le, .framer-1BGCh.framer-v-1uvdq .framer-y5w3le { cursor: pointer; }", ".framer-1BGCh.framer-v-1uvdq .framer-y5w3le { height: 22px; width: 108px; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicHeight 22
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicWidth 98
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","fixed"]},"BHd727JRP":{"layout":["fixed","fixed"]},"RkuQdhlTZ":{"layout":["fixed","fixed"]},"E8DBhSnYX":{"layout":["fixed","fixed"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerVariables {"TtVIfkkob":"title"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       */
const FramerxmXIrm86E = withCSS(Component, css);
export default FramerxmXIrm86E;
FramerxmXIrm86E.displayName = "Ask a Question - Footer";
FramerxmXIrm86E.defaultProps = {
  height: 22,
  width: 98
};
addPropertyControls(FramerxmXIrm86E, {
  TtVIfkkob: {
    defaultValue: "",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  variant: {
    options: ["TfJ77UFlF", "RkuQdhlTZ"],
    optionTitles: ["Ask a Question - Footer", "Request a demo"],
    title: "Variant",
    type: ControlType.Enum
  }
});
addFonts(FramerxmXIrm86E, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/xmXIrm86E:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerxmXIrm86E",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "22",
        "framerVariables": "{\"TtVIfkkob\":\"title\"}",
        "framerIntrinsicWidth": "98",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"fixed\"]},\"BHd727JRP\":{\"layout\":[\"fixed\",\"fixed\"]},\"RkuQdhlTZ\":{\"layout\":[\"fixed\",\"fixed\"]},\"E8DBhSnYX\":{\"layout\":[\"fixed\",\"fixed\"]}}}",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./xmXIrm86E.map