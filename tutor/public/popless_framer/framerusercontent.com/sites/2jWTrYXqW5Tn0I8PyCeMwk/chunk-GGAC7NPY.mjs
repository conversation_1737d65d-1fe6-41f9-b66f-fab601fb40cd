import{d as ue,f as le,g as de,h as pe,i as fe}from"./chunk-BI4OMGMN.mjs";import{b as H}from"./chunk-VWWF2A63.mjs";import{H as j,K as a,N as ie,O as ce,h as ae,j as T,k as x,n as P,o as E}from"./chunk-5F276QAW.mjs";import{c as C}from"./chunk-OIST4OYN.mjs";function m(e){this.ready=new Promise((n,i)=>{var s=C.indexedDB.open(location.origin);s.onupgradeneeded=o=>{this.db=o.target.result,this.db.createObjectStore("store")},s.onsuccess=o=>{this.db=o.target.result,n()},s.onerror=o=>{this.db=o.target.result,i(o)}})}m.prototype.get=function(e){return this.ready.then(()=>new Promise((n,i)=>{var s=this.getStore().get(e);s.onsuccess=o=>n(o.target.result),s.onerror=i}))};m.prototype.getStore=function(){return this.db.transaction(["store"],"readwrite").objectStore("store")};m.prototype.set=function(e,n){return this.ready.then(()=>new Promise((i,s)=>{var o=this.getStore().put(n,e);o.onsuccess=i,o.onerror=s}))};m.prototype.delete=function(e,n){C.indexedDB.deleteDatabase(location.origin)};var Y=e=>e.split("").reduce((n,i)=>(n=(n<<5)-n+i.charCodeAt(0),n&n),0);function W(e){return`https://cors-anywhere.herokuapp.com/${e}`}async function G(e,n=new m("cache")){let i=e,s=await n.get(i);if(s)return s;var o=new XMLHttpRequest;return o.open("GET",e,!0),o.responseType="blob",new Promise((h,l)=>{o.onload=async function(){this.status===200?(await n.set(e,this.response),h(this.response)):l(new Error(`Response status ${this.status} ${this.statusText}`))},o.onerror=function(R){l(R)},o.send()})}async function J(e,n=new m("cache")){let i=e,s=await n.get(i);return s||null}var d;(function(e){e.Fill="fill",e.Contain="contain",e.Cover="cover",e.None="none",e.ScaleDown="scale-down"})(d||(d={}));var y;(function(e){e.StartTime="startTime",e.Beginning="beginning",e.NoLoop="noLoop"})(y||(y={}));var c;(function(e){e.None="none",e.MetaData="metadata",e.Auto="auto",e.ForceCache="force"})(c||(c={}));var u;(function(e){e.Video="Upload",e.Url="URL"})(u||(u={}));function Ve(e){let{width:n,height:i,topLeft:s,topRight:o,bottomRight:h,bottomLeft:l,id:R,children:k,...V}=e;return V}function X(e){let n=Ve(e);return H(De,{...n})}var De=ae(function(n){let{srcType:i,srcFile:s,srcUrl:o,playing:h,canvasPlay:l,loopType:R,muted:k,playsinline:V,controls:me,preload:g,progress:ye,objectFit:he,backgroundColor:ge,radius:be,topLeft:ve,topRight:Ce,bottomRight:Te,bottomLeft:xe,isMixed:Pe,onSeeked:q,onPause:z,onPlay:Q,onEnd:Z,onClick:Re,onMouseEnter:we,onMouseLeave:Le,onMouseDown:Fe,onMouseUp:Se,poster:Ee,restartOnEnter:ke,posterEnabled:ee,startTime:D,volume:te}=n,_e=ce(),_=D===100?99.9:D,r=E(),w=E(!1),M=pe(),B=E(null),O=E(null),A=R!==y.NoLoop,re=R===y.Beginning,L=P(()=>j.current()!==j.preview,[]),N=g===c.ForceCache,oe=g===c.Auto,Me=P(()=>L?!0:k,[L,k]),F=!L||l,p=P(()=>h,[]),b=T(()=>{var t;_e&&((t=r.current)===null||t===void 0||t.play())},[]),I=T(()=>{var t;return(t=r.current)===null||t===void 0?void 0:t.pause()},[]),ne=T((t=!0)=>{re?b():U(_,t)},[_,re]),U=(t,v=!1)=>{if(r.current){let f=Math.abs(r.current.currentTime-t*.01*r.current.duration)<.3;r.current.duration>0&&!f&&(r.current.currentTime=t*.01*r.current.duration),p&&F&&v&&b()}};x(()=>{U(_)},[D,s,o]);let Oe=fe(ye,{transform:t=>t*.01,onChange:(t,v)=>{U(t)}});le(()=>{B.current!==null&&r.current&&(ke?ne(!B.current||O.current):(!O&&A||!B.current)&&b())}),de(()=>{r.current&&(O.current=r.current.ended,B.current=r.current.paused,I())});let S=T((t=!1)=>{if(i===u.Url)return t?W(o):o;if(i===u.Video)return s},[i,s,o]),se=T(async t=>{if(t){if(r.current=t,M){r.current.src=S();return}if(g===c.ForceCache){if(w.current)return;let v=S(!0),f=await G(v);f&&r.current&&(r.current.src=URL.createObjectURL(f),w.current=!0)}else if(g===c.Auto){if(w.current)return;let v=S(!0),f=await J(v);f&&r.current?r.current.src=URL.createObjectURL(f):r.current.src=S(),w.current=!0}}},[g]);x(()=>{L&&(w.current=!1),se(r.current)},[s,o,i,ee,l,g,A,p]),x(()=>{h&&F?b():I()},[h]),x(()=>{M&&r.current&&p&&setTimeout(()=>{b()},50)},[]),x(()=>{r.current&&!k&&(r.current.volume=te/100)},[te]);let Be=P(()=>Y(JSON.stringify({srcType:i,srcUrl:o,srcFile:s,autoPlay:p,canvasPlay:l,isForcedCache:N})),[i,o,s,p,l,N]),Ne=Pe?`${ve}px ${Ce}px ${Te}px ${xe}px`:`${be}px`,Ue=P(()=>(N||oe)&&!M?null:S(),[M,oe,N]);return H("video",{autoPlay:p&&F,ref:se,onClick:Re,onMouseEnter:we,onMouseLeave:Le,onMouseDown:Fe,onMouseUp:Se,poster:ee?Ee:void 0,style:{width:"100%",height:"100%",borderRadius:Ne,display:"block",objectFit:he,backgroundColor:ge,objectPosition:"50% 50%"},onSeeked:t=>{q&&q(t)},onPause:t=>{z&&z(t)},onPlay:t=>{Q&&Q(t)},onEnded:t=>{Z&&Z(t),A&&F&&r.current&&ne()},onCanPlay:()=>{F&&r.current&&p?b():I(),r.current&&r.current.currentTime<.3&&U(_)},src:Ue,controls:L?!1:me,muted:Me,playsInline:V},Be)});X.defaultProps={srcType:u.Url,srcUrl:"https://assets.mixkit.co/videos/preview/mixkit-ice-cream-glass-of-red-soda-5094-small.mp4",srcFile:"",posterEnabled:!0,poster:"https://misc.framerstatic.com/components/video-poster.jpg",controls:!1,autoPlay:!0,canvasPlay:!1,fullLoop:!1,muted:!0,playsinline:!0,restartOnEnter:!1,preload:c.Auto,objectFit:d.Cover,backgroundColor:"rgba(0,0,0,0)",radius:0,volume:25,startTime:0};ie(X,{srcType:{type:a.Enum,displaySegmentedControl:!0,title:"Source",options:[u.Url,u.Video]},srcUrl:{type:a.String,title:" ",placeholder:"../example.mp4",hidden(e){return e.srcType===u.Video}},srcFile:{type:a.File,title:" ",allowedFileTypes:["mp4"],hidden(e){return e.srcType===u.Url}},playing:{type:a.Boolean,title:"Playing",enabledTitle:"Yes",disabledTitle:"No"},posterEnabled:{type:a.Boolean,title:"Poster",enabledTitle:"Yes",disabledTitle:"No"},poster:{type:a.Image,title:" ",defaultValue:X.defaultProps.poster,hidden:({posterEnabled:e})=>!e},backgroundColor:{type:a.Color,title:"Background"},radius:{title:"Radius",type:a.FusedNumber,toggleKey:"isMixed",toggleTitles:["Radius","Radius per corner"],valueKeys:["topLeft","topRight","bottomRight","bottomLeft"],valueLabels:["TL","TR","BR","BL"],min:0},startTime:{title:"Start Time",type:a.Number,min:0,max:100,step:.1,unit:"%"},loopType:{type:a.Enum,title:"Loop",optionTitles:["From Start Time","From Beginning","Don't Loop"],options:[y.StartTime,y.Beginning,y.NoLoop]},objectFit:{type:a.Enum,title:"Fit",options:[d.Cover,d.Fill,d.Contain,d.ScaleDown,d.None]},canvasPlay:{type:a.Boolean,title:"On Canvas",enabledTitle:"Play",disabledTitle:"Pause",hidden(e){return e.autoPlay===!1}},restartOnEnter:{type:a.Boolean,title:"On ReEnter",enabledTitle:"Restart",disabledTitle:"Resume"},controls:{type:a.Boolean,title:"Controls",enabledTitle:"Show",disabledTitle:"Hide"},muted:{type:a.Boolean,title:"Muted",enabledTitle:"Yes",disabledTitle:"No"},volume:{type:a.Number,max:100,min:0,unit:"%",hidden:({muted:e})=>e},preload:{type:a.Enum,title:"Cache",options:[c.Auto,c.None,c.ForceCache]},onEnd:{type:a.EventHandler},onSeeked:{type:a.EventHandler},onPause:{type:a.EventHandler},onPlay:{type:a.EventHandler},...ue});export{X as a};
//# sourceMappingURL=chunk-GGAC7NPY.mjs.map
