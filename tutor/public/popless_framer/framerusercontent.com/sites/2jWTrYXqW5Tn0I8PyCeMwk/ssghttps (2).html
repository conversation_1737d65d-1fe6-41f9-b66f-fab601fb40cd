import { createContext, useRef, use<PERSON>allback, useMemo, createElement, useContext } from 'react';

function _extends() {
  _extends = Object.assign || function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  return _extends.apply(this, arguments);
}

function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;

  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }

  return target;
}

/**
 * Logs messages in the console with a corresponding urgency
 *
 * @param level the urgency of the message
 * @param message the message to log in the console
 */
var log = function log(level, message) {
  if (process.env.NODE_ENV !== "production") {
    var packageName = '[react-use-intercom]';

    switch (level) {
      case 'info':
        console.log(packageName + " " + message);
        break;

      case 'warn':
        console.warn(packageName + " " + message);
        break;

      case 'error':
        console.error(packageName + " " + message);
        break;

      default:
        console.log(packageName + " " + message);
    }
  }
};

var isEmptyObject = function isEmptyObject(obj) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
};
var isSSR = typeof window === 'undefined';
/**
 * Removes object entries where the value equals to `undefined`
 *
 * @param obj
 */

var removeUndefined = function removeUndefined(obj) {
  Object.keys(obj).forEach(function (key) {
    if (obj[key] && typeof obj[key] === 'object') removeUndefined(obj[key]);else if (obj[key] === undefined) delete obj[key];
  });
  return obj;
};

/**
 * Safely exposes `window.Intercom` and passes the arguments to the instance
 *
 * @param method method passed to the `window.Intercom` instance
 * @param args arguments passed to the `window.Intercom` instance
 *
 * @see {@link https://developers.intercom.com/installing-intercom/docs/intercom-javascript}
 */

var IntercomAPI = function IntercomAPI(method) {
  if (!isSSR && window.Intercom) {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }

    return window.Intercom.apply(null, [method].concat(args));
  } else {
    log('error', method + " Intercom instance is not initalized yet");
  }
};

var NO_INTERCOM_PROVIDER_MESSAGE = 'Please wrap your component with `IntercomProvider`.';
var IntercomContext = /*#__PURE__*/createContext({
  boot: function boot() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  shutdown: function shutdown() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  hardShutdown: function hardShutdown() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  update: function update() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  hide: function hide() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  show: function show() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  showMessages: function showMessages() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  showNewMessages: function showNewMessages() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  getVisitorId: function getVisitorId() {
    log('error', NO_INTERCOM_PROVIDER_MESSAGE);
    return '';
  },
  startTour: function startTour() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  },
  trackEvent: function trackEvent() {
    return log('error', NO_INTERCOM_PROVIDER_MESSAGE);
  }
});

// @ts-nocheck

/**
 * Snippet to initialize the Intercom instance
 *
 * @param appId - Intercom app id
 * @param [timeout=0] - Amount of milliseconds that the initialization should be delayed, defaults to 0
 *
 * @see {@link https://developers.intercom.com/installing-intercom/docs/basic-javascript}
 */
var initialize = function initialize(appId, timeout) {
  if (timeout === void 0) {
    timeout = 0;
  }

  var w = window;
  var ic = w.Intercom;

  if (typeof ic === 'function') {
    ic('reattach_activator');
    ic('update', w.intercomSettings);
  } else {
    var d = document;

    var i = function i() {
      i.c(arguments);
    };

    i.q = [];

    i.c = function (args) {
      i.q.push(args);
    };

    w.Intercom = i;

    var l = function l() {
      setTimeout(function () {
        var s = d.createElement('script');
        s.type = 'text/javascript';
        s.async = true;
        s.src = 'https://widget.intercom.io/widget/' + appId;
        var x = d.getElementsByTagName('script')[0];
        x.parentNode.insertBefore(s, x);
      }, timeout);
    };

    if (document.readyState === 'complete') {
      l();
    } else if (w.attachEvent) {
      w.attachEvent('onload', l);
    } else {
      w.addEventListener('load', l, false);
    }
  }
};

var mapMessengerAttributesToRawMessengerAttributes = function mapMessengerAttributesToRawMessengerAttributes(attributes) {
  return {
    custom_launcher_selector: attributes.customLauncherSelector,
    alignment: attributes.alignment,
    vertical_padding: attributes.verticalPadding,
    horizontal_padding: attributes.horizontalPadding,
    hide_default_launcher: attributes.hideDefaultLauncher,
    session_duration: attributes.sessionDuration,
    action_color: attributes.actionColor,
    background_color: attributes.backgroundColor
  };
};

var mapDataAttributesCompanyToRawDataAttributesCompany = function mapDataAttributesCompanyToRawDataAttributesCompany(attributes) {
  return _extends({
    company_id: attributes.companyId,
    name: attributes.name,
    created_at: attributes.createdAt,
    plan: attributes.plan,
    monthly_spend: attributes.monthlySpend,
    user_count: attributes.userCount,
    size: attributes.size,
    website: attributes.website,
    industry: attributes.industry
  }, attributes.customAttributes);
};

var mapDataAttributesAvatarToRawDataAttributesAvatar = function mapDataAttributesAvatarToRawDataAttributesAvatar(attributes) {
  return {
    type: attributes.type,
    image_url: attributes.imageUrl
  };
};

var mapDataAttributesToRawDataAttributes = function mapDataAttributesToRawDataAttributes(attributes) {
  var _attributes$companies;

  return _extends({
    email: attributes.email,
    user_id: attributes.userId,
    created_at: attributes.createdAt,
    name: attributes.name,
    phone: attributes.phone,
    last_request_at: attributes.lastRequestAt,
    unsubscribed_from_emails: attributes.unsubscribedFromEmails,
    language_override: attributes.languageOverride,
    utm_campaign: attributes.utmCampaign,
    utm_content: attributes.utmContent,
    utm_medium: attributes.utmMedium,
    utm_source: attributes.utmSource,
    utm_term: attributes.utmTerm,
    avatar: attributes.avatar && mapDataAttributesAvatarToRawDataAttributesAvatar(attributes.avatar),
    user_hash: attributes.userHash,
    company: attributes.company && mapDataAttributesCompanyToRawDataAttributesCompany(attributes.company),
    companies: (_attributes$companies = attributes.companies) == null ? void 0 : _attributes$companies.map(mapDataAttributesCompanyToRawDataAttributesCompany)
  }, attributes.customAttributes);
};
var mapIntercomPropsToRawIntercomProps = function mapIntercomPropsToRawIntercomProps(props) {
  return removeUndefined(_extends({}, mapMessengerAttributesToRawMessengerAttributes(props), mapDataAttributesToRawDataAttributes(props)));
};

var IntercomProvider = function IntercomProvider(_ref) {
  var appId = _ref.appId,
      _ref$autoBoot = _ref.autoBoot,
      autoBoot = _ref$autoBoot === void 0 ? false : _ref$autoBoot,
      autoBootProps = _ref.autoBootProps,
      children = _ref.children,
      onHide = _ref.onHide,
      onShow = _ref.onShow,
      onUnreadCountChange = _ref.onUnreadCountChange,
      _ref$shouldInitialize = _ref.shouldInitialize,
      shouldInitialize = _ref$shouldInitialize === void 0 ? !isSSR : _ref$shouldInitialize,
      apiBase = _ref.apiBase,
      initializeDelay = _ref.initializeDelay,
      rest = _objectWithoutPropertiesLoose(_ref, ["appId", "autoBoot", "autoBootProps", "children", "onHide", "onShow", "onUnreadCountChange", "shouldInitialize", "apiBase", "initializeDelay"]);

  var isBooted = useRef(false);
  var isInitialized = useRef(false);
  if (!isEmptyObject(rest) && process.env.NODE_ENV !== "production") log('error', ['some invalid props were passed to IntercomProvider. ', "Please check following props: " + Object.keys(rest).join(', ') + "."].join(''));
  var boot = useCallback(function (props) {
    if (!window.Intercom && !shouldInitialize) {
      log('warn', 'Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`');
      return;
    }

    if (isBooted.current) {
      return;
    }

    var metaData = _extends({
      app_id: appId
    }, apiBase && {
      api_base: apiBase
    }, props && mapIntercomPropsToRawIntercomProps(props));

    window.intercomSettings = metaData;
    IntercomAPI('boot', metaData);
    isBooted.current = true;
  }, [apiBase, appId, shouldInitialize]);

  if (!isSSR && shouldInitialize && !isInitialized.current) {
    initialize(appId, initializeDelay); // attach listeners

    if (onHide) IntercomAPI('onHide', onHide);
    if (onShow) IntercomAPI('onShow', onShow);
    if (onUnreadCountChange) IntercomAPI('onUnreadCountChange', onUnreadCountChange);

    if (autoBoot) {
      boot(autoBootProps);
    }

    isInitialized.current = true;
  }

  var ensureIntercom = useCallback(function (functionName, callback) {
    if (functionName === void 0) {
      functionName = 'A function';
    }

    if (!window.Intercom && !shouldInitialize) {
      log('warn', 'Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`');
      return;
    }

    if (!isBooted.current) {
      log('warn', ["'" + functionName + "' was called but Intercom has not booted yet. ", "Please call 'boot' before calling '" + functionName + "' or ", "set 'autoBoot' to true in the IntercomProvider."].join(''));
      return;
    }

    return callback();
  }, [shouldInitialize]);
  var shutdown = useCallback(function () {
    if (!isBooted.current) return;
    IntercomAPI('shutdown');
    isBooted.current = false;
  }, []);
  var hardShutdown = useCallback(function () {
    if (!isBooted.current) return;
    IntercomAPI('shutdown');
    delete window.Intercom;
    delete window.intercomSettings;
    isBooted.current = false;
  }, []);
  var refresh = useCallback(function () {
    ensureIntercom('update', function () {
      var lastRequestedAt = new Date().getTime();
      IntercomAPI('update', {
        last_requested_at: lastRequestedAt
      });
    });
  }, [ensureIntercom]);
  var update = useCallback(function (props) {
    ensureIntercom('update', function () {
      if (!props) {
        refresh();
        return;
      }

      var rawProps = mapIntercomPropsToRawIntercomProps(props);
      window.intercomSettings = _extends({}, window.intercomSettings, rawProps);
      IntercomAPI('update', rawProps);
    });
  }, [ensureIntercom, refresh]);
  var hide = useCallback(function () {
    ensureIntercom('hide', function () {
      IntercomAPI('hide');
    });
  }, [ensureIntercom]);
  var show = useCallback(function () {
    ensureIntercom('show', function () {
      return IntercomAPI('show');
    });
  }, [ensureIntercom]);
  var showMessages = useCallback(function () {
    ensureIntercom('showMessages', function () {
      IntercomAPI('showMessages');
    });
  }, [ensureIntercom]);
  var showNewMessages = useCallback(function (message) {
    ensureIntercom('showNewMessage', function () {
      if (!message) {
        IntercomAPI('showNewMessage');
      } else {
        IntercomAPI('showNewMessage', message);
      }
    });
  }, [ensureIntercom]);
  var getVisitorId = useCallback(function () {
    return ensureIntercom('getVisitorId', function () {
      return IntercomAPI('getVisitorId');
    });
  }, [ensureIntercom]);
  var startTour = useCallback(function (tourId) {
    ensureIntercom('startTour', function () {
      IntercomAPI('startTour', tourId);
    });
  }, [ensureIntercom]);
  var trackEvent = useCallback(function (event, metaData) {
    ensureIntercom('trackEvent', function () {
      if (metaData) {
        IntercomAPI('trackEvent', event, metaData);
      } else {
        IntercomAPI('trackEvent', event);
      }
    });
  }, [ensureIntercom]);
  var providerValue = useMemo(function () {
    return {
      boot: boot,
      shutdown: shutdown,
      hardShutdown: hardShutdown,
      update: update,
      hide: hide,
      show: show,
      showMessages: showMessages,
      showNewMessages: showNewMessages,
      getVisitorId: getVisitorId,
      startTour: startTour,
      trackEvent: trackEvent
    };
  }, [boot, shutdown, hardShutdown, update, hide, show, showMessages, showNewMessages, getVisitorId, startTour, trackEvent]);
  var content = useMemo(function () {
    return children;
  }, [children]);
  return createElement(IntercomContext.Provider, {
    value: providerValue
  }, content);
};
var useIntercomContext = function useIntercomContext() {
  return useContext(IntercomContext);
};

var useIntercom = function useIntercom() {
  return useIntercomContext();
};

export { IntercomProvider, useIntercom };
//# sourceMappingURL=react-use-intercom.esm.js.map
