const metadata = params => ({
  breakpoints: [{
    hash: "1gnlts8",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "teh5uf",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "k11cgl",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | Suppprt - We're here to help",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};