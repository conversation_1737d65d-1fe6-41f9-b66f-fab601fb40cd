// Generated by <PERSON><PERSON>r (1b71f6a)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, Link, RichText, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  bPciUZ9rs: {
    hover: true
  }
};
const cycleOrder = ["bPciUZ9rs"];
const variantClassNames = {
  bPciUZ9rs: "framer-v-yidjqs"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {};
const transitions = {
  default: {
    damping: 60,
    delay: 0,
    duration: .3,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "spring"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "bPciUZ9rs",
  link: Ch1s8zeV8,
  heading: AcqMsHTxB = "Become a tutor",
  body: KFv0h_nnm = "Get started with the only\nall-in-one tutoring platform.",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "bPciUZ9rs",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-lvQX7", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: Ch1s8zeV8,
        openInNewTab: false,
        children: /*#__PURE__*/_jsxs(motion.a, {
          ...restProps,
          className: `${cx("framer-yidjqs", className)} framer-1e86zj2`,
          "data-framer-name": "Default",
          layoutDependency: layoutDependency,
          layoutId: "bPciUZ9rs",
          ref: ref,
          style: {
            ...style
          },
          transition: transition,
          ...addPropertyOverrides({
            "bPciUZ9rs-hover": {
              "data-framer-name": undefined
            }
          }, baseVariant, gestureVariant),
          children: [/*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--font-selector": "SW50ZXItTWVkaXVt",
                  "--framer-font-family": '"Inter-Medium", "Inter", sans-serif',
                  "--framer-font-weight": "500"
                },
                children: "Become a tutor"
              })
            }),
            className: "framer-l1o36t",
            fonts: ["Inter-Medium"],
            layoutDependency: layoutDependency,
            layoutId: "yHR_aRVA6",
            style: {
              "--framer-paragraph-spacing": "0px"
            },
            text: AcqMsHTxB,
            transition: transition,
            verticalAlignment: "top",
            withExternalLayout: true
          }), /*#__PURE__*/_jsx(RichText, {
            __fromCanvasComponent: true,
            children: /*#__PURE__*/_jsx(React.Fragment, {
              children: /*#__PURE__*/_jsx(motion.p, {
                style: {
                  "--framer-font-size": "15px",
                  "--framer-line-height": "140%",
                  "--framer-text-color": "var(--extracted-r6o4lv)"
                },
                children: "Get started with the onlyall-in-one tutoring platform."
              })
            }),
            className: "framer-m6yrno",
            layoutDependency: layoutDependency,
            layoutId: "aeZvJYhnc",
            style: {
              "--extracted-r6o4lv": "rgb(95, 99, 104)",
              "--framer-link-text-color": "rgb(0, 153, 255)",
              "--framer-link-text-decoration": "underline",
              "--framer-paragraph-spacing": "0px"
            },
            text: KFv0h_nnm,
            transition: transition,
            variants: {
              "bPciUZ9rs-hover": {
                "--extracted-r6o4lv": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168)) "
              }
            },
            verticalAlignment: "top",
            withExternalLayout: true,
            ...addPropertyOverrides({
              "bPciUZ9rs-hover": {
                children: /*#__PURE__*/_jsx(React.Fragment, {
                  children: /*#__PURE__*/_jsx(motion.p, {
                    style: {
                      "--framer-font-size": "15px",
                      "--framer-line-height": "140%",
                      "--framer-text-color": "var(--extracted-r6o4lv)"
                    },
                    children: "Get started with the only all-in-one tutoring platform."
                  })
                })
              }
            }, baseVariant, gestureVariant)
          })]
        })
      })
    })
  });
});
const css = ['.framer-lvQX7 [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-lvQX7 * { box-sizing: border-box; }", ".framer-lvQX7 .framer-1e86zj2 { display: block; }", ".framer-lvQX7 .framer-yidjqs { align-content: flex-start; align-items: flex-start; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: hidden; padding: 0px 0px 0px 0px; position: relative; text-decoration: none; width: min-content; }", ".framer-lvQX7 .framer-l1o36t { flex: none; height: auto; position: relative; white-space: pre; width: auto; }", ".framer-lvQX7 .framer-m6yrno { align-self: stretch; flex: none; height: auto; position: relative; white-space: pre-wrap; width: auto; word-break: break-word; word-wrap: break-word; }", ".framer-lvQX7 .framer-v-yidjqs .framer-yidjqs { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-lvQX7 .framer-yidjqs { gap: 0px; } .framer-lvQX7 .framer-yidjqs > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-lvQX7 .framer-yidjqs > :first-child { margin-top: 0px; } .framer-lvQX7 .framer-yidjqs > :last-child { margin-bottom: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicHeight 134
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerIntrinsicWidth 117
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]},"pvvdNLsWT":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       * @framerVariables {"Ch1s8zeV8":"link","AcqMsHTxB":"heading","KFv0h_nnm":"body"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       */
const FramerbEbqHaZIR = withCSS(Component, css);
export default FramerbEbqHaZIR;
FramerbEbqHaZIR.displayName = "header-link";
FramerbEbqHaZIR.defaultProps = {
  height: 134,
  width: 117
};
addPropertyControls(FramerbEbqHaZIR, {
  Ch1s8zeV8: {
    title: "Link",
    type: ControlType.Link
  },
  AcqMsHTxB: {
    defaultValue: "Become a tutor",
    displayTextArea: false,
    title: "Heading",
    type: ControlType.String
  },
  KFv0h_nnm: {
    defaultValue: "Get started with the only\nall-in-one tutoring platform.",
    displayTextArea: true,
    title: "Body",
    type: ControlType.String
  }
});
addFonts(FramerbEbqHaZIR, []);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerbEbqHaZIR",
      "slots": [],
      "annotations": {
        "framerIntrinsicWidth": "117",
        "framerIntrinsicHeight": "134",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]},\"pvvdNLsWT\":{\"layout\":[\"auto\",\"auto\"]}}}",
        "framerVariables": "{\"Ch1s8zeV8\":\"link\",\"AcqMsHTxB\":\"heading\",\"KFv0h_nnm\":\"body\"}",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./bEbqHaZIR.map