// Generated by Fr<PERSON>r (1042f6d)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, Container, cx, GeneratedComponentContext, getFonts, PropertyOverrides, removeHiddenBreakpointLayers, Text, useActiveVariantCallback, useHydratedBreakpointVariants, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import Intercom from "https://framerusercontent.com/modules/UIhUTcd796YH7Ndybys8/totj55n8qE3VYpdXhshW/Intercom.js";
import HeaderNavigation from "https://framerusercontent.com/modules/fskCjCUrITFMyzFEVxe6/pJmgmAsUZMP1nR6O0Tqj/AigekX7zV.js";
import ShapeTheFuture from "https://framerusercontent.com/modules/dBrLJ5gckm33BkzpqEZU/HF9fmVyNBRYacm9SFNaD/q5ZpJvWbA.js";
import FooterNew from "https://framerusercontent.com/modules/v0PedC1F1TzmBYHVlPVi/WMTNxzvcB1mArFuofoHm/ZH45lfARx.js";
import metadataProvider from "https://framerusercontent.com/modules/ncnmr7kacGpbj2nd98Vd/eGyAAzgp41Pi8lUqNqGJ/CqoljZCO0.js";
const HeaderNavigationFonts = getFonts(HeaderNavigation);
const IntercomFonts = getFonts(Intercom);
const ShapeTheFutureFonts = getFonts(ShapeTheFuture);
const FooterNewFonts = getFonts(FooterNew);
const cycleOrder = ["aQ31VLiDi", "dUnoHw2xX", "DNMzUtJwd"];
const breakpoints = {
  aQ31VLiDi: "(min-width: 1280px)",
  DNMzUtJwd: "(max-width: 809px)",
  dUnoHw2xX: "(min-width: 810px) and (max-width: 1279px)"
};
const isBrowser = () => typeof document !== "undefined";
const variantClassNames = {
  aQ31VLiDi: "framer-v-1gnlts8",
  DNMzUtJwd: "framer-v-k11cgl",
  dUnoHw2xX: "framer-v-teh5uf"
};
if (isBrowser()) {
  removeHiddenBreakpointLayers("aQ31VLiDi", breakpoints, variantClassNames);
}
const humanReadableVariantMap = {
  Desktop: "aQ31VLiDi",
  Phone: "DNMzUtJwd",
  Tablet: "dUnoHw2xX"
};
const transitions = {
  default: {
    duration: 0
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "aQ31VLiDi",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  React.useLayoutEffect(() => {
    const metadata = metadataProvider();
    document.title = metadata.title || "";
    if (metadata.viewport) {
      var ref;
      (ref = document.querySelector('meta[name="viewport"]')) === null || ref === void 0 ? void 0 : ref.setAttribute("content", metadata.viewport);
    }
  }, []);
  const [baseVariant, hydratedBaseVariant] = useHydratedBreakpointVariants(variant, breakpoints, false);
  const gestureVariant = undefined;
  const transition = transitions.default;
  const {
    activeVariantCallback,
    delay
  } = useActiveVariantCallback(undefined);
  const poplessLink1hxdeaz = activeVariantCallback(async (...args) => {
    window.open("https://popless.com", "_blank", "noreferrer noopener");
  });
  const twitterLink1bj8fo6 = activeVariantCallback(async (...args) => {
    window.open("https://twitter.com/popless_hq", "_blank", "noreferrer noopener");
  });
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(GeneratedComponentContext.Provider, {
    value: {
      primaryVariantId: "aQ31VLiDi",
      variantClassNames
    },
    children: /*#__PURE__*/_jsx(LayoutGroup, {
      id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
      children: /*#__PURE__*/_jsxs(motion.div, {
        "data-framer-generated": true,
        className: cx("framer-4CF5y"),
        style: {
          display: "contents",
          pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
        },
        children: [/*#__PURE__*/_jsx(motion.div, {
          ...restProps,
          className: cx("framer-1gnlts8", className),
          ref: ref,
          style: {
            ...style
          },
          children: /*#__PURE__*/_jsxs(motion.main, {
            className: "framer-1xyy69v",
            "data-framer-name": "Main",
            name: "Main",
            children: [/*#__PURE__*/_jsx(Container, {
              className: "framer-1626arn-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  DNMzUtJwd: {
                    variant: "aZMkidfTG"
                  },
                  dUnoHw2xX: {
                    variant: "aZMkidfTG"
                  }
                },
                children: /*#__PURE__*/_jsx(HeaderNavigation, {
                  height: "100%",
                  id: "pZ_UwZ9VR",
                  layoutId: "pZ_UwZ9VR",
                  style: {
                    width: "100%"
                  },
                  variant: "yBBOIO6L6",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsxs(motion.header, {
              className: "framer-122bxcw",
              "data-framer-name": "Stack",
              name: "Stack",
              children: [/*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "center",
                className: "framer-117r2to",
                fonts: ["GF;Inter-700"],
                rawHTML: "<h1 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Support</span><br></span></h1>",
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "center",
                className: "framer-k0fgvz",
                fonts: ["GF;Inter-500"],
                rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Reach out to us on Discord or send an email to </span><a style='' href=\"mailto:<EMAIL>\" target=\"_blank\" rel=\"noreferrer noopener\"><EMAIL></a><span style=''> for general inquiries, bug reports, or to inquire about anything you couldn’t find in the support hub.</span><br></span></span>",
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Container, {
                className: "framer-1b3n0b7-container",
                children: /*#__PURE__*/_jsx(Intercom, {
                  appId: "lsvujawt",
                  height: "100%",
                  id: "V0gxCRz1D",
                  layoutId: "V0gxCRz1D",
                  style: {
                    height: "100%",
                    width: "100%"
                  },
                  width: "100%"
                })
              })]
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-dd8hw2-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  DNMzUtJwd: {
                    variant: "nGPSZ5OLT"
                  },
                  dUnoHw2xX: {
                    variant: "zKr8Brn0b"
                  }
                },
                children: /*#__PURE__*/_jsx(ShapeTheFuture, {
                  color: 'var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36)) /* {"name":"Gray/Very Dark Gray"} */',
                  height: "100%",
                  id: "KAhctdU4q",
                  imageD: new URL("https://framerusercontent.com/images/EldSc7fbYUcWcTuUYmP1lvQYEE.jpg").href,
                  layoutId: "KAhctdU4q",
                  text: "Shape the future of Popless.",
                  title: "Charlotte and Emma",
                  title2: "Dietician Tutors | New York",
                  title3: "Charlotte and Emma",
                  title4: "Dietician Tutors | New York",
                  variant: "zmLFamz5S",
                  width: "100%"
                })
              })
            }), /*#__PURE__*/_jsx(Container, {
              className: "framer-wrezny-container",
              children: /*#__PURE__*/_jsx(PropertyOverrides, {
                breakpoint: baseVariant,
                overrides: {
                  DNMzUtJwd: {
                    variant: "Zz_9kWOfb"
                  }
                },
                children: /*#__PURE__*/_jsx(FooterNew, {
                  height: "100%",
                  id: "j6SMlDpQr",
                  layoutId: "j6SMlDpQr",
                  poplessLink: poplessLink1hxdeaz,
                  style: {
                    width: "100%"
                  },
                  twitterLink: twitterLink1bj8fo6,
                  variant: "zyTRmFlly",
                  width: "100%"
                })
              })
            })]
          })
        }), /*#__PURE__*/_jsx("div", {
          id: "overlay"
        })]
      })
    })
  });
});
const css = ['.framer-4CF5y [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-4CF5y .framer-1t6p7xe { display: block; }", ".framer-4CF5y .framer-1gnlts8 { align-content: center; align-items: center; background-color: #ffffff; display: flex; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1280px; }", ".framer-4CF5y .framer-1xyy69v { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 100%; }", ".framer-4CF5y .framer-1626arn-container { flex: none; height: auto; left: 0px; position: absolute; right: 0px; top: 0px; z-index: 2; }", ".framer-4CF5y .framer-122bxcw { align-content: center; align-items: center; display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 24px; height: min-content; justify-content: flex-start; max-width: 1200px; overflow: visible; padding: 150px 120px 20px 120px; position: relative; width: 1200px; }", '.framer-4CF5y .framer-117r2to { --framer-font-family: "Inter", sans-serif; --framer-font-size: 64px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: -2px; --framer-line-height: 72px; --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 780px; word-break: break-word; word-wrap: break-word; }', '.framer-4CF5y .framer-k0fgvz { --framer-font-family: "Inter", sans-serif; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: -0.2px; --framer-line-height: 1.4em; --framer-link-text-color: var(--token-7b040a80-5f57-4f9a-a734-c0f3178785ca, #0368e0); --framer-text-alignment: center; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 748px; word-break: break-word; word-wrap: break-word; }', ".framer-4CF5y .framer-1b3n0b7-container { flex: none; height: 1px; position: relative; width: 1px; }", ".framer-4CF5y .framer-dd8hw2-container { flex: none; height: auto; position: relative; width: auto; }", ".framer-4CF5y .framer-wrezny-container { flex: none; height: auto; position: relative; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (scale:1)) { .framer-4CF5y .framer-1gnlts8, .framer-4CF5y .framer-1xyy69v, .framer-4CF5y .framer-122bxcw { gap: 0px; } .framer-4CF5y .framer-1gnlts8 > * { margin: 0px; margin-bottom: calc(10px / 2); margin-top: calc(10px / 2); } .framer-4CF5y .framer-1gnlts8 > :first-child, .framer-4CF5y .framer-1xyy69v > :first-child, .framer-4CF5y .framer-122bxcw > :first-child { margin-top: 0px; } .framer-4CF5y .framer-1gnlts8 > :last-child, .framer-4CF5y .framer-1xyy69v > :last-child, .framer-4CF5y .framer-122bxcw > :last-child { margin-bottom: 0px; } .framer-4CF5y .framer-1xyy69v > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-4CF5y .framer-122bxcw > * { margin: 0px; margin-bottom: calc(24px / 2); margin-top: calc(24px / 2); } }", "@media (min-width: 1280px) { .framer-4CF5y .hidden-1gnlts8 { display: none !important; } }", "@media (min-width: 810px) and (max-width: 1279px) { .framer-4CF5y .hidden-teh5uf { display: none !important; } .framer-4CF5y .framer-1gnlts8 { width: 810px; } .framer-4CF5y .framer-1626arn-container { order: 0; } .framer-4CF5y .framer-122bxcw { max-width: 700px; order: 1; padding: 150px 24px 20px 24px; width: 100%; } .framer-4CF5y .framer-117r2to { --framer-font-size: 58px; --framer-line-height: 64px; width: 100%; } .framer-4CF5y .framer-k0fgvz { width: 80%; } .framer-4CF5y .framer-dd8hw2-container { order: 2; } .framer-4CF5y .framer-wrezny-container { order: 3; }}", "@media (max-width: 809px) { .framer-4CF5y .hidden-k11cgl { display: none !important; } .framer-4CF5y .framer-1gnlts8 { width: 390px; } .framer-4CF5y .framer-1626arn-container { order: 0; } .framer-4CF5y .framer-122bxcw { order: 1; padding: 150px 24px 50px 24px; width: 100%; } .framer-4CF5y .framer-117r2to { --framer-font-size: 42px; --framer-line-height: 48px; width: 100%; } .framer-4CF5y .framer-k0fgvz { width: 100%; } .framer-4CF5y .framer-dd8hw2-container { order: 2; } .framer-4CF5y .framer-wrezny-container { order: 3; }}"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerIntrinsicHeight 1413
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerIntrinsicWidth 1280
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"dUnoHw2xX":{"layout":["fixed","auto"]},"DNMzUtJwd":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          * @framerResponsiveScreen
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          */
const FramerCqoljZCO0 = withCSS(Component, css);
export default FramerCqoljZCO0;
FramerCqoljZCO0.displayName = "Support";
FramerCqoljZCO0.defaultProps = {
  height: 1413,
  width: 1280
};
addFonts(FramerCqoljZCO0, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/CqoljZCO0:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZhrib2Bg-4.ttf",
  weight: "700"
}, {
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:screen/CqoljZCO0:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  weight: "500"
}, ...HeaderNavigationFonts, ...IntercomFonts, ...ShapeTheFutureFonts, ...FooterNewFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FramerCqoljZCO0",
      "slots": [],
      "annotations": {
        "framerIntrinsicWidth": "1280",
        "framerIntrinsicHeight": "1413",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"dUnoHw2xX\":{\"layout\":[\"fixed\",\"auto\"]},\"DNMzUtJwd\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerContractVersion": "1",
        "framerResponsiveScreen": ""
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};