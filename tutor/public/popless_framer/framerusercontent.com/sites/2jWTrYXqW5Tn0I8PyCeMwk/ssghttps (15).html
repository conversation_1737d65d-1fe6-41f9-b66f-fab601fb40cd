import { jsx as _jsx } from "react/jsx-runtime"; // Generated by Framer (76f10ba)
import { addFonts, addPropertyControls, ControlType, cx, Text, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
const enabledGestures = {
  bjh_ekXTo: {
    hover: true
  }
};
const cycleOrder = ["bjh_ekXTo"];
const variantClassNames = {
  bjh_ekXTo: "framer-v-6ncvr9"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {};
const transitions = {
  default: {
    type: "spring",
    ease: [.44, 0, .56, 1],
    duration: .3,
    delay: 0,
    stiffness: 500,
    damping: 60,
    mass: 1
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "bjh_ekXTo",
  title: XNbeM2lzk = "Show all",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    variants,
    baseVariant,
    gestureVariant,
    classNames,
    transition,
    setVariant,
    setGestureState
  } = useVariantState({
    defaultVariant: "bjh_ekXTo",
    variant,
    transitions,
    variantClassNames,
    enabledGestures,
    cycleOrder
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-qeSWs", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(motion.div, {
        ...restProps,
        className: cx("framer-6ncvr9", className),
        style: {
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          borderTopRightRadius: 6,
          borderTopLeftRadius: 6,
          backgroundColor: "var(--token-36a54893-9e5b-4622-8567-34f1e61e2ee9, rgb(255, 255, 255))",
          ...style
        },
        layoutId: "bjh_ekXTo",
        transition: transition,
        layoutDependency: layoutDependency,
        "data-framer-name": "Default",
        ref: ref,
        variants: {
          "bjh_ekXTo-hover": {
            backgroundColor: "var(--token-a1e13941-d14a-4692-855b-e794eebdfa6f, rgb(229, 232, 232))"
          }
        },
        children: /*#__PURE__*/_jsx(Text, {
          style: {
            "--framer-font-family": '"Inter", sans-serif',
            "--framer-font-style": "normal",
            "--framer-font-weight": 500,
            "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
            "--framer-font-size": "14px",
            "--framer-letter-spacing": "0px",
            "--framer-text-transform": "none",
            "--framer-text-decoration": "none",
            "--framer-line-height": "22px",
            "--framer-text-alignment": "center"
          },
          withExternalLayout: true,
          verticalAlignment: "center",
          __fromCanvasComponent: true,
          alignment: "center",
          fonts: ["GF;Inter-500"],
          className: "framer-88skih",
          layoutId: "spO6L1dgh",
          transition: transition,
          layoutDependency: layoutDependency,
          rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='font-size: 0'><span style=''>Show all</span><br></span></span>",
          text: XNbeM2lzk,
          variants: {
            "bjh_ekXTo-hover": {
              "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))"
            }
          },
          ...addPropertyOverrides({
            "bjh_ekXTo-hover": {
              rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='font-size: 0'><span style=''>Show all</span><br></span></span>",
              alignment: "center",
              fonts: ["GF;Inter-500"]
            }
          }, baseVariant, gestureVariant)
        })
      })
    })
  });
});
const css = ['.framer-qeSWs [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-qeSWs * { box-sizing: border-box; }", ".framer-qeSWs .framer-6ncvr9 { position: relative; overflow: visible; width: min-content; height: 36px; display: flex; flex-direction: row; justify-content: center; align-content: center; align-items: center; flex-wrap: nowrap; gap: 12px; padding: 12px 24px 12px 24px; }", ".framer-qeSWs .framer-88skih { position: relative; overflow: visible; width: auto; height: auto; flex: none; white-space: pre; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-qeSWs framer-6ncvr9 > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-qeSWs framer-6ncvr9 > :first-child { margin-left: 0px; } .framer-qeSWs framer-6ncvr9 > :last-child { margin-right: 0px; } }", ".framer-qeSWs.framer-v-6ncvr9 .framer-6ncvr9 { cursor: pointer; }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerIntrinsicHeight 36
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerIntrinsicWidth 104
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","fixed"]},"ZXNF6wND_":{"layout":["auto","fixed"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   * @framerVariables {"XNbeM2lzk":"title"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   */
const FramerkbM8MMn1e = withCSS(Component, css);
export default FramerkbM8MMn1e;
FramerkbM8MMn1e.displayName = "Button - White";
FramerkbM8MMn1e.defaultProps = {
  width: 104,
  height: 36
};
addPropertyControls(FramerkbM8MMn1e, {
  XNbeM2lzk: {
    type: ControlType.String,
    title: "Title",
    defaultValue: "Show all",
    displayTextArea: false
  }
});
addFonts(FramerkbM8MMn1e, [{
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  family: "Inter",
  style: "normal",
  weight: "500",
  moduleAsset: {
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
    localModuleIdentifier: "local-module:canvasComponent/kbM8MMn1e:default"
  }
}]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerkbM8MMn1e",
      "slots": [],
      "annotations": {
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"fixed\"]},\"ZXNF6wND_\":{\"layout\":[\"auto\",\"fixed\"]}}}",
        "framerVariables": "{\"XNbeM2lzk\":\"title\"}",
        "framerIntrinsicHeight": "36",
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "104"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./kbM8MMn1e.map