const metadata = params => ({
  breakpoints: [{
    hash: "13u8zsk",
    mediaQuery: "(min-width: 1280px)"
  }, {
    hash: "z8pafb",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "1lb2ibk",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  robots: "noindex",
  socialImage: new URL("https://framerusercontent.com/images/GCcRgX99aUBQHaWIilloYzFsGVs.jpg").href,
  title: "Popless | Student referrals - Introducing the student referral program",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};