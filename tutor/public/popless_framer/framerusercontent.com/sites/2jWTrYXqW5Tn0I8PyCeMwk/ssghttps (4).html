import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addPropertyControls, ControlType, RenderTarget } from "framer";
import { motion } from "framer-motion";
import { IntercomProvider } from "https://framer.com/m/framer/react-use-intercom.js@0.0.3";
import { neutralStateStyle, stateParagraphStyle, stateTitleStyle } from "https://framer.com/m/framer/integrations-styles.js@^0.2.0"; /**
                                                                                                                                     * INTERCOM
                                                                                                                                     *
                                                                                                                                     * @framerIntrinsicWidth 200
                                                                                                                                     * @framerIntrinsicHeight 100
                                                                                                                                     *
                                                                                                                                     * @framerSupportedLayoutWidth fixed
                                                                                                                                     * @framerSupportedLayoutHeight fixed
                                                                                                                                     */
export default function Intercom({
  appId,
  style,
  ...props
}) {
  const isCanvas = RenderTarget.current() === RenderTarget.canvas;
  return isCanvas ? /*#__PURE__*/_jsxs(motion.div, {
    style: {
      ...neutralStateStyle,
      ...style
    },
    children: [/*#__PURE__*/_jsx("h1", {
      style: stateTitleStyle,
      children: "Intercom"
    }), /*#__PURE__*/_jsx("p", {
      style: stateParagraphStyle,
      children: "Drop this component into a Screen to add Intercom."
    })]
  }) : /*#__PURE__*/_jsx(IntercomProvider, {
    autoBoot: true,
    appId: appId
  });
}
;
addPropertyControls(Intercom, {
  appId: {
    title: "ID",
    type: ControlType.String,
    description: "Create an [Intercom](https://www.intercom.com/) account and copy your workspace ID. [Learn more…](https://www.framer.com/sites/integrations/intercom/)"
  }
});
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "Intercom",
      "slots": [],
      "annotations": {
        "framerContractVersion": "1",
        "framerSupportedLayoutHeight": "fixed",
        "framerIntrinsicHeight": "100",
        "framerIntrinsicWidth": "200",
        "framerSupportedLayoutWidth": "fixed"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./Intercom.map