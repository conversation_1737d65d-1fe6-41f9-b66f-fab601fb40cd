import { jsx as _jsx } from "react/jsx-runtime"; // Generated by <PERSON><PERSON>r (9563782)
import * as React from "react";
import { motion, LayoutGroup } from "framer-motion";
import { addFonts, withCSS, addPropertyControls, ControlType, cx, useVariantState, Text } from "framer";
const cycleOrder = ["M3SwnaRw0"];
const variantClassNames = {
  M3SwnaRw0: "framer-v-1e2ggx2"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {};
const transitions = {
  default: {
    type: "spring",
    ease: [.44, 0, .56, 1],
    duration: .3,
    delay: 0,
    stiffness: 500,
    damping: 60,
    mass: 1
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
const useRandomID = () => {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
};
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "M3SwnaRw0",
  title: YvFnaCpqp = "Start referral",
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    variants,
    baseVariant,
    gestureVariant,
    classNames,
    transition,
    setVariant,
    setGestureState
  } = useVariantState({
    defaultVariant: "M3SwnaRw0",
    variant,
    transitions,
    variantClassNames,
    cycleOrder
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-vo7pw", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : undefined
      },
      children: /*#__PURE__*/_jsx(motion.div, {
        ...restProps,
        className: cx("framer-1e2ggx2", className),
        style: {
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          borderTopRightRadius: 6,
          borderTopLeftRadius: 6,
          boxShadow: "0px 2px 4px 0px rgba(0, 0, 0, 0.15)",
          backgroundColor: "rgb(0, 0, 0)",
          ...style
        },
        layoutId: "M3SwnaRw0",
        transition: transition,
        layoutDependency: layoutDependency,
        "data-framer-name": "Desktop",
        ref: ref,
        children: /*#__PURE__*/_jsx(Text, {
          style: {
            "--framer-font-family": '"Inter", sans-serif',
            "--framer-font-style": "normal",
            "--framer-font-weight": 500,
            "--framer-text-color": "rgb(255, 255, 255)",
            "--framer-font-size": "16px",
            "--framer-letter-spacing": "0px",
            "--framer-text-transform": "none",
            "--framer-text-decoration": "none",
            "--framer-line-height": "16px",
            "--framer-text-alignment": "center"
          },
          withExternalLayout: true,
          verticalAlignment: "center",
          __fromCanvasComponent: true,
          __link: "",
          alignment: "center",
          fonts: ["GF;Inter-500"],
          className: "framer-1pw5m9y",
          layoutId: "dg7MnRFMp",
          transition: transition,
          layoutDependency: layoutDependency,
          rawHTML: "<span style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Start referral</span><br></span></span>",
          text: YvFnaCpqp
        })
      })
    })
  });
});
const css = ['.framer-vo7pw [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-vo7pw * { box-sizing: border-box; }", ".framer-vo7pw .framer-1e2ggx2 { position: relative; overflow: visible; width: min-content; height: min-content; display: flex; flex-direction: row; justify-content: center; align-content: center; align-items: center; flex-wrap: nowrap; gap: 12px; padding: 16px 12px 16px 12px; }", ".framer-vo7pw .framer-1pw5m9y { position: relative; overflow: visible; width: auto; height: auto; flex: none; white-space: pre; }", ".framer-vo7pw .framer-3awsst { position: relative; overflow: visible; width: min-content; height: auto; flex: none; align-self: stretch; display: flex; flex-direction: row; justify-content: flex-start; align-content: center; align-items: center; flex-wrap: nowrap; gap: 10px; padding: 0px 0px 0px 0px; }", ".framer-vo7pw .framer-1nnterx { position: relative; width: 22px; height: 22px; flex: none; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-vo7pw framer-1e2ggx2 > * { margin: 0px; margin-left: calc(12px / 2); margin-right: calc(12px / 2); } .framer-vo7pw framer-1e2ggx2 > :first-child, .framer-vo7pw framer-3awsst > :first-child { margin-left: 0px; } .framer-vo7pw framer-1e2ggx2 > :last-child, .framer-vo7pw framer-3awsst > :last-child { margin-right: 0px; } .framer-vo7pw framer-3awsst > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicHeight 48
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerIntrinsicWidth 122
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["auto","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @framerVariables {"YvFnaCpqp":"title"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                */
const FrameraAKtK24Uj = withCSS(Component, css);
export default FrameraAKtK24Uj;
FrameraAKtK24Uj.displayName = "Assets/Button Refer";
FrameraAKtK24Uj.defaultProps = {
  width: 122,
  height: 48
};
addPropertyControls(FrameraAKtK24Uj, {
  YvFnaCpqp: {
    type: ControlType.String,
    title: "Title",
    defaultValue: "Start referral",
    displayTextArea: false
  }
});
addFonts(FrameraAKtK24Uj, [{
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
  family: "Inter",
  style: "normal",
  weight: "500",
  moduleAsset: {
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZhrib2Bg-4.ttf",
    localModuleIdentifier: "local-module:canvasComponent/aAKtK24Uj:default"
  }
}]);
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "reactComponent",
      "name": "FrameraAKtK24Uj",
      "slots": [],
      "annotations": {
        "framerIntrinsicHeight": "48",
        "framerContractVersion": "1",
        "framerIntrinsicWidth": "122",
        "framerVariables": "{\"YvFnaCpqp\":\"title\"}",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"auto\",\"auto\"]}}}"
      }
    },
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};
//# sourceMappingURL=./aAKtK24Uj.map