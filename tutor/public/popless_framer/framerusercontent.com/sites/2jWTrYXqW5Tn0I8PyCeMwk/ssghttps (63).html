import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors([]);
export const fonts = [];
export const css = [".framer-S29dN .framer-styles-preset-1oq1n:not(.rich-text-wrapper), .framer-S29dN .framer-styles-preset-1oq1n.rich-text-wrapper a { --framer-link-text-color: #ffffff; --framer-link-text-decoration: underline; --framer-link-hover-text-color: #A8A8A8; --framer-link-hover-text-decoration: underline; }"];
export const className = "framer-S29dN";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};