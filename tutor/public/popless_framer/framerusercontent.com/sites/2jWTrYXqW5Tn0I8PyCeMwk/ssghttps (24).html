import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter"]);
export const fonts = [];
export const css = ['.framer-r9vjV .framer-styles-preset-16bzrdu:not(.rich-text-wrapper), .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper p, .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 2em; --framer-text-alignment: start; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; }', '@media (max-width: 1279px) and (min-width: 810px) { .framer-r9vjV .framer-styles-preset-16bzrdu:not(.rich-text-wrapper), .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper p, .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 2em; --framer-text-alignment: start; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-r9vjV .framer-styles-preset-16bzrdu:not(.rich-text-wrapper), .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper p, .framer-r9vjV .framer-styles-preset-16bzrdu.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter", sans-serif; --framer-font-size: 16px; --framer-font-style: normal; --framer-font-weight: 400; --framer-letter-spacing: 0px; --framer-line-height: 2em; --framer-text-alignment: start; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #202124); --framer-text-decoration: none; --framer-text-transform: none; } }'];
export const className = "framer-r9vjV";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};