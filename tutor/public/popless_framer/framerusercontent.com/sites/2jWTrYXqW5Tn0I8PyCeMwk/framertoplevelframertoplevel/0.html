
        import * as React from "react"
        import * as ReactD<PERSON> from "react-dom/client"
        import * as Fr<PERSON>r from "framer"

        window.__framer_importFromPackage = (packageAndFilename, exportIdentifier) => () => {
            return React.createElement(Framer.ErrorPlaceholder, { error: 'Package component not supported: "' + exportIdentifier + '" in "' + packageAndFilename + '"' })
        }

        // A lot of libraries assume process.env.NODE_ENV is present in runtime/buildtime, so we are polyfilling it
        window.process = {
            ...window.process,
            env: {
                ...(window.process ? window.process.env: undefined),
                NODE_ENV: "production"
            }
        }

        // Fallback support for stack gaps
        Framer.installFlexboxGapWorkaroundIfNeeded()

        ;(async () => {
            const routes = {UNJugco9I: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/f7WWo0jjlPWnSmwtYLgY/pbW67k0hZwqQichgv0NQ/UNJugco9I.js")), path: "/"}, HpZJQctQp: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/ZKsZVmEKNLJri4mtbJwv/LUarfPayLta2Bpcz7tFa/HpZJQctQp.js")), path: "/old-home"}, BNamIfoSR: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/jcExVmCG31R6zlTDtRKF/0IypvQyFIex2DqgJKg2X/BNamIfoSR.js")), path: "/test"}, QF7hn4Urr: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/kRk63aOIqFK8dLOS70pa/ULBppygAcL0P9QHT9Z1B/QF7hn4Urr.js")), path: "/test_OLD"}, l8HMji2Yk: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/GHBrs3qcgE1MZN8jgi8G/5D7ZwSKA3sDIMaKDAqDo/l8HMji2Yk.js")), path: "/lllllllllllllllll-primary-llllllllllllllllllllllllllllllllllllllllll-4"}, eAoNDui3w: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/lzzCQFJPvYDGMtr96WMD/LUdhlsGjjnIk5U3FfTOf/eAoNDui3w.js")), path: "/tutoring"}, fwhrKHx6D: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/uUXVH9UjaDTQYeVupzbk/MY4mEtNTDE7LBWZ0m41d/fwhrKHx6D.js")), path: "/pricing"}, QXYyFVKW8: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/VyfvCC8tRrR3cwsezG4w/cKiV35YmJw6WLxBr7pRZ/QXYyFVKW8.js")), path: "/group-classes"}, ioeg9oqlo: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/phroKXAeUVLC6sUOjRji/7EP2dD51DkI1UVWLVIMj/ioeg9oqlo.js")), path: "/marketplace"}, qFQZWHHvr: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/lWPWy9atCrJxUpdINAPH/tdnc7Xb9a3skaxocRpTk/qFQZWHHvr.js")), path: "/updates"}, ijFjMFkqN: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/iNYB5yyQBpdDXuyNMGpf/Q8BlRCpwAi6YU2yDcH8r/ijFjMFkqN.js")), path: "/faq"}, J_W7bxNYj: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/pYYo8uh1dkjb7Yki2kbY/OGPa7AkF7IaGRVdLSkKa/J_W7bxNYj.js")), path: "/mission"}, CqoljZCO0: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/8FRuXS68TJ23bNdkzHef/pJRGF9IrrIfaAkxhpSaQ/CqoljZCO0.js")), path: "/support"}, AYia5YfcP: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/oFTB7j7OzpPlUbRtsGig/JmkMUF1yj2MkbVLVAaY6/AYia5YfcP.js")), path: "/refer"}, wmQov76yY: {elements: {cFxm4yzqh: "classes"}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/6MNBbH4WsvcA7VOZ8H7B/RJB4RCYZ23rZjulRwXwl/wmQov76yY.js")), path: "/frank-gibson/organic-chemistry"}, AVhswzP0P: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/FcTAmEEffbjIFqmYctnc/fJYFm4HSiaxcwkrvMPW9/AVhswzP0P.js")), path: "/lllllllllllll-secondary-llllllllllllllllllllllllllllllllllllllllll-4"}, BhhXd9j5C: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/zKdvoKfFdgRFWt9IvspV/eCmkrDVcRfhWOl9XEbvo/BhhXd9j5C.js")), path: "/discord"}, fkcJWEs7D: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/1pXaXgP7RD7h2hK0CTOb/fQ0zj7Vn64rxmSPVLEZZ/fkcJWEs7D.js")), path: "/contact"}, hR530hScY: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/ZOWgD0xjex6D7TCNcZR3/FomC99dfw2XQyDbagbAm/hR530hScY.js")), path: "/accessibility"}, P8liG2Pv7: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/87625W19MOYHyNWpQOXb/jznwgjAI2HB0G5ezoUSk/P8liG2Pv7.js")), path: "/legal-and-policies"}, gMU7brHGJ: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/6b4e57GndRYF5KNizDmc/WMXvdQvbOa3zaIBFNPTZ/gMU7brHGJ.js")), path: "/request-access"}, SA5mxN5n1: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/2mDbxhTWj1aYUDtbBgAH/ipLbQ9qAOYDG1maSfJjN/SA5mxN5n1.js")), path: "/request-access/thanks"}, j8quOYh_9: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/6JFoNrxQZJsoXpoLKmFK/Ra0lCbnvL8nYnk9XbloJ/j8quOYh_9.js")), path: "/lllllllllllll-careers-lllllllllllllllllllllllllllllllllllllll"}, QdObsJ2ml: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/q3Eb1hB2C6AWsM5swSIF/Dhhu6kNWPve2w2ZAxRmv/QdObsJ2ml.js")), path: "/careers"}, l6a0R6XRQ: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/BsptAJFUbNjqDW5qHQwF/4MDJv4gp4ohIEwh0mVH9/l6a0R6XRQ.js")), path: "/careers/senior-software-engineer-frontend"}, kjpXaLPF9: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/llytiwXk7WdmzFoKsWEN/SQY5T4EK9GpicBIM6zkz/kjpXaLPF9.js")), path: "/careers/senior-software-engineer-mobile"}, bA9whlkdU: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/RmQG5LYcUBCh4g1AVep4/8V5CouBUqEm1xPRS8GP2/bA9whlkdU.js")), path: "/lllllllllllll-hidden-lllllllllllllllllllllllllllllllllllllll-2"}, LycHsr6mA: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/nBYubYOy9XBSomqAn09r/WkXmV4MdXvTLCj7MuCgr/LycHsr6mA.js")), path: "/compare"}, F3QuH4fKY: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/pVXnJXywpF3czMS1Psbq/r8dyLllB2C0qqQx25xpD/F3QuH4fKY.js")), path: "/discover"}, tgeC919VQ: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/C2YBsyce9mD9QGf5HMDO/wK6F8mTbrNi1QfbblbZC/tgeC919VQ.js")), path: "/blog"}, dBAu0VgXP: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/SQ4TbF1bsWv8xu1BCC94/tMPpmOg3ppoZWriP4oNr/dBAu0VgXP.js")), path: "/lllllllllllll-demo-lllllllllllllllllllllllllllllllllllllll-2-3"}, DHwLFQTVJ: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/oDvvfDC5YbqEzxCWw4FS/3YWIMHe3dDUNc19vh9tV/DHwLFQTVJ.js")), path: "/x/insights"}, mN2tYItDV: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/6cMzi3kVCryPrZxyro6j/vTHYQisW8RAc1YXSaQCD/mN2tYItDV.js")), path: "/x/meeting"}, DS6_d4CKE: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/V8l1xzHq27ZaJFeH5f68/KVe0SslYCaldWOGkOGAq/DS6_d4CKE.js")), path: "/x/messages"}, CecifcH5l: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/rQXAR2VqH0uTTNaNNpTX/eECgTRcKxi9LXUa4x5MN/CecifcH5l.js")), path: "/lllllllllllll-archive-lllllllllllllllllllllllllllllllllllllll-2-4"}, gds6TdRhp: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/z3SfdLqnjJ3mgxUUe8O2/qAkNTMPErsBpOSpFgIOM/gds6TdRhp.js")), path: "/host"}, rc8XUGs4y: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/J2320R9iy7Jdz7rCeJgf/mBBrdr0OCZwbSSm6BlzY/rc8XUGs4y.js")), path: "/tutoring-3-3"}, sXWYZmn5y: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/dsfyC9yv8y32NcCLebEK/wrQluY6aiSDs3v0AA2qD/sXWYZmn5y.js")), path: "/tutoring-spare-page"}, AOFAcxjJb: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/B0e0xkNXjE5ckxlDlZpE/I66lVAT9ZcGfqTtUwiVv/AOFAcxjJb.js")), path: "/pricing-spare-page"}, RpnLZX9Xn: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/ktMlvxWhFrpZKaoWClfU/TXyoo50b32m4HvaobgpS/RpnLZX9Xn.js")), path: "/page"}, Mn8_KtK1_: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/31o1nUNmhpa3NFANAN06/cAuTSsT4EthWK4y0EOPg/Mn8_KtK1_.js")), path: "/article/:ZROL0a4vs"}, caI63G_Mf: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/C5IDiOHQv0aDx009zHzV/VLJlyWkqKio8sM8wBxA7/caI63G_Mf.js")), path: "/legal/:L4KcAaijt"}, ekvU4cI_s: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/Hj8M7LbfX5ovRMRc6SrB/LZOV944IfIqHHXnLWCfW/ekvU4cI_s.js")), path: "/blog/:Yyaoae6sb"}, LFx1gnWnR: {elements: {}, page: Framer.lazy(() => import("https://framerusercontent.com/modules/5OlDo1lKMYeFnp4EaVQy/Qq4KZnA7zeTM0HhZcRWy/LFx1gnWnR.js")), path: "/updates/:aGtle5Xwm"}, e0CIRIH9Y: {page: Framer.lazy(() => import("https://framerusercontent.com/modules/vH8CqUWOxfPNpX0in0RG/EfNGJuLNhLLbG74FtY1Q/e0CIRIH9Y.js"))}}
            const notFoundPage = Framer.lazy(() => import("__framer-not-found-page"))
            const container = document.getElementById("main")

            let routeId, pathVariables, shouldHydrate = false
            if ("framerHydrateV2" in container.dataset) {
                const routeData = JSON.parse(container.dataset.framerHydrateV2)
                routeId = routeData.routeId
                pathVariables = routeData.pathVariables
                shouldHydrate = true
            } else {
                const routeData = Framer.inferInitialRouteFromPath(routes, decodeURIComponent(location.pathname))
                routeId = routeData.routeId
                pathVariables = routeData.pathVariables
            }

            const route = routes[routeId]
            const RootComponent = await route.page.preload()
            routes[routeId].page = RootComponent

            const element = React.createElement(
                Framer.PageRoot,
                {
                    RootComponent,
                    isWebsite: true,
                    routeId,
                    pathVariables,
                    routes,
                    notFoundPage,
                    isReducedMotion: false,
                    includeDataObserver: false,
                }
            )

            if (shouldHydrate) {
                React.startTransition(() => {
                    ReactDOM.hydrateRoot(container, element)
                })
            } else {
                ReactDOM.createRoot(container).render(element)
            }
        })().catch(error => {
            window.__send_framer_event && window.__send_framer_event("published_site_load_error", {
                message: String(error),
                stack: error instanceof Error && typeof error.stack === "string" ? error.stack : null
            })
            throw error
        })

        
    