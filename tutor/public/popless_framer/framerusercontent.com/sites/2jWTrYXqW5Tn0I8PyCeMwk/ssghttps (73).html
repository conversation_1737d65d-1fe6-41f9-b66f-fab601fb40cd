// Generated by Fr<PERSON>r (30f03e7)
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { addFonts, addPropertyControls, ControlType, cx, getFonts, Link, Text, useVariantState, withCSS } from "framer";
import { LayoutGroup, motion } from "framer-motion";
import * as React from "react";
import AssetsButtonMain from "https://framerusercontent.com/modules/ocbwDwrCSLiVC873hFen/SZ3EEPoJIeHkm0wEe9x3/W7ao3lSRJ.js";
const AssetsButtonMainFonts = getFonts(AssetsButtonMain);
const enabledGestures = {
  k7dB1IPY5: {
    hover: true
  }
};
const cycleOrder = ["k7dB1IPY5", "MQyYd_49D"];
const variantClassNames = {
  k7dB1IPY5: "framer-v-1l58967",
  MQyYd_49D: "framer-v-amjcnp"
};
function addPropertyOverrides(overrides, ...variants) {
  const nextOverrides = {};
  variants === null || variants === void 0 ? void 0 : variants.forEach(variant => variant && Object.assign(nextOverrides, overrides[variant]));
  return nextOverrides;
}
const humanReadableVariantMap = {
  Default: "k7dB1IPY5",
  Mobile: "MQyYd_49D"
};
const transitions = {
  "k7dB1IPY5-hover": {
    damping: 60,
    delay: 0,
    duration: 0,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  },
  default: {
    damping: 60,
    delay: 0,
    duration: 0,
    ease: [.44, 0, .56, 1],
    mass: 1,
    stiffness: 500,
    type: "tween"
  }
};
const BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function useRandomID() {
  const ref = React.useRef(null);
  if (ref.current === null) {
    ref.current = Array(5).fill(0).map(() => BASE62[Math.floor(Math.random() * BASE62.length)]).join("");
  }
  return ref.current;
}
const Component = /*#__PURE__*/React.forwardRef(function ({
  id,
  style: externalStyle = {},
  className,
  width,
  height,
  layoutId,
  variant: outerVariant = "k7dB1IPY5",
  title: oWvjRDreT = "Title",
  position: E3PiDyb_D = "Remote",
  link: ohnNEvsXM,
  ...restProps
}, ref) {
  const outerVariantId = humanReadableVariantMap[outerVariant];
  const variant = outerVariantId || outerVariant;
  const {
    baseVariant,
    classNames,
    gestureVariant,
    setGestureState,
    setVariant,
    transition,
    variants
  } = useVariantState({
    cycleOrder,
    defaultVariant: "k7dB1IPY5",
    enabledGestures,
    transitions,
    variant,
    variantClassNames
  });
  const layoutDependency = variants.join("-") + restProps.layoutDependency;
  const defaultLayoutId = useRandomID();
  const {
    pointerEvents,
    ...style
  } = externalStyle;
  return /*#__PURE__*/_jsx(LayoutGroup, {
    id: layoutId !== null && layoutId !== void 0 ? layoutId : defaultLayoutId,
    children: /*#__PURE__*/_jsx(motion.div, {
      "data-framer-generated": true,
      initial: variant,
      animate: variants,
      onHoverStart: () => setGestureState({
        isHovered: true
      }),
      onHoverEnd: () => setGestureState({
        isHovered: false
      }),
      onTapStart: () => setGestureState({
        isPressed: true
      }),
      onTap: () => setGestureState({
        isPressed: false
      }),
      onTapCancel: () => setGestureState({
        isPressed: false
      }),
      className: cx("framer-V8VU6", classNames),
      style: {
        display: "contents",
        pointerEvents: pointerEvents !== null && pointerEvents !== void 0 ? pointerEvents : "auto"
      },
      children: /*#__PURE__*/_jsx(Link, {
        href: ohnNEvsXM,
        openInNewTab: true,
        children: /*#__PURE__*/_jsx(motion.a, {
          ...restProps,
          className: `${cx("framer-1l58967", className)} framer-mhmppm`,
          "data-framer-name": "Default",
          layoutDependency: layoutDependency,
          layoutId: "k7dB1IPY5",
          ref: ref,
          style: {
            backgroundColor: "rgba(0, 0, 0, 0)",
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
            ...style
          },
          transition: transition,
          variants: {
            "k7dB1IPY5-hover": {
              backgroundColor: "var(--token-ce5164cd-4223-4bb7-8552-21eb990c41c0, rgb(245, 245, 245))",
              borderBottomLeftRadius: 7,
              borderBottomRightRadius: 7,
              borderTopLeftRadius: 7,
              borderTopRightRadius: 7
            }
          },
          ...addPropertyOverrides({
            "k7dB1IPY5-hover": {
              "data-framer-name": undefined
            },
            MQyYd_49D: {
              "data-framer-name": "Mobile"
            }
          }, baseVariant, gestureVariant),
          children: /*#__PURE__*/_jsxs(motion.div, {
            className: "framer-12g59sb",
            layoutDependency: layoutDependency,
            layoutId: "b_yWPMCzh",
            transition: transition,
            children: [/*#__PURE__*/_jsxs(motion.div, {
              className: "framer-g758k4",
              layoutDependency: layoutDependency,
              layoutId: "on3821DG9",
              transition: transition,
              children: [/*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "left",
                className: "framer-xjv2y2",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "FH53LXtQk",
                rawHTML: "<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Title</span><br></span></p>",
                style: {
                  "--framer-font-family": '"Inter", sans-serif',
                  "--framer-font-size": "18px",
                  "--framer-font-style": "normal",
                  "--framer-font-weight": 400,
                  "--framer-letter-spacing": "0px",
                  "--framer-line-height": "26px",
                  "--framer-link-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  "--framer-link-text-decoration": "underline",
                  "--framer-text-alignment": "left",
                  "--framer-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  "--framer-text-decoration": "none",
                  "--framer-text-transform": "none"
                },
                text: oWvjRDreT,
                transition: transition,
                variants: {
                  MQyYd_49D: {
                    "--framer-font-size": "16px"
                  }
                },
                verticalAlignment: "top",
                withExternalLayout: true
              }), /*#__PURE__*/_jsx(Text, {
                __fromCanvasComponent: true,
                alignment: "left",
                className: "framer-1vulrol",
                "data-framer-name": "Remote",
                fonts: ["GF;Inter-regular"],
                layoutDependency: layoutDependency,
                layoutId: "vQI9kq2G1",
                rawHTML: "<p style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Remote</span><br></span></p>",
                style: {
                  "--framer-font-family": '"Inter", sans-serif',
                  "--framer-font-size": "16px",
                  "--framer-font-style": "normal",
                  "--framer-font-weight": 400,
                  "--framer-letter-spacing": "0px",
                  "--framer-line-height": "30px",
                  "--framer-link-text-color": "var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))",
                  "--framer-link-text-decoration": "underline",
                  "--framer-text-alignment": "left",
                  "--framer-text-color": "var(--token-840e2253-3db7-4fe4-9c04-053d2f50f134, rgb(168, 168, 168))",
                  "--framer-text-decoration": "none",
                  "--framer-text-transform": "none"
                },
                text: E3PiDyb_D,
                transition: transition,
                variants: {
                  MQyYd_49D: {
                    "--framer-text-color": "rgb(128, 128, 128)"
                  }
                },
                verticalAlignment: "top",
                withExternalLayout: true
              })]
            }), /*#__PURE__*/_jsx(motion.div, {
              className: "framer-836ely-container",
              layoutDependency: layoutDependency,
              layoutId: "RA5ovwIrX-container",
              transition: transition,
              children: /*#__PURE__*/_jsx(AssetsButtonMain, {
                background: "rgb(3, 104, 224)",
                buttonBG: "rgb(0, 0, 0)",
                height: "100%",
                id: "RA5ovwIrX",
                layoutId: "RA5ovwIrX",
                style: {
                  height: "100%"
                },
                textColour: "rgb(255, 255, 255)",
                title: "Apply",
                variant: "w5a5PqQdU",
                width: "100%"
              })
            })]
          })
        })
      })
    })
  });
});
const css = ['.framer-V8VU6 [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none; }', "@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }", ".framer-V8VU6 * { box-sizing: border-box; }", ".framer-V8VU6 .framer-mhmppm { display: block; }", ".framer-V8VU6 .framer-1l58967 { align-content: center; align-items: center; display: flex; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; text-decoration: none; width: 1161px; }", ".framer-V8VU6 .framer-12g59sb { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: row; flex-wrap: nowrap; height: min-content; justify-content: space-between; overflow: visible; padding: 12px 12px 12px 12px; position: relative; width: 1px; }", ".framer-V8VU6 .framer-g758k4 { align-content: center; align-items: center; display: flex; flex: 1 0 0px; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: min-content; justify-content: flex-start; overflow: visible; padding: 0px 0px 0px 0px; position: relative; width: 1px; }", ".framer-V8VU6 .framer-xjv2y2, .framer-V8VU6 .framer-1vulrol { flex: none; height: auto; overflow: hidden; position: relative; white-space: pre-wrap; width: 100%; word-break: break-word; word-wrap: break-word; }", ".framer-V8VU6 .framer-836ely-container { flex: none; height: 40px; position: relative; width: auto; }", ".framer-V8VU6 .framer-v-1l58967 .framer-1l58967 { cursor: pointer; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-V8VU6 .framer-1l58967, .framer-V8VU6 .framer-g758k4 { gap: 0px; } .framer-V8VU6 .framer-1l58967 > * { margin: 0px; margin-left: calc(10px / 2); margin-right: calc(10px / 2); } .framer-V8VU6 .framer-1l58967 > :first-child { margin-left: 0px; } .framer-V8VU6 .framer-1l58967 > :last-child { margin-right: 0px; } .framer-V8VU6 .framer-g758k4 > * { margin: 0px; margin-bottom: calc(0px / 2); margin-top: calc(0px / 2); } .framer-V8VU6 .framer-g758k4 > :first-child { margin-top: 0px; } .framer-V8VU6 .framer-g758k4 > :last-child { margin-bottom: 0px; } }", ".framer-V8VU6.framer-v-amjcnp .framer-1l58967 { width: 320px; }", ".framer-V8VU6.framer-v-amjcnp .framer-12g59sb { align-content: flex-start; align-items: flex-start; flex-direction: column; gap: 3px; justify-content: flex-start; padding: 0px 0px 0px 0px; }", ".framer-V8VU6.framer-v-amjcnp .framer-g758k4 { flex: none; width: 100%; }", "@supports (background: -webkit-named-image(i)) and (not (font-palette:dark)) { .framer-V8VU6.framer-v-amjcnp .framer-12g59sb { gap: 0px; } .framer-V8VU6.framer-v-amjcnp .framer-12g59sb > * { margin: 0px; margin-bottom: calc(3px / 2); margin-top: calc(3px / 2); } .framer-V8VU6.framer-v-amjcnp .framer-12g59sb > :first-child { margin-top: 0px; } .framer-V8VU6.framer-v-amjcnp .framer-12g59sb > :last-child { margin-bottom: 0px; } }"]; /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * This is a generated Framer component.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerIntrinsicHeight 80
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerIntrinsicWidth 1161
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerCanvasComponentVariantDetails {"propertyName":"variant","data":{"default":{"layout":["fixed","auto"]},"MQyYd_49D":{"layout":["fixed","auto"]},"L2zZxSeQB":{"layout":["fixed","auto"]}}}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  * @framerVariables {"oWvjRDreT":"title","E3PiDyb_D":"position","ohnNEvsXM":"link"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  */
const FramerSpyChwHOV = withCSS(Component, css);
export default FramerSpyChwHOV;
FramerSpyChwHOV.displayName = "Main/Job Opening";
FramerSpyChwHOV.defaultProps = {
  height: 80,
  width: 1161
};
addPropertyControls(FramerSpyChwHOV, {
  variant: {
    options: ["k7dB1IPY5", "MQyYd_49D"],
    optionTitles: ["Default", "Mobile"],
    title: "Variant",
    type: ControlType.Enum
  },
  oWvjRDreT: {
    defaultValue: "Title",
    displayTextArea: false,
    title: "Title",
    type: ControlType.String
  },
  E3PiDyb_D: {
    defaultValue: "Remote",
    displayTextArea: false,
    title: "Position",
    type: ControlType.String
  },
  ohnNEvsXM: {
    title: "Link",
    type: ControlType.Link
  }
});
addFonts(FramerSpyChwHOV, [{
  family: "Inter",
  moduleAsset: {
    localModuleIdentifier: "local-module:canvasComponent/SpyChwHOV:default",
    url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf"
  },
  style: "normal",
  url: "https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf",
  weight: "400"
}, ...AssetsButtonMainFonts]);
export const __FramerMetadata__ = {
  "exports": {
    "Props": {
      "type": "tsType",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "default": {
      "type": "reactComponent",
      "name": "FramerSpyChwHOV",
      "slots": [],
      "annotations": {
        "framerVariables": "{\"oWvjRDreT\":\"title\",\"E3PiDyb_D\":\"position\",\"ohnNEvsXM\":\"link\"}",
        "framerCanvasComponentVariantDetails": "{\"propertyName\":\"variant\",\"data\":{\"default\":{\"layout\":[\"fixed\",\"auto\"]},\"MQyYd_49D\":{\"layout\":[\"fixed\",\"auto\"]},\"L2zZxSeQB\":{\"layout\":[\"fixed\",\"auto\"]}}}",
        "framerIntrinsicWidth": "1161",
        "framerIntrinsicHeight": "80",
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};