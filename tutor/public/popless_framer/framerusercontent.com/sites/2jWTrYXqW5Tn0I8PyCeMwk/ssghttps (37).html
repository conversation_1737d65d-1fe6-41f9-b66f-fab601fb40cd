const metadata = params => ({
  breakpoints: [{
    hash: "1nsxunx",
    mediaQuery: "(min-width: 1920px)"
  }, {
    hash: "xuwg8b",
    mediaQuery: "(min-width: 1280px) and (max-width: 1919px)"
  }, {
    hash: "kal1qa",
    mediaQuery: "(min-width: 810px) and (max-width: 1279px)"
  }, {
    hash: "e0t7kp",
    mediaQuery: "(max-width: 809px)"
  }],
  elements: {},
  title: "Popless | The all-in-one tutor platform for private tutors and group classes",
  viewport: "width=device-width"
});
export default metadata;
export const metadataVersion = 1;
export const __FramerMetadata__ = {
  "exports": {
    "default": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "metadataVersion": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};