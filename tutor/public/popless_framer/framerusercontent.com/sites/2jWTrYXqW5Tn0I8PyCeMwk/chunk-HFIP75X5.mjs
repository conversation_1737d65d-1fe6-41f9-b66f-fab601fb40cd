import{a as z}from"./chunk-OPNTFX3G.mjs";import{b as r}from"./chunk-VWWF2A63.mjs";import{K as g,N as w,P as u,Q as o,g as c,ga as b,ja as v,n as h,qa as _,t as y,ta as a,ua as C,v as x,xa as V}from"./chunk-5F276QAW.mjs";var G=["XAG5E5Jgd"],A={XAG5E5Jgd:"framer-v-yfw3in"},E={},K={default:{type:"spring",ease:[.44,0,.56,1],duration:.3,delay:0,stiffness:500,damping:60,mass:1}},J=c(function({style:R,className:X,width:L,height:k,layoutId:n,variant:s="XAG5E5Jgd",title:S="Privacy Policy",...i},T){let f=E[s]||s,{variants:m,baseVariant:H,gestureVariant:P,classNames:N,transition:l,setVariant:j,setGestureState:e}=_({defaultVariant:"XAG5E5Jgd",variant:f,transitions:K,variantClassNames:A,cycleOrder:G}),p=m.join("-")+i.layoutDependency,U=h(()=>({}),[]),d=v(H,P,U),B=z();return r(x,{id:n??B,children:r(y.div,{initial:f,animate:m,onHoverStart:()=>e({isHovered:!0}),onHoverEnd:()=>e({isHovered:!1}),onTapStart:()=>e({isPressed:!0}),onTap:()=>e({isPressed:!1}),onTapCancel:()=>e({isPressed:!1}),className:o("framer-XHiXU",N),style:{display:"contents"},children:r(u,{...i,layoutId:"XAG5E5Jgd",className:o("framer-yfw3in",X),style:{...R},background:null,direction:"horizontal",distribution:"start",alignment:"center",gap:10,__fromCanvasComponent:!0,__contentWrapperStyle:{width:"100%",height:"auto",padding:"0px 0px 0px 0px"},center:!1,"data-framer-name":"Variant 1",transition:l,layoutDependency:p,ref:T,...d("XAG5E5Jgd"),children:r(C,{style:{"--framer-font-family":'"Inter", sans-serif',"--framer-font-style":"normal","--framer-font-weight":600,"--framer-text-color":"var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, rgb(32, 33, 36))","--framer-font-size":"32px","--framer-letter-spacing":"-1px","--framer-text-transform":"none","--framer-text-decoration":"none","--framer-line-height":"1.4em","--framer-text-alignment":"left"},withExternalLayout:!0,verticalAlignment:"top",__fromCanvasComponent:!0,alignment:"left",fonts:["GF;Inter-600"],layoutId:"NDch71v_1",className:"framer-1tahg5s",rawHTML:"<h1 style='font-size: 0; line-height: 0; tab-size: 4; white-space: inherit; word-wrap: inherit'><span style='direction: ltr; font-size: 0'><span style=''>Privacy Policy</span><br></span></h1>",text:S,transition:l,layoutDependency:p,...d("NDch71v_1")})})})})}),D=['.framer-XHiXU [data-border="true"]::after { content: ""; border-width: var(--border-top-width, 0) var(--border-right-width, 0) var(--border-bottom-width, 0) var(--border-left-width, 0); border-color: var(--border-color, none); border-style: var(--border-style, none); width: 100%; height: 100%; position: absolute; box-sizing: border-box; left: 0; top: 0; border-radius: inherit; pointer-events: none;}',"@supports (aspect-ratio: 1) { body { --framer-aspect-ratio-supported: auto; } }",".framer-XHiXU * { box-sizing: border-box; }",".framer-XHiXU .framer-yfw3in { position: relative; overflow: visible; width: 203px; height: min-content; }",".framer-XHiXU .framer-1tahg5s { position: relative; overflow: hidden; width: 1px; height: auto; flex: 1 0 0px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; }"],t=b(J,D),$=t;t.displayName="Terms/H1";t.defaultProps={width:203,height:45};w(t,{vsL9WVh4y:{type:g.String,title:"Title",defaultValue:"Privacy Policy",displayTextArea:!1}});V(t,[{url:"https://fonts.gstatic.com/s/inter/v8/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",family:"Inter",style:"normal",weight:"600",moduleAsset:{url:"https://fonts.gstatic.com/s/inter/v8/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZhrib2Bg-4.ttf",localModuleIdentifier:"local-module:canvasComponent/BlyUUTgI8:default"}}]);a.loadWebFontsFromSelectors(["Inter-Bold"]);var ae=[],ne=['.framer-y4sJy h2.framer-styles-preset-1m9bzi2, .framer-y4sJy .framer-styles-preset-1m9bzi2 h2 { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-style: normal; --framer-font-weight: 700; --framer-text-color: #333333; --framer-font-size: 28px; --framer-letter-spacing: 0px; --framer-text-transform: none; --framer-text-decoration: none; --framer-line-height: 1.4em; --framer-text-alignment: start; }'],oe="framer-y4sJy";a.loadWebFontsFromSelectors(["Inter-Bold"]);var me=[],le=['.framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 22px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; }','@media (max-width: 1279px) and (min-width: 810px) { .framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 20px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; } }','@media (max-width: 809px) and (min-width: 0px) { .framer-sRzif .framer-styles-preset-ci2ngw:not(.rich-text-wrapper), .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper h3, .framer-sRzif .framer-styles-preset-ci2ngw.rich-text-wrapper [data-preset-tag="h3"] { --framer-font-family: "Inter-Bold", "Inter", sans-serif; --framer-font-size: 18px; --framer-font-style: normal; --framer-font-weight: 700; --framer-letter-spacing: 0px; --framer-line-height: 1.4em; --framer-text-alignment: start; --framer-text-color: #333333; --framer-text-decoration: none; --framer-text-transform: none; } }'],pe="framer-sRzif";export{$ as a,ae as b,ne as c,oe as d,me as e,le as f,pe as g};
//# sourceMappingURL=chunk-HFIP75X5.mjs.map
