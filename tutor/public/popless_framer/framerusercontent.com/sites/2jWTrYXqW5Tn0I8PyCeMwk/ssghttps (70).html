import { fontStore } from "framer";
fontStore.loadWebFontsFromSelectors(["Inter-Medium"]);
export const fonts = [];
export const css = ['.framer-UfH4Y .framer-styles-preset-1in54k:not(.rich-text-wrapper), .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper p, .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter-Medium", "Inter", sans-serif; --framer-font-size: 14px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #000000); --framer-text-decoration: none; --framer-text-transform: none; }', '@media (max-width: 1279px) and (min-width: 810px) { .framer-UfH4Y .framer-styles-preset-1in54k:not(.rich-text-wrapper), .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper p, .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter-Medium", "Inter", sans-serif; --framer-font-size: 11px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #000000); --framer-text-decoration: none; --framer-text-transform: none; } }', '@media (max-width: 809px) and (min-width: 0px) { .framer-UfH4Y .framer-styles-preset-1in54k:not(.rich-text-wrapper), .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper p, .framer-UfH4Y .framer-styles-preset-1in54k.rich-text-wrapper [data-preset-tag="p"] { --framer-font-family: "Inter-Medium", "Inter", sans-serif; --framer-font-size: 9px; --framer-font-style: normal; --framer-font-weight: 500; --framer-letter-spacing: 0px; --framer-line-height: 22px; --framer-paragraph-spacing: 0px; --framer-text-alignment: left; --framer-text-color: var(--token-15b486ba-2fc9-40ec-b282-d340ae737628, #000000); --framer-text-decoration: none; --framer-text-transform: none; } }'];
export const className = "framer-UfH4Y";
export const __FramerMetadata__ = {
  "exports": {
    "fonts": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "css": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "className": {
      "type": "variable",
      "annotations": {
        "framerContractVersion": "1"
      }
    },
    "__FramerMetadata__": {
      "type": "variable"
    }
  }
};