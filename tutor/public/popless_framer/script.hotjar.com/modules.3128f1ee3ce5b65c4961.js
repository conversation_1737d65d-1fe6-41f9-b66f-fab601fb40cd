/*! For license information please see modules.3128f1ee3ce5b65c4961.js.LICENSE.txt */

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="a4e746b2-b70d-5ba4-808b-4e6f21af91a5")}catch(e){}}();
!function(){var e={4788:function(e,t,n){"use strict";n.d(t,{s:function(){return r}});const r=Object.freeze({IDENTIFY_USER:"identify_user",AUTOTAG_RECORDING:"autotag_recording",TAG_RECORDING:"tag_recording",HEATMAP_HELO:"heatmap_helo",RECORDING_HELO:"recording_helo",REPORT_USER_ID:"report_user_id",MUTATION:"mutation",MOUSE_CLICK:"mouse_click",INPUT_CHOICE_CHANGE:"input_choice_change",KEY_PRESS:"key_press",MOUSE_MOVE:"mouse_move",RELATIVE_MOUSE_MOVE:"relative_mouse_move",CLIPBOARD:"clipboard",PAGE_VISIBILITY:"page_visibility",SCROLL_REACH:"scroll_reach",SCROLL:"scroll",SELECT_CHANGE:"select_change",VIEWPORT_RESIZE:"viewport_resize",SCRIPT_PERFORMANCE:"script_performance",REPORT_CONTENT:"report_content",INSERTED_RULE:"inserted_rule",DELETED_RULE:"deleted_rule"})},6939:function(e,t,n){"use strict";n.d(t,{f:function(){return f},W:function(){return g}});const r=Object.freeze({LIVE:"LIVE",REVIEW_WEBAPP:"REVIEW_WEBAPP",REVIEW:"REVIEW",STAGING:"STAGING",DEV:"DEV",DEV_OLD:"DEV_OLD"}),o=(()=>{const e=document.location.host.match(/^(insights-webapp|surveys-webapp|insights|surveys)-(.*?)((?:\.[^.]+)?(?:\.hotjarians\.net)|(?:\.[^.]+)?(?:\.eks\.hotjar\.com))$/);return e&&{component:e[1],reviewId:e[2],domain:e[3],reviewUrlSuffix:e[2]+e[3]}})()?.reviewUrlSuffix,i=Object.freeze({[r.LIVE]:{INSIGHTS:"insights.hotjar.com",SURVEYS:"surveys.hotjar.com"},[r.REVIEW]:{INSIGHTS:`insights-${o}`,SURVEYS:`surveys-${o}`},[r.REVIEW_WEBAPP]:{INSIGHTS:`insights-webapp-${o}`,SURVEYS:`surveys-webapp-${o}`},[r.STAGING]:{INSIGHTS:"insights-staging.hotjar.com",SURVEYS:"surveys-staging.hotjar.com"},[r.DEV]:{INSIGHTS:"local.hotjar.com:8443",SURVEYS:"surveys.local.hotjar.com:8443"},[r.DEV_OLD]:{INSIGHTS:"local.hotjar.com",SURVEYS:"surveys.local.hotjar.com"}}),a=e=>(t,n)=>t===i[e][n],s=a(r.DEV),c=a(r.DEV_OLD),u=a(r.LIVE),l=a(r.REVIEW_WEBAPP),h=a(r.REVIEW),d=a(r.STAGING),f=(e,t)=>{if(t)return`https://${t}/${e}`;const n=((e="INSIGHTS",t=document.location.host)=>u(t,e)?r.LIVE:s(t,e)?r.DEV:c(t,e)?r.DEV_OLD:l(t,e)?r.REVIEW_WEBAPP:h(t,e)?r.REVIEW:d(t,e)?r.STAGING:r.LIVE)();return`https://${i[n].SURVEYS}/${e}`},g=(e=document.location.href)=>{const t=[i[r.LIVE].SURVEYS,i[r.REVIEW_WEBAPP].SURVEYS,i[r.REVIEW].SURVEYS,i[r.STAGING].SURVEYS,i[r.DEV].SURVEYS,i[r.DEV_OLD].SURVEYS],n=document.createElement("a");return n.href=e,t.indexOf(n.hostname)>=0}},9163:function(e,t,n){"use strict";n.d(t,{R:function(){return r}});var r={getFeatures:hj.tryCatch((function(){return hj.settings.features||[]}),"hj.features.getFeatures"),hasFeature:hj.tryCatch((function(e){var t;try{var n=window.localStorage.getItem("HJ_OVERRIDE_FEATURE:".concat(e));t="true"===n||"1"===n}catch(e){t=!1}return r.getFeatures().indexOf(e)>-1||t}),"hj.features.hasFeature")}},9982:function(e,t,n){"use strict";n.d(t,{H:function(){return o}});var r=n(8422),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,r=0,o=!1;function c(){0!=--r||o||t.bind(this)()}function u(){o=!0,n.bind(this)()}Object.keys(e).forEach((function(t){var n=e[t];"string"==typeof n&&(n=[n]),r+=n.length,n.forEach((function(e){i(e,t,c,u)}))}))},i=function(e,t,n,o){var i;t===r.vH.SCRIPT?(i=document.createElement("script")).src="".concat(hj.scriptDomain).concat(e):t===r.vH.STYLESHEET&&((i=document.createElement("link")).href="".concat(hj.scriptDomain).concat(e),i.rel="stylesheet"),i.onload=n,i.onerror=o,document.getElementsByTagName("head")[0].appendChild(i)};function a(){}function s(){var e=this.src||this.href;hj.exceptions.log(new Error("Failed to load module: ".concat(e,".")),"loader")}},8417:function(e,t,n){"use strict";n.d(t,{c:function(){return i}});var r=n(9982),o=!1,i=function(e){var t=e.title,n=e.message,i=e.status,a=window.hjLazyModules;o?hj.widget.renderNotificationWidget({title:t,message:n,status:i}):(0,r.H)(a.NOTIFICATION,(function(){o=!0,hj.widget.renderNotificationWidget({title:t,message:n,status:i})}))}},4808:function(e,t,n){"use strict";n.d(t,{n:function(){return l}});var r=n(9780),o=[],i=[];function a(e,t){hj.tryCatch(t,"Rendering")(e)}function s(e,t){hj.widgetDelay.set((function(){hj.tryCatch(t,"Rendering")(e)}),1e3*e.display_delay)}function c(e,t,n){var r=hj.hq(document),i=hj.hq(window),a=[];function s(){hj.tryCatch(t,"Rendering")(e),r.off("mousemove."+n),r.off("mouseout."+n)}o.push(n),r.off("mousemove."+n),r.off("mouseout."+n),r.on("mousemove."+n,hj.tryCatch((function(e){var t,n,r,o;a.push({x:e.clientX,y:e.clientY}),a.length>2&&((null===(t=a[1])||void 0===t?void 0:t.x)===(null===(n=a[2])||void 0===n?void 0:n.x)&&(null===(r=a[1])||void 0===r?void 0:r.y)===(null===(o=a[2])||void 0===o?void 0:o.y)?a.pop():a.shift())}),"Rendering")),r.on("mouseout."+n,hj.tryCatch((function(e){var t=this;e.relatedTarget&&(!t||e.relatedTarget===t||t.compareDocumentPosition(e.relatedTarget)&Node.DOCUMENT_POSITION_CONTAINED_BY)||function(e){var t=a[1],n=a[0];if(e|=0,void 0!==t&&!(n&&t.y>=n.y||e>0))if(n&&n.x===t.x)s();else if(n){var r=n.y-t.y/n.x-t.x,o=-(n.y-r*n.x)/r;o>0&&o<i.width()&&s()}}(e.clientY)}),"Rendering"))}function u(e,t,n){var o=hj.hq(document),a=hj.hq(window);i.push(n),a.off("scroll."+n),a.on("scroll."+n,hj.tryCatch((function(){var i=o.height();(o.scrollTop()+(0,r.bY)().height)/i>=.5&&(a.off("scroll."+n),t(e))}),"Rendering"))}var l={clearAllAbandonEvents:hj.tryCatch((function(){o.forEach((function(e){hj.log.debug("Removing abandon events for "+e,"rendering"),hj.hq(document).off("mousemove."+e),hj.hq(document).off("mouseout."+e)})),o=[]}),"hj.rendering.clearAllAbandonEvents"),clearAllScrollEvents:hj.tryCatch((function(){i.forEach((function(e){hj.log.debug("Removing scroll events for "+e,"rendering"),hj.hq(window).off("scroll."+e)})),i=[]}),"hj.rendering.clearAllScrollEvents"),callAccordingToCondition:hj.tryCatch((function(e,t,n){var r={immediate:a,delay:s,abandon:c,scroll:u}["inline"===e.display_type?"immediate":e.display_condition];if(hj.log.debug("Calling active item ("+t+"): "+e.display_condition,"rendering"),!r)throw new Error('Unhandled display condition: "'+e.display_condition+'"');hj.tryCatch(r,"Rendering")(e,n,t)}),"hj.rendering.callAccordingToCondition")}},2537:function(e,t,n){"use strict";n.d(t,{b:function(){return r}});var r={storage:{},set:hj.tryCatch((function(e){r.storage.events=e}),"sessionEvents.set"),get:hj.tryCatch((function(){return r.storage.events}),"sessionEvents.get")}},277:function(e,t,n){"use strict";n.d(t,{q:function(){return o}});var r=n(9780),o={},i={};function a(e,t){var n;if(!o.matchOperations[e.match_operation])return hj.exceptions.log(new Error('Targeting error - "'.concat(e.match_operation,'" match operation does not exist.')),"hj.targeting.matchPatternWithRule"),!1;e.rule_type=e.rule_type||e.component;var r=function(e,t){return!t||"date"!==e.rule_type||"less_than"!==e.match_operation&&"greater_than"!==e.match_operation?t:(new Date(t).valueOf()/1e3).toFixed(0)}(e,t);return(n=o.matchOperations[e.match_operation](e,r)).error?(hj.exceptions.log(new Error("Targeting error - ".concat(e.match_operation," - ").concat(n.error)),"hj.targeting.matchPatternWithRule"),!1):("url"!==e.component&&e.negate&&(n=!n),function(e,t,n){var r=n?"Match ":"No Match ";!n||"url"!==e.component&&"device"!==e.component||(r+=e.component+"|"+e.match_operation+"|"+e.pattern+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting")),"attribute"!==e.component&&"event"!==e.component||(r+=e.component+"|"+e.name+" ("+e.rule_type+")|"+e.match_operation+"|"+e.pattern+"|compared with: "+t+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting"))}(e,t,n),n)}function s(e){var t,n=(0,r.vO)();return(t=0===e.length||3===e.length||e.some((function(e){return a(e,n)})))?hj.log.debug("Device match found","targeting"):hj.log.debug("No device match found","targeting"),t}var c=function(e){var t,n,r;return null==e||null===(t=e.toLowerCase())||void 0===t||null===(n=t.split("#")[0])||void 0===n||null===(r=n.split("?")[0])||void 0===r?void 0:r.replace("http://www.","").replace("https://www.","").replace("http://","").replace("https://","").replace(/\/$/,"")},u=function(e,t,n){return function(){return e.apply(null,arguments)?t.apply(null,arguments):n.apply(null,arguments)}},l=function(e,t){return void 0!==e&&void 0!==t&&null!==e&&null!==t},h=function(e,t){return!isNaN(e.pattern)&&!isNaN(t)&&""!==t&&"boolean"!=typeof t},d=function(e,t){if(!t||t.toString()===parseInt(t,10).toString())return!1;var n=new Date(t);return"Invalid Date"!==n&&!isNaN(n)},f=function(e){return u(l,e,(function(){return!1}))},g=function(e){return f(u(h,e,(function(e,t){return{error:t?"Cannot compare non-numeric values (rule: { name: '".concat(e.name,"' },\n                        pattern: '").concat(t,"')."):void 0}})))},p=function(e){return f(u(d,e,(function(e,t){return{error:"Cannot compare dates. Given pattern is not a date (rule: { name: '".concat(e.name,"' }, pattern: '").concat(t,"').")}})))};o.matchUrl=function(e,t){var n,r=!1,o=!1,i=!1;if(0===e.length)return hj.log.debug("No URL targeting rules set","targeting"),!1;for(var s=0;s<e.length;s+=1){var c,u;if(!(r=!!(n=e[s])&&a(n,t))||null!==(c=n)&&void 0!==c&&c.negate||(i=!0),r&&null!==(u=n)&&void 0!==u&&u.negate){o=!0;break}}return(r=i&&!o)?hj.log.debug("URL match found without any negate rules","targeting"):o?hj.log.debug("Negate URL rule match found","targeting"):hj.log.debug("No URL match found","targeting"),r},o.matchRules=hj.tryCatch((function(e,t,n){for(var r,c,u,l,h,d=e.rules,f=e.userAttributeMatchLogic,g={device:{rules:[],isMatch:null,doMatch:s},url:{rules:[],isMatch:null,doMatch:o.matchUrl},attribute:{rules:[]},trigger:{rules:[]}},p=0;p<d.length;p+=1){var v=d[p];if(void 0!==v){var y=g[v.component];void 0!==y?y.rules.push(v):hj.exceptions.log(new Error("Targeting error - ".concat(g[v.component]," targeting component is not supported.")),"hj.targeting.matchRules")}}hj.log.debug("-- Matching rules for new item --");var m=function(e,n){var r;"__proto__"!==t&&"constructor"!==t&&(void 0===i[t]&&(i[t]=[]),null===(r=i[t])||void 0===r||r.push({eventName:e,callback:n}),hj.event.listen(e,n))};if(Object.keys(i).forEach((function(e){var n;e!==t&&(null===(n=i[e])||void 0===n||n.forEach((function(e){hj.event.removeListener(e.eventName,e.callback)})),delete i[e])})),null!==(r=g.device)&&void 0!==r&&r.doMatch&&(null===(c=g.device)||void 0===c||!c.doMatch(g.device.rules)))return!1;var j=function(){var e;return function(e,t){function n(e){hj.log.debug(e,"targeting")}if(void 0===e||0===e.length)return n("No specific user attribute targeting rules set."),!0;var r=function(e){return a(e,hj.userAttributes.get(e.name))};return"and"===t&&e.every(r)?(n("All user attributes matched. (AND logic)"),!0):"or"===t&&e.some(r)?(n("A user attribute matched. (OR logic)"),!0):(n("User attributes set do not match current visitor."),!1)}(null===(e=g.attribute)||void 0===e?void 0:e.rules,null!=f?f:"and")},b=null===(u=g.trigger)||void 0===u?void 0:u.rules;if(null!=b&&b.length){hj.log.debug("Setting up targeting listeners for trigger rules","targeting");var w=function(){j()&&(null==n||n(!0))};b.forEach((function(e){var t="trigger:"+e.pattern;m(t,w)}))}var S=g.url&&g.url.doMatch&&(null===(l=g.url)||void 0===l?void 0:l.doMatch(g.url.rules,t));return 0===(null===(h=g.attribute)||void 0===h?void 0:h.rules.length)||j()?!!S&&(null==n||n(),!0):(hj.log.debug("Setting up targeting listeners for attribute rules","targeting"),m("user-attributes-set",(function(){S&&j()&&(null==n||n(!0))})),!1)}),"hj.targeting.matchRules"),o.matchOperations={exact:function(e,t){return"string"===e.rule_type?f((function(e,t){return t.toLowerCase()===e.pattern.toLowerCase()}))(e,t):"boolean"===e.rule_type?(e.pattern="true"===String(e.pattern),t===e.pattern):"number"===e.rule_type?null!=t&&Number(t)===Number(e.pattern):t===e.pattern},starts_with:f((function(e,t){return 0===(t=t||"").indexOf(e.pattern)})),ends_with:f((function(e,t){var n=t.length-e.pattern.length==-1?0:t.length-e.pattern.length;return t.substring(n,t.length)===e.pattern})),contains:f((function(e,t){return-1!==(t=t||"").indexOf(e.pattern)})),regex:f((function(e,t){try{return new RegExp(e.pattern).test(t)}catch(e){return hj.log.error("The regular expression used for page targeting was invalid. Please provide a valid regular\n                        expression.\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115012727628"),!1}})),simple:f((function(e,t){return c(t)===c(e.pattern)})),greater_than:g((function(e,t){return Number(t)>Number(e.pattern)})),less_than:g((function(e,t){return Number(t)<Number(e.pattern)})),unknown:function(e,t){return 0!==(t="string"==typeof t?t.trim():t)&&!1!==t&&!t},exact_date:p((function(e,t){var n=new Date(1e3*e.pattern);return new Date(t).toDateString()===n.toDateString()})),exact_days_ago:p((function(e,t){var n,r=new Date;return n=r.setDate(r.getDate()-Number(e.pattern)),n=new Date(n),new Date(t).toDateString()===n.toDateString()})),more_than_days_ago:p((function(e,t){var n=Number(e.pattern)+1,r=(new Date).getTime()/1e3;return r-=86400*n,new Date(t).getTime()/1e3<=r})),less_than_days_ago:p((function(e,t){if(0===Number(e.pattern))return o.matchOperations.exact_days_ago(e,t);var n=(new Date).getTime()/1e3;return n-=86400*Number(e.pattern),new Date(t).getTime()/1e3>=n}))}},512:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var r=n(9780),o={id:void 0,attributes:{},init:hj.tryCatch((function(){if(hj.settings.user_attributes_enabled){var e=function(){var e="true"===hj.bridge.storage.items.HAS_CACHED_USER_ATTRIBUTES.get()?hj.bridge.storage.localStorage.USER_ATTRIBUTES.get():void 0;if(e)try{var t=(0,r.kz)(e)?e:hj.b64DecodeUnicode(e);return JSON.parse(t)}catch(e){return}}();e&&(o.id=e.userId,o.attributes=e.attributes,delete o.attributes.contentsquare_replay_link)}}),"userAttributes.init"),reset:hj.tryCatch((function(){o.id=void 0,o.attributes={},hj.bridge.storage.items.HAS_CACHED_USER_ATTRIBUTES.clear(),hj.bridge.storage.localStorage.USER_ATTRIBUTES.clear()}),"userAttributes.reset"),set:hj.tryCatch((function(e,t){var n,i;t&&t.contentsquare_replay_link&&(hj.log.debug("Storing contentsquare_replay_link user attribute: ".concat(t.contentsquare_replay_link),"userAttributes"),o.attributes.contentsquare_replay_link=t.contentsquare_replay_link),hj.settings.user_attributes_enabled?(void 0!==o.id&&null!==o.id&&o.id!==e&&(hj.log.debug("User ID changed, resetting all attributes before continuing.","userAttributes"),o.reset()),o.id=e,o.attributes=(0,r.PM)(o.attributes,t),n={attributes:o.attributes,userId:o.id},i=hj.b64EncodeUnicode(JSON.stringify(n)),hj.bridge.storage.localStorage.USER_ATTRIBUTES.set(i),hj.bridge.storage.items.HAS_CACHED_USER_ATTRIBUTES.set("true",!0),hj.event.signal("user-attributes-set"),hj.bridge.isRecordingEnabled()?o.flush():hj.log.debug("No recording in progress. Not sending.","userAttributes")):hj.log.debug("User attributes not enabled. Doing nothing.","userAttributes")}),"userAttributes.set"),flush:hj.tryCatch((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hj.hq.noop;if(void 0!==o.id||Object.keys(o.attributes).length){var t,n,r=hj.bridge.storage.items.USER_ATTRIBUTES_HASH.get({resetExpiry:!0}),i=hj.md5(JSON.stringify({allAttributes:o.attributes,userId:o.id}));if(r===i)return e(null,o.id,o.attributes),void hj.log.debug("No changed user attributes. Not sending.","userAttributes");hj.bridge.storage.items.USER_ATTRIBUTES_HASH.set(i),hj.debug.isOn()&&(t=o.id,n=o.attributes,hj.ajax.post("".concat(hj.identifyEndpoint,"/sites/").concat(hj.settings.site_id,"/users/").concat(hj.bridge.getSessionUserId(!0),"/validate"),{user_id:t,attributes:n},hj.tryCatch((function(e){e.errors&&Object.keys(e.errors).length>0?hj.log.debug("User validation API call PARTIALLY successful (some errors).","userAttributes",e):hj.log.debug("User validation API call successful.","userAttributes",e)}),"userAttributes"),hj.tryCatch((function(e){hj.log.debug("User Attributes validation API call failed.","userAttributes",e.responseText&&JSON.parse(e.responseText)||"unknown_failure")}),"userAttributes"))),hj.bridge.flushUserAttributes(o.id,o.attributes),e(null,o.id,o.attributes)}else e(Error("no_user"))}),"userAttributes.flush"),get:hj.tryCatch((function(e){return"user_id"===e?o.id:e&&o.attributes[e]}),"userAttributes.get")}},6946:function(e,t,n){"use strict";n.r(t),n.d(t,{initVoC:function(){return C},runWidgets:function(){return E}});var r=n(9663),o=n(9780),i={getAsNumber:function(){var e=hj.bridge.getSessionUserId(!0);return(parseInt((null==e?void 0:e.slice(-10))||"",16)+1)/Math.pow(2,40)},compareRatio:hj.tryCatch((function(e,t){return i.getAsNumber()*(t?100:1)<=e}),"identifier.compareRatio")},a=n(4808),s=["af","ar","bg","ca","cs","cy","da","de","el","en","es","et","fa","fi","fr","he","hr","hu","id","it","ja","ko","lt","lv","mis","nb","nl","pl","pt_BR","pt","ro","ru","sk","sl","sq","sr","sv","sw","th","tl","tr","uk","vi","zh_CN","zh_TW"],c=Object.freeze({POPOVER:"popover",FULL_SCREEN:"full_screen",EXTERNAL:"external_link",BUTTON:"button",INLINE_EMBEDDED:"inline",BUBBLE:"bubble",MOBILE_POPOVER:"mobile_popover"}),u=hj.tryCatch((function(){var e,t,n,r,u,l,h,d;hj.widget=(n=["ar","fa","he"],r=[],u=[],l=[],h=[],d=!1,(t={}).ctrl=void 0,t.data=void 0,t.model={},t.activeLanguageDirection="ltr",t.widgetAttributePrefix="_hj-f5b2a1eb-9b07",t.getCtaLinks=function(){return{polls:"https://www.hotjar.com/feedback-surveys?utm_source=client&utm_medium=poll&utm_campaign=insights",surveys:"https://www.hotjar.com/?utm_source=client&utm_medium=survey&utm_campaign=insights"}},t.getActiveLanguage=function(){var t;return null!==(t=e)&&void 0!==t?t:"en"},t.addMatchingWidget=function(e,n,o,a,s,l,f,g){var p=f===c.INLINE_EMBEDDED;if(void 0===a||hj.isPreview||i.compareRatio(a,!0)){var v={type:e,id:n,created:o,runCallback:s,removeCallback:l,isInlineEmbedded:p,targetingRules:g,displayType:f};d?t.setActiveWidget(v):((p?u:r).push(v),p&&h.push(v))}else hj.log.debug("Session identifier not in targeting percentage. Widget will not match.","targeting")},t.clearWidget=hj.tryCatch((function(){var e="#_hj_poll_container,._hj-widget-container";hj.hq(e).length>0&&(hj.log.debug("Removing previously shown widget from DOM","widgets"),hj.hq(e).remove())}),"widgets"),t.emptyMatchingWidgets=function(){r=[],u=[],l.forEach((function(e){e.disconnect()})),l=[],d=!1},t.isPhoneOrTablet=hj.tryCatch((function(){return hj.widget.isVeryNarrowScreen()||"phone"===(0,o.vO)()||"tablet"===(0,o.vO)()}),"common"),t.isVeryNarrowScreen=hj.tryCatch((function(){return hj.hq(window).width()<=450}),"common"),t.removeActiveWidget=function(e){hj.widgetDelay.clear(),a.n.clearAllAbandonEvents(),a.n.clearAllScrollEvents(),e=e||function(){},t.activeWidget?(t.activeWidget.removeCallback&&t.activeWidget.removeCallback(e),delete t.activeWidget):e()},t.runLatestMatchingWidget=function(){var e,n;r.forEach((function(t){var r=function(e){return"incoming"===e.type||"button"===e.displayType||"bubble"===e.displayType?0:1}(t);(!e||r>n||r===n&&t.created>e.created)&&(e=t,n=r)})),e?t.setActiveWidget(e):t.removeActiveWidget(),d=!0},t.runInlineEmbeddedWidgets=function(){h=h.filter((function(e){var t=hj.targeting.matchUrl(e.targetingRules,location.href);return t||e.removeCallback(),t})),u.forEach((function(e){e.runCallback()}))},t.setActiveWidget=function(e){t.activeWidget&&e.type===t.activeWidget.type&&e.id===t.activeWidget.id||t.removeActiveWidget((function(){e.runCallback(),t.activeWidget=e}))},t.setLanguage=hj.tryCatch((function(t){var r=(t||"en").replace("-","_");if(!s.includes(r))throw new Error('Invalid language "'+t+'"');e=r,hj.widget.activeLanguageDirection=n.indexOf(r)>-1?"rtl":"ltr",hj.widget.isActiveLanguageDirectionRtl="rtl"===hj.widget.activeLanguageDirection}),"common"),t.registerObserverForInlineWidget=function(e){l.push(e)},t),hj.widgetDelay=function(){var e={},t=null;return e.clear=hj.tryCatch((function(){clearTimeout(t),t=null}),"hj.widgetDelay.clear"),e.set=hj.tryCatch((function(n,r){e.clear(),t=setTimeout(n,r)}),"hj.widgetDelay.set"),e}()}),"widgets"),l=n(6939),h={popover:{isExternal:!1,allowsScreenshots:!0},full_screen:{isExternal:!1,allowsScreenshots:!0},inline:{isExternal:!1,allowsScreenshots:!0},button:{isExternal:!1,allowsScreenshots:!0},bubble:{isExternal:!1,allowsScreenshots:!0},external_link:{isExternal:!0,allowsScreenshots:!1},mobile_popover:{isExternal:!0,allowsScreenshots:!1}},d=function(e){return h[e].isExternal},f=n(9982),g=function(e,t){var n,r;if(hj.isPreview)return!1;var o=(null===(n=e.targeting)||void 0===n?void 0:n.filter((function(e){return"url"===e.component})))||[],i=null===(r=e.targeting)||void 0===r?void 0:r.some((function(e){return"trigger"===e.component})),a=e.display_type===c.EXTERNAL||e.display_type===c.MOBILE_POPOVER,s=i||a||!0===hj.targeting.matchUrl(o,hj.currentUrl);return hj.tryCatch((function(){if(!s)throw new Error("Rendered survey out of URL targeting in ".concat(t," with ID: ").concat(e.id))}),"render-out-of-targeting")(),!s};function p(e,t){for(var n=e.querySelectorAll(t),r=0;r<n.length;r++){var o=n[r];o&&o.parentElement&&o.parentElement.removeChild(o)}}var v=hj.tryCatch((function(){hj.surveyImpressionsMoved=!0,hj.polls=function(){var e,t={},n=window.hjLazyModules,r=hj.tryCatch((function(e,t){g(e,"doRenderSurvey")||hj.widget.renderSurvey(e,t)}),"polls");function i(){return n.SURVEY_V2}var s=hj.tryCatch((function(t,n){if(!g(t,"renderWidget")){hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[t.id]=t,hj.widget.pollsResponsesUUID=hj.widget.pollsResponsesUUID||{},hj.widget.pollsResponsesUUID[t.id]=hj.uuid(),hj.widget.setLanguage(t.language),hj.log.debug("Rendering poll widget.","poll");var o=i();o!==e?(0,f.H)(i(),(function(){e=o,r(t,n)})):r(t,n)}}),"polls"),u=hj.tryCatch((function(e){var t=JSON.parse(JSON.stringify(e));return function(e){hj.hq.each(e.content.questions,(function(e,t){t.answers&&hj.hq.each(t.answers,(function(e,t){t.index=e}))}))}(t),function(e){hj.hq.each(e.content.questions,(function(e,t){t.randomize_answer_order&&(t.pin_last_to_bottom?t.answers=(0,o.TV)(t.answers.slice(0,-1)).concat(t.answers.slice(-1)):(0,o.TV)(t.answers))}))}(t),t}),"polls"),v=hj.tryCatch((function(e,t){hj.widget.pollsData=hj.widget.pollsData||{};var n=u(e);hj.widget.pollsData[e.id]=n,hj.settings.legal_name=e.legal_name,hj.settings.privacy_policy_url=e.privacy_policy_url,t&&(hj.settings.features=t),hj.tryCatch((function(){s(n)}),n,"polls")()}),"polls");return t.add=hj.tryCatch((function(e){hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[e.id]=u(e),hj.tryCatch(a.n.callAccordingToCondition,"polls")(hj.widget.pollsData[e.id],"poll",s)}),"polls"),t.addEmbedded=hj.tryCatch((function(e,n){hj.widget.emptyMatchingWidgets();var r="external_link"===e.display_type?void 0:e.targeting_percentage;hj.widget.addMatchingWidget("poll",e.id,e.created_epoch_time,r,(function(){var t=u(e),r=t.display_type===c.MOBILE_POPOVER;r||(t.skin="light",t.background="#ffffff",t.pageBackground=e.background),hj.bridge.storage.items.POLL_DONE.exists(e.id)&&"always"!==e.persist_condition&&(hj.log.debug("Offsite poll "+e.id+" was already submitted.","poll"),t.is_submitted=!0),hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[t.id]=t,s(t,n[0]),"function"!=typeof window.hjRenderCallback||r||window.hjRenderCallback({background:e.background})}),t.remove,e.display_type)}),"polls"),t.remove=hj.tryCatch((function(e,t){hj.widget.pollsData[t]?(p(document.body,".".concat("_hj-widget-container")),p(document.body,".".concat("_hj-widget-iframe")),delete hj.widget.pollsData[t],e&&setTimeout((function(){e()}),1)):e&&e()}),"polls"),t.run=hj.tryCatch((function(e){var n;if(hj.hq("._hj-survey-embed-container").length>0){var r,o=hj.hq("._hj-survey-embed-container"),i=o.attr("data-survey-id"),a=null===(r=hj.settings.polls)||void 0===r?void 0:r.find((function(e){return d(e.display_type)&&e.uuid==i}));return void 0===a?void((0,l.W)(e)&&(hj.hq(document).trigger("hj-embedded-survey-mismatch"),hj.widgetDelay.clear(),hj.widget.emptyMatchingWidgets(),hj.log.debug("Could not match the embedded UUID.","poll"))):(hj.log.debug("Offsite poll #".concat(a.id," has matched with the embedded UUID ").concat(i),"poll"),void t.addEmbedded(a,o))}if(!(0,l.W)(e)){var s=null===(n=hj.settings.polls)||void 0===n?void 0:n.filter((function(e){return t=e.display_type,void 0!==h[t]&&!d(e.display_type);var t}));null==s||s.forEach((function(n){hj.targeting.matchRules({rules:n.targeting,userAttributeMatchLogic:n.targeting_user_attribute_match_logic},e,hj.tryCatch((function(){hj.log.debug("Poll #"+n.id+" has matched.","poll"),hj.bridge.storage.items.POLL_DONE.exists(n.id)&&"always"!==n.persist_condition?hj.log.debug("Poll was already submitted.","poll"):hj.widget.addMatchingWidget("poll",n.id,n.created_epoch_time,n.targeting_percentage,(function(){return t.add(n)}),(function(e){return t.remove(e,n.id)}),n.display_type,n.targeting)}),"polls.run.matchRules-callback"))}))}}),"polls"),t.enablePreview=function(){window._hjPollReload=v},hj.isPreview&&t.enablePreview(),t}()}),"polls"),y=Object.freeze({MODAL:"_hj-modal",FOOTER:"_hj-footer",SURVEY_INVITES:"_hj_survey_invite_container",HEATMAP_RETAKER:"_hj-heatmap-retaker",ADMIN_WIDGET:"_hj_admin_widget",NOTICATION:"_hj-notification"}),m=(Object.freeze({RETAKER:"_hjRetakerTrsToken",TARGETING:"_hjRetakerTargeting"}),n(277)),j=function(e){if(e){if(e.startsWith("data:image")||e.startsWith("blob:")||e.startsWith("http"))return e;var t="live"===hj.environment?hj.surveyImagesHost:"d23waydkwbngmu.cloudfront.net";return"https://".concat(t,"/").concat(e)}},b=function(){var e={},t=window.hjLazyModules;function n(e){return e.display_type===c.EXTERNAL?(0,l.f)(e.uuid,hj.surveysHost):e.public_url}return e.run=hj.tryCatch((function(r){if(!(0,l.W)(r)){var o=(hj.settings.polls||[]).filter((function(e){return e.invite_enabled&&e.display_type===c.EXTERNAL})),i=(hj.settings.surveys||[]).concat(o);hj.hq.each(i||[],(function(o,i){m.q.matchRules({rules:i.targeting,userAttributeMatchLogic:i.targeting_user_attribute_match_logic},r,hj.tryCatch((function(){hj.log.debug("Survey #"+i.id+" has matched.","survey"),hj.bridge.storage.items.SURVEY_INVITES_CLOSED.exists(i.id)?hj.log.debug("Survey was already viewed.","survey"):hj.widget.addMatchingWidget("survey",i.id,i.created_epoch_time,i.targeting_percentage,(function(){hj.survey.data=i,a.n.callAccordingToCondition(hj.survey.data,"survey",hj.tryCatch((function(){var e;e=hj.survey.data,hj.widget.surveyInvitationData={id:e.id,title:e.invite.title,description:e.invite.description,button:e.invite.button,close:e.invite.close,url:n(e),logoUrl:j(e.logo_path||e.logo_url)},(0,f.H)(t.SURVEY_INVITATION)}),"polls"))}),e.remove)}),"surveys.run.matchRules-callback"))}))}}),"surveys"),e.remove=hj.tryCatch((function(e){hj.survey.data?(hj.hq(".".concat(y.SURVEY_INVITES)).length>0&&hj.hq(".".concat(y.SURVEY_INVITES)).remove(),delete hj.survey.data,e&&setTimeout((function(){e()}),1)):e&&e()})),e},w=hj.tryCatch((function(){hj.survey={invites:b(),ctrl:void 0,data:void 0,model:{},activeLanguageDirection:"ltr"}}),"surveys"),S=n(4613),_=!1,C=function(){_||((0,r.E)(),u(),v(),w(),_=!0)},E=function(e){hj.widget&&((0,S.isUserTest)()?(0,f.H)(window.hjLazyModules.USER_TEST,(function(){var e,t;null===(e=(t=hj.widget).initUserTest)||void 0===e||e.call(t)}),(function(){hj.log.error("Failed to load user test module")})):(hj.widget.emptyMatchingWidgets(),hj.polls.run(e),hj.survey.invites.run(e),hj.widget.runLatestMatchingWidget(),hj.widget.runInlineEmbeddedWidgets()))}},9663:function(e,t,n){"use strict";n.d(t,{E:function(){return l}});var r=n(2537),o=n(9780);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a.apply(this,arguments)}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u=function(){var e,t,n=(t=hj.askUrl||(null===(e=hj.defaults)||void 0===e?void 0:e.askUrl),{v1:"".concat(t,"api/v1"),v2:"".concat(t,"api/v2")}),u={},l={granted:null,callbacks:[],inProgress:!1},h=function(){return hj.bridge.getSessionUserId(!0)||""},d=[],f=!1;function g(e,t,n,r){d.push([e,t,n,r]),f||function e(){if(0!==d.length){f=!0;var t=function(e,t,n){if(t&&!Array.isArray(t)&&"number"==typeof t.length){var r=t.length;return c(t,4<r?4:r)}return e(t,4)}(s,d.shift()),n=t[0],r=t[1],o=t[2],i=t[3];hj.ajax.post(n,r,(function(t){try{o&&o(t)}finally{e()}}),(function(t){try{i&&i(t)}finally{e()}}))}else f=!1}()}return u.getConsentGranted=hj.tryCatch((function(e){var t,n="?";null!==l.granted&&e?e(l.granted):(e&&l.callbacks.push(e),l.inProgress||(n+="user_id="+h(),l.inProgress=!0,hj.ajax.get("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent").concat(n),(function(e){for(l.granted=!!e.success&&-1!==e.scopes.indexOf("associate"),l.inProgress=!1;t=l.callbacks.pop();)hj.tryCatch(t,"ConsentData")(l.granted)}),(function(){for(l.granted=!1,l.inProgress=!1;t=l.callbacks.pop();)hj.tryCatch(t,"ConsentData")(l.granted)}))))}),"hj.request.getConsentGranted"),u.grantConsent=hj.tryCatch((function(e,t){e.user_id=h(),e.action="grant_for_response",g("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent/associate"),e,(function(e){l.granted=!!e.success,t&&hj.tryCatch(t,"GrantConsent")(l.granted)}),(function(){l.granted=!1}))}),"hj.request.getConsent"),u.savePollResponse=hj.tryCatch((function(e,t,s,c,u){var l=hj.widget.pollsResponsesUUID[e];s.action="create_or_update_poll_response";var d=r.b.get();d&&(s.events=hj.hq.stringify(d)),function(e,t,n,r,s){var c=(0,o.bY)();n.window_width=n.window_width||c.width,n.window_height=n.window_height||c.height,n.user_id=t?h():void 0,n.url=location.href,n.account_id=hj.settings.account_id||null,n.payload_signature=hj.settings.account_signature||null,hj.userAttributes.flush((function(t,o,c){if(t){n.identify_user_id=hj.userAttributes.get("user_id")||null;var u=hj.userAttributes.get("contentsquare_replay_link");u&&(n.identify_attributes={contentsquare_replay_link:u})}else n.identify_user_id=o,n.identify_attributes=c;if(void 0!==hj.sdk){n.language=hj.sdk.getDeviceLanguage();var l={contentsquare_replay_link:hj.sdk.getReplayLink()};n.identify_attributes=a(a({},"object"===i(n.identify_attributes)?n.identify_attributes:void 0),l)}g(e,n,(function(e){r&&r(e)}),(function(){s&&s(new Error("Error sending response"))}))}))}("".concat(n.v2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(e,"/response/").concat(l),t,s,(function(t){t.is_new_response&&hj.event.signal("poll.send",{id:e,response_id:t.poll_response_id}),null==c||c(t)}),u)}),"data"),u.completePollResponse=hj.tryCatch((function(e,t){var r=hj.widget.pollsResponsesUUID[e];g("".concat(n.v2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(e,"/response/").concat(r),{action:"finish_poll_response",completion_time_from_engagement_ms:t.fromEngagement,completion_time_from_render_ms:t.fromRender})}),"data"),u};function l(){try{hj.request=u()}catch(e){hj.exceptions.log(e,"hj.request")}}},4613:function(e,t,n){"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.r(t),n.d(t,{isUserTest:function(){return u},userTestStore:function(){return s}});var o="https://voc.hotjar.com",i=[o,"https://voc.ew1-integration-1.hotjarians.net","https://hj-engage-unmoderated-review.s3.eu-west-1.amazonaws.com"],a="hj-uut",s={get:function(){var e=window.sessionStorage.getItem(a);return e?JSON.parse(e):null},getValue:function(e){var t=s.get();return null==t?void 0:t[e]},set:function(e){if(e){var t,n=null!==(t=s.get())&&void 0!==t?t:{};window.sessionStorage.setItem(a,JSON.stringify(r(r({},n),e)))}},clear:function(){window.sessionStorage.removeItem(a)},validDomains:i},c=function(e){var t=new URLSearchParams(e);return!!t.has("project_uuid")||("1"===t.get("is_preview")?t.has("task_uuid"):t.has("response_uuid")&&t.has("task_uuid")&&t.has("participation_uuid"))},u=function(){var e,t=null!==(e=function(){var e=document.referrer;if("string"==typeof e&&function(e){if("string"!=typeof e)return!1;try{var t=new URL(e),n="1"===t.searchParams.get("is_preview");return!!i.some((function(t){return null==e?void 0:e.includes(t)}))||!!n&&t.hostname.endsWith(".hotjar.com")&&t.pathname.includes("research/projects/tests")}catch(e){return!1}}(e)&&c(new URL(e).search))return e}())&&void 0!==e?e:function(){var e=new URLSearchParams(window.location.search).get("hj_uut");if(i.some((function(e){var t;return null===(t=document.referrer)||void 0===t?void 0:t.includes(e)}))&&e){var t=window.atob(e);if(c(t)){var n=new URL(o);return n.search=t,n.toString()}}}(),n=void 0!==t,r=null!==s.get();return n&&s.set({referrer:t}),r||n}},6175:function(e,t,n){"use strict";n.d(t,{K:function(){return i}});var r,o,i=((o=function(){return r()}).test=r=function(){var e;if(!navigator)return"No User-Agent Provided";if(null!==(e=navigator.userAgentData)&&void 0!==e&&e.mobile)return"mobile";var t=function(e){return navigator.userAgent.match(e)};return t(/GoogleTV|SmartTV|Internet.TV|NetCast|NETTV|AppleTV|boxee|Kylo|Roku|DLNADOC|CE\-HTML/i)||t(/Xbox|PLAYSTATION.3|Wii/i)?"tv":t(/iPad/i)||t(/tablet/i)&&!t(/RX-34/i)||t(/FOLIO/i)||t(/Linux/i)&&t(/Android/i)&&!t(/Fennec|mobi|HTC.Magic|HTCX06HT|Nexus.One|SC-02B|fone.945|Chromebook/i)||t(/Kindle/i)||t(/Mac.OS/i)&&t(/Silk/i)||t(/GT-P10|SC-01C|SHW-M180S|SGH-T849|SCH-I800|SHW-M180L|SPH-P100|SGH-I987|zt180|HTC(.Flyer|\_Flyer)|Sprint.ATP51|ViewPad7|pandigital(sprnova|nova)|Ideos.S7|Dell.Streak.7|Advent.Vega|A101IT|A70BHT|MID7015|Next2|nook/i)||t(/MB511/i)&&t(/RUTEM/i)?"tablet":t(/BOLT|Fennec|Iris|Maemo|Minimo|Mobi|mowser|NetFront|Novarra|Prism|RX-34|Skyfire|Tear|XV6875|XV6975|Google.Wireless.Transcoder/i)||t(/Opera/i)&&t(/Windows.NT.5/i)&&t(/HTC|Xda|Mini|Vario|SAMSUNG\-GT\-i8000|SAMSUNG\-SGH\-i9/i)?"mobile":t(/Windows.(NT|XP|ME|9)/)&&!t(/Phone/i)||t(/Win(9|.9|NT)/i)||t(/Macintosh|PowerPC/i)&&!t(/Silk/i)||t(/Linux/i)&&t(/X11/i)||t(/Solaris|SunOS|BSD/i)||t(/Bot|Crawler|Spider|Yahoo|ia_archiver|Covario-IDS|findlinks|DataparkSearch|larbin|Mediapartners-Google|NG-Search|Snappy|Teoma|Jeeves|TinEye/i)&&!t(/Mobile/i)||t(/\b(CrOS|Chromebook)\b/i)?"desktop":"mobile"},o)},8714:function(e,t,n){"use strict";n.d(t,{I:function(){return o},IU:function(){return i},xj:function(){return r}});var r=function(e){return e.replace(/%u[\dA-F]{4}|%[\dA-F]{2}/gi,(function(e){return e.startsWith("%u")?String.fromCharCode(parseInt(e.slice(2),16)):String.fromCharCode(parseInt(e.slice(1),16))}))},o=function(e){return e.replace(/[^a-zA-Z0-9@*_+-./]/g,(function(e){var t=e.charCodeAt(0);return t<256?"%"+t.toString(16).padStart(2,"0").toUpperCase():"%u"+t.toString(16).padStart(4,"0").toUpperCase()}))};function i(e){return r(encodeURIComponent(e))}},9780:function(e,t,n){"use strict";n.d(t,{IN:function(){return f},JJ:function(){return h},PM:function(){return u},PY:function(){return c},TV:function(){return i},Tb:function(){return p},bY:function(){return s},cr:function(){return d},kz:function(){return l},vO:function(){return a}});var r=n(6175);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}var i=hj.tryCatch((function(e){var t,n,r;for(t=e.length-1;t>0;t-=1)n=Math.floor(Math.random()*(t+1)),r=e[t],e[t]=e[n],e[n]=r;return e}),"utils.shuffle"),a=(hj.tryCatch((function(e){return/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e)}),"utils.validateEmail"),hj.tryCatch((function(){return hj.userDeviceType||(hj.userDeviceType=(0,r.K)(),"mobile"===hj.userDeviceType&&(hj.userDeviceType="phone")),hj.userDeviceType}),"utils.deviceType")),s=hj.tryCatch((function(){var e=function(){try{return window.self!==window.top}catch(e){return!0}}(),t={width:!e&&window.screen?window.screen.width:document.body.clientWidth,height:!e&&window.screen?window.screen.height:document.body.clientHeight};return{width:window.innerWidth||document.documentElement.clientWidth||t.width,height:window.innerHeight||document.documentElement.clientHeight||t.height}}),"utils.getWindowSize"),c=function(e,t,n,r,o){var i=1;if(void 0!==o&&hj.log.debug("Retry iteration ".concat(i," of ").concat(o)),e())return t(!0);var a=setInterval((function(){return i++,e()?(clearInterval(a),t(!0)):i>=r?(clearInterval(a),t(!1)):void 0}),n)},u=function(e,t){var n={},r={};return[e,t].forEach((function(e){if(e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&"length"!==t&&(n[t]=e[t])})),Object.keys(n).sort().forEach((function(e){r[e]=n[e]})),r},l=function(e){try{var t=JSON.parse(e);if(t&&"object"===o(t))return!0}catch(e){}return!1},h=function(e,t){return function(n,r){hj.log.debug("[".concat(e,"] ").concat(n),t,r)}},d=function(e){return e&&"string"==typeof e?e.replace(/[\W_]+/g,"-"):""},f=function(e){return Array.from(e).map((function(e){return JSON.stringify(e)}))};function g(e){try{return decodeURIComponent(e)}catch(t){return e}}function p(e){for(var t,n=[],r=new RegExp("[^?&]?"+e.replace(/\[/,"\\[").replace(/]/,"\\]")+"=([^&]+)","g");t=r.exec(location.search);)n.push(g(t[1]));switch(n.length){case 0:return"";case 1:return n[0];default:return n}}},8422:function(e,t,n){"use strict";n.d(t,{E$:function(){return c},R0:function(){return a},fb:function(){return u},jS:function(){return s},oO:function(){return i},vH:function(){return o},vO:function(){return r}}),Object.freeze({LAST_RECORDING_ACTIVITY_STORE_DEBOUNCE:5e3,MAX_TIME_SINCE_LAST_RECORDING_ACTIVITY_IN_SESSION:12e4});var r=window.hjLazyModules,o={SCRIPT:"js",STYLESHEET:"css"},i=(Object.freeze({id:null,selector_version:2}),60),a=60*i,s=24*a*365,c=i/2,u=7},8172:function(e,t,n){"use strict";n.r(t),n.d(t,{exceptionLogger:function(){return s},initErrorLogging:function(){return c}});var r=n(8422);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}function a(e){if(!window.navigator&&!window.location&&!window.document)return e;var t=e.request&&e.request.url||window.location&&window.location.href,n=(window.document||{}).referrer,r=(window.navigator||{}).userAgent,o=i(i(i({},e.request&&e.request.headers),n&&{Referer:n}),r&&{"User-Agent":r}),a=i(i({},t&&{url:t}),{},{headers:o});return i(i({},e),{},{request:a})}var s=function(e){var t;function n(e,t,n){(void 0!==hj.log?hj.log.debug:function(){})(e,t,n)}var i,s=null,c=0,u=[],l=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/g,h=/\d{1,3}\.\d{1,3}\.\d{1,3}\.(\d{1,3})/g,d=/\d{4,}([-\s]?\d{4,}){2,}/g,f=/password(.*)/g,g={},p=[],v=null!==hj.errorUrl,y=void 0!==hj.sdk||window.location.search.indexOf("hjErrorLoggerSamplingDisabled=1")>0||null!=e&&e.isNotSampled?1:.002,m=(null==e?void 0:e.throttleDelay)||1e3,j=(null==e?void 0:e.maxErrors)||10,b=["event_id","stacktrace","release","useragent","User-Agent","logger","scriptversion","module","errorgroup","errormessagegroup"],w=/[?&]logErrors/.test(location.search);function S(e){return hj.hq.each(e,(function(t,n){n&&"object"===o(n)?S(n):n&&"string"==typeof n&&-1==b.indexOf(t)&&(n=(n=(n=(n=n.replace(h,"<XXX>")).replace(l,"<<EMAIL>>")).replace(d,"123456789012")).replace(f,"<******>"),e[t]=n)})),e}function _(){return u.every((function(e){return"loaded"===e.state}))}function C(e){e.state="loaded",e.onLoad(),_()&&v&&g.startProcessing&&g.startProcessing()}return u=[{name:"sentry",check:function(){return void 0!==hj.Sentry},url:"".concat(hj.scriptDomain).concat(null===(t=r.vO.SENTRY)||void 0===t?void 0:t.js),state:"unloaded",onLoad:function(){var e=new hj.Sentry.BrowserClient({dsn:hj.errorUrl,environment:hj.environmentID,release:"insights-client-script-"+window.hjBootstrap.revision,sampleRate:y,defaultIntegrations:!1,integrations:[],beforeSend:function(e){if(!/.*Google.*/.test(window.navigator.userAgent))return S(e)}});(i=new hj.Sentry.Hub(e)).setUser({id:_hjSettings.hjid}),i.getScope().addEventProcessor(a)}}],g.sendException=function(e){try{i.captureException(e.exception,{tags:{logger:e.module}})}catch(e){n("Failed to log exception: "+e,"Exception")}},g.tryCatch=function(e,t){return function(){try{return e.apply(this,arguments)}catch(e){if(w&&console.error(e),window.__TESTS__)throw e;g.log&&g.log(e,t)}}},g.log=function(e,t){var r;n("Exception occurred in '"+t+"'","Exception",e),r={exception:e,module:t},p.length<j&&(_()||u.filter((function(e){return"unloaded"===e.state})).forEach((function(e){e.check()?C(e):(function(e){var t;e.state="loading";var n=document.createElement("script");n.src=e.url,null===(t=document.getElementsByTagName("head")[0])||void 0===t||t.appendChild(n)}(e),function(e){var t=setInterval((function(){e.check()&&(clearInterval(t),C(e))}),10)}(e))})),p.push(r))},g.getQueue=function(){return p},g.startProcessing=function(){g.isProcessing&&!g.isProcessing()&&(s=setInterval((function(){var e;(e=p.shift())&&(g.sendException&&g.sendException(e),c++),c>=j&&g.stopProcessing&&g.stopProcessing()}),m))},g.isProcessing=function(){return null!==s},g.stopProcessing=function(){g.isProcessing&&g.isProcessing()&&s&&(clearInterval(s),s=null)},g};function c(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isNotSampled:!1}).isNotSampled;window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window._hjSettings=window._hjSettings||{},hj.defaults={host:"in.hotjar.com",environment:"live",environmentID:"live",insightsHost:"insights.hotjar.com",insightsApiHost:"insights.hotjar.com",staticHost:"static.hotjar.com",varsHost:"vars.hotjar.com",surveysHost:"surveys.hotjar.com",surveyImagesHost:"survey-images.hotjar.com",errorUrl:"https://<EMAIL>/1803150",identifyEndpoint:"https://identify.hotjar.com",viewCounterEndpoint:"https://vc.hotjar.io/sessions",viewCounterHitPercentage:.25,surveyImpressionsEndpoint:"https://surveystats.hotjar.io/hit",askUrl:"https://ask.hotjar.io/"},hj.host=_hjSettings.host||hj.defaults.host,hj.insightsHost=_hjSettings.insightsHost||hj.defaults.insightsHost,hj.insightsApiHost=_hjSettings.insightsApiHost||hj.defaults.insightsApiHost,hj.staticHost=_hjSettings.staticHost||hj.defaults.staticHost,hj.varsHost=_hjSettings.varsHost||hj.defaults.varsHost,hj.surveysHost=_hjSettings.surveysHost||hj.defaults.surveysHost,hj.surveyImagesHost=hj.defaults.surveyImagesHost,hj.errorUrl=void 0!==_hjSettings.errorUrl?_hjSettings.errorUrl:hj.defaults.errorUrl,hj.environment=_hjSettings.environment||hj.defaults.environment,hj.environmentID=_hjSettings.environmentID||hj.defaults.environmentID,hj.identifyEndpoint=_hjSettings.identifyEndpoint||hj.defaults.identifyEndpoint,hj.askUrl=_hjSettings.askUrl||hj.defaults.askUrl,hj.viewCounterEndpoint=void 0!==_hjSettings.viewCounterEndpoint?_hjSettings.viewCounterEndpoint:hj.defaults.viewCounterEndpoint,hj.viewCounterHitPercentage=void 0!==_hjSettings.viewCounterHitPercentage?_hjSettings.viewCounterHitPercentage:hj.defaults.viewCounterHitPercentage,hj.surveyImpressionsEndpoint=void 0!==_hjSettings.surveyImpressionsEndpoint?_hjSettings.surveyImpressionsEndpoint:hj.defaults.surveyImpressionsEndpoint,hj.exceptions=s({isNotSampled:e}),hj.tryCatch=hj.exceptions.tryCatch}},1214:function(){hj.tryCatch((function(){hj.xcom=hj.tryCatch((function(){var e,t={},n=[],r=1,o="https://"+hj.insightsHost+"/x",i="_hjXcomProxyFrame",a=hj.tryCatch((function(){if(1==r){window.addEventListener?window.addEventListener("message",s,!1):window.attachEvent("onmessage",s),r=2;var t=document.createElement("iframe");t.style.position="fixed",t.style.top=-100,t.style.left=-100,t.width=1,t.height=1,t.id=i,t.src=o,document.body.appendChild(t),e=document.getElementById(i)}}),"data");t.send=hj.tryCatch((function(t,o){10==r?e.contentWindow.postMessage({eventId:t,data:o},"*"):(n.push({eventId:t,data:o}),a())}));var s=function(e){"knockknock"==e.data.eventId&&(r=10,n.forEach((function(e){t.send(e.eventId,e.data)})),n=[])};return t}),"xcom")()}),"xcom")()},6967:function(e,t,n){"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}function o(e,t){return l(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||c(e,t)||s()}function i(e,t,n){if(t&&!Array.isArray(t)&&"number"==typeof t.length){var r=t.length;return u(t,void 0!==n&&n<r?n:r)}return e(t,n)}function a(e){return l(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||c(e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e){if(Array.isArray(e))return e}n.r(t),n.d(t,{fullSelector:function(){return C},selector:function(){return _}});var h="hj-shadow:",d="hj-lwc:",f=[],g=[],p={},v=function(e){return Array.from(e.children).filter((function(e){return e.matches("slot")}))},y=function(e){var t;return"ShadowRoot"===(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)},m=function(e){var t;return y(null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e))},j=function(e){var t,n,r="data-hj-ignore-attributes";if(void 0!==e.attr(r))return!0;if(document.body.hasAttribute(r))return!0;for(var o=null!==(t=null===(n=e.get(0))||void 0===n?void 0:n.parentElement)&&void 0!==t?t:null;null!==o&&o!==document.body;){if(o.hasAttribute(r))return!0;o=o.parentElement}return!1};function b(e,t){var n=/(#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,r=/(\s|#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,s=t.ignoreUUIDClasses?/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/:{test:function(){return!1}},c=hj.tryCatch((function(e){var n,r,s,c,g,y,b,w,S,_=function(e,t){var n,r;if(p[t]){var s=p[t];for(g=0;g<s.length;g++)if(s[g]===e[0])return g}if(b=hj.hq(t),null!=t&&null!==(n=t.includes)&&void 0!==n&&n.call(t,h)||null!=t&&null!==(r=t.includes)&&void 0!==r&&r.call(t,d)){var c=new RegExp("^(.*?(".concat(h,"|").concat(d,").*?)>(.*)$"),"i"),u=i(o,t.match(c),4),l=u[1],f=u[3];hj.hq.each(hj.hq(l.replace(h,"").replace(d,"")),(function(e,t){b=Array.from(b).concat(function(e,t){if(!e||!t)return[];var n=[],r=[];r.push({host:e,selector:t});for(var o=function(){var e=r.shift(),t=e.host,o=i(a,e.selector.replace(h,"").replace(d,"").split(">")),s=o[0],c=o.slice(1).join(">");v(t)[0]&&Array.from(v(t)).forEach((function(e){Array.from(e.assignedElements({flatten:!0})).forEach((function(e){e.matches(s)&&(c?r.push({host:e.shadowRoot||e,selector:c}):n.push(e))}))})),Array.from(t.children).filter((function(e){return e.matches(s)})).forEach((function(e){c?r.push({host:e.shadowRoot||e,selector:c}):n.push(e)}))};r.length>0;)o();return n}(t.shadowRoot,f))}))}for(p[t]=b,g=0;g<b.length;g++)if(b[g]===e[0])return g;return 0};if(!0!==t.getFullSelector&&!j(e)&&!m(e.get(0))){if(w=f(e.attr("id")),S=l(e.attr("name")),w)return"0:#"+w;if(S)return _(e,y='*[name="'+S+'"]')+":"+y}return _(e,y=u(e))+":"+(null!==(n=y)&&void 0!==n&&null!==(r=n.includes)&&void 0!==r&&r.call(n,d)?null===(s=y)||void 0===s||null===(c=s.replaceAll)||void 0===c?void 0:c.call(s,d,""):y)}),"common"),u=hj.tryCatch((function(e,n){var r=e.get(0);if(!r)return n;if(void 0===n&&(n=""),e.is("html"))return"html"+n;var o=_(r.nodeName.toLowerCase());if(r.shadowRoot&&n&&(o=function(e){return!!e.shadowRoot&&!y(e.shadowRoot)}(r)?"".concat(d).concat(o):"".concat(h).concat(o)),!j(e)&&!m(r)){var i=f(e.attr("id"));if(i)return o+"#"+i+n;if("body"!==o||!t.ignoreBodyClasses){var a=S(e.attr("class"));a&&(o+=a)}}if(null!==r.assignedSlot&&void 0!==r.assignedSlot){for(var s=r.assignedSlot;null!==s.assignedSlot&&void 0!==s.assignedSlot;)s=s.assignedSlot;return u(hj.hq(s).parent(),">"+o+n)}return u(e.parent(),">"+o+n)}),"common"),l=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1)&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||s.test(e)||""===e||r.push(e)})),r.join(" "))},f=function(e){return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||w(e))&&(e=e.replace(r,"\\$1"),e=b(e),g(e))},g=function(e){if(!e)return e;var t=e.charAt(0);return/\d/.test(t)?"\\3"+t+" "+e.slice(1):e},b=function(e){if(!e||"-"!==e.charAt(0))return e;var t=e.charAt(0),n=e.charAt(1);return/\d/.test(n)?t+"\\3"+n+" "+e.slice(2):e},w=function(e){return 1===e.length&&"-"===e},S=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||w(e))&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||s.test(e)||""===e||r.push(g(b(e)))})),r.length?"."+r.join("."):"")},_=function(e){return e.replace(t.disallowedTagNameCharactersRE,"")};return c(e)}var w={2:{ignoreClassList:["over","hover","active","selected","scrolled"],ignoreBodyClasses:!0,ignoreUUIDClasses:!0,maxClassesAllowed:5,disallowedTagNameCharactersRE:/[^a-zA-Z0-9-_]/g}},S={2:r(r({},w[2]),{},{getFullSelector:!0})},_=function(e){return f[e=!e||e<2?2:e]||(f[e]={get:function(t){return b(t,w[e])},clearMatchingElementsCache:function(){p={}}}),f[e]},C=function(e){return g[e=!e||e<2?2:e]||(g[e]={get:function(t){return b(t,S[e])}}),g[e]};hj.tryCatch((function(){hj.selector=_,hj.fullSelector=C}))()},6783:function(e,t,n){"use strict";n.r(t);var r=n(512),o=n(2537);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}hj.tryCatch((function(){hj.loader.registerModule("Command",function(){var e={},t={},n=window.hj.q,a=["ready","stateChange","trigger","session_event"],s=!1;function c(){var e=Array.prototype.slice.call(n.shift()),r=t[e[0]],o=e.slice(1);hj.log.debug("Processing command: "+e[0]+" "+o.map((function(e){return"object"!==i(e)&&"function"!=typeof e||null===e?e:JSON.stringify(e)})).join(", "),"command"),hj.hq.isFunction(r)?function(e){return!hj.optOut||a.indexOf(e)>=0}(e[0])?hj.tryCatch(r,"command")(o):hj.log.debug('Command "'+e[0]+'" blocked due to opt-out',"command"):hj.log.debug('Unknown command: "'+e[0]+'"',"command"),n.length>0&&hj.tryCatch(c)()}function u(e){e[0]&&hj.event.signal("trigger:"+e[0])}function l(e){if(e[0]&&Array.isArray(e[0])){var t=hj.privacy.getTagsWithoutPII(e[0]);hj.behaviorData.tagRecording(t)}else hj.log.error("tagRecording was called with an invalid argument. Please provide an array of tags. For example: hj('tagRecording', ['tag1', 'tag2']).\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115011819488-How-to-Tag-your-Hotjar-Recordings")}return t.vpv=function(){},t.stateChange=function(e){var t=window.location.href;e&&e.length>=1&&e[0]&&(t=hj.url.getUrlFromString(e[0])),hj.log.debug('Changing state to URL "'+t+'"',"command"),hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)},t.tagRecording=function(e){l(e)},t.trigger=function(e){u(e)},t.identify=function(e){if(e[1]){var t=e[0];t=!t&&0!==t||"null"===t||"undefined"===t?null:String(t),r.r.set(t,e[1])}else null!=e[0]&&"object"===i(e[0])&&r.r.set(null,e[0])},t.event=function(e){u(e),l([[e[0]]])},t.session_event=function(e){o.b.set(e[0])},t.gaSetTracker=function(e){e[0]&&hj.integrations.google_analytics.sendHotjarUserId.setTracker(e[0])},t._xhr=function(){},t.ready=function(e){e.forEach((function(e){e()}))},e.run=function(){hj.command=this},e.activate=function(){s||(s=!0,Object.defineProperty(n,"push",{writable:!0,value:function(){for(var e=0;e<arguments.length;e+=1)this[this.length]=arguments[e];return c(),this.length}}),n.length>0&&c())},hj.initialVisitDataSent&&e.activate(),e}(),!0)}),"command")()},1229:function(e,t,n){"use strict";n.r(t);var r=n(4788),o=n(6939),i=n(9521),a=n(6849),s=n(6742),c=n(7698),u=n(724),l=n(3883),h=n(5148);hj.tryCatch((function(){return hj.loader.registerModule("BehaviorData",(e={},hj.behaviorData={tagRecording:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e=e||[];for(var o=[],i=u.y.get("autoTagsToProcess"),s=u.y.get("tagsToProcess"),l=function(){var t=hj.hq.trim(e[h]);if(t.length&&t.length<=hj.maxRecordingTagLength){var r={name:t,time:hj.time.getNow(),timestamp:c.f_.now()};hj.tryCatch((function(){n&&(r.selector=n.selector,r.page_x=n.pageX,r.page_y=n.pageY,r.offset_x=n.offsetX,r.offset_y=n.offsetY)}),"behavior-data.tagRecording")(),o.push(r)}else hj.log.warn('Invalid recording tag: " '.concat(t,' ", should have length 1.. ').concat(hj.maxRecordingTagLength," characters, was ").concat(t.length,"."))},h=0;h<e.length;h+=1)l();if(o.length)if(u.y.get("active")){var d=t?r.s.AUTOTAG_RECORDING:r.s.TAG_RECORDING;(0,a.N)(d,o,!0).flush()}else t?(i=i.concat(o),u.y.set("autoTagsToProcess",i)):(s=s.concat(o),u.y.set("tagsToProcess",s))}},e.run=hj.tryCatch((function(t){hj.isPreview||(0,o.W)(t)||l.Q.get("session.inSample")&&e.runRecordings(t)}),"behavior-data.run"),e.runRecordings=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hj.hq.noop;l.Q.get("session.sessionResumed")&&i.l.trackSessionResumed(),function(e){if(u.y.set("active",!1),hj.settings.record){var t=void 0===hj.settings.record_targeting_rules||!hj.settings.record_targeting_rules.length;i.l.setRecordingEnabled(l.Q.get("session.recordingEnabled"));var n=i.l.isRecordingEnabled();hj.log.debug("_hjRecordingEnabled is set to "+n,"recordings"),t||n?(n||(0,h.IU)({recordingEnabled:!0}),s.N.start()):hj.targeting.matchRules({rules:hj.settings.record_targeting_rules,userAttributeMatchLogic:void 0},e,hj.tryCatch((function(){(0,h.IU)({recordingEnabled:!0}),s.N.start()}),"behavior-data.maybeStartRecordings")),l.Q.get("session.sessionResumed")&&((0,h.IU)({sessionResumed:!1}),s.N.resume())}}(e)},e),!1);var e}),"behavior-data")()},6849:function(e,t,n){"use strict";n.d(t,{N:function(){return r}});var r=function(e,t,n){return hj.eventStream.write(e,t,n)}},6742:function(e,t,n){"use strict";n.d(t,{N:function(){return K}});var r,o,i=n(4788),a=n(512),s=n(9521),c=n(6849),u=n(7698),l={setup:!1,listen:hj.tryCatch((function(e){l.setup?e&&l.send(i.s.INSERTED_RULE,hj.insertedRules.getCurrentInsertedRules()):(hj.insertedRules.init(),hj.insertedRules.register(l.send.bind(l,i.s.INSERTED_RULE),!0),hj.cssBlobs.register(l.send.bind(l,i.s.INSERTED_RULE)),hj.deletedRules.init(),hj.deletedRules.register(l.send.bind(l,i.s.DELETED_RULE)),l.setup=!0)}),"behavior-data.cssRules.listen"),send:hj.tryCatch((function(e,t){t.length&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:u.f_.now(),rules:t};hj.debug.emit(e,n),(0,c.N)(e,n,!1)}),"behavior-data.cssRules"))}),"behavior-data.cssRules.send")},h=hj.tryCatch((function(e,t){var n=hj.hq(e),r=hj.selector(s.l.getSelectorVersion()).get(n);if(r&&t){var o={offset_x:t.pageX,offset_y:t.pageY,selector:r};s.l.isRecordingEnabled()&&(o.time=hj.time.getNow(),o.timestamp=u.f_.now()),(0,c.N)(i.s.MOUSE_CLICK,o,!1).flush()}}),"behavior-data.frameMouseClicks.frameClick"),d={setup:!1,listen:hj.tryCatch((function(){d.setup||(hj.log.debug("Setting up frame mouse click listeners.","events"),d.send(),d.setup=!0)}),"behavior-data.frameMouseClicks.listen"),send:hj.tryCatch((function(){if(s.l.isRecordingEnabled()){var e=location.origin,t=hj.hq("iframe");t.length&&[].forEach.call(t,(function(t){if(t.src&&-1!==t.src.indexOf(e))try{var n=t.contentWindow;hj.hq(n).on("mousedown",(function(e){h(t,e)}))}catch(e){}}))}}),"behavior-data.frameMouseClicks.send")},f={setup:!1,listen:hj.tryCatch((function(){f.setup||(hj.log.debug("Setting up input choice change listeners.","events"),hj.hq(document).on("change","input[type=checkbox], input[type=radio]",f.send),f.setup=!0)}),"behavior-data.inputChoiceChange.listen"),send:hj.tryCatch((function(e){if("INPUT"===e.target.tagName&&("checkbox"===e.target.type||"radio"===e.target.type)&&s.l.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.is(":checked");(0,c.N)(i.s.INPUT_CHOICE_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:u.f_.now()},!0).flush()}}),"behavior-data.inputChoiceChange.send")},g=!1,p=!1,v=[],y={setup:!1,listen:hj.tryCatch((function(){y.setup||(hj.log.debug("Setting up key press listeners.","events"),hj.hq(document).on("input","input",y.update),hj.hq(document).on("blur","input",y.send),hj.hq(document).on("input","textarea",y.update),hj.hq(document).on("blur","textarea",y.send),y.setup=!0)}),"behavior-data.keyPress.listen"),update:hj.tryCatch((function(e){var t=hj.hq(e.target),n=t.val();p=p||hj.privacy.isRiskyNotAllowlistedOrSuppressedElement(e.target,void 0,!0),v.push({time:hj.time.getNow(),timestamp:u.f_.now(),selector:hj.selector().get(t),text:n,type:e.target.type,suppression:p?"full":"none"}),g=!0}),"behavior-data.keyPress.update"),send:hj.tryCatch((function(){if(s.l.isRecordingEnabled()&&g){if(p){var e=v[0],t=v[v.length-1],n=hj.privacy.getSuppressedText(e.type,t.text),r=Math.floor((t.time-e.time)/Math.max(n.length,1));v=[];for(var o=0;o<Math.min(n.length-1,500);o++)v.push({time:e.time+r*o,timestamp:e.timestamp+r*o,selector:e.selector,text:n.substring(0,o+1),type:e.type,suppression:"full"});t.text=n,v.push(t)}hj.hq.each(v,(function(e,t){delete t.type})),(0,c.N)(i.s.KEY_PRESS,v,!0).flush(),g=!1,p=!1,v=[]}}),"behavior-data.keyPress.send")},m=0,j=0,b=!1,w=0,S=0,_=null,C={setup:!1,listen:hj.tryCatch((function(){C.setup||(hj.log.debug("Setting up mouse move listeners.","events"),hj.hq(document).on("mousemove",C.update),setInterval(C.send,100),C.setup=!0)}),"behavior-data.mouseMove.listen"),update:hj.tryCatch((function(e){m=e.clientX,j=e.clientY;var t=hj.hq(document.elementFromPoint(m,j));if(t[0]){var n=t.offset();n&&(w=e.pageX-parseInt(n.left,10),S=e.pageY-parseInt(n.top,10),_=hj.selector(s.l.getSelectorVersion()).get(t),b=!0)}}),"behavior-data.mouseMove.update"),send:hj.tryCatch((function(){s.l.isRecordingEnabled()&&b&&(s.l.isRecordingEnabled()&&((0,c.N)(i.s.MOUSE_MOVE,{time:hj.time.getNow(),timestamp:u.f_.now(),x:m,y:j}),(0,c.N)(i.s.RELATIVE_MOUSE_MOVE,{time:hj.time.getNow(),timestamp:u.f_.now(),offset_x:w,offset_y:S,selector:_})),b=!1)}),"behavior-data.mouseMove.send")},E=hj.tryCatch((function(e,t){if(!e||!t)return!1;var n=t.replaceAll(/[* ]/g,"");return Array.from(n).some((function(t){return e.includes(t)}))}),"behavior-data.mouseClick.containsCharsFromSource"),I=!1,T={setup:!1,listen:hj.tryCatch((function(){T.setup||(hj.log.debug("Setting up mouse click listeners.","events"),hj.hq(document).on("mousedown",T.send),T.setup=!0)}),"behavior-data.mouseClick.listen"),send:hj.tryCatch((function(e){var t,n;if(s.l.isRecordingEnabled()){I&&(C.update(e),I=!1);var r=(null==e||null===(t=e.composedPath)||void 0===t||null===(n=t.call(e))||void 0===n?void 0:n.length)?e.composedPath()[0]:e.target,o=hj.selector(s.l.getSelectorVersion()).get(hj.hq(r)),a=hj.fullSelector(s.l.getSelectorVersion()).get(hj.hq(r)),l=[];if(r&&"pageX"in e&&"pageY"in e&&void 0!==o){var h=hj.hq(r).offset();l.left=e.pageX-h.left,l.top=e.pageY-h.top;var d={offset_x:l.left,offset_y:l.top,selector:o,full_selector:a,page_x:e.pageX,page_y:e.pageY,text:null,time:hj.time.getNow(),timestamp:u.f_.now()},f=0,g=!1,p=r;do{if("BUTTON"===p.tagName||"A"===p.tagName){g=!0;break}f++,p=p.parentNode}while(p&&(f<=2||g));if(g){var v=hj.privacy.getSuppressedTextNode(r),y=v.content,m=v.suppressionType;if(y&&("none"===m||"partial"===m&&E(y,r.textContent))){var j=y.trim().substring(0,100);j&&(d.text=j)}}(0,c.N)(i.s.MOUSE_CLICK,d,!1).flush()}}}),"behavior-data.mouseClick.send"),trackCoordinatesOnNextClick:function(){I=!0}},N=hj.tryCatch((function(e){var t={},n=!1;return t.listen=hj.tryCatch((function(){n||(hj.log.debug("Setting up "+e+" event listener.","events"),hj.hq(document).on(e,t.send),n=!0)}),"behavior-data."+e+".listen"),t.send=hj.tryCatch((function(){s.l.isRecordingEnabled()&&(0,c.N)(i.s.CLIPBOARD,{time:hj.time.getNow(),timestamp:u.f_.now(),action:e},!0).flush()}),"behavior-data."+e+".send"),t}),"behavior-data.newClipboardEventListener"),O={listen:hj.tryCatch((function(){document.addEventListener("visibilitychange",(function(){return O.send(!document.hidden)}),!1)}),"behavior-data.pageVisibility.listen"),send:hj.tryCatch((function(e){var t={time:hj.time.getNow(),timestamp:u.f_.now(),page_visible:e};hj.debug.emit("page_visibility",t),(0,c.N)(i.s.PAGE_VISIBILITY,t,!1).flush()}),"behavior-data.pageVisibility.send")},R=!1,A={setup:!1,listen:hj.tryCatch((function(){A.setup||(hj.log.debug("Setting up scroll listeners.","events"),window.addEventListener("scroll",A.update,!0),hj.event.listen("shadow-event:scroll",A.update),setInterval(A.send,100),0!==hj.ui.getScrollPosition().top&&A.update(),A.setup=!0)}),"behavior-data.scroll.listen"),update:hj.tryCatch((function(e){r=e?e.target===window.document?window:e.target:window,R=!0}),"behavior-data.scroll.update"),send:hj.tryCatch((function(){if(s.l.isRecordingEnabled()&&R){var e=(r=r||window)===window?"window":hj.selector(s.l.getSelectorVersion()).get(hj.hq(r))||"window",t=hj.ui.getScrollPosition(r);(0,c.N)(i.s.SCROLL,{elem:e,time:hj.time.getNow(),timestamp:u.f_.now(),x:t.left,y:t.top}),R=!1}}),"behavior-data.scroll.send")},k=0,x={setup:!1,listen:hj.tryCatch((function(){x.setup||(hj.log.debug("Setting up scroll reach listeners.","events"),hj.hq(window).on("scroll resize",x.send,!0),x.setup=!0)}),"behavior-data.scrollReach.listen"),send:hj.tryCatch((function(){if(s.l.isRecordingEnabled()){var e=hj.ui.getBottomAsPercentage();e>k&&(k=e,(0,c.N)(i.s.SCROLL_REACH,{time:hj.time.getNow(),timestamp:u.f_.now(),max_bottom:k},!0))}}),"behavior-data.scrollReach.send")},P={setup:!1,listen:hj.tryCatch((function(){P.setup||(hj.log.debug("Setting up select change listeners.","events"),hj.hq(document).on("change","select",P.send),P.setup=!0)}),"behavior-data.selectChange.listen"),send:hj.tryCatch((function(e){if("SELECT"===e.target.tagName&&s.l.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.val();(0,c.N)(i.s.SELECT_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:u.f_.now()},!0).flush()}}),"behavior-data.selectChange.send")},D={setup:!1,listen:hj.tryCatch((function(){D.setup||(hj.log.debug("Setting up screen size change listeners.","events"),o=hj.ui.getWindowSize(),setInterval(D.checkResize,1e3),D.checkResize(),D.setup=!0)}),"behavior-data.viewportResize.listen"),checkResize:hj.tryCatch((function(){var e=hj.ui.getWindowSize();e.width===o.width&&e.height===o.height||(o=e,D.send())}),"behavior-data.viewportResize.checkResize"),send:hj.tryCatch((function(){s.l.isRecordingEnabled()&&(0,c.N)(i.s.VIEWPORT_RESIZE,{time:hj.time.getNow(),timestamp:u.f_.now(),w:o.width,h:o.height}).flush()}),"behavior-data.viewportResize.send")},M={setup:!1,listen:hj.tryCatch((function(){M.setup||(hj.adoptedStyleSheets.init(),hj.adoptedStyleSheets.register(M.send.bind(M,"adopted_style_sheets"),!0),M.setup=!0)}),"behavior-data.adoptedStyleSheets.listen"),send:hj.tryCatch((function(e,t){t&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:u.f_.now(),parentSelector:t.parentSelector,sheets:t.sheets,nodeId:t.nodeId};hj.debug.emit(e,n),(0,c.N)(e,n,!1)}),"behavior-data.adoptedStyleSheets"))}),"behavior-data.adoptedStyleSheets.send")},U=N("copy"),L=N("cut"),H=N("paste"),V=[M,U,l,L,d,f,y,T,C,O,H,A,x,P,D],q={enableRecording:hj.tryCatch((function(e){s.l.setRecordingEnabled(!0),V.forEach((function(t){t.listen(e)})),hj.autotag.start()}),"behavior-data.events.enableRecording")},W=n(5547),z=n(3883),B=n(724);function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},F.apply(this,arguments)}var G=!1,Y=(0,W.tU)((function(e,t,n,r,o){var a;(0,c.N)((a={},a[i.s.RECORDING_HELO]=function(){return function(e,t,n){return{playback_version:3,script_context_id:hj.scriptContextId,start_time:t,start_timestamp:n,page_visit_info:e,resumed:s.l.isResumedSession(),first_seen:!0===hj.store.session.get("session.firstSeen")}}(hj.visitData.getPageVisitInfo(t,hj.settings.site_id),n,r)},a)).flush(),hj.eventStream.reportPageContent(e),(0,c.N)(i.s.SCROLL_REACH,{max_bottom:hj.ui.getBottomAsPercentage()},!0),o?q.enableRecording():B.y.on("pageInfo",(function(e,t){K.initializeTreeMirror(e.urlMD5),q.enableRecording(z.Q.get("session.sessionResumed")),t()}))})),K={isTreeMirrorInitialized:function(){return G},start:hj.tryCatch((function(){s.l.setRecordingEnabled(!0),B.y.set("active",!0);var e=hj.ui.getWindowSize(),t=hj.time.getNow(),n=u.f_.now(),r=B.y.get("pageVisitKey");Y(r,r,e,t,n,this.isTreeMirrorInitialized());var o=B.y.get("tagsToProcess");o.length&&((0,c.N)(i.s.TAG_RECORDING,o,!0).flush(),B.y.set("tagsToProcess",[]));var l=B.y.get("autoTagsToProcess");l.length&&((0,c.N)(i.s.AUTOTAG_RECORDING,l,!0).flush(),B.y.set("autoTagsToProcess",[])),hj.settings.user_attributes_enabled&&a.r.flush()}),"behavior-data.recording.start"),setAndSendPageContent:function(e,t){var n=z.Q.get("sessionAccepted");z.Q.on("sessionAccepted",(function(){return hj.eventStream.storePageContent(t,e)}),!n)},reset:function(){B.y.reset({pageVisitKey:void 0,pageInfo:void 0,pageContent:void 0,tagsToProcess:[],autoTagsToProcess:[],active:!1}),hj.eventStream.clearPageContent(),hj.treeMirror.resetMutationListeners(),G=!1},initializeTreeMirror:hj.tryCatch((function(e){hj.treeMirror.mutationObserverAvailable&&hj.treeMirror.getTree(J.bind(null,e),X)}),"behavior-data.initializeTreeMirror"),resume:function(){A.update(),T.trackCoordinatesOnNextClick()}},J=function(e,t){K.setAndSendPageContent(e,JSON.stringify(F({docType:hj.html.getPageDoctype()},t))),G=!0},X=function(e){var t=e.removed,n=e.addedOrMoved,r=e.attributes,o=e.text,a={};(t.length||n.length||r.length||o.length)&&(a.time=hj.time.getNow(),a.timestamp=u.f_.now(),t.length&&(a.a=t),n.length&&(a.b=n),r.length&&(a.c=r),o.length&&(a.d=o),hj.selector().clearMatchingElementsCache(),hj.debug.emit("mutation",a),(0,c.N)(i.s.MUTATION,a,!1))}},724:function(e,t,n){"use strict";n.d(t,{y:function(){return r}});var r=(0,n(4557).M)({isPageVisitStale:!1,pageVisitKey:void 0,pageInfo:void 0,pageContent:void 0,tagsToProcess:[],autoTagsToProcess:[],active:!1},"recording")},9521:function(e,t,n){"use strict";n.d(t,{l:function(){return a}});var r=!1,o=2,i=!1,a={isRecordingEnabled:function(){return r},setRecordingEnabled:function(e){r=e},getSelectorVersion:function(){return o},setSelectorVersion:function(e){o=e},trackSessionResumed:function(){i=!0},isResumedSession:function(){return i}}},6682:function(e,t,n){"use strict";n.r(t);var r=n(9521),o=n(3883),i=n(5153),a=n(4788),s=n(6849),c=n(7698),u=n(1131);hj.bridge={storage:u.t,getSessionUserId:i.bN,getSessionFirstSeen:function(){return o.Q.get("session.firstSeen")},getPageContent:hj.html.getPageContent,flushUserAttributes:function(e,t){if(r.l.isRecordingEnabled()){var n={hj_id:(0,i.bN)(!0),user_id:e,attributes:t,time:hj.time.getNow(),timestamp:c.f_.now()};(0,s.N)(a.s.IDENTIFY_USER,n,!0).flush(),hj.log.debug("User attributes sent up websocket.","userAttributes",n)}},isRecordingEnabled:r.l.isRecordingEnabled}},4931:function(e,t,n){"use strict";n.r(t);var r=[],o={getModules:hj.tryCatch((function(){return r}),"common"),registerModule:hj.tryCatch((function(e,t,n){r.push({name:e,module:t,nonTracking:void 0!==n&&n})}),"common")},i={isOn:function(){var e;return void 0===_hjSettings.hjdebug&&(_hjSettings.hjdebug="true"===(null===(e=hj.bridge.storage.items.DEBUG_FLAG)||void 0===e?void 0:e.get())),_hjSettings.hjdebug},on:function(e){_hjSettings.hjdebug=!0,e&&hj.bridge.storage.items.DEBUG_FLAG.set(!0)},off:function(){_hjSettings.hjdebug=!1,hj.bridge.storage.items.DEBUG_FLAG.clear()},emit:function(e,t){"undefined"!=typeof _hjEmitters&&_hjEmitters.includes&&_hjEmitters.includes(e)&&window.postMessage({data:t,message:hj.bridge.storage.items.DEBUG_FLAG.getKey(),type:e},"*")}};function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}var s=!1,c="",u={autotag:"#ff0099",command:"#0079a4",cookies:"#5a2c22",data:"#009bd2",event:"#ff7000",events:"#ffc000",exception:"#e63946",heatmap:"#700000",init:"#6600cc",integration:"#2a9072",poll:"#00a000",property:"#ff33cc",recording:"#dd0000",rendering:"#bd00ea",sampling:"#efb0a1",survey:"#007000",targeting:"#00ee00",visitdata:"#00668a",websocket:"#0dc0ff"},l={init:function(){void 0===window.console&&(window.console={debug:function(){},trace:function(){},log:function(){},info:function(){},warn:function(){},error:function(){}})},debug:function(e,t,n){var r=t&&u[t.toLowerCase()]||"#2a9d8f";c!=e&&s&&(console.groupEnd(),s=!1),c=e,void 0!==hj.debug&&hj.debug.isOn()&&("object"===a(e)?hj.hq.each(e,(function(e,n){hj.log.debug(e+": "+n,t,null)})):(e=t&&"string"==typeof e?t.toUpperCase()+": "+e:e,e="%c"+(new Date).toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1")+":%c "+e,n?(s||(console.groupCollapsed(e+":","color: #999;","color: "+r+";"),s=!0),console.log(n)):console.log(e,"color: #999;","color: "+r+";")))},info:function(e){console.log("%c"+e,"color: #006EFF")},warn:function(e){console.log("%c"+e,"color: #E8910C")},error:function(e){console.error("Hotjar error: "+e)},warnIfEmpty:function(e,t){if(Array.isArray(e)?0===e.length:hj.hq.isNullOrUndefined(e)){var n=null===e?"null":void 0===e?"undefined":"no value";hj.log.debug("WARNING: [".concat(t,"] a value was expected but ").concat(n," was found!"))}}},h=n(4808),d=n(277),f=n(9163),g=n(512),p=n(8714),v=function(e,t){return e+t&4294967295};function y(e,t){var n=j(n=e[0],i=e[1],o=e[2],r=e[3],t[0],7,-680876936),r=j(r,n,i,o,t[1],12,-389564586),o=j(o,r,n,i,t[2],17,606105819),i=j(i,o,r,n,t[3],22,-1044525330);n=j(n,i,o,r,t[4],7,-176418897),r=j(r,n,i,o,t[5],12,1200080426),o=j(o,r,n,i,t[6],17,-1473231341),i=j(i,o,r,n,t[7],22,-45705983),n=j(n,i,o,r,t[8],7,1770035416),r=j(r,n,i,o,t[9],12,-1958414417),o=j(o,r,n,i,t[10],17,-42063),i=j(i,o,r,n,t[11],22,-1990404162),n=j(n,i,o,r,t[12],7,1804603682),r=j(r,n,i,o,t[13],12,-40341101),o=j(o,r,n,i,t[14],17,-1502002290),n=b(n,i=j(i,o,r,n,t[15],22,1236535329),o,r,t[1],5,-165796510),r=b(r,n,i,o,t[6],9,-1069501632),o=b(o,r,n,i,t[11],14,643717713),i=b(i,o,r,n,t[0],20,-373897302),n=b(n,i,o,r,t[5],5,-701558691),r=b(r,n,i,o,t[10],9,38016083),o=b(o,r,n,i,t[15],14,-660478335),i=b(i,o,r,n,t[4],20,-405537848),n=b(n,i,o,r,t[9],5,568446438),r=b(r,n,i,o,t[14],9,-1019803690),o=b(o,r,n,i,t[3],14,-187363961),i=b(i,o,r,n,t[8],20,1163531501),n=b(n,i,o,r,t[13],5,-1444681467),r=b(r,n,i,o,t[2],9,-51403784),o=b(o,r,n,i,t[7],14,1735328473),n=w(n,i=b(i,o,r,n,t[12],20,-1926607734),o,r,t[5],4,-378558),r=w(r,n,i,o,t[8],11,-2022574463),o=w(o,r,n,i,t[11],16,1839030562),i=w(i,o,r,n,t[14],23,-35309556),n=w(n,i,o,r,t[1],4,-1530992060),r=w(r,n,i,o,t[4],11,1272893353),o=w(o,r,n,i,t[7],16,-155497632),i=w(i,o,r,n,t[10],23,-1094730640),n=w(n,i,o,r,t[13],4,681279174),r=w(r,n,i,o,t[0],11,-358537222),o=w(o,r,n,i,t[3],16,-722521979),i=w(i,o,r,n,t[6],23,76029189),n=w(n,i,o,r,t[9],4,-640364487),r=w(r,n,i,o,t[12],11,-421815835),o=w(o,r,n,i,t[15],16,530742520),n=S(n,i=w(i,o,r,n,t[2],23,-995338651),o,r,t[0],6,-198630844),r=S(r,n,i,o,t[7],10,1126891415),o=S(o,r,n,i,t[14],15,-1416354905),i=S(i,o,r,n,t[5],21,-57434055),n=S(n,i,o,r,t[12],6,1700485571),r=S(r,n,i,o,t[3],10,-1894986606),o=S(o,r,n,i,t[10],15,-1051523),i=S(i,o,r,n,t[1],21,-2054922799),n=S(n,i,o,r,t[8],6,1873313359),r=S(r,n,i,o,t[15],10,-30611744),o=S(o,r,n,i,t[6],15,-1560198380),i=S(i,o,r,n,t[13],21,1309151649),n=S(n,i,o,r,t[4],6,-145523070),r=S(r,n,i,o,t[11],10,-1120210379),o=S(o,r,n,i,t[2],15,718787259),i=S(i,o,r,n,t[9],21,-343485551),e[0]=v(n,e[0]),e[1]=v(i,e[1]),e[2]=v(o,e[2]),e[3]=v(r,e[3])}function m(e,t,n,r,o,i){return t=v(v(t,e),v(r,i)),v(t<<o|t>>>32-o,n)}function j(e,t,n,r,o,i,a){return m(t&n|~t&r,e,t,o,i,a)}function b(e,t,n,r,o,i,a){return m(t&r|n&~r,e,t,o,i,a)}function w(e,t,n,r,o,i,a){return m(t^n^r,e,t,o,i,a)}function S(e,t,n,r,o,i,a){return m(n^(t|~r),e,t,o,i,a)}function _(e){var t,n=[];for(t=0;64>t;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}var C="0123456789abcdef".split("");function E(e){for(var t="",n=0;4>n;n++)t+=C[e>>8*n+4&15]+C[e>>8*n&15];return t}var I=function(e,t){var n="";try{n=function(e){for(var t=0;t<e.length;t++)e[t]=E(e[t]);return e.join("")}(function(e){var t,n=e.length,r=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=e.length;t+=64)y(r,_(e.substring(t-64,t)));e=e.substring(t-64);var o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<e.length;t++)o[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),55<t)for(y(r,o),t=0;16>t;t++)o[t]=0;return o[14]=8*n,y(r,o),r}((0,p.IU)(e)))}catch(e){t?n="":hj.exceptions.log(e,"md5")}return n};"5d41402abc4b2a76b9719d911017c592"!=I("hello")&&(v=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n});var T=hj.tryCatch((function(){var e=function(){try{return window.self!==window.top}catch(e){return!0}}(),t={width:!e&&window.screen?window.screen.width:document.body.clientWidth,height:!e&&window.screen?window.screen.height:document.body.clientHeight};return{width:window.innerWidth||document.documentElement.clientWidth||t.width,height:window.innerHeight||document.documentElement.clientHeight||t.height}}),"common"),N=hj.tryCatch((function(){var e,t;if(document&&document.documentElement&&document.documentElement.clientWidth)e=document.documentElement.clientWidth,t=document.documentElement.clientHeight;else{var n=T();e=n.width,t=n.height}return{width:e,height:t}}),"common"),O=hj.tryCatch((function(e){return e=e||window,{left:hj.hq(e).scrollLeft(),top:hj.hq(e).scrollTop()}}),"common"),R=hj.tryCatch((function(){var e=parseInt(1e3*(hj.hq(window).scrollTop()+hj.ui.getWindowSize().height)/hj.hq(document).height(),10);return Math.min(1e3,e)}),"common"),A=hj.tryCatch((function(e){var t=hj.ui.getScrollPosition();hj.hq(document).on("scroll.hotjarDisable resize.hotjarDisable mousewheel.hotjarDisable DOMMouseScroll.hotjarDisable touchmove.hotjarDisable",hj.tryCatch((function(n){n.preventDefault(),window.scrollTo(t.left,t.top),e&&e()}),"common"))}),"common"),k=hj.tryCatch((function(){hj.hq(document).off("scroll.hotjarDisable"),hj.hq(document).off("resize.hotjarDisable"),hj.hq(document).off("mousewheel.hotjarDisable"),hj.hq(document).off("DOMMouseScroll.hotjarDisable"),hj.hq(document).off("touchmove.hotjarDisable")}),"common"),x={getWindowSize:T,getDocumentSize:N,getScrollPosition:O,getBottomAsPercentage:R,disableScrolling:A,enableScrolling:k},P=n(8417),D=n(7352),M=n(1469),U=n(1416),L=n(7698),H=n(1131);function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(this,arguments)}var q=function(){var e,t=null,n={},r="hjViewportId",o="hjActiveViewportIds",i={};function a(){var e,t=window.localStorage.getItem(o);if(!t)return[];try{e=JSON.parse(t)}catch(t){window.localStorage.removeItem(o),e=[]}return Array.isArray(e)?e:(window.localStorage.removeItem(o),[])}return n.getId=hj.tryCatch((function(){return null==t&&(H.t.canUseSessionStorage()?(function(){if(H.t.canUseLocalStorage()&&H.t.canUseSessionStorage()){var e=a();if(0!==e.length){var t=L.f_.now(),n=e.filter((function(e){return e.expireTimestamp>t}));e.length!==n.length&&window.localStorage.setItem(o,JSON.stringify(n));var i=sessionStorage.getItem(r);i&&n.some((function(e){return e.id===i}))&&window.sessionStorage.removeItem(r)}}}(),null==(t=window.sessionStorage.getItem(r))&&(t=(0,D.v4)(),window.sessionStorage.setItem(r,t)),function(){if(H.t.canUseLocalStorage()){var e=L.f_.now()+216e5,n=a();n.push({id:t,expireTimestamp:e}),window.localStorage.setItem(o,JSON.stringify(n))}}()):t=(0,D.v4)()),t}),"hj.viewport.getId"),n.isPageVisitStale=hj.tryCatch((function(){if(!H.t.canUseLocalStorage())return!1;if(!t)return!1;var n=window.localStorage.getItem(o),r="".concat(t,"-").concat(e,"-").concat(n);return null!=i[r]||(n?null==n.match(new RegExp(t))?i[r]=!1:i[r]=null===n.match(new RegExp('{[^}]*id":"'.concat(t,'"[^}]*"pageVisitKey":"').concat(e,'"}'))):i[r]=!1),i[r]}),"hj.viewport.isPageVisitStale"),n.setPageVisitForViewport=hj.tryCatch((function(n){if(e=n,H.t.canUseLocalStorage())if(t){var r=a();if(0!==r.length){var i=r.findIndex((function(e){return e.id===t}));-1!==i&&(r[i]=V(V({},r[i]),{},{pageVisitKey:n}),window.localStorage.setItem(o,JSON.stringify(r)))}}else hj.exceptions.log(new Error("Viewport ID was not correctly initialized (viewportId: ".concat(t,", pageVisitKey: ").concat(n,")")),"hj.viewport")}),"hj.viewport.setPageVisitForViewport"),window.addEventListener("pagehide",(function(){!function(){if(H.t.canUseLocalStorage()){var e=a();if(0!==e.length){var n=e.filter((function(e){return e.id!==t}));0===n.length?window.localStorage.removeItem(o):window.localStorage.setItem(o,JSON.stringify(n))}}}()})),n},W=n(5547);function z(e){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},z(e)}function B(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return F(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?F(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function G(e,t,n){return t=K(t),function(e,t){if(t&&("object"===z(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Y()?Reflect.construct(t,n||[],K(e).constructor):t.apply(e,n))}function Y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Y=function(){return!!e})()}function K(e){return K=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},K(e)}function J(e,t){return J=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},J(e,t)}function X(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Q(r.key),r)}}function $(e,t,n){return t&&X(e.prototype,t),n&&X(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Q(e){var t=function(e,t){if("object"!=z(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==z(t)?t:String(t)}function Z(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var ee=function(e){return"childList"===e.type},te=$((function e(){var t=this;Z(this,e),this.isIndex=hj.tryCatch((function(e){return+e==e>>>0}),"NodeMap.isIndex"),this.nodeId=hj.tryCatch((function(t){var n=t[e.ID_PROP];return n||(n=t[e.ID_PROP]=e.nextId_++),n}),"NodeMap.nodeId"),this.set=hj.tryCatch((function(e,n){var r=t.nodeId(e);t.nodes[r]=e,t.values[r]=n}),"NodeMap.set"),this.get=hj.tryCatch((function(e){var n=t.nodeId(e);return t.values[n]}),"NodeMap.get"),this.has=hj.tryCatch((function(e){return t.nodeId(e)in t.nodes}),"NodeMap.has"),this.deleteNode=hj.tryCatch((function(e){var n=t.nodeId(e);delete t.nodes[n],delete t.values[n]}),"NodeMap.deleteNode"),this.keys=hj.tryCatch((function(){var e=[];for(var n in t.nodes)t.isIndex(n)&&e.push(t.nodes[n]);return e}),"NodeMap.keys"),this.getValues=hj.tryCatch((function(){var e=[];for(var n in t.values)t.isIndex(n)&&e.push(t.values[n]);return e}),"NodeMap.getValues"),this.nodes=[],this.values=[]}));te.ID_PROP="__hj_mutation_summary_node_map_id__",te.nextId_=1;var ne=function(e){return e[e.STAYED_OUT=0]="STAYED_OUT",e[e.ENTERED=1]="ENTERED",e[e.STAYED_IN=2]="STAYED_IN",e[e.REPARENTED=3]="REPARENTED",e[e.REORDERED=4]="REORDERED",e[e.EXITED=5]="EXITED",e}(ne||{}),re=$((function e(t){var n=this;Z(this,e),this.getAttributeOldValue=hj.tryCatch((function(e){if(n.attributeOldValues)return n.isCaseInsensitive&&(e=e.toLowerCase()),n.attributeOldValues[e]}),"NodeChange.getAttributeOldValue"),this.getAttributeNamesMutated=hj.tryCatch((function(){var e=[];if(!n.attributeOldValues)return e;for(var t in n.attributeOldValues)e.push(t);return e}),"NodeChange.getAttributeNamesMutated"),this.attributeMutated=hj.tryCatch((function(e,t){n.attributes=!0,n.attributeOldValues=n.attributeOldValues||{},e in n.attributeOldValues||(n.attributeOldValues[e]=t)}),"NodeChange.attributeMutated"),this.characterDataMutated=hj.tryCatch((function(e){n.characterData||(n.characterData=!0,n.characterDataOldValue=e)}),"NodeChange.characterDataMutated"),this.removedFromParent=hj.tryCatch((function(e){n.childList=!0,n.added||n.oldParentNode?n.added=!1:n.oldParentNode=e}),"NodeChange.removedFromParent"),this.insertedIntoParent=hj.tryCatch((function(){n.childList=!0,n.added=!0}),"NodeChange.insertedIntoParent"),this.getOldParent=hj.tryCatch((function(){if(n.childList){if(n.oldParentNode)return n.oldParentNode;if(n.added)return null}return n.node.parentNode}),"NodeChange.getOldParent"),this.childList=!1,this.attributes=!1,this.characterData=!1,this.added=!1,this.oldParentNode=null,this.characterDataOldValue=null,this.attributeOldValues=null,this.node=t,this.isCaseInsensitive=this.node.nodeType===Node.ELEMENT_NODE&&this.node instanceof HTMLElement&&this.node.ownerDocument instanceof HTMLDocument})),oe=$((function e(){Z(this,e),this.added=new te,this.removed=new te,this.maybeMoved=new te,this.oldPrevious=new te})),ie=function(e){function t(e,n){var r;return Z(this,t),(r=G(this,t)).getChange=hj.tryCatch((function(e){var t=r.get(e);if(t)return t;var n=new re(e);return r.set(e,n),n}),"TreeChanges.getChange"),r.getOldParent=hj.tryCatch((function(e){var t=r.get(e);return t?t.getOldParent():e.parentNode}),"TreeChanges.getOldParent"),r.getIsReachable=hj.tryCatch((function(e){if(e===r.rootNode)return!0;if(!e)return!1;if(r.reachableCache.get(e))return!0;var t=r.getIsReachable(e.parentNode);return r.reachableCache.set(e,t),t}),"TreeChanges.getIsReachable"),r.getWasReachable=hj.tryCatch((function(e){if(e===r.rootNode)return!0;if(!e)return!1;if(r.wasReachableCache.get(e))return!0;var t=r.getOldParent(e);if(t===e)return!1;var n=r.getWasReachable(t);return r.wasReachableCache.set(e,n),n}),"TreeChanges.getWasReachable"),r.reachabilityChange=hj.tryCatch((function(e){var t=r.getIsReachable(e),n=r.getWasReachable(e);return t?n?ne.STAYED_IN:ne.ENTERED:n?ne.EXITED:ne.STAYED_OUT}),"TreeChanges.reachabilityChange"),r.anyParentsChanged=!1,r.anyAttributesChanged=!1,r.anyCharacterDataChanged=!1,r.rootNode=e,r.wasReachableCache=new te,r.reachableCache=new te,n.forEach((function(e){ee(e)?(r.anyParentsChanged=!0,e.removedNodes.forEach((function(t){return r.getChange(t).removedFromParent(e.target)})),e.addedNodes.forEach((function(e){return r.getChange(e).insertedIntoParent()}))):function(e){return"attributes"===e.type}(e)?(r.anyAttributesChanged=!0,r.getChange(e.target).attributeMutated(e.attributeName,e.oldValue)):function(e){return"characterData"===e.type}(e)&&(r.anyCharacterDataChanged=!0,r.getChange(e.target).characterDataMutated(e.oldValue))})),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&J(e,t)}(t,e),$(t)}(te),ae=$((function e(t,n,r){var o=this;Z(this,e),this.processMutations=hj.tryCatch((function(){(o.treeChanges.anyParentsChanged||o.treeChanges.anyAttributesChanged)&&o.treeChanges.keys().forEach((function(e){return o.visitNode(e)}))}),"MutationProjection.processMutations"),this.visitNode=hj.tryCatch((function(e,t){if(!o.visited.has(e)){o.visited.set(e,!0);var n=o.treeChanges.get(e),r=t;if((n&&n.childList||!r)&&(r=o.treeChanges.reachabilityChange(e)),r!==ne.STAYED_OUT){if(r===ne.ENTERED)o.entered.push(e);else if(r===ne.EXITED)o.exited.push(e),o.ensureHasOldPreviousSiblingIfNeeded(e);else if(r===ne.STAYED_IN){var i=ne.STAYED_IN;n&&n.childList&&(n.oldParentNode!==e.parentNode?(i=ne.REPARENTED,o.ensureHasOldPreviousSiblingIfNeeded(e)):o.wasReordered(e)&&(i=ne.REORDERED)),o.stayedIn.set(e,i)}if(r!==ne.STAYED_IN)for(var a=e.firstChild;a;a=a.nextSibling)o.visitNode(a,r)}}}),"MutationProjection.visitNode"),this.ensureHasOldPreviousSiblingIfNeeded=hj.tryCatch((function(e){if(o.calcOldPreviousSibling){o.processChildlistChanges();var t=e.parentNode,n=o.treeChanges.get(e);if(n&&n.oldParentNode&&(t=n.oldParentNode),t){var r=o.childListChangeMap,i=r.get(t);i||(i=new oe,r.set(t,i)),!i.oldPrevious.has(e)&&e.previousSibling&&i.oldPrevious.set(e,e.previousSibling)}}}),"MutationProjection.ensureHasOldPreviousSiblingIfNeeded"),this.getChanged=hj.tryCatch((function(e){o.entered.forEach((function(t){return e.added.push(t)})),o.stayedIn.keys().forEach((function(t){var n=o.stayedIn.get(t);n===ne.REPARENTED?e.reparented.push(t):n===ne.REORDERED&&e.reordered.push(t)})),o.exited.forEach((function(t){return e.removed.push(t)})),e.attributeChanged=o.attributeChangedNodes(),e.characterDataChanged=o.getCharacterDataChanged()}),"MutationProjection.getChanged"),this.attributeChangedNodes=hj.tryCatch((function(){if(!o.treeChanges.anyAttributesChanged)return{};var e={};return o.treeChanges.keys().forEach((function(t){var n=o.treeChanges.get(t);if(n.attributes&&ne.STAYED_IN===o.treeChanges.reachabilityChange(t)){var r=t;n.getAttributeNamesMutated().forEach((function(t){if(n.getAttributeOldValue(t)!==r.getAttribute(t)){var o=t;e[o]=[].concat(function(e,t,n){if(t&&!Array.isArray(t)&&"number"==typeof t.length){var r=t.length;return F(t,r)}return e(t,n)}(B,e[o]||[]),[r])}}))}})),e}),"MutationProjection.attributeChangedNodes"),this.getCharacterDataChanged=hj.tryCatch((function(){if(!o.treeChanges.anyCharacterDataChanged)return[];var e=o.treeChanges.keys(),t=[];return e.forEach((function(e){if(ne.STAYED_IN===o.treeChanges.reachabilityChange(e)){var n=o.treeChanges.get(e);n.characterData&&e.textContent!=n.characterDataOldValue&&t.push(e)}})),t}),"MutationProjection.getCharacterDataChanged"),this.getChildlistChange=hj.tryCatch((function(e){var t=o.childListChangeMap,n=t.get(e);return n||(n=new oe,t.set(e,n)),n}),"MutationProjection.getChildlistChange"),this.processChildlistChanges=hj.tryCatch((function(){o.childListChangeMap||(o.childListChangeMap=new te,o.mutations.forEach((function(e){if(ee(e)&&(o.treeChanges.reachabilityChange(e.target)===ne.STAYED_IN||o.calcOldPreviousSibling)){var t=o.getChildlistChange(e.target),n=e.previousSibling;e.removedNodes.forEach((function(e){r(e,n),t.added.has(e)?t.added.deleteNode(e):(t.removed.set(e,!0),t.maybeMoved.deleteNode(e)),n=e})),r(e.nextSibling,n),e.addedNodes.forEach((function(e){t.removed.has(e)?(t.removed.deleteNode(e),t.maybeMoved.set(e,!0)):t.added.set(e,!0)}))}function r(e,n){!e||t.oldPrevious.has(e)||t.added.has(e)||t.maybeMoved.has(e)||n&&(t.added.has(n)||t.maybeMoved.has(n))||t.oldPrevious.set(e,n)}})))}),"MutationProjection.processChildlistChanges"),this.wasReordered=hj.tryCatch((function(e){if(!o.treeChanges.anyParentsChanged)return!1;o.processChildlistChanges();var t=e.parentNode,n=o.treeChanges.get(e);if(n&&n.oldParentNode&&(t=n.oldParentNode),!t)return!1;var r=o.childListChangeMap.get(t);if(!r)return!1;if(r.moved)return r.moved.get(e);r.moved=new te;var i=new te;function a(e){var t=r,n=t.moved;if(!e)return!1;if(!t.maybeMoved.has(e))return!1;var o=n.get(e);return void 0!==o||(i.has(e)?o=!0:(i.set(e,!0),o=function(e){var t=r;if(u.has(e))return u.get(e);for(var n=e.previousSibling;n&&(t.added.has(n)||a(n));)n=n.previousSibling;return u.set(e,n),n}(e)!==c(e)),i.has(e)?(i.deleteNode(e),n.set(e,o)):o=n.get(e)),o}var s=new te;function c(e){var t=r,n=s.get(e);if(void 0!==n)return n;for(var o=t.oldPrevious.get(e);o&&(t.removed.has(o)||a(o));)o=c(o);return void 0===o&&(o=e.previousSibling),s.set(e,o),o}var u=new te;return r.maybeMoved.keys().forEach(a),r.moved.get(e)}),"MutationProjection.wasReordered"),this.entered=[],this.exited=[],this.stayedIn=new te,this.visited=new te,this.rootNode=t,this.mutations=n,this.calcOldPreviousSibling=r,this.treeChanges=new ie(t,n),this.processMutations()})),se=$((function e(t){Z(this,e),this.added=[],this.removed=[],this.reparented=[],this.reordered=[],this.valueChanged=[],this.attributeChanged={},this.characterDataChanged=[],t.getChanged(this)})),ce=$((function e(t){var n=this;Z(this,e),this.validateOptions=hj.tryCatch((function(e){var t={callback:!0,rootNode:!0,oldPreviousSibling:!0};for(var n in e)if(!(n in t))throw Error("Invalid option: "+n);if("function"!=typeof e.callback)throw Error("Invalid options: callback is required and must be a function");return{callback:e.callback,rootNode:e.rootNode||document,oldPreviousSibling:!!e.oldPreviousSibling}}),"MutationSummary.validateOptions"),this.changesToReport=hj.tryCatch((function(e){return!!e.added.length||(!!e.removed.length||(!!e.reordered.length||(!!e.reparented.length||(!!e.valueChanged.length||(!!e.characterDataChanged.length||!(!e.attributeChanged||!Object.keys(e.attributeChanged).some((function(t){var n,r;return!(null===(n=e.attributeChanged)||void 0===n||null===(r=n[t])||void 0===r||!r.length)}))))))))}),"MutationSummary.changesToReport"),this.observerCallback=hj.tryCatch((function(e){if(e&&e.length){var t=new ae(n.root,e,n.options.oldPreviousSibling),r=new se(t);n.changesToReport(r)&&n.callback(r)}}),"MutationSummary.observerCallback"),this.reconnect=hj.tryCatch((function(){var e;if(n.connected)throw Error("Already connected");null===(e=n.observer)||void 0===e||e.observe(n.root,{childList:!0,subtree:!0,attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0}),n.connected=!0}),"MutationSummary.reconnect"),this.disconnect=hj.tryCatch((function(){var e;null===(e=n.observer)||void 0===e||e.disconnect(),n.connected=!1}),"MutationSummary.disconnect");var r=void 0!==window.WebKitMutationObserver?window.WebKitMutationObserver:window.MutationObserver;this.connected=!1,this.options=this.validateOptions(t),this.root=this.options.rootNode,this.callback=this.options.callback,void 0!==r&&(this.observer=new r((function(e){return n.observerCallback(e)})),this.reconnect())}));function ue(e){return ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ue(e)}function le(){return le=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},le.apply(this,arguments)}function he(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,fe(r.key),r)}}function de(e,t,n){return t&&he(e.prototype,t),n&&he(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function fe(e){var t=function(e,t){if("object"!=ue(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=ue(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ue(t)?t:String(t)}var ge=void 0!==window.MutationObserver||void 0!==window.WebKitMutationObserver,pe=function(e){return e.nodeType===Node.ELEMENT_NODE},ve=de((function e(t,n,r){var o=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.hookAttachShadow=hj.tryCatch((function(){var e=o,t=Element.prototype.attachShadow;Element.prototype.attachShadow=function(){var n=t.apply(this,arguments);return e.shadowRoots.set(this,n),e.listenToMutations(n),e.propagateNonComposedEvents(n),n}}),"TreeMirrorClient.hookAttachShadow"),this.propagateNonComposedEvents=hj.tryCatch((function(e){["scroll"].forEach((function(t){e.addEventListener(t,(function(e){e.composed||hj.event.signal("shadow-event:".concat(e.type),e)}),!0)}))}),"TreeMirrorClient.propagateNonComposedEvents"),this.listenToMutations=hj.tryCatch((function(e){o.mutationSummaries.push(new ce({rootNode:e,callback:hj.tryCatch((function(e){return o.applyChanged(e)}),"hj.treeMirrorClient"),oldPreviousSibling:!0}))}),"TreeMirrorClient.listenToMutations"),this.serializeTarget=hj.tryCatch((function(e){var t=e.target,n=e.onSerialization,r=e.onDemand,i=o.serializeNode(t).id,a=function(){try{return window.self!==window.top}catch(e){return!0}}(),s=t.nodeType===Node.DOCUMENT_NODE&&t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0?(0,W.oL)(t.adoptedStyleSheets,t):null;return o.hookAttachShadow(),function(e){var t=e.target,n=e.task,r=e.onFinish,o=e.config,i=void 0===o?{maxIterations:5,maxTaskTime:250}:o,a=[],s=0,c=L.f_.now(),u=t.firstChild,l=[];!function e(){if(u){a.push(n(u)),u=u.nextSibling;var t=L.f_.now()-c;t>=i.maxTaskTime&&s<i.maxIterations?(l.push(t),s++,setTimeout((function(){return c=L.f_.now(),e()}),0)):e()}else l.push(L.f_.now()-c),r(a,{maxTimes:l,iterations:s}),hj.metrics.timeEnd("task-execution-time",{tag:{task:"dom-serialization-async"},total:(o=l,Math.max.apply(null,o.filter(Boolean))),extraTags:{iterations:s}});var o}()}({target:t,task:function(e){return o.serializeNode(e,!0,r)},onFinish:function(e){return n({rootId:i,children:e,isIframe:a,adoptedStyleSheetsRules:s})}})}),"TreeMirrorClient.serializeTarget"),this.disconnect=hj.tryCatch((function(){o.mutationSummaries&&o.mutationSummaries.length&&(o.mutationSummaries.forEach((function(e){return e.disconnect()})),o.mutationSummaries=[])}),"TreeMirrorClient.disconnect"),this.rememberNode=hj.tryCatch((function(e){var t=o.nextId++;return o.knownNodes.set(e,t),pe(e)&&e.shadowRoot&&!o.shadowRoots.get(e)&&o.shadowRoots.set(e,e.shadowRoot),t}),"TreeMirrorClient.rememberNode"),this.forgetNode=hj.tryCatch((function(e){o.knownNodes.deleteNode(e),o.shadowRoots.get(e)&&o.shadowRoots.deleteNode(e)}),"TreeMirrorClient.forgetNode"),this.serializeNode=hj.tryCatch((function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0;if(null==e)return null;var i=o.knownNodes.get(e);if(i&&!n)return{id:i};var a=null!=i?i:o.rememberNode(e),s=pe(e)?e.shadowRoot||o.shadowRoots.get(e):void 0,c=function(e){var t,n,r,o,i=e.node,a=e.shouldSuppressNode,s={};switch(i.nodeType){case Node.DOCUMENT_TYPE_NODE:t=i,s.name=""===t.name?"html":t.name,s.publicId=t.publicId,s.systemId=t.systemId;break;case Node.COMMENT_NODE:case Node.TEXT_NODE:o=hj.metrics.time(),n=hj.privacy.getSuppressedTextNode(i,a),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},start:o}),a=n.shouldSuppressNode,s.textContent=n.content;break;case Node.ELEMENT_NODE:o=hj.metrics.time(),r=hj.privacy.getSuppressedNode(i,a),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},start:o}),a=r.shouldSuppressNode,s.tagName=r.node.tagName,s.attributes=r.node.attributes,!s.attributes["data-hj-suppressed"]&&"IMG"===s.tagName&&i.currentSrc&&(s.attributes.src=i.currentSrc),"http://www.w3.org/1999/xhtml"!==(null==i?void 0:i.namespaceURI)&&(s.namespaceURI=i.namespaceURI)}return{properties:s,shouldSuppressNode:a}}({node:e,shouldSuppressNode:r}),u=c.properties,l=c.shouldSuppressNode,h=le(le({nodeType:e.nodeType,id:a},function(e){var t=e.shadowRoot,n=e.getNextId,r={};if(!t)return r;if(r.hasShadowRoot=!0,r.isSyntheticShadow=!!t.synthetic,t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0){var o=t.adoptedStyleSheets.reduce((function(e,r){for(var o=[],i=0;i<r.cssRules.length;i++)o.push(r.cssRules[i].cssText);var a=n();return r.ownerHostNode=t.host,r.sheetId=a,e.push({id:a,rules:o}),e}),[]);r.adoptedStyleSheetsRules=o}return r}({shadowRoot:s,getNextId:W.YN})),u);return e.nodeType===Node.ELEMENT_NODE&&(hj.cssBlobs.handleBlobStyles(e),"SCRIPT"===h.tagName||"NOSCRIPT"===h.tagName?(h.childNodes=[{nodeType:Node.TEXT_NODE,id:o.redactedContentId,textContent:""}],o.redactedContentId--):t&&(h.childNodes=function(e){var t=e.node,n=e.shadowRoot,r=e.serialize,o=e.initialChildNodes||[];if(n&&n.childNodes.length){o=[];for(var i=n.firstChild;i;i=i.nextSibling){var a=r(i);a.isInShadowRoot=!0,o.push(a)}}if(t.childNodes.length)for(var s=t.firstChild;s;s=s.nextSibling)o.push(r(s));return o}({node:e,initialChildNodes:h.childNodes,shadowRoot:s,serialize:function(e){return o.serializeNode(e,!0,n,l)}}))),h}),"TreeMirrorClient.serializeNode"),this.serializeAddedAndMoved=hj.tryCatch((function(e,t,n){var r=e.concat(t).concat(n),i=new te;r.forEach((function(e){var t=e.parentNode;if(t){var n=i.get(t);n||(n=new te,i.set(t,n)),n.set(e,!0)}}));var a=[];return i.getValues().forEach((function(e){for(var t,n=e.keys();n.length;){for(var r=n[0];r.previousSibling&&e.has(r.previousSibling);)r=r.previousSibling;for(;r&&e.has(r);){var i=o.serializeNode(r);r.previousSibling&&(i.previousSibling=o.serializeNode(r.previousSibling));var s=r.parentNode;void 0,(t=s.host)&&pe(t)?(i.parentNode=o.serializeNode(s.host,!1,!0),i.isInShadowRoot=!0):i.parentNode=o.serializeNode(s),a.push(i),e.deleteNode(r),r=r.nextSibling}n=e.keys()}})),a}),"TreeMirrorClient.serializeAddedAndMoved"),this.serializeAttributeChanges=hj.tryCatch((function(e){var t=new te;return Object.keys(e).forEach((function(n){e[n].forEach((function(e){var r=t.get(e);r||((r=o.serializeNode(e)).attributes={},t.set(e,r));var i=e.getAttribute(n);if(hj.cssBlobs.handleBlobStyles(e),null!=i){i.length&&"src"!==n&&"class"!==n&&(i=i.replace(/-?\d+\.\d+%/g,(function(e){return Math.round(parseFloat(e))+"%"})).replace(/-?\d+\.\d+/g,(function(e){return parseFloat(e).toFixed(1)})));var a={value:i,name:n},s=hj.privacy.getSuppressedNodeAttribute(e,a,!1);s&&(r.attributes[s.name]=s.value)}else r.attributes[n]=i}))})),t.getValues()}),"TreeMirrorClient.serializeAttributeChanges"),this.applyChanged=hj.tryCatch((function(e){var t=e.removed.filter((function(e){return null!=o.knownNodes.get(e)})).map((function(e){return o.serializeNode(e)})),n=o.serializeAddedAndMoved(e.added,e.reparented,e.reordered),r=o.serializeAttributeChanges(e.attributeChanged),i=e.characterDataChanged.map((function(e){var t,n=o.serializeNode(e);return n.textContent=null!==(t=hj.privacy.getSuppressedTextNode(e,!1).content)&&void 0!==t?t:void 0,n}));o.onMutation({removed:t,addedOrMoved:n,attributes:r,text:i}),e.removed.forEach((function(e){return o.forgetNode(e)})),o.processPostMutationCallbacks()}),"TreeMirrorClient.applyChanged"),this.getKnownNode=hj.tryCatch((function(e){return o.knownNodes.get(e)}),"TreeMirrorClient.getKnownNode"),this.onTreeMirrorUpdate=hj.tryCatch((function(e){o.postMutationCallbacks.push(e)}),"TreeMirrorClient.onTreeMirrorUpdate"),this.processPostMutationCallbacks=hj.tryCatch((function(){for(var e=void 0;e=o.postMutationCallbacks.shift();)e()}),"TreeMirrorClient.processPostMutationCallbacks"),this.onMutation=n,this.nextId=1,this.redactedContentId=-100,this.mutationSummaries=[],this.postMutationCallbacks=[],this.knownNodes=new te,this.shadowRoots=new te,this.serializeTarget({target:t,onSerialization:function(e){r(e),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},flush:!0})}}),ge&&(this.listenToMutations(t),this.shadowRoots.getValues().forEach((function(e){o.listenToMutations(e),o.propagateNonComposedEvents(e)})))})),ye=null,me=[],je=hj.tryCatch((function(e){return me.forEach((function(t){return t(e)}))}),"TreeMirror.mutationHandler"),be={mutationObserverAvailable:ge,getTree:hj.tryCatch((function(e,t){ye&&ge?ye.serializeTarget({target:document,onDemand:!0,onSerialization:e}):ye=new ve(document,je,e),t&&ge&&me.push(t)}),"TreeMirror.getTree"),resetMutationListeners:hj.tryCatch((function(){me=[]}),"TreeMirror.resetMutationListeners"),disconnect:hj.tryCatch((function(){ye&&(ye.disconnect(),ye=null)}),"TreeMirror.disconnect"),getNodeId:hj.tryCatch((function(e){var t;return null===(t=ye)||void 0===t?void 0:t.getKnownNode(e)}),"TreeMirror.getNodeId"),getClient:hj.tryCatch((function(){return ye}),"TreeMirror.getClient"),onTreeMirrorUpdate:hj.tryCatch((function(e){var t;null===(t=ye)||void 0===t||t.onTreeMirrorUpdate(e)}),"TreeMirror.onTreeMirrorUpdate")};function we(e){return we="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},we(e)}function Se(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ce(r.key),r)}}function _e(e,t,n){return t&&Se(e.prototype,t),n&&Se(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ce(e){var t=function(e,t){if("object"!=we(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=we(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==we(t)?t:String(t)}var Ee=_e((function e(t,n){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.deserializeDocument=hj.tryCatch((function(e,t,n){return r.root=document.cloneNode(),n&&(r.idMap={}),r.idMap[e]=r.root,t.forEach((function(e){return r.deserializeNode(e,r.root,n)})),r.root}),"TreeMirrorHost.deserializeDocument"),this.deserializeNode=hj.tryCatch((function(e,t){var n,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=!1;if(null===e)return null;var a=r.idMap[e.id];if(a&&!o)return a;var s=null!==(n=r.root.ownerDocument)&&void 0!==n?n:r.root;switch(e.nodeType){case Node.COMMENT_NODE:a=s.createComment(e.textContent);break;case Node.TEXT_NODE:a=s.createTextNode(e.textContent);break;case Node.DOCUMENT_TYPE_NODE:a=s.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case Node.ELEMENT_NODE:try{a=s.createElement(e.tagName)}catch(e){var c=e instanceof Error?e.message:"unknown";a=s.createComment('hj.treeMirror.deserializeNode.error: "'+c+'"'),i=!0;break}Object.keys(e.attributes).forEach((function(t){try{var n=e.attributes;r.delegate&&r.delegate.setAttribute&&r.delegate.setAttribute(a,t,n[t])||a.setAttribute(t,n[t])}catch(e){}}))}if(!a)throw"Could not create node of type: "+e.nodeType;return r.idMap[e.id]=a,t&&t.appendChild(a),e.childNodes&&!i&&e.childNodes.forEach((function(e){r.deserializeNode(e,a,o)})),a}),"TreeMirrorHost.deserializeNode"),this.root=t,this.delegate=n,this.idMap={}})),Ie=null,Te=hj.tryCatch((function(e){var t;Ie||(Ie=new Ee(document.cloneNode(),{setAttribute:(t=function(e,t,n){e.setAttribute(t,n)},function(e,n,r){return"data-hj-suppressed"===n&&"object"==typeof r?(function(e,t,n){const r="https://"+(n||hj.insightsHost)+"/static/app/img/transparent.png",o=function(e,t){const n="https://"+(t||hj.insightsHost)+"/static/app/img/suppressed.png",r=(e.meta||{}).style||{},o=r.width,i=r.height;return[e.style,'background: url("'+n+'") repeat !important',o?"width: "+o:"",i?"height: "+i:""].filter((function(e){return e}))}(t,n);[["src",r],["style",o.join(";")]].forEach((function(t){e.setAttribute(t[0],t[1])}))}(e,r,undefined),!0):t(e,n,r)})})),be.getTree((function(t){var n=t.rootId,r=t.children;return e(Ie.deserializeDocument(n,r,!0))}))}),"TreeMirrorHost.getMirroredDocument"),Ne=(new Date).getTime(),Oe={reset:function(){Ne=(new Date).getTime()},getNow:function(){return(new Date).getTime()-Ne}},Re={};Re={getPageContent:hj.tryCatch((function(e,t){function n(e,t){t=t||document;var n=parseInt(e.split(":")[0]),r=e.substring(e.indexOf(":")+1);return t.querySelectorAll(r)[n]}t=t||[],Te((function(r){var o=hj.insertedRules.getCurrentInsertedRules(),i={};o.forEach((function(e){var o=n(e.parentSelector,r);if(i[e.parentSelector]||(i[e.parentSelector]=""),o&&"LINK"===o.tagName){var a=o.attributes,s=a.href.value.split(location.hostname).pop().replace("/",""),c=r.querySelector(".blob-hash-"+s);if(!c){c=document.createElement("style"),i[e.parentSelector]=i[e.parentSelector]+"/* Originally: <link ";for(var u=0,l=a.length;u<l;u++)"id"!==a[u].name&&"class"!==a[u].name||c.setAttribute(a[u].name,a[u].value),i[e.parentSelector]+=a[u].name+'="'+a[u].value+'" ';i[e.parentSelector]+="> */",c.classList.add("blob-hash-"+s),o.parentNode.insertBefore(c,o.nextSibling)}o=c}i[e.parentSelector]=i[e.parentSelector]+"\n"+e.rule,-1===t.indexOf('link[href*="blob:"]')&&t.push('link[href*="blob:"]')})),Object.keys(i).forEach((function(e){n(e,r).textContent=i[e]})),function(e,t,n){var r,o=e.querySelectorAll("head iframe"),i=0==document.getElementsByTagName("base").length?location.origin:document.getElementsByTagName("base")[0].href;[].forEach.call(o,(function(e){e.parentNode.removeChild(e)})),n&&n.forEach((function(t){r=e.querySelectorAll(t),hj.hq.each(r,(function(e,t){t.parentNode.removeChild(t)}))})),Array.prototype.slice.call(e.getElementsByTagName("img")).forEach((function(e){if(e.srcset){if(""===e.src){var t=e.srcset.match(/(?:([^"'\s,]+)(\s*(?:\s+\d+[wx]))?(?:,\s*)?)/g)[0];if(t){var n=t.indexOf(" ");n>0&&(t=t.substring(0,n)),0!==(t=t.replace(",","")).indexOf("http")&&(t=i+t),e.src=t}}e.removeAttribute("srcset")}})),Array.prototype.slice.call(e.getElementsByTagName("source")).forEach((function(e){e.attributes.srcset&&e.removeAttribute("srcset")})),[].forEach.call(e.querySelectorAll("script"),(function(e){e.parentNode.removeChild(e)})),t(Re.getPageDoctype()+e.documentElement.outerHTML)}(r,e,t)}))}),"common"),getPageDoctype:hj.tryCatch((function(){return null===document.doctype?"":"<!DOCTYPE "+(document.doctype.name||"html")+(document.doctype.publicId?' PUBLIC "'+document.doctype.publicId+'"':"")+(!document.doctype.publicId&&document.doctype.systemId?" SYSTEM":"")+(document.doctype.systemId?' "'+document.doctype.systemId+'"':"")+">\n"}),"common")};var Ae,ke="manual",xe={setMode:hj.tryCatch((function(e){ke=e,Ae&&clearInterval(Ae),"automatic_with_fragments"===ke?Ae=setInterval((function(){var e=""===location.hash&&location.href.indexOf("#")>-1?"#":location.hash,t="".concat(location.origin).concat(location.pathname).concat(location.search).concat(e);hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)}),199):"automatic"===ke&&(Ae=setInterval((function(){var e="".concat(location.origin).concat(location.pathname).concat(location.search);hj.currentUrl&&hj.currentUrl.split("#")[0]!=e&&hj._init.reinit(e)}),199))}))},Pe={getCSSURLs:function(){var e=[],t=document.styleSheets;return hj.log.debug("Getting CSS","dpc","Starting"),t&&t.length>0&&hj.hq.each(t,(function(t,n){n.href&&0===n.href.indexOf("http")&&(hj.log.debug("Getting CSS","dpc",n.href),e.push(n.href))})),e}},De={},Me={remove:hj.tryCatch((function(e){var t="survey_".concat(e);"function"==typeof De[t]&&window.removeEventListener("resize",De[t])}),"hj.resizeHandlers.setHandler"),add:hj.tryCatch((function(e,t){var n="survey_".concat(t);Me.remove(t),De[n]=e,window.addEventListener("resize",e)}),"hj.resizeHandlers.setHandler")},Ue={getVariant:function(e,t,n){return hj.tryCatch((function(){var r;if(!(Array.isArray(t)&&Array.isArray(n)&&t.length&&n.length&&t.length===n.length))throw new Error("Options and probability split must be arrays of equal length.");if(n.reduce((function(e,t){return e+t}))<.99)throw new Error("Probability splits should add up to at least 0.99.");null!==(r=window.hjSiteSettings.experimentation)&&void 0!==r&&r.variants||(window.hjSiteSettings.experimentation={variants:{}});var o=window.hjSiteSettings.experimentation.variants[e];if(!o){for(var i,a=Math.random(),s=0,c=0;c<=n.length-1;c++){var u=s+n[c];if(a<=u){i=t[c];break}s=u}window.hjSiteSettings.experimentation.variants[e]=o=i}return o}),"hj.experimentation.getVariant")()}};n(6402),hj.tryCatch((function(){if(void 0!==hj.scriptLoaded)return window.console=window.console||{warn:function(){}},console.warn("Hotjar Tracking Warning: Multiple Hotjar tracking codes were detected on this page. Tracking will not work as expected."),hj.verifyInstall?void(0,P.c)("Hotjar installation invalid.","It appears you have more than one Hotjar tracking code set up on this page. Hotjar cannot work properly if multiple Hotjar scripts are loaded concurrently. Please make sure you only install the one tracking code provided for this site.","bad"):void 0;window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window.hj.q=window.hj.q||[],hj.hostname=hj.host.split(":")[0],hj.port=443,hj.apiUrlBase="https://"+hj.host+"/api/v1",hj.apiUrlBaseV2="https://"+hj.host+"/api/v2",hj.isPreview=Boolean(_hjSettings.preview),hj.userDeviceType=null,hj.optOut=!1,hj.windowSize=null,hj.maxRecordingTagLength=250,hj.settings=hj.isPreview?{}:window.hjSiteSettings,hj.time=Oe,hj.uuid=D.v4,hj.locationListener=xe,hj.encodeAsUtf8=M.IU,hj.b64EncodeUnicode=M.GT,hj.b64DecodeUnicode=M.Di,hj.md5=I,hj.debug=i,hj.log=l,hj.url=U,hj.loader=o,hj.userAttributes=g.r,hj.targeting=d.q,hj.rendering=h.n,hj.ui=x,hj.dom=Pe,hj.html=Re,hj.features=f.R,hj.viewport=hj.tryCatch(q,"common")(),hj.treeMirror=be,hj.resizeListeners=Me,hj.experimentation=Ue}),"common")()},1416:function(e,t,n){"use strict";n.r(t),n.d(t,{getMidLevelDomain:function(){return u},getParameter:function(){return i},getUrlFromString:function(){return s},tryDecodeURIComponent:function(){return a}});var r=n(6366),o=n(6052);function i(e){var t,n,r=[];for(t=new RegExp("[^?&]?"+e.replace(/\[/,"\\[").replace(/]/,"\\]")+"=([^&]+)","g");n=t.exec(location.search);)r.push(a(n[1]));switch(r.length){case 0:return"";case 1:return r[0];default:return r}}function a(e){try{return decodeURIComponent(e)}catch(t){return e}}function s(e){return(0,o.N)(e,"http")||((0,o.N)(e,"/")||(e="/"+e),e=location.protocol+"//"+location.hostname+(""!=location.port?":"+location.port:"")+e),e}var c={};function u(e){if(!c[e]){var t,n=e.lastIndexOf(".");t=l(e,n),c[e]=t||e}return c[e]}function l(e,t){t=t?t-1:e.length;var n,r=e.lastIndexOf(".",t-1);return r>-1&&(function(e){try{var t={domain:e};d.set(h,e,t);var n=d.get(h);return n&&d.remove(h,t),n}catch(e){return!1}}(n=e.substring(r))||(n=l(e,r))),n}var h="_hjTLDTest",d=r.Z.withAttributes({sameSite:"None",secure:!0})},6326:function(e,t,n){"use strict";n.r(t);var r=n(5547);hj.tryCatch((function(){hj.adoptedStyleSheets=function(){var e={},t=[],n=null,o=null,i=!1;function a(e){t.forEach((function(t){t(e)}))}function s(e){return hj.selector().get(hj.hq(e))}return e.init=function(){i||(void 0!==window.ShadowRoot&&(n=Object.getOwnPropertyDescriptor(ShadowRoot.prototype,"adoptedStyleSheets"))&&Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",{set:function(){n.set.apply(this,arguments);var e=arguments[0],t=this.host,o={sheets:(0,r.oL)(e,t)};if(t instanceof Node&&document.contains(t))o.parentSelector=s(t),o.nodeId=hj.treeMirror.getNodeId(t),a(o);else{var i=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=i.host;o.parentSelector=s(e),o.nodeId=hj.treeMirror.getNodeId(e),a(o)}))}}}),void 0!==window.document.adoptedStyleSheets&&(o=Object.getOwnPropertyDescriptor(Document.prototype,"adoptedStyleSheets"))&&Object.defineProperty(Document.prototype,"adoptedStyleSheets",{set:function(){o.set.apply(this,arguments);var e=arguments[0],t={sheets:(0,r.oL)(e,this)};t.nodeId=hj.treeMirror.getNodeId(this),t.parentSelector=null,t.isOnDocument=!0,a(t)}}),i=!0)},e.register=function(e){t.push(e)},e.destroy=function(){n&&(Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",n),n=null,i=!1),o&&(Object.defineProperty(Document.prototype,"adoptedStyleSheets",o),o=null,i=!1),t.length=0},e}()}),"hj.adoptedStyleSheets")()},289:function(){hj.tryCatch((function(){var e,t;hj.cssBlobs=(t=[],(e={}).register=function(e){t.push(e)},e.handleBlobStyles=hj.tryCatch((function(e){var n=[];"link"===e.tagName.toLowerCase()&&"rel"in e.attributes&&"stylesheet"===e.attributes.rel.value&&"href"in e.attributes&&0===e.attributes.href.value.indexOf("blob:")&&setTimeout((function(){for(var r=hj.selector().get(hj.hq(e)),o=hj.treeMirror.getNodeId(e),i=0,a=e.sheet.cssRules.length;i<a;i++)n.push({parentSelector:r,rule:e.sheet.cssRules[i].cssText,nodeId:o,index:a+i});t.forEach((function(e){e(n)}))}),100)}),"hj.cssBlobs.apply"),e)}),"hj.cssBlobs")()},219:function(e,t,n){"use strict";n.r(t);var r=n(5547);hj.tryCatch((function(){hj.deletedRules=function(){var e,t={},n=!1,o=[],i=function(e,t,n,r){var o,i={};return i.isOnDocument=r,i.index=t,i.parentSelector=r?null:(o=e,hj.selector().get(hj.hq(o))),i.nodeId=hj.treeMirror.getNodeId(e),n&&(i.id=n),i};function a(e){o.forEach((function(t){t([e])}))}return t.init=function(){n||(e=CSSStyleSheet.prototype.deleteRule,CSSStyleSheet.prototype.deleteRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),o=this.ownerNode||this.ownerHostNode,s=t[0],c=!!o&&o.nodeType===Node.DOCUMENT_NODE;if(c||(0,r.m$)(o)){var u=this.sheetId||null;a(i(o,s,u,c))}else{var l=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=l.ownerNode||l.ownerHostNode;if((0,r.m$)(e)){var t=l.sheetId||null;a(i(e,s,t,c))}}))}return n},n=!0)},t.register=function(e){o.push(e)},t.destroy=function(){e&&(CSSStyleSheet.prototype.deleteRule=e,e=null,n=!1),o=[]},t}()}),"hj.deletedRules")()},3251:function(e,t,n){n(542),n(219),n(289),n(6326)},542:function(e,t,n){"use strict";n.r(t);var r=n(5547);hj.tryCatch((function(){hj.insertedRules=function(){var e,t={},n=[],o=!1,i=function(e,t,n,r,o){var i,a={};return a.isOnDocument=o,a.rule=t,a.index=n,a.parentSelector=o?null:(i=e,hj.selector().get(hj.hq(i))),a.nodeId=hj.treeMirror.getNodeId(e),r&&(a.id=r),a};function a(e){n.forEach((function(t){t([e])}))}return t.init=function(){o||(e=CSSStyleSheet.prototype.insertRule,CSSStyleSheet.prototype.insertRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),o=this.ownerNode||this.ownerHostNode,s=t[0],c=t[1],u=!!o&&o.nodeType===Node.DOCUMENT_NODE;if(u||(0,r.m$)(o)){var l=this.sheetId||null;a(i(o,s,c,l,u))}else{var h=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=h.ownerNode||h.ownerHostNode;if((0,r.m$)(e)){var t=h.sheetId||null;a(i(e,s,c,t,u))}}))}return n},o=!0)},t.register=function(e,r){n.push(e),r&&e(t.getCurrentInsertedRules())},t.getCurrentInsertedRules=hj.tryCatch((function(e){for(var t=Array.prototype.slice,n=t.call(document.styleSheets).filter((function(n){if(void 0!==e&&!0===e&&n.href&&0===n.href.indexOf("blob:"))return!0;var r="";if(n.href&&0!==n.href.indexOf("blob:"))return!1;try{n.cssRules&&n.cssRules.length}catch(e){return!1}if(0===n.cssRules.length)return!1;n.ownerNode&&void 0!==n.ownerNode.innerText?r=n.ownerNode.innerText:n.ownerNode&&void 0!==n.ownerNode.innerHTML&&(r=n.ownerNode.innerHTML);var o=r.match(/{/g);return(o?o.length:0)<function e(n){var r=0;return t.call(n).forEach((function(t){t.cssRules?(r++,r+=e(t.cssRules)):r++})),r}(n.cssRules)})),r=[],o=function(){var e=n[i],o=hj.selector().get(hj.hq(e.ownerNode)),a=t.call(e.cssRules),s=a.length;a.forEach((function(e,t){r.push({parentSelector:o,rule:e.cssText,index:t+s,nodeId:void 0})}))},i=0,a=n.length;i<a;i++)o();return r}),"hj.insertedRules.getCurrentInsertedRules"),t.destroy=function(){e&&(CSSStyleSheet.prototype.insertRule=e,e=null,o=!1),n=[]},t}()}),"hj.insertedRules")()},8553:function(e,t,n){"use strict";function r(e,t,n){t=t||hj.hq.noop,n=n||hj.hq.noop,hj.hq.ajax({url:e,success:hj.tryCatch(t,"Data"),error:hj.tryCatch(n,"Data")})}function o(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{contentType:"json"};n=n||hj.hq.noop,r=r||hj.hq.noop;var i=null!=o&&o.query?"".concat(e,"?").concat(o.query):e;return hj.hq.ajax({url:i,type:"POST",data:"json"===(null==o?void 0:o.contentType)?hj.hq.stringify(t):t,contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}function i(e,t,n,r){return n=n||hj.hq.noop,r=r||hj.hq.noop,hj.hq.ajax({url:e,type:"POST",data:hj.hq.stringify(t),contentType:"application/json; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}function a(e,t,n,r){n=n||hj.hq.noop,r=r||hj.hq.noop,hj.hq.ajax({url:e,type:"PUT",data:hj.hq.stringify(t),contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}n.r(t),n.d(t,{get:function(){return r},post:function(){return o},postAsJSON:function(){return i},putAsJSON:function(){return a}}),hj.ajax={get:r,post:o,putAsJSON:a,postAsJSON:i}},2129:function(e,t,n){"use strict";n.d(t,{K:function(){return r}});var r=(0,n(4557).M)({eventsRetrySuccess:!1,initMessageQueued:!1},"event-stream")},7434:function(e,t,n){"use strict";n.r(t),n.d(t,{eventStream:function(){return k}}),n(4931);var r=n(4788);const o=Object.freeze({MOUSE_CLICK:r.s.MOUSE_CLICK,MOUSE_MOVE:r.s.MOUSE_MOVE,SCROLL:r.s.SCROLL,VIEWPORT_RESIZE:r.s.VIEWPORT_RESIZE,KEY_PRESS:r.s.KEY_PRESS,SELECT_CHANGE:r.s.SELECT_CHANGE,INPUT_CHOICE_CHANGE:r.s.INPUT_CHOICE_CHANGE,CLIPBOARD:r.s.CLIPBOARD});var i=n(7698);function a(e){return"".concat(parseFloat(e/1048576).toFixed(2))}var s=n(1131),c=n(3883),u=n(724);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}var h=hj.tryCatch((function(e){var t,n,h,d,f,g={},p="",v=void 0,y=null,m=!1,j=i.f_.now(),b=!1;g.events=[],g.storePageContent=function(t,n){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5"),e.storePageContent(t,(function(e){var t=e.content_uuid;t?(m=!0,u.y.set("pageContent",{uuid:t,md5:n})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var n=a(t.length);g.write("content_size_too_large",{size:n,source:"page_visit",timestamp:i.f_.now()},!1)}}))},g.reportPageContent=function(e){f=u.y.on("pageContent",(function(t){var n,o=t.uuid,i=t.md5,a=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(o,"sendReportContent: uuid"),hj.log.warnIfEmpty(i,"sendReportContent: md5"),hj.log.warnIfEmpty(a,"sendReportContent: webResourceInfos");var s={page_content_url_md5:i,page_content_uuid:o,web_resource_infos:a,content_submitted:m};g.writeNewFrame((n={},n[r.s.REPORT_CONTENT]=s,n),e).flush(),m=!1}))},g.clearPageContent=function(){f(),u.y.set("pageContent",void 0)},g.setCurrentPageVisitKey=hj.tryCatch((function(e){p!==e&&(p=e,t()),g.flush()}),"data"),g.writeNewFrame=hj.tryCatch((function(e,n){return t(n),g.write(e),t(),g})),g.write=hj.tryCatch((function(e,t,n){var r;if(hj.isPreview)return g;if("object"===l(e))return hj.hq.each(e,(function(e,t){g.write(e,t,!0)})),g;var o=e,a=S(o);if(!a&&b)return g;if(!a&&C())return E(),g;a&&(y=i.f_.now()),a&&b&&I();var s=null!==(r=g.events.pop())&&void 0!==r?r:w(p);return n?s[o]=t:(s[o]=s[o]||[],s[o].push(t)),g.events.push(s),g}),"data"),g.flush=hj.tryCatch((function(){var n=c.Q.get("user.id"),r=c.Q.get("sessionAccepted");if(v&&clearInterval(v),!r||!n)return t();var o=_(),i=o.length;if(i>0)for(var a=0;a<i;a++){var s=o[a];e.send(s,n)}v=setInterval(g.flush,1e3)}),"data");var w=function(e){return{pageVisitKey:e}},S=function(e){return hj.hq.inArray(e,hj.hq.values(o))};t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};g.events.push(t)}),"data");var _=hj.tryCatch((function(){for(var e,r=g.events.length,o=[],i=[],a=[],s=0;s<r;s++)if(e=g.events[s],Object.keys(e).length>1){var c=[];e.autotag_recording&&(c=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),c.length>0?i.push(e):(e.clipboard&&o.push(e.clipboard),a.push(n(e)))}var u=d(i,o);return g.events=u.leftover,t(),[].concat(a,u.sendable)}),"data");d=hj.tryCatch((function(e,t){var r=[],o=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?r.push(t):o.push(n(t))})),{leftover:r,sendable:o}}),"data.filterRageClicks"),n=hj.tryCatch((function(e){return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=hj.viewport.getId(),"object"===l(e.mutation)&&(e.mutation=h(e.mutation)),e}),"data"),h=hj.tryCatch((function(e){var t,n="";if("object"===l(e))return hj.hq.each(e,(function(r,o){"object"===l(o.c)&&(hj.hq.each(o.c,(function(o,i){"object"===l(i.attributes)&&"string"==typeof i.attributes.style&&(i.attributes.style===t&&i.id===n&&(e[r].c[o]=null),t=i.attributes.style,n=i.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data");var C=function(){var e,t=null!==(e=y)&&void 0!==e?e:j;return i.f_.now()-t>18e5},E=function(){hj.store.recording.set("active",!1),hj.store.session.set("session",null),s.t.items.HJ_SESSION.clear(),b=!0},I=function(){b=!1,hj._init.reinit(window.location.href,!0)};return g})),d=n(5148),f=n(5547),g=n(7990),p=n(8422),v=n(2129),y={maxRetries:5,delay:3e3,firstAttemptDelay:0,baseExponent:2};function m(e){var t=this;if(!e)throw new Error("HotjarWebSocket requires a flush callback");this._connected=!1,this._connecting=!1,this._isReconnecting=!1,this._closedPermanently=!1,this.sessionTimedOutDueToInactivity=!1,this._finished=!1,this._pingInterval=3e4,this._pingIntervalId=void 0,this._sessionInactiveInterval=18e5,this._lastUserActivityTime=(new i.f_).getTime(),this._unloadTimeoutStarted=!1,this._ws=null,this._wsUrl="",this._flush=e,this._msgPrefix="{}\n",this._clearPings=function(){clearInterval(t._pingIntervalId)},this._closeConnection=function(){t._connected=!1,t._connecting=!1,t._isReconnecting=!1,t._closedPermanently=!0}}m.prototype.connect=hj.tryCatch((function(){if(this._sessionIsDisabled())return!1;if(!this._connected&&!this._connecting&&!this._isReconnecting){this._connecting=!0;var e=_hjSettings.wsMockUrl||"wss://".concat((0,f.LL)(),"/api/v2/client/ws");this._wsUrl="".concat(e,"?v=").concat(p.fb,"&site_id=").concat(hj.settings.site_id);try{this._createAndConnect()}catch(e){hj.log.debug("The Web Socket connection was refused. \n ".concat(e))}}return!0}),"data.HotjarWebSocket.connect"),m.prototype.updateLastUserActivityTime=hj.tryCatch((function(){this._lastUserActivityTime=(new i.f_).getTime()}),"data.HotjarWebSocket.updateLastUserActivityTime"),m.prototype.disconnect=hj.tryCatch((function(){this._connected&&(hj.log.debug("Disconnecting Web Socket.","websocket"),this._flush(),this.close())}),"data.HotjarWebSocket.disconnect"),m.prototype.isConnected=hj.tryCatch((function(){return!!this._connected&&((new i.f_).getTime()-this._lastUserActivityTime<=this._sessionInactiveInterval||(hj.store.recording.set("active",!1),hj.store.session.set("session",null),s.t.items.HJ_SESSION.clear(),this.close(),this.sessionTimedOutDueToInactivity=!0,!1))}),"data.HotjarWebSocket.isConnected"),m.prototype.send=hj.tryCatch((function(e){this._sessionIsDisabled()||(hj.log.debug("Sending data to Web Socket","websocket",e),this._ws.send(e))}),"data.HotjarWebSocket.send"),m.prototype.close=hj.tryCatch((function(){hj.log.debug("Closing Web Socket.","websocket"),this._connected=!1,this._connecting=!1,this._finished=!0,this._ws.close()}),"data.HotjarWebSocket.close"),m.prototype.getBufferedAmount=hj.tryCatch((function(){return this._ws.bufferedAmount}),"data.HotjarWebSocket.getBufferedAmount"),m.prototype._sessionIsDisabled=hj.tryCatch((function(){return this._closedPermanently||this.sessionTimedOutDueToInactivity||!0===hj.store.session.get("session.sessionRejected")}),"data.HotjarWebSocket._sessionIsDisabled"),m.prototype._ping=hj.tryCatch((function(){this._connected&&(hj.log.debug("Pinging Web Socket","websocket"),this._ws.send(this._msgPrefix+"ping"))}),"data.HotjarWebSocket._ping"),m.prototype._createAndConnect=hj.tryCatch((function(){var e=this;if(e._finished)hj.log.debug("Unload event triggered, don't reconnect"),!1===e._unloadTimeoutStarted&&(e._unloadTimeoutStarted=!0,setTimeout((function(){e._finished=!1,e._unloadTimeoutStarted=!1}),1e3));else{hj.log.debug("Connecting to Web Socket ["+e._wsUrl+"]","websocket");var t=function(n,r){var o,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{o=new WebSocket(e._wsUrl)}catch(e){return void hj.log.debug("Web socket connection ".concat(i?"retry":""," error. \n ").concat(e))}return o.onopen=function(t){if(n(o),e._onOpen.call(e,t),i){v.K.set("eventsRetrySuccess",!0);var a=i.reason,s=i.extraTags;hj.metrics.timeEnd("time-to-reconnect",{tag:{reason:a},start:r,extraTags:s})}},o.onmessage=function(t){e._onMessage.call(e,t)},o.onclose=function(r){e._clearPings(),e._connected=!1,hj.log.debug("Web Socket closed.","websocket");var o={message:r.reason,code:r.code,wasClean:r.wasClean};if(hj.metrics.count("session-interruption",{tag:{reason:"websocket-close"},extraTags:o}),r.wasClean&&1012!=r.code)4e3==r.code&&console.warn("Hotjar Tracking Code is out of date and this session will not be captured anymore. Please reload the page to update your Tracking Code."),e._closeConnection();else if(hj.log.warn("Websocket close was unclean: "+r.code),!e._isReconnecting){var i={reason:r.wasClean?"service-restart":"unclean-close",extraTags:o};e._isReconnecting=!0,e._connecting=!0,function(n,r){var o=hj.metrics.time();(0,g.TL)((function(){return t(n,o,r)}),(function(){return e._closeConnection()}),y)}(n,i)}},o.onerror=function(t){hj.log.debug("onError was called.","websocket",t),v.K.set("eventsRetrySuccess",!1),e._connected&&(e._clearPings(),e._connected=!1)},o};e._ws=t((function(t){e._ws=t})),window.addEventListener("beforeunload",e.disconnect,!1)}}),"data.HotjarWebSocket._createAndConnect"),m.prototype._onOpen=hj.tryCatch((function(){hj.log.debug("Web Socket opened.","websocket"),this._pingIntervalId=setInterval(this._ping.bind(this),this._pingInterval),this._connected=!0,this._connecting=!1,this._isReconnecting=!1,this._flush()}),"data.HotjarWebSocket._onOpen"),m.prototype._onMessage=hj.tryCatch((function(e){var t;try{t=JSON.parse(e.data)}catch(t){throw hj.log.warn("Could not parse websocket message: "+e.data),t}switch(t.type){case"SESSION_TOO_LARGE":hj.log.warn("Session became too large. Will stop sending websocket data."),(0,d.IU)({sessionRejected:!0}),this.disconnect();break;case"SESSION_RATE_LIMITED":hj.log.warn("Session was rate limited. Will stop sending websocket data."),hj.metrics.count("session-rejection",{tag:{reason:"rate-limited"}}),(0,d.IU)({sessionRejected:!0}),this.disconnect();break;case"SESSION_ACCEPTED":hj.log.debug("Session accepted."),hj.store.session.set("sessionAccepted",!0);break;default:hj.log.warn("Received unknown websocket message: "+e.data)}}),"data.HotjarWebSocket._onMessage");var j=n(5153);function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}var w=hj.tryCatch((function(e){var t,n,l,h,f,g,p="",y=void 0,w=!1,S=Object.create({events:[],storePageContent:function(t,n,r){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5");var o=t.length,s=function(r){var s=r.success,c=r.compressed,l=r.time;s&&l&&hj.metrics.timeEnd("pc-compression-time",{tag:{task:"process-chunks"},total:l}),e.storePageContent(s?c:t,(function(e,r){if(207===(null==r?void 0:r.status)&&"gzip error"===e[1])return hj.metrics.distr("compression-malformed",{task:"gzip",value:o,extraTags:{compressedLength:c.length}}),S.storePageContent(t,n,!0);var i=e.content_uuid;i?(w=!0,u.y.set("pageContent",{uuid:i,md5:n}),s&&o&&hj.metrics.distr("compression-ratio",{task:"page-content",value:parseFloat(Number(o/c.length).toFixed(1))})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var t=a(o);S.write("content_size_too_large",{size:t,source:"page_visit",timestamp:i.f_.now()},!1)}}),{query:"site_id=".concat(hj.settings.site_id)+(s?"&gzip=1":"")})};r?s({success:!1}):function(e,t){var n,r,o,i,a=function(e){try{e&&hj.metrics.count("session-exception",{tag:{module:"compression"},extraTags:{message:e.message,name:e.name}}),t({success:!1})}catch(e){}};try{if(!function(){try{return"CompressionStream"in window}catch(e){return!1}}())return a();(n=new ReadableStream({start:function(t){t.enqueue(e),t.close()}}).pipeThrough(new TextEncoderStream).pipeThrough(new CompressionStream("gzip")),r=n.getReader(),o=[],i=hj.metrics.timeWatcher(),r.read().then((function e(t){var n=t.done,a=t.value;if(i.start(),n){var s=o.reduce((function(e,t){return e+t.length}),0),c=new Uint8Array(s),u=0;return o.forEach((function(e){c.set(e,u),u+=e.length})),{time:i.end(),compressed:c}}return o.push(a),i.incr(),r.read().then(e)}))).then((function(e){var n=e.time,r=e.compressed;t({success:!0,compressed:r,time:n})})).catch(a)}catch(e){a(e)}}(t,s)},reportPageContent:function(e){w=!1,g=u.y.on("pageContent",(function(t){var n,o=t.uuid,i=t.md5,a=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(o,"sendReportContent: uuid"),hj.log.warnIfEmpty(i,"sendReportContent: md5"),hj.log.warnIfEmpty(a,"sendReportContent: webResourceInfos");var s={page_content_url_md5:i,page_content_uuid:o,web_resource_infos:a,content_submitted:w};S.writeNewFrame((n={},n[r.s.REPORT_CONTENT]=s,n),e).flush()}))},clearPageContent:function(){g(),u.y.set("pageContent",void 0)},setCurrentPageVisitKey:hj.tryCatch((function(e){p!==e&&(p=e,t()),S.flush()}),"data"),writeNewFrame:hj.tryCatch((function(e,n){return t(n),S.write(e),t(),S})),write:hj.tryCatch((function(e,t,n,r){var o;if(hj.isPreview)return S;if("object"===b(e))return hj.hq.each(e,(function(e,t){S.write(e,t,!0,r)})),S;var i=e;if(!r&&!S._ws.connect()){if(!S._ws.sessionTimedOutDueToInactivity||!C(i))return S;S._ws=new m(S.flush),hj._init.reinit(window.location.href,!0),S._ws.connect()}var a=null!==(o=S.events.pop())&&void 0!==o?o:_(p);return n?a[i]=t:(a[i]=a[i]||[],a[i].push(t)),S.events.push(a),S}),"data"),queueEndSignal:hj.tryCatch((function(){hj.log.debug("Should send end signal to Web Socket with the next flush","websocket"),S.shouldSendEndSignal=!0}),"data"),sendEndSignal:hj.tryCatch((function(){hj.log.debug("Sending end signal to Web Socket","websocket"),S._ws.send(S._ws._msgPrefix+"end_signal"),S._ws.close(),s.t.items.HJ_SESSION.clear(),S.shouldSendEndSignal=!1}),"data"),sendInitMessage:hj.tryCatch((function(){hj.log.debug("Sending init message to Web Socket","websocket"),v.K.set("initMessageQueued",!1),S._ws.send(S._ws._msgPrefix+"init")}),"data"),flush:hj.tryCatch((function(){var e,r,o=(0,j.bN)(),s=c.Q.get("sessionAccepted");if(y&&clearInterval(y),s||(c.Q.on("sessionAccepted",S.flush,!0),t()),S._ws.isConnected()&&o){var u,l={sid:(0,d.MQ)(),uuid:o,viewportid:null===(u=hj.viewport)||void 0===u?void 0:u.getId(),site_id:hjSiteSettings.site_id,timestamp:i.f_.now(),beta:(0,d.qi)()};if(S._ws._msgPrefix=hj.hq.stringify(l)+"\n",v.K.get("initMessageQueued")&&S.sendInitMessage(),S.shouldSendEndSignal)return S.sendEndSignal();if(s){if((r=(e=n()).length)>0){E(e)&&S._ws.updateLastUserActivityTime();for(var h=0;h<r;h++){var f=e[h],g=hj.hq.stringify(f);if(g.length<10485760)S._ws.send(S._ws._msgPrefix+g);else{var p=hj.hq.stringify({content_size_too_large:{size:a(g.length),source:"flush",timestamp:i.f_.now()},page_visit_key:f.page_visit_key,viewport_id:f.viewport_id});S._ws.send(S._ws._msgPrefix+p)}}}y=setInterval(S.flush,1e3)}}}),"data"),close:hj.tryCatch((function(){S._ws.disconnect()}),"data"),_ws:void 0});S._ws=new m(S.flush);var _=function(e){return{pageVisitKey:e}},C=function(e){return hj.hq.inArray(e,hj.hq.values(o))},E=function(e){return e.some((function(e){return Object.keys(e).some(C)}))};return t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};S.events.push(t)}),"data"),n=hj.tryCatch((function(){for(var e,n=S.events.length,r=[],o=[],i=[],a=0;a<n;a++)if(e=S.events[a],Object.keys(e).length>1){var s=[];e.autotag_recording&&(s=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),s.length>0?o.push(e):(e.clipboard&&r.push(e.clipboard),i.push(l(e)))}var c=f(o,r);return S.events=c.leftover,t(),[].concat(i,c.sendable)}),"data"),f=hj.tryCatch((function(e,t){var n=[],r=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?n.push(t):r.push(l(t))})),{leftover:n,sendable:r}}),"data.filterRageClicks"),l=hj.tryCatch((function(e){var t;return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=null===(t=hj.viewport)||void 0===t?void 0:t.getId(),"object"===b(e.mutation)&&(e.mutation=h(e.mutation)),e}),"data"),h=hj.tryCatch((function(e){var t,n="";if("object"===b(e))return hj.hq.each(e,(function(r,o){"object"===b(o.c)&&(hj.hq.each(o.c,(function(o,i){i&&null!==i.attributes&&"object"===b(i.attributes)&&"string"==typeof i.attributes.style&&(i.attributes.style===t&&i.id===n&&(e[r].c[o]=null),t=i.attributes.style,n=i.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data"),S})),S=n(6621);function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,E(r.key),r)}}function E(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==_(t)?t:String(t)}var I=(0,S.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),T=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.pageContentResource=I}var t,n;return t=e,(n=[{key:"storePageContent",value:function(e,t,n,r){return this.pageContentResource.post(e,t,n,{contentType:"text",query:null==r?void 0:r.query})}},{key:"send",value:function(){}}])&&C(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),N=(n(6402),n(8553));function O(e){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O(e)}function R(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,A(r.key),r)}}function A(e){var t=function(e,t){if("object"!=O(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=O(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==O(t)?t:String(t)}var k,x={maxRetries:5,delay:4e3},P=(0,S.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),D=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.viewport=t,this.siteId=n,this.maxRetries=r}var t,n;return t=e,(n=[{key:"storePageContent",value:function(e,t,n){return P.post(e,t,n,{contentType:"text"})}},{key:"send",value:function(e,t){var n=(_hjSettings.eventsHost||(_hjSettings.eventsHost="https://events.hotjar.io"),_hjSettings.eventsHost+"/"),r={uuid:t,viewportid:this.viewport.getId(),site_id:this.siteId},o=hj.hq.stringify(r)+"\n"+hj.hq.stringify(e),i=!1;!function e(){return(0,N.post)(n,o,(function(e){hj.log.debug("Events sent successfully to host","events",e),i=!1,v.K.set("eventsRetrySuccess",!0)}),(function(){hj.log.debug("Events failed to send to host, retrying","events"),v.K.set("eventsRetrySuccess",!1),i||(i=!0,(0,g.TL)(e,null,x))}),{contentType:"text"})}()}}])&&R(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();k=hj.features.hasFeature("ingestion.http")?h(new D(hj.viewport,hj.settings.site_id)):w(new T),hj.eventStream=k},3265:function(e,t,n){"use strict";n.r(t);var r={},o={},i={};function a(e,t,n){var r;e[t]||(e[t]=[]),null===(r=e[t])||void 0===r||r.push(n)}r={listen:hj.tryCatch((function(e,t){a(o,e,t),function(e){var t;i[e]&&(null===(t=i[e])||void 0===t||t.forEach((function(t){r.signal(e,t)})),delete i[e])}(e)}),"hj.event.listen"),removeListener:hj.tryCatch((function(e,t){var n=o[e];if(n){var r=n.indexOf(t);-1!==r&&n.splice(r,1)}}),"hj.event.removeListener"),signal:hj.tryCatch((function(e,t,n){var r;o[e]?null===(r=o[e])||void 0===r||r.forEach((function(e){e(t)})):n?a(i,e,t):i[e]=[t]}),"hj.event.signal"),clearAllListeners:hj.tryCatch((function(){o={},i={}}),"hj.event.clearAllListeners")},hj.event=r},5004:function(e,t,n){"use strict";n.r(t);var r=n(9663);n(1088),n(5727),(0,r.E)(),n(6621),n(8553),n(7434),n(3265)},6621:function(e,t,n){"use strict";n.r(t),n.d(t,{Resource:function(){return a}});var r,o=n(8553),i=new RegExp("<.+:(.+)>");function a(e,t,n){var r={},o=function(){for(var t=e.split("/"),n=[],r=0;r<t.length;r+=1){var o=i.exec(t[r]);o?n.push({label:o[1],isDynamic:!0}):n.push({label:t[r],isDynamic:!1})}return n}();return function(){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var i=t[e][0],a=t[e][1];r[e]=s({urlPathTokens:o,method:i,requiredArgs:a,getHost:n})}}(),r}function s(e){var t=e.urlPathTokens,n=e.method,r=e.requiredArgs,i=e.getHost;function a(e){if(!r)return e;for(var t={},n=0;n<r.length;n+=1)if(t[r[n]]=e[r[n]],void 0===t[r[n]])throw new Error('Argument "'+r[n]+'" is missing.');return t}return function(e,r,s,c){var u,l="https://".concat(null!==(u=null==i?void 0:i())&&void 0!==u?u:hj.host).concat(function(e,t){for(var n=[],r=0;r<e.length;r+=1){var o=e[r];if(o.isDynamic){var i=t[o.label];if(void 0===i)throw new Error('Argument "'+o.label+'" is missing.');n.push(i)}else n.push(o.label)}return n.join("/")}(t,e));switch(n){case"GET":o.get(l,r,s);break;case"POST":o.post(l,a(e),r,s,c);break;case"PUT":o.putAsJSON(l,a(e),r,s);break;default:throw new Error('HTTP method "'+n+'" is not supported.')}}}hj.resource=((r={}).TokenRestrictedStorage=a("/api/v1/trs/<string:token>",{put:["PUT",["token","data"]]}),r)},1088:function(e,t,n){"use strict";n.r(t);var r=n(724),o=n(3883);hj.store={session:o.Q,recording:r.y}},4557:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}n.d(t,{M:function(){return o}});var o=function(e,t){var n=Object.assign(Object.create({}),e),o=Object.create({}),i=function(e){return!(null==e||"object"===r(e)&&0===Object.keys(e).length)},a={get:function(e){try{if(Object.prototype.hasOwnProperty.call(n,e))return n[e];var o=e.split(".");if(o.length>1){var i=o[1],a=n[o[0]];return i&&a&&"object"===r(a)?a[i]:void 0}return}catch(e){hj.exceptions.log(e,"hj.store.".concat(t))}},set:function(e,s){try{if(Object.prototype.hasOwnProperty.call(n,e))n[e]=s,s&&"object"===r(s)&&Object.keys(s).forEach((function(t){var n="".concat(String(e),".").concat(t),r=o[n];if(r&&s){var c=s[t];i(c)&&r.forEach((function(e){e(c,a.off(n,e))}))}}));else{var c=e.split(".");if(c.length>1){var u=c[0],l=n[u],h=c[1];h&&l&&"object"===r(l)&&(l[h]=s);var d,f=a.get(u);i(f)&&(null===(d=o[u])||void 0===d||d.forEach((function(e){e(f,a.off(u,e))})))}}var g;i(s)&&(null===(g=o[e])||void 0===g||g.forEach((function(t){t(s,a.off(e,t))})))}catch(e){hj.exceptions.log(e,"hj.store.".concat(t))}},on:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(null===(n=o[e])||void 0===n?void 0:n.push(t))||(o[e]=[t]);var s=a.get(e);return i(s)&&!r&&t(s,a.off(e,t)),a.off(e,t)},off:function(e,t){return function(){var n;o[e]=null===(n=o[e])||void 0===n?void 0:n.filter((function(e){return e!==t}))}},remove:function(e){delete n[e],delete o[e]},reset:function(e){n=Object.assign(Object.create({}),e),o=Object.create({})}};return Object.create(a)}},5727:function(e,t,n){"use strict";n.r(t);var r=n(724),o=n(7352),i=n(5148),a=n(3883);hj.visitData=hj.tryCatch((function(){var e={};return e.getPageVisitInfo=hj.tryCatch((function(e,t){var n=r.y.get("pageInfo"),o=navigator.userAgent,i=navigator.language,a={script_revision:window.hjBootstrap.revision,user_agent:o||"",accept_language:i||"",page_url:(null==n?void 0:n.url)||"",url_hash:(null==n?void 0:n.urlMD5)||"",window_width:e.width,window_height:e.height,site_id:t},s=document.referrer;return""!==s&&(a.referrer=s),a}),"data"),e.track=hj.tryCatch((function(e){var t=(0,o.v4)();hj.eventStream.setCurrentPageVisitKey(t),e=e||document.location.href,hj.log.debug("Tracking: ".concat(e),"visitData"),r.y.set("pageVisitKey",t),r.y.set("pageInfo",{url:e,urlMD5:hj.md5(hj.b64EncodeUnicode(e))})}),"data"),e.trackView=hj.tryCatch((function(){if(void 0===a.Q.get("session.absoluteSessionInProgress")){hj.log.debug("viewcounter: Determining if we should track this session...","visitData");var e=Math.random(),t=hj.viewCounterEndpoint&&hj.settings.site_id&&e<=hj.viewCounterHitPercentage,n="s=".concat(hj.viewCounterHitPercentage,"&r=").concat(e),r="".concat(hj.viewCounterEndpoint,"/").concat(hj.settings.site_id,"?").concat(n);t?((0,i.IU)({absoluteSessionInProgress:!0}),hj.ajax.get(r),hj.log.debug("viewcounter: This session was tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage})):((0,i.IU)({absoluteSessionInProgress:!1}),hj.log.debug("viewcounter: This session will NOT be tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage}))}else hj.log.debug("viewcounter: Session already checked. Skipping shouldTrackSession check.","visitData")}),"data"),e}),"data")(),hj.pageVisit=hj.visitData},1469:function(e,t,n){"use strict";n.d(t,{Di:function(){return s},GT:function(){return a},IU:function(){return i}});var r=n(8714),o=n(7698);function i(e){return(0,r.xj)((0,o.Lz)(e))}function a(e){return(0,o.UP)(i(e))}function s(e){return function(e){return(0,o.eC)((0,r.I)(e))}((0,o.aF)(e))}},260:function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,{fF:function(){return a}});var i="🐛",a=function(e){for(var t,n=arguments.length,a=new Array(n>1?n-1:0),s=1;s<n;s++)a[s-1]=arguments[s];return(t=console).debug.apply(t,[i,e].concat(function(e,t,n){if(t&&!Array.isArray(t)&&"number"==typeof t.length){var r=t.length;return o(t,r)}return e(t,n)}(r,a||[])))}},7698:function(e,t,n){"use strict";n.d(t,{Lz:function(){return i},UP:function(){return s},aF:function(){return c},eC:function(){return a},f_:function(){return o}});var r=n(6303),o=(0,r.N)(Date,"Date"),i=(0,r.N)(encodeURIComponent,"encodeURIComponent"),a=(0,r.N)(decodeURIComponent,"decodeURLComponent"),s=(0,r.N)(btoa,"btoa"),c=(0,r.N)(atob,"atob")},6303:function(e,t,n){"use strict";n.d(t,{N:function(){return s}});var r,o=n(260),i=n(8973),a=function(e){return(0,o.fF)("[safeNative] ".concat(e))},s=function(e,t){try{if(!r){var n=function(){if(document.body){var e=document.createElement("iframe");return e.id="_hjSafeContext_".concat(function(){return arguments.length>0&&void 0!==arguments[0]||(0,i.U)(45887),Math.floor(1e8*Math.random())}()),e.title="_hjSafeContext",e.tabIndex=-1,e.setAttribute("aria-hidden","true"),e.src="about:blank",e.style.setProperty("display","none","important"),e.style.setProperty("width","1px","important"),e.style.setProperty("height","1px","important"),e.style.setProperty("opacity","0","important"),e.style.setProperty("pointer-events","none","important"),document.body.appendChild(e),e}}();if(!n)return a("Unable to access an IFrame context, using default property."),e;r=n}var o=("function"==typeof e?e.name:t)||t;return o?r.contentWindow?r.contentWindow[o]||(a("Unable to access property with name [".concat(o,"] from an IFrame context")),e):(a("Unable to access contentWindow property"),e):(a("Unable to name property or missing fallbackName"),e)}catch(t){return a("An unexpected error occurred".concat(t instanceof Error?": ".concat(t.message):"",". Using default constructor")),e}}},1736:function(e,t,n){"use strict";n.d(t,{Ib:function(){return i},Wr:function(){return c}});var r=n(7698),o=n(8422),i=4*o.R0*1e3,a=o.R0/2*1e3,s=function(e,t){return hj.metrics.count("session-exception",{tag:{module:"session-expiry"},extraTags:{errorMessage:hj.metrics.getErrorMessage(e),errorId:t}})},c=function(e){try{var t;if(null===(t=hj.store)||void 0===t||!t.session)return!0;var n=hj.store.session.get("session.created");if("number"==typeof n&&r.f_.now()+a>n+i){var o=i-(r.f_.now()-n);o<0&&(o=0),setTimeout((function(){try{e(),hj.store.session.set("session",null),hj.eventStream.close(),hj.metrics.count("session-interruption",{tag:{reason:"too-long"}})}catch(e){s(e,"clear-cookie")}}),o)}return"number"==typeof n&&r.f_.now()>=n+i}catch(e){return s(e,"check-cookie"),!0}}},3883:function(e,t,n){"use strict";n.d(t,{Q:function(){return r}});var r=(0,n(4557).M)({sessionAccepted:!1,session:null,user:{}},"session")},5148:function(e,t,n){"use strict";n.d(t,{MQ:function(){return v},qi:function(){return y},oc:function(){return j},IU:function(){return m}});var r=Math.random(),o=function(){return function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";switch(hj.url.getParameter("hjIncludeInSessionSample")){case"0":return e=!1,hj.log.debug("You have set includedInSessionSample to false.","sampling"),e;case"1":return e=!0,hj.log.debug("You have set includedInSessionSample to true.","sampling"),e}return void 0!==(e=hj.store.session.get("session.inSample"))?(hj.log.debug("[".concat(t,"] Included in session/CC sample (already set from session)? ").concat(e),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:e,mathRandomResult:r}),e):0===hj.settings.rec_value?(hj.log.debug("[".concat(t,"] rec_value is at 0, not adding to sample."),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:e}),e=!1):hj.settings.continuous_capture_enabled?(e=1===hj.settings.rec_value||void 0!==hj.settings.rec_value&&null!==hj.settings.rec_value&&(r||1)<=hj.settings.rec_value,hj.log.debug("[".concat(t,"] Included in session/CC sample? ").concat(e),"sampling",{rec_value:hj.settings.rec_value,mathRandomResult:r}),e):(hj.log.debug("[".concat(t,"] Included in session/CC sample (setting check)? ").concat(e),"sampling",{"hj.settings.continuous_capture_enabled":hj.settings.continuous_capture_enabled}),e=!1)}(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown")},i=n(1131),a=n(7698),s=n(1736),c=n(3883),u=n(1949),l=n(7990);function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}var d=(0,u.q)({storageAccessor:i.t.items.HJ_SESSION,serializer:l.qC,deserializer:l.vB}),f=function(e){var t;c.Q.set("session",e),void 0!==e.absoluteSessionInProgress&&(t=e.absoluteSessionInProgress?1:0),d.set({id:e.id,c:e.created,s:e.inSample?1:0,r:e.recordingEnabled?1:0,sb:e.sessionizerBetaEnabled?1:0,sr:e.sessionRejected?1:0,se:e.sessionResumed?1:0,fs:e.firstSeen?1:0,sp:t})},g=function(){var e=d.get();if(e){if(e.c){var t,n=e.id,r=e.c,o=Boolean(e.s),i=Boolean(e.r),a=Boolean(e.sb),s=Boolean(e.sr),c=Boolean(e.se),u=Boolean(e.fs);return 1!==e.sp&&0!==e.sp||(t=Boolean(e.sp)),{id:n,created:r,inSample:o,sessionizerBetaEnabled:a,recordingEnabled:i,sessionRejected:s,sessionResumed:c,firstSeen:u,absoluteSessionInProgress:t}}return null}return e},p=function(){var e=c.Q.get("session");return null!=e&&e.id?e:g()},v=function(){var e;return null===(e=p())||void 0===e?void 0:e.id},y=function(){var e;return(null===(e=p())||void 0===e?void 0:e.sessionizerBetaEnabled)?1:0},m=function(e){var t=p();if(t){var n=h(h({},t),e);f(n)}},j=hj.tryCatch((function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=g();r||c.Q.set("session",null);var i,u,h,d,p,v,y,j,b,w,S,_,C,E=c.Q.get("session");if(null!==(e=E)&&void 0!==e&&e.created||(E=r),null!==(t=E)&&void 0!==t&&t.created&&E.created+s.Ib<a.f_.now()&&(c.Q.set("session",null),E=null),null===E){var I=(i={inSample:o("registerSession"),sessionResumed:n,sessionizerBetaEnabled:hj.features.hasFeature("sessionizer_beta_enabled")},w=null!==(u=null==i?void 0:i.id)&&void 0!==u?u:(0,l.DO)(),S=null!==(h=null==i?void 0:i.created)&&void 0!==h?h:a.f_.now(),_=null!==(d=null==i?void 0:i.inSample)&&void 0!==d&&d,C=null!==(p=(null==i?void 0:i.recordingEnabled)&&_)&&void 0!==p&&p,{id:w,created:S,inSample:_,sessionizerBetaEnabled:null!==(v=null==i?void 0:i.sessionizerBetaEnabled)&&void 0!==v&&v,recordingEnabled:C,sessionRejected:null!==(y=null==i?void 0:i.sessionRejected)&&void 0!==y&&y,sessionResumed:null!==(j=null==i?void 0:i.sessionResumed)&&void 0!==j&&j,firstSeen:null!==(b=null==i?void 0:i.firstSeen)&&void 0!==b&&b,absoluteSessionInProgress:null==i?void 0:i.absoluteSessionInProgress});f(I)}else{var T;if(c.Q.set("session",E||{}),null!==(T=E)&&void 0!==T&&T.created){var N=o("registerSession");E.inSample!==N&&m({inSample:N})}}}),"session.registerSession")},5153:function(e,t,n){"use strict";n.d(t,{Iv:function(){return f},bN:function(){return d},pZ:function(){return h}});var r=n(1131),o=n(7698),i=n(3883),a=n(1949),s=n(7990);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}var u=(0,a.q)({storageAccessor:r.t.items.HJ_SESSION_USER,serializer:s.qC,deserializer:s.vB}),l=function(){return u.get()},h=function(){var e=u.getKey();if(e&&!(e.indexOf("undefined")>-1)){var t,n,r,i,a=l(),h={id:null!==(n=null==(t=c(c({},a),{},{existing:!!a}))?void 0:t.id)&&void 0!==n?n:(0,s.Nm)((0,s.DO)()),created:null!==(r=null==t?void 0:t.created)&&void 0!==r?r:o.f_.now(),existing:null!==(i=null==t?void 0:t.existing)&&void 0!==i&&i};return function(e){u.set(e)}(h),h}},d=function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=i.Q.get("user.id")||(null===(e=l())||void 0===e?void 0:e.id);if(n)return n;if(t){var r=h();return null==r?void 0:r.id}},f=function(){if((0,s.ij)()){var e,t,n,r=i.Q.get("user")||l();null!==(e=r)&&void 0!==e&&e.id||(r=h()),null!==(t=r)&&void 0!==t&&t.id?i.Q.set("user",r):hj.log.warnIfEmpty(null===(n=r)||void 0===n?void 0:n.id,"user agent check: userId")}}},1949:function(e,t,n){"use strict";n.d(t,{q:function(){return o}});var r=n(1469),o=function(e){var t=e.storageAccessor,n=e.serializer,o=e.deserializer;return{get:function(){var e=t.get();if(!e)return null===e?null:void 0;try{return o((0,r.Di)(e))}catch(e){hj.metrics.count("session-exception",{tag:{module:"deserialize-cookie"},extraTags:{errorMessage:hj.metrics.getErrorMessage(e),cookieKey:t.getKey()}})}},set:function(e){t.set((0,r.GT)(n(e)))},getKey:function(){return t.getKey()}}}},8973:function(e,t,n){"use strict";n.d(t,{U:function(){return r}});var r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function(){return e+=t}}},7990:function(e,t,n){"use strict";n.d(t,{DO:function(){return a},Nm:function(){return s},TL:function(){return i},ij:function(){return l},qC:function(){return c},vB:function(){return u}});var r=n(2129),o=n(7352);function i(e,t,n){!function o(i){if(i>0&&e(),i>=n.maxRetries)t&&t();else{var a=0===i&&n.firstAttemptDelay?n.firstAttemptDelay:n.delay*Math.pow(n.baseExponent||3,i);setTimeout((function(){!0!==r.K.get("eventsRetrySuccess")&&o(i+1)}),a)}}(0)}var a=function(){return(0,o.v4)()},s=function(e){return(0,o.v5)(e,"ded6f544-7265-46bb-ab52-fefac2598466")},c=function(e){return JSON.stringify(e)},u=function(e){return JSON.parse(e)},l=function(){var e;return null===(e=window.navigator)||void 0===e?void 0:e.userAgent}},6052:function(e,t,n){"use strict";n.d(t,{N:function(){return r}});var r=function(e,t){return e.substring(0,t.length)===t}},310:function(e,t,n){"use strict";n.r(t);var r=n(6939),o=n(9982),i=n(512),a=n(8422),s=n(6946),c=n(8417),u=n(9780),l=n(9163);function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}var d,f=(d={_sendVerifyInstallation:function(e){var t="".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/verify-installation?sv=").concat(_hjSettings.hjsv||0);hj.ajax.post(t,{},hj.tryCatch(e,"init._sendVerifyInstallation"))},_reportVerificationResults:function(e,t,n){if(e){var r=l.R.hasFeature("tcvs_v2")?"https://".concat("live"===hj.environment?"tcvsapi.contentsquare.com":"gateway-tcvs.staging.eu-west-1.csq.io","/v1/verification/").concat(e,"/result"):"".concat(hj.apiUrlBase,"/tcvs/verification/").concat(e,"/result"),o=h(h({},n&&{error_detail:n}),{},{status:t});(l.R.hasFeature("tcvs_v2")?hj.ajax.postAsJSON:hj.ajax.post)(r,o,(function(){hj.tcVerificationResultsSent=!0}),(function(e){e&&400!==e.status&&404!==e.status&&hj.exceptions.log(new Error("TCVS endpoint failed"),"init._reportVerificationResults"),hj.tcVerificationResultsSent=!0}))}}},{verifyInstallationAuto:function(){if(hj.settings.tracking_code_verified)hj.log.debug("Tracking code verified.","init");else{hj.log.debug("Tracking code verified not found, updating first data.","init");var e="".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/verify-installation/auto");hj.ajax.post(e,{},void 0,(function(t){hj.exceptions.log(new Error("Url: ".concat(e," - Status: ").concat(t.status)),"init._verifyInstallationAuto")}))}},verifyInstallation:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Hotjar",n=(0,u.Tb)("hjVerifyInstall"),r=(0,u.Tb)("hjVerifyUUID"),o=function(){(0,c.c)({title:"".concat(t," installation invalid"),message:"The tracking code you are trying to verify does not match the one installed on this page. Please make sure you install the correct tracking code provided for this site.",status:"bad"}),d._reportVerificationResults(r,"wrong_code",{expected:String(hj.verifyInstall),actual:String(hjSiteSettings.site_id)});var e="Passed Site ID: ".concat(hj.verifyInstall," != Configured Site ").concat(hj.settings.site_id);hj.exceptions.log(new Error(e),"init._verifyInstallation")};try{e=sessionStorage.getItem("hjVerifyInstall")}catch(e){}if(n||e){hj.verifyInstall=parseInt(n||e);try{sessionStorage.setItem("hjVerifyInstall",n||e)}catch(e){}if(window.hjBootstrapCalled&&window.hjBootstrapCalled.length>1){var i,a=window.hjBootstrapCalled.filter((function(e,t){return window.hjBootstrapCalled.indexOf(e)===t})),s="You have "+window.hjBootstrapCalled.length+" tracking scripts installed on your site. ",l=2===window.hjBootstrapCalled.length?"script as this":"scripts as these",h=!!window.dataLayer;a.length>1?(i=s+"Please remove the duplicate "+l+" will cause issues.",(0,c.c)({title:"Multiple different ".concat(t," scripts detected"),message:i,status:"bad"}),d._reportVerificationResults(r,"multiple_codes",{expected:String(hj.verifyInstall),actual:a,gtm:!1})):h?(i=s+"If you've installed ".concat(t," through GTM - please remove the duplicate ")+l+" will cause issues.",d._reportVerificationResults(r,"multiple_codes",{expected:String(hj.verifyInstall),actual:a,gtm:!0}),(0,c.c)({title:"Multiple ".concat(t," scripts detected"),message:i,status:"bad"})):hj.verifyInstall===hj.settings.site_id?(i=s+"This will not affect data collection, but we do suggest removing redundant scripts.",d._reportVerificationResults(r,"warning",{expected:String(hj.verifyInstall),reason:"multiple scripts"}),(0,c.c)({title:"Multiple ".concat(t," scripts detected"),message:i,status:"warning"})):o();var f="Passed Site ID: "+hj.verifyInstall+" contains multiple scripts "+window.hjBootstrapCalled.join(", ");hj.exceptions.log(new Error(f),"init._verifyInstallation")}else hj.verifyInstall===hj.settings.site_id?(d._sendVerifyInstallation((function(e){e.success||hj.exceptions.log(new Error("Verify installation endpoint failed"),"init._verifyInstallation")})),(0,c.c)({title:"".concat(t," installation verified"),message:"The ".concat(t," tracking code has been properly installed on this page. ")+"Browse your site in this window if you wish to verify installation on any other pages.",status:"good"}),d._reportVerificationResults(r,"ok")):o()}}}),g=f.verifyInstallation,p=f.verifyInstallationAuto,v=hj.tryCatch((function(){var e=(0,u.Tb)("hjDebug");e&&("1"===e||"true"===e?hj.debug.on(!0):hj.debug.off())}),"init"),y=n(7352),m=n(6052),j="retaker_ready",b=(navigator.userAgent.indexOf("Mac"),"_hjRetakerOriginalTarget");function w(){var e;return null!==(e=document.getElementById("_hj_hm-retaker"))&&void 0!==e&&e}function S(e,t){return!!e&&(e.startsWith?e.startsWith(t):(0,m.N)(e,t))}function _(e){if(!e||0===e.length)return"";var t=e.split("//")[1],n="";(n=t.indexOf("/")?t.split("/")[0]:t).indexOf("?")&&(n=n.split("?")[0]);var r=n.split(".");return r.length<=2?n:r.slice(1).join(".")}var C="https://".concat(hj.insightsHost);try{hj.ui.retaker=Object.freeze({loadWidget:function(){w()||window.opener.postMessage({type:j,origin:window.location.origin},C)}})}catch(e){}var E=n(6742),I=n(5148),T=n(5153),N=n(1131),O=n(5547),R=n(5687),A=n.n(R),k=n(484),x=n.n(k),P=n(7698),D=n(6849);function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}var U={maxLogsPerPage:100,_overflow:!1,_total:0,init:function(){hj.tryCatch((function(){hj.settings.session_capture_console_consent&&(hj.isPreview||hj.store.session.get("session.inSample")&&(window.addEventListener("error",U.onError),window.addEventListener("unhandledrejection",U.onUnhandledRejection)))}),"error-capture.run")()},onError:function(e){U.addError(e)},onUnhandledRejection:function(e){U.addError({error:e.reason,message:"Uncaught (in promise)",filename:"",lineno:0})},addError:function(e){hj.tryCatch((function(){var t=e.error,n=e.message,r=e.lineno,o=e.filename;if((t||n||r||o)&&!U.hasReachedOverflow()){var i=o&&"object"===M(o)?U.truncate(U.stringify(o),100):o,a="object"===M(r)?null:r,s=t&&t instanceof Error&&t.stack?x().parse(t).slice(0,20):null;(0,D.N)("error",{text:hj.privacy.suppressErrorMessage(U.getErrorMessage(e)),filename:i,lineno:a,trace:s,time:hj.time.getNow(),timestamp:P.f_.now()})}}),"error-capture.addError")()},getErrorMessage:function(e){if(!e.error)return e.message||"";var t=U.truncate(U.stringify(e.error),1e3);return e.message?e.error instanceof Error&&-1!==e.message.indexOf(t)?t.replace(t,e.message):"".concat(e.message," ").concat(t):t},stringify:function(e){return e instanceof Error?e.toString():A()(e)},truncate:function(e,t){return e.length<=t?e:"".concat(e.substring(0,t-3),"...")},hasReachedOverflow:function(){return!U._overflow&&U._total<U.maxLogsPerPage?(U._total++,!1):(U._overflow=!0,!0)}},L=n(8564),H=n(2129);hj.tryCatch((function(){void 0===hj.scriptLoaded&&(hj._init=hj.tryCatch((function(){var e={_verifyInstallation:function(){g()}};return e._browserIsSupported=hj.tryCatch((function(){return!((0,O.HY)()<11&&(hj.log.debug("IE < 11 is not supported.","init"),"1"===hj.url.getParameter("hjVerifyInstallation")&&(0,c.c)({title:"Hotjar installation cannot be verified.",message:"Sorry – your browser is not supported.",status:"bad"}),1))}),"init"),e._checkEndSignal=hj.tryCatch((function(){"1"===hj.url.getParameter("hjEndSignal")&&hj.eventStream.queueEndSignal()}),"init"),e._canRun=hj.tryCatch((function(){return!(-1!==navigator.userAgent.indexOf("Hotjar")||(N.t.canUseCookies()?N.t.canUseLocalStorage()?!N.t.canUseSessionStorage()&&(hj.log.debug("sessionStorage is not available","init"),1):(hj.log.debug("localStorage is not available","init"),1):(hj.log.debug("Cookies are not enabled"),1)))}),"init"),e._configureStateChangeListenMode=function(){var e="manual";hj.settings&&hj.settings.state_change_listen_mode&&(e=hj.settings.state_change_listen_mode),hj.locationListener.setMode(e)},e._queueInitMessage=function(){hj.features.hasFeature("wsky.session_rate_limiting")&&H.K.set("initMessageQueued",!0)},e.run=hj.tryCatch((function(t){hj.currentUrl=t,hj.scriptLoaded=!0,e._browserIsSupported()&&(v(),e._checkEndSignal(),e._canRun()?e._run(t):g())}),"init"),e._run=hj.tryCatch((function(t){if("1"!==navigator.doNotTrack&&"1"!==window.doNotTrack&&"1"!==navigator.msDoNotTrack||(hj.log.debug("Visitor has opted out of tracking.","init"),hj.optOut=!0),hj.log.debug("Site settings","init",hj.settings),function(e){L.f.set(e)}(hj.currentUrl),function(){if(window.opener&&window.opener.postMessage){var e="https://".concat(hj.insightsHost);if(!S(document.referrer+"",e)&&!function(){var e=sessionStorage.getItem(b);if(e)return e===window.location.origin}()){var t=S(document.referrer+"",window.location.origin),n=_(document.referrer+"")!==_(window.location.origin);if(!t&&n)return}window.addEventListener("message",(function(t){if(S(t.origin,e)&&t.data&&"retaker_start"===t.data.type){var n;if(w())return;sessionStorage.setItem(b,window.location.origin),sessionStorage.setItem("_hjRetakerMode",t.data.mode),sessionStorage.setItem("_hjRetakerStrings",JSON.stringify(null!==(n=t.data.strings)&&void 0!==n?n:"{}")),(0,o.H)(a.vO.HEATMAP_RETAKER)}}));try{r()}catch(e){window.addEventListener("load",(function(){r()}))}window.addEventListener("popstate",(function(){i()})),window.addEventListener("hashchange",(function(){i()}))}function r(){window.opener.postMessage({type:j,origin:window.location.origin},e)}function i(){w()||r()}}(),hj.settings.site_id){var n=(0,T.pZ)();i.r.init(),e._configureStateChangeListenMode(),e._queueInitMessage(),e._runPage(t),U.init(),hj.isIncludedInSample=function(){return hj.store.session.get("session.inSample")},n&&!n.existing&&(0,I.IU)({firstSeen:!0}),!hj.features.hasFeature("wsky.session_rate_limiting")&&hj.store.session.get("user.id")&&hj.store.session.set("sessionAccepted",!0),p(),g(),hj.command.activate(),"1"===hj.url.getParameter("hjIncludeInSample")&&(0,c.c)({title:"Hotjar tracking active.",message:"Hotjar tracking is active for your session.",status:"good"})}else hj.log.warn("Script execution for halted due to no site id: ".concat(window.location.href),"init")}),"init"),e.reinit=hj.tryCatch((function(t,n){hj.currentUrl=t,hj.metrics.reset(),n&&(N.t.items.HAS_CACHED_USER_ATTRIBUTES.clear(),N.t.localStorage.USER_ATTRIBUTES.clear(),E.N.reset()),e._runPage(t,n)}),"init"),e._runPage=hj.tryCatch((function(e,t){(0,I.oc)(t),hj.optOut||(hj.store.session.get("session.inSample")&&!(0,r.W)(e)&&(hj.visitData.track(e),(0,T.Iv)()),hj.visitData.trackView()),hj.hq.each(hj.loader.getModules(),(function(t,n){hj.optOut&&!n.nonTracking||(hj.log.debug("Running module","init",n.name),n.module.run(e))})),(0,s.runWidgets)(e)}),"init"),e}),"init")(),hj.hq(document).ready((function(){hj.log.debug("Document is ready. Initializing...","init"),hj.scriptContextId=(0,y.v4)(),hj._init.run(location.href),(hj.metrics.getConfig("browser").inLab||hj.metrics.getState("isMetricsEnabled"))&&(0,o.H)(a.vO.BROWSER_PERF)})))}),"init")()},4416:function(e,t,n){"use strict";n.r(t);var r=n(9780);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){var r;return r=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==o(r)?r:String(r))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(){var e;return"function"==typeof window[null!==(e=window.GoogleAnalyticsObject)&&void 0!==e?e:"ga"]}function l(){(0,window[window.GoogleAnalyticsObject||"ga"])((function(e){var t=e.get("sendHitTask");e.set("sendHitTask",(function(e){t(e);var n=function(e){var t=decodeURIComponent(e).split("&").map((function(e){return e.split("=")})).filter((function(e){var t,n,r=(t=e,n=1,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0];return["t","ec","ea","el","ev"].indexOf(r)>-1})).reduce((function(e,t){return a(a({},e),{},s({},t[0],t[1]))}),{});return t.t?{event:t.t,category:t.ec||"",action:t.ea||"",label:t.el||"",value:t.ev||""}:{}}(e.get("hitPayload"));"event"===n.event&&d("ga",n)}))}))}function h(e,t){!t||!t.length||t.length<2||"event"!==t[0]||d(e,{action:t[1],category:t[2]&&t[2].event_category||"",label:t[2]&&t[2].event_label||"",value:t[2]&&t[2].value||""})}function d(e,t){if(!function(e){return"detect_user"===e.action&&"Hotjar"===e.category}(t)){var n=function(e){return"GA_"+[e.action,e.category,e.label,e.value].filter((function(e){return 0===e||Boolean(e)})).join("-")}(t),r=function(e){return"GA_"+e.action}(t);hj.log.debug("intercepting ".concat(e,": ")+JSON.stringify(t)),hj("event",n),n!==r&&hj("event",r),hj.event.signal("int.ga",t)}}var f,g,p={gaClientSent:!1,gtagClientSent:!1,dataLayerSent:!1},v=function(e){return p[e]},y=function(e,t){p[e]&&(p[e]=t)},m=(0,r.JJ)("ga.forward_events","integration"),j=!1,b=hj.tryCatch((function(){if(!j){j=!0;var e=u(),t="function"==typeof window.gtag;(0,r.PY)((function(){return void 0!==window.dataLayer&&void 0!==window.dataLayer.length}),(function(e){if(e)return m("Intercepting dataLayer"),function(){var e=window.dataLayer;if(!0!==e._hj){e.forEach((function(e){h("dataLayer",e)}));var t=e.push;e.push=function(){t.apply(e,arguments),h("dataLayer",arguments[0])},e._hj=!0}}(),void(v("dataLayerSent")||(hj.metrics.count("ga-version",{tag:{version:"datalayer"}}),y("dataLayerSent",!0)));var n;t&&(m("Intercepting gtag"),n=window.gtag,window.gtag=function(){var e=Array.prototype.slice.call(arguments);n.apply(null,e),h("gtag",e)},v("gtagClientSent")||(hj.metrics.count("ga-version",{tag:{version:"gtag"}}),y("gtagClientSent",!0)))}),3e3,5),e&&(m("Intercepting ga"),l(),v("gaClientSent")||(hj.metrics.count("ga-version",{tag:{version:"ga"}}),y("gaClientSent",!0)))}}),"ga.forward_events"),w={setup:b},S=(0,r.JJ)("ga.send_hjuid"),_=60,C=!0,E=!1,I=hj.tryCatch((function(){E||(E=!0,hj.store.session.on("user.id",(function(e){"string"!=typeof e||e.length<8?S("invalid userid: '".concat(e,"'")):(S("got userId"),g=e.substring(0,8),O())})),T())}),"integrations.googleAnalytics"),T=hj.tryCatch((function(){if(!f){if(u()){v("gaClientSent")||(hj.metrics.count("ga-version",{tag:{version:"ga"}}),y("gaClientSent",!0));var e=window[window.GoogleAnalyticsObject||"ga"];return S("`ga` variable is available, waiting for tracker."),void e((function(e){N(e,!0)}))}_<=0?S("given up looking for GA module"):(_-=1,setTimeout(T,500))}}),"integrations"),N=hj.tryCatch((function(e,t){e&&(f&&t||(f!==e&&(S("got fresh tracker"),C=!0),f=e,O()))}),"integrations"),O=hj.tryCatch((function(){C&&g&&f&&(C=!1,f.send("event","Hotjar","detect_user",g,{nonInteraction:1}),S("successfully sent detect_user event"))}),"integrations"),R={setup:I,setTracker:hj.tryCatch((function(e){N(e,!1)}),"integrations.googleAnalytics")},A=(0,r.JJ)("ga.send_hjuid_gtag"),k=!1,x=hj.tryCatch((function(){if(!k){k=!0;var e=window.dataLayer;void 0!==e?(v("datalayerSent")||(hj.metrics.count("ga-version",{tag:{version:"datalayer"}}),y("datalayerSent",!0)),v("gtagClientSent")||(hj.metrics.count("ga-version",{tag:{version:"gtag"}}),y("gtagClientSent",!0)),hj.store.session.on("user.id",(function(e){if(e&&"string"==typeof e&&!(e.length<8)){var n=e.substring(0,8);t("set","user_properties",{hjuid:n}),t("event","detect_user",{event_category:"Hotjar",event_label:n,non_interaction:!0})}}))):A("`dataLayer` is undefined")}function t(){A("calling gtag(".concat((0,r.IN)(arguments),")")),e.push(arguments)}}),"ga.send_hjuid_gtag"),P={forwardEvents:w,sendHotjarUserId:R,sendHotjarUserIdGtag:{setup:x}},D=function(){return null!==hj.bridge.storage.items.HUBSPOT_UTK.get()},M=(0,r.JJ)("hubspot","integrations"),U={setup:hj.tryCatch((function(){M("HubSpot setup started"),(0,r.PY)(D,(function(e){if(e){var t=hj.bridge.storage.items.HUBSPOT_UTK.get();M("HubSpot UTK found: ".concat(t)),hj.event.signal("int.hubspot",{utk:t})}else M("HubSpot UTK cookie not found")}),5e3,5)}),"hubspot.setup")},L=(0,r.JJ)("optimizely","integration"),H=window.optimizely;function V(){L("looking for tags.");var e=H.get("state").getExperimentStates({isActive:!0});for(var t in e){var n=e[t],r=(n.isInExperimentHoldback?"HB_":"")+n.experimentName,o=n.variation.name||n.variation.id;hj.event.signal("exp.opt",{experimentId:r,variantId:o}),hj("event",r+"/"+o)}}var q,W={setup:hj.tryCatch((function(){void 0!==H&&"function"==typeof H.push&&"function"==typeof H.get&&void 0!==H.get("state")&&void 0!==H.get("data")?(L("listening for campaignDecided event."),(H=window.optimizely||[]).push({type:"addListener",filter:{name:"campaignDecided"},handler:function(e){"applied"===e.name&&(L("campaign decided; ready to tag experiments."),V())}}),V()):L("`window.optimizely` is not ready")}),"optimizely.setup")},z={setup:hj.tryCatch((function(){void 0!==window.ub&&void 0!==window.ub.page&&void 0!==window.ub.page.variantId?(hj.log.debug("Unbounce experiment in page '".concat(window.ub.page.name,"' is on variant '").concat(window.ub.page.variantId,"'"),"integration",window.ub),hj.event.signal("exp.ub",{experimentId:window.ub.page.name,variantId:window.ub.page.variantId}),hj("event","".concat((0,r.cr)(window.ub.page.name),"-variant-").concat(window.ub.page.variantId))):hj.log.debug("Unbounce experiment not found","integration")}),"unbounce.setup")},B=(0,r.JJ)("mixpanel.send_events"),F=function(){return void 0!==window.mixpanel&&void 0!==window.mixpanel.set_config},G={setup:hj.tryCatch((function(){(0,r.PY)(F,(function(e){var t,n,o,i;e?(B("Registering mixpanel hook"),!q&&null!==(t=window.mixpanel.config)&&void 0!==t&&null!==(n=t.hooks)&&void 0!==n&&n.before_send_events&&(q=null===(o=window.mixpanel.config)||void 0===o||null===(i=o.hooks)||void 0===i?void 0:i.before_send_events),window.mixpanel.set_config({hooks:{before_send_events:function(e){q&&q(e);var t=(0,r.cr)(e.event);return B("sending mixpanel payload: ".concat(e.event)),hj("event","MP_".concat(t)),hj.event.signal("int.mp",{event:t}),e}}})):B("mixpanel global object not found or set_config not ready")}),3e3,5)}),"mixpanel.setup")},Y=(0,r.JJ)("ab-tasty","integration");function K(){return void 0!==window.ABTasty&&void 0!==window.ABTasty.hitServiceNotifierSubscribe}var J={setup:hj.tryCatch((function(){function e(e){if("CAMPAIGN"===e.type&&e.data&&e.data.caname&&e.data.vaname){var t=e.data,n=t.caname,o=t.vaname;Y("AB Tasty campaign '".concat(n,"' is on variant '").concat(o,"'"),e),hj.event.signal("exp.abt",{experimentId:n,variantId:o},!0);var i=(0,r.cr)("".concat(n,"-").concat(o));hj("event",i),Y("Sending event '".concat(i,"'"))}else Y("Invalid AB Tasty event",e)}(0,r.PY)(K,(function(t){t?(Y("AB Tasty global object found"),window.ABTasty.hitServiceNotifierSubscribe(e,"CAMPAIGN",{withHitHistory:!0})):Y("AB Tasty global object not found")}),2e3,10)}),"abTasty.setup")},X=(0,r.JJ)("kissmetrics","integration"),$=function(){return void 0!==window.KM&&void 0!==window.KM.i},Q={setup:hj.tryCatch((function(){(0,r.PY)($,(function(e){if(e){var t=window.KM.i();X("Kissmetrics User ID found: ".concat(t)),hj("identify",null,{kissmetrics_id:t})}}),5e3,5)}),"kissmetrics.setup")};hj.tryCatch((function(){hj.loader.registerModule("IntegrationsModule",(hj.integrations=hj.tryCatch((function(){return{optimizely:W,google_analytics:P,hubspot:U,unbounce:z,mixpanel:G,abTasty:J,kissmetrics:Q}}),"integrations")(),{run:hj.tryCatch((function(){var e=hj.settings.integrations;e&&(e.optimizely&&e.optimizely.tag_recordings&&hj.integrations.optimizely.setup(),e.google_analytics&&(e.google_analytics.tag_sessions&&hj.integrations.google_analytics.forwardEvents.setup(),e.google_analytics.send_hotjar_id&&(hj.integrations.google_analytics.sendHotjarUserId.setup(),hj.integrations.google_analytics.sendHotjarUserIdGtag.setup())),e.unbounce&&e.unbounce.tag_recordings&&hj.integrations.unbounce.setup(),e.mixpanel&&e.mixpanel.send_events&&hj.integrations.mixpanel.setup(),e.hubspot&&e.hubspot.enabled&&e.hubspot.send_recordings&&hj.integrations.hubspot.setup(),e.abtasty&&e.abtasty.tag_recordings&&hj.integrations.abTasty.setup(),e.kissmetrics&&e.kissmetrics.send_user_id&&hj.integrations.kissmetrics.setup())}))}),!1)}),"integrations")()},6402:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}try{!function(t,n){var r=function(e){return new o(e)};r.isValidSelector=function(e){try{return hj.hq(e),!0}catch(e){return!1}},r.isEmptyObject=function(e){return!Object.keys(e).length},r.isFunction=function(e){return"function"==typeof e},r.isWindow=function(e){return e===t},r.isDocument=function(e,t){return e===(t||n)},r.noop=function(){},r.stringify=function(e){if(void 0===Array.prototype.toJSON)return JSON.stringify(e);var t=Array.prototype.toJSON;delete Array.prototype.toJSON;try{return JSON.stringify(e)}finally{Array.prototype.toJSON=t}},r.each=function(t,n){var r,o,i;if("object"===e(t)&&"[object Array]"!==Object.prototype.toString.call(t)){if((o=t[Object.keys(t)[0]])&&void 0!==o.nodeName){for(r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&"length"!==r&&!1===n(r,t[r],t))break}else for(r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&!1===n(r,t[r],t))break}else if(void 0!==t)for(i=0;i<t.length&&!1!==n(i,t[i],t);i+=1);},r.trim=function(e){return"string"==typeof e?e.replace(/^\s+|\s+$/gm,""):""},r.inArray=function(e,t){var n=t.indexOf(e);return void 0!==n&&-1!==n},r.values=function(e){return Object.keys(e).map((function(t){return e[t]}))},r.isUndefined=function(e){return void 0===e},r.isNullOrUndefined=function(e){return null===e||r.isUndefined(e)},r.indexOf=function(t,n){if("object"===e(n)){var r=n.indexOf(t);return void 0!==r?r:-1}return-1},r.ajax=function(e){var t=new XMLHttpRequest;e.type=e.type||"GET",e.timeout_ms=e.timeout_ms||15e3,e.withCredentials&&(t.withCredentials=!0),t.open(e.type,e.url,!0),t.timeout=e.timeout_ms,"POST"!==e.type&&"PUT"!==e.type||!e.contentType||t.setRequestHeader("Content-Type",e.contentType),t.onload=function(){t.status>=200&&t.status<400?r.isFunction(e.success)&&e.success(t.responseText&&JSON.parse(t.responseText),t):r.isFunction(e.error)&&e.error(t)},t.onerror=function(){r.isFunction(e.error)&&e.error(t)},t.ontimeout=function(){r.isFunction(e.timeout)&&e.timeout(t)},r.isFunction(e.requestAnnotator)&&e.requestAnnotator(t),"POST"!==e.type&&"PUT"!==e.type||!e.data?t.send():t.send(e.data)},r.get=function(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.timeout=15e3,n.onload=function(){n.status>=200&&n.status<400&&t&&t(n.responseText)},n.send()},r.eventHandlers={},r.selector="";var o=function(t){var o,i,a,s=window._hjDocument||n;if(r.selector=t,r.isWindow(t))return this[0]=window,this.length=1,this;if(r.isDocument(t,s))return this[0]=s,this.length=1,this;if("object"===e(t))return this[0]=t,this.length=1,this;if("string"==typeof t&&"<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&t.length>=3)return(o=s.createElement("div")).innerHTML=t,this[0]=o.childNodes[0],this.length=1,this;if("string"==typeof t){isNaN(t.charAt(1))||"."!==t.charAt(0)&&"#"!==t.charAt(0)||(t=t.charAt(0)+"\\3"+t.charAt(1)+" "+t.slice(2));try{i=s.querySelectorAll(t)}catch(e){return this.length=0,this}for(a=0;a<i.length;a+=1)this[a]=i[a];return this.length=i.length,this}return this};o.prototype.val=function(e){return void 0!==e&&this.length>0&&(this[0].value=e),void 0===this[0]?void 0:this[0]?this[0].value:""},o.prototype.text=function(e){return void 0===e?this[0].textContent:this[0].textContent=e},o.prototype.each=function(e,t){Array.prototype.forEach.call(this,(function(e,n,r){t(n,e,r)}))},o.prototype.append=function(t){var o;"object"===e(t)?"body"===r.selector?n.body.appendChild(t.get(0)):this[0].appendChild(t.get(0)):"body"===r.selector?((o=n.createElement("div")).innerHTML=t,n.body.appendChild(o)):((o=n.createElement("div")).innerHTML=t,this[0].appendChild(o))},o.prototype.hasClass=function(e){return this[0].classList?this[0].classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)},o.prototype.addClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.add(e):this[t].className+=" "+e;return this},o.prototype.removeClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.remove(e):this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ");return this},o.prototype.toggleClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.contains(e)?this[t].classList.remove(e):this[t].classList.add(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)?this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," "):this[t].className+=" "+e;return this},o.prototype.is=function(e){return function(e,t){var n=e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector;if(n)return n.call(e,t);for(var r=e.parentNode.querySelectorAll(t),o=r.length;o>=0;o-=1)if(r[o]===e)return!0;return!1}(this[0],e)},o.prototype.remove=function(){var e;for(e=0;e<this.length;e+=1)this[e].parentNode.removeChild(this[e])},o.prototype.click=function(e){var t,r;for(t=0;t<this.length;t+=1)(r=n.createEvent("HTMLEvents")).initEvent("click",!0,!1),this[t].dispatchEvent(r),e&&e()},o.prototype.trigger=function(e){var t,r,o,i=e.split(" ");for(t=0;t<this.length;t+=1)for(r=0;r<i.length;r+=1)(o=n.createEvent("HTMLEvents")).initEvent(i[r],!0,!1),this[t].dispatchEvent(o)},o.prototype.on=function(t,o,i,a){var s,c,u,l,h,d,f,g,p=t.split(" ");if(a=!!a,r.isDocument(this[0])&&"string"==typeof o)for(c=0;c<p.length;c+=1)"string"==typeof o?("boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u=o+"."+p[c],l=function(e){if(h=n.querySelectorAll(o)){for(d=e.target,f=-1;d&&-1===(f=Array.prototype.indexOf.call(h,d));)d=d.parentElement;f>-1&&i.call(d,e)}},Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(l),n.addEventListener(p[c].split(".")[0],l,!0)):("boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),Array.isArray(r.eventHandlers.document)||(r.eventHandlers.document=[]),r.eventHandlers.document.push(o),this[0].addEventListener(p[c].split(".")[0],o,a));else if(r.isDocument(this[0]))for(c=0;c<p.length;c+=1)"boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),u="document."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(o),n.addEventListener(p[c].split(".")[0],o,a);else if(r.isWindow(this[0]))for(c=0;c<p.length;c+=1)"boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),u="window."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(o),window.addEventListener(p[c].split(".")[0],o,a);else for(s=0;s<this.length;s+=1)for(c=0;c<p.length;c+=1)"object"===e(o)?(g=o,o=function(e){e.data=g,i.call(this[s],e)}):"boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),u=r.selector+"."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(o),this[s].addEventListener(p[c].split(".")[0],o,a);return this},o.prototype.off=function(t,o,i,a){var s,c,u,l=t.split(" ");for(a=!!a,s=0;s<this.length;s+=1)for(c=0;c<l.length;c+=1)if(r.isDocument(this[s])&&"string"==typeof o)if(void 0===i){if("object"===e(r.eventHandlers[o+"."+l[c]])){for(u=0;u<r.eventHandlers[o+"."+l[c]].length;u+=1)n.removeEventListener(l[c].split(".")[0],r.eventHandlers[o+"."+l[c]][u],!0);delete r.eventHandlers[o+"."+l[c]]}}else n.removeEventListener(l[c].split(".")[0],i,a);else if(r.isDocument(this[s]))if(void 0===o){if("object"===e(r.eventHandlers["document."+l[c]])){for(u=0;u<r.eventHandlers["document."+l[c]].length;u+=1)n.removeEventListener(l[c].split(".")[0],r.eventHandlers["document."+l[c]][u],a);delete r.eventHandlers["document."+l[c]]}}else n.removeEventListener(l[c].split(".")[0],o,a);else if(r.isWindow(this[s]))if(void 0===o){if("object"===e(r.eventHandlers["window."+l[c]])){for(u=0;u<r.eventHandlers["window."+l[c]].length;u+=1)window.removeEventListener(l[c].split(".")[0],r.eventHandlers["window."+l[c]][u],a);delete r.eventHandlers["window."+l[c]]}}else window.removeEventListener(l[c].split(".")[0],o,a);else if(void 0===o){if("object"===e(r.eventHandlers[r.selector+"."+l[c]])){for(u=0;u<r.eventHandlers[r.selector+"."+l[c]].length;u+=1)this[s].removeEventListener(l[c].split(".")[0],r.eventHandlers[r.selector+"."+l[c]][u],a);delete r.eventHandlers[r.selector+"."+l[c]]}}else this[s].removeEventListener(l[c].split(".")[0],o,a);return this},o.prototype.scrollTop=function(){return r.isWindow(this[0])||r.isDocument(this[0])?window.document.body.scrollTop||window.document.documentElement.scrollTop:this[0].scrollTop},o.prototype.scrollLeft=function(){return r.isWindow(this[0])||r.isDocument(this[0])?n.body.scrollLeft||n.documentElement.scrollLeft:this[0].scrollLeft},o.prototype.height=function(){var e;return r.isWindow(this[0])?n.documentElement.clientHeight:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollHeight,e.scrollHeight,this[0].body.offsetHeight,e.offsetHeight,e.clientHeight)):Math.max(this[0].scrollHeight,this[0].offsetHeight)},o.prototype.width=function(){var e;return r.isWindow(this[0])?n.documentElement.clientWidth:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollWidth,e.scrollWidth,this[0].body.offsetWidth,e.offsetWidth,e.clientWidth)):Math.max(this[0].scrollWidth,this[0].offsetWidth)},o.prototype.outerHeight=function(){return this[0].offsetHeight},o.prototype.offset=function(){var e=(this[0]&&this[0].ownerDocument).documentElement;return{top:this[0].getBoundingClientRect().top+window.pageYOffset-e.clientTop,left:this[0].getBoundingClientRect().left+window.pageXOffset-e.clientLeft}},o.prototype.attr=function(t,n){var r;if(n||""===n){for(r=0;r<this.length;r+=1)this[r].setAttribute(t,n);return this}return this[0]&&"object"===e(this[0])&&null!==this[0].getAttribute(t)?this[0].getAttribute(t):void 0},o.prototype.ready=function(e){r.isDocument(this[0])&&("interactive"===n.readyState||"complete"===n.readyState||"loaded"===n.readyState?e():n.addEventListener("DOMContentLoaded",e,!1))},o.prototype.parent=function(){var e;return"#document-fragment"===(null===(e=this[0].parentNode)||void 0===e?void 0:e.nodeName)?r(this[0].parentNode.host):r(this[0].parentNode)},o.prototype.get=function(e){return this[e]},o.prototype.show=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="";return this},o.prototype.hide=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="none";return this},o.prototype.focus=function(){var e;for(e=0;e<this.length;e+=1)this[e].focus();return this},o.prototype.blur=function(){var e;for(e=0;e<this.length;e+=1)this[e].blur();return this},o.prototype.clone=function(){return this[0].cloneNode(!0)},o.prototype.removeAttr=function(e){var t;for(t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},o.prototype.find=function(e){var t,n,o=r();try{t=this[0].querySelectorAll(e)}catch(e){return this.length=0,this}for(n=0;n<t.length;n+=1)o[n]=t[n];return o.length=t.length,o},o.prototype.is=function(t){var n,o=!1;if(!t||"object"!==e(this[0]))return!1;if("object"===e(t))return r(this[0]).get(0)===t.get(0);if("string"==typeof t){if(":visible"===t)return!(this[0].offsetWidth<=0&&this[0].offsetHeight<=0);if(":hidden"===t)return this[0].offsetWidth<=0&&this[0].offsetHeight<=0;if(":checked"===t)return this[0].checked;if(!(t.indexOf("[")>-1))return r(this[0]).get(0).nodeName.toLowerCase()===t;if((n=/([A-Za-z]+)\[([A-Za-z-]+)=([A-Za-z]+)]/g.exec(t)).length)return r.each(r(this[0]).get(0).attributes,(function(e,t){t.name===n[2]&&t.value===n[3]&&(o=!0)})),r(this[0]).get(0).nodeName.toLowerCase()===n[1]&&o}},o.prototype.css=function(t,n){var r,o;for(o=0;o<this.length;o+=1)if("object"===e(t))for(r in t)this[o].style[r]=t[r];else{if("number"!=typeof n&&"string"!=typeof n)return getComputedStyle(this[o])[t];if("__proto__"===t||"constructor"===t)return;this[o].style[t]=n}return this},o.prototype.animate=function(e,t){var n,o=this;for(void 0===t&&(t=400),n=0;n<o.length;n+=1)r.each(e,(function(e,r){r=r.toString();var i,a,s=getComputedStyle(o[n])[e].replace(/[0-9.-]/g,""),c=parseFloat(r),u=r.replace(/[0-9.-]/g,""),l=s||u,h=c-p,d=parseFloat(t/10),f=h/d,g=[],p=parseFloat(getComputedStyle(o[n])[e])||0;for(i=0;i<d;i+=1)p+=f,g.push({attribute:e,value:l?parseInt(p)+l:parseFloat(p).toFixed(1)});g.pop(),g.push({attribute:e,value:c+l}),g.length&&function e(t,n){var r=n[0].attribute;"__proto__"!==r&&"constructor"!==r&&(t.style[r]=n[0].value,n.shift(),n.length?a=setTimeout((function(){e(t,n)}),10):clearTimeout(a))}(o[n],g)}));return this},o.prototype.filter=function(e){return Array.prototype.filter.call(n.querySelectorAll(r.selector),(function(t,n){e(n,t)}))},t.hj=t.hj||{},t.hj.hq=t.hj.hq||r}(window,document)}catch(e){hj.exceptions.log(e,"hquery")}},7352:function(e,t,n){"use strict";n.d(t,{v4:function(){return b},v5:function(){return j}});for(var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,o=function(e){return"string"==typeof e&&r.test(e)},i=[],a=0;a<256;++a)i.push((a+256).toString(16).substr(1));var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase();if(!o(n))throw TypeError("Stringified UUID is invalid");return n};function c(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function u(e,t){return e<<t|e>>>32-t}var l,h=function(e,t,n){function r(e,t,r,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof t&&(t=function(e){if(!o(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}(t)),16!==t.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(t),a.set(e,t.length),(a=n(a))[6]=15&a[6]|80,a[8]=63&a[8]|128,r){i=i||0;for(var c=0;c<16;++c)r[i+c]=a[c];return r}return s(a)}try{r.name="v5"}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}(0,0,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var o=0;o<r.length;++o)e.push(r.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,a=Math.ceil(i/16),s=new Array(a),l=0;l<a;++l){for(var h=new Uint32Array(16),d=0;d<16;++d)h[d]=e[64*l+4*d]<<24|e[64*l+4*d+1]<<16|e[64*l+4*d+2]<<8|e[64*l+4*d+3];s[l]=h}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var f=0;f<a;++f){for(var g=new Uint32Array(80),p=0;p<16;++p)g[p]=s[f][p];for(var v=16;v<80;++v)g[v]=u(g[v-3]^g[v-8]^g[v-14]^g[v-16],1);for(var y=n[0],m=n[1],j=n[2],b=n[3],w=n[4],S=0;S<80;++S){var _=Math.floor(S/20),C=u(y,5)+c(_,m,j,b)+w+t[_]+g[S]>>>0;w=b,b=j,j=u(m,30)>>>0,m=y,y=C}n[0]=n[0]+y>>>0,n[1]=n[1]+m>>>0,n[2]=n[2]+j>>>0,n[3]=n[3]+b>>>0,n[4]=n[4]+w>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),d=h,f=new Uint8Array(16);function g(){if(!l&&!(l="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return l(f)}var p=function(e,t,n){var r=(e=e||{}).random||(e.rng||g)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return s(r)},v="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto),y=new Array(16),m=v?void 0:function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),y[t]=e>>>((3&t)<<3)&255;return y},j=d,b=function(e,t,n){return m&&((e=e||{}).rng=m),p(e,t,n)}},4005:function(e,t,n){"use strict";n.r(t);var r=["button","reset","submit"],o=["password","email","hidden"],i=["default-style","content-type","refresh"],a=["style","script"],s=["address","address1","address2","addressline1","addressline2","cell","cellphone","dateofbirth","dob","email","familyname","firstname","fullname","lastname","mobile","name","phone","postalcode","postcode","surname","tel","telephone","username","zip","zipcode","nationalinsurancenumber","nin","ppsn","security","securitynum","socialsec","socialsecuritynumber","socsec","ssn","adgangskode","authpw","contrasena","contrasenya","contraseña","contrasinal","cyfrinair","fjalëkalim","focalfaire","geslo","hasło","heslo","jelszó","kennwort","kωδικός","kωδικόςπρόσβασης","lozinka","lykilorð","lösenord","motdepasse","parakalw","parola","paroladordine","parole","parool","pasahitza","pass","passord","password","passwort","pw","pwd","pword","pwrd","salasana","sapwd","senha","sifre","slaptažodis","userpw","userpwd","wachtwoord","лозинка","парола","пароль","פּאַראָל","كلمهالسر","पासवर्ड","パスワード","密码","密碼","암호","cc","cccsc","cccvc","cccvv","ccexp","ccexpiry","ccexpmonth","ccexpyear","ccname","ccnum","ccnumber","cctype","creditcard","csc","cvc","cvv","exp","accountnum","accountnumber","bic","iban","ibanaccountnum","ibanaccountnumber","pin","pinno","pinnum","secq","secret","secretq","secretquestion","securityq","securityquestion","sortcode","swift"],c=n(8564),u=/\d+/,l=new RegExp(u.source,"g"),h=/[^@\s]+@[^@\s]+\.[^@\s]+/,d=new RegExp(h.source,"g"),f=/([+(]{0,2}\d[-_ ()/]{0,4}){9,}/,g=new RegExp(f.source,"g"),p=function(){return Math.random()<.5?-1:1},v=hj.tryCatch((function(e){return e&&e.parentNode?"#document-fragment"===e.parentNode.nodeName?e.parentNode.host:e.parentNode:null}),"hj.privacy.getParentNode"),y=hj.tryCatch((function(e){var t=Object.prototype.toString.call(e);return-1!==["[object HTMLDocument]","[object Document]"].indexOf(t)}),"hj.privacy._isDocument"),m=hj.tryCatch((function(e){return f.test(e)}),"hj.privacy._hasCCNumOrSSN"),j=hj.tryCatch((function(e){return e.indexOf("@")>-1&&h.test(e)}),"hj.privacy._hasEmail"),b=hj.tryCatch((function(e){return u.test(e)}),"hj.privacy._hasDigitSequence"),w=/(?:\d{1,3}\.){3}\d{1,3}/,S=/(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}/,_=hj.tryCatch((function(e){return w.test(e)||S.test(e)}),"hj.privacy._hasIPAddress"),C=hj.tryCatch((function(e){return e.value||e.textContent}),"hj.privacy._getTextFromElement"),E=hj.tryCatch((function(e){if(e&&"string"==typeof e)return Boolean(e.match(/^\s*data:(image\/[a-z]+|application\/octet-stream);base64,([A-Za-z0-9+/=])+\s*$/))}),"hj.privacy._isBase64Value"),I=/\S+/g,T=/\s?background[^;]+;?\s?/g,N=hj.tryCatch((function(e,t){return new Array((e||16)+1).join(t||"*")}),"Suppresser.createReplacementStr"),O=hj.tryCatch((function(e){var t={};return e.style&&e.style.width||(t.width=e.offsetWidth+"px"),e.style&&e.style.height||(t.height=e.offsetHeight+"px"),t}),"Suppresser.getSuppressedImageSize"),R=hj.tryCatch((function(e){return e&&e.length?e.replace(I,(function(e){return N(Math.max(1,e.length+p()))})):N(16+p())}),"Suppresser.textContentHandler"),A=hj.tryCatch((function(e){var t=e&&e.slice(0,2083);return t&&t.length&&(hj.settings.anonymize_emails&&(t=t.replace(d,R)),hj.settings.anonymize_digits&&t&&t.length&&(t=t.replace(l,R)),t=t.replace(g,(function(e){return e.replace(l,R)}))),t}),"Suppresser.textContentHandler"),k=hj.tryCatch((function(e){var t=e?16:10;return new Date(2839968e5).toISOString().substring(0,t)}),"Suppresser.getLocalDateStr"),x=hj.tryCatch((function(e){if(e)return e.replace(T,"")}),"Suppresser.imageStyleHandler"),P={text:R,full:R,partial:A,textContent:A,imgStyle:x,password:function(){return N()},number:function(){return N(16,"1")},date:function(){return k(!1)},datetime:function(){return k(!0)},"datetime-local":function(){return k(!0)},time:function(){return"00:00"},month:function(){return"1979-01"},week:function(){return"1979-W1"}},D={getSuppressedText:function(e,t){var n=P[e];return n?n(t):R(t)},getSuppressedImageNode:function(e){var t={src:"",meta:{style:O(e)}},n=x(e.getAttribute("style"));return n&&(t.style=n),t},textHandler:R};function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}var U=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&o.indexOf(n)>-1}),"hj.privacy._isSupressedInputType"),L=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&r.indexOf(n)>-1}),"hj.privacy._isAllowlistedInputType"),H=hj.tryCatch((function(e){if(e&&e.tagName){var t=e.tagName.toLowerCase();return a.indexOf(t)>-1}}),"hj.privacy._isAllowlistedElement"),V=hj.tryCatch((function(e){var t=e&&e["http-equiv"]&&e["http-equiv"].value;return!t||i.some((function(e){return!!t.match(e)}))}),"hj.privacy._isValidHttpEquiv"),q=hj.tryCatch((function(e){return[e.name,e.id].map((function(e){return e.replace(/[\s_-]+/g,"").toLocaleLowerCase()})).some((function(e){return s.indexOf(e)>-1}))}),"hj.privacy._hasSuppressedNameOrId"),W=hj.tryCatch((function(e){return"object"===M(e.attributes)&&(void 0!==e.attributes["data-hj-suppress"]||void 0!==e.attributes["data-hj-masked"])||"string"==typeof e.className&&e.className&&e.className.indexOf("data-hj-suppress")>-1}),"hj.privacy._isExplicitlySuppressed"),z=hj.tryCatch((function(e){for(;e&&!y(e);){if(W(e))return!0;e=v(e)}return!1}),"hj.privacy._isSelfOrAncestorSuppressed"),B=hj.tryCatch((function(e){return hj.settings.recording_capture_keystrokes&&(e.attributes&&void 0!==e.attributes["data-hj-whitelist"]||e.className&&e.className.indexOf("data-hj-whitelist")>-1||e.attributes&&void 0!==e.attributes["data-hj-allow"]||e.className&&e.className.indexOf("data-hj-allow")>-1)}),"hj.privacy._isUserAllowlisted"),F=[U,q,function(e){return m(C(e))},function(e){return j(C(e))}],G=hj.tryCatch((function(e){return F.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressInputOrTextarea"),Y=[function(e){return!(!e||!hj.settings.anonymize_digits)&&b(e)},function(e){return!(!e||hj.settings.anonymize_digits)&&m(e)},function(e){return!(!e||!hj.settings.anonymize_emails)&&"string"==typeof e&&j(e)}],K=hj.tryCatch((function(e,t){return(!t||!H(t))&&Y.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressTextContent"),J=hj.tryCatch((function(e){var t,n,r;if(!Boolean(e.parentNode))return null;var o=null!==(t=(null===(n=e.parentNode)||void 0===n?void 0:n.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&(null===(r=e.parentNode)||void 0===r?void 0:r.synthetic))&&void 0!==t&&t,i=e.parentNode;return o?i.host:i}),"hj.privacy._getParentNode"),X={isRiskyNotAllowlistedOrSuppressedElement:hj.tryCatch((function(e,t,n){if(void 0===e||!e||void 0===e.tagName)return!1;if(c.f.get()||hj.settings.suppress_all)return!0;if(hj.settings.suppress_text)return"IMG"!==e.tagName&&"VIDEO"!==e.tagName&&"SOURCE"!==e.tagName||!t||"src"!==t.name&&"style"!==t.name&&"srcset"!==t.name||z(e);var r="TEXTAREA"===e.tagName||"INPUT"===e.tagName&&!L(e);return r&&B(e)?(n||hj.metrics.count("tracked-features",{tag:{feature:"data-hj-allow"},extraTags:{inputType:e.attributes.type}}),G(e)):r||z(e)}),"hj.privacy.isRiskyNotAllowlistedOrSuppressedElement"),isAttributeSuppressable:hj.tryCatch((function(e,t,n,r){var o={INPUT:{attrs:["value","placeholder"]},TEXTAREA:{attrs:["value","placeholder"]},A:{attrs:["href"],shouldSuppressAttrValueCheck:function(e){return!(e&&e.match(/^data:/))}},OPTION:{attrs:["label","value"]},PROGRESS:{attrs:["value"]},OPTGROUP:{attrs:["label"]},IMG:{attrs:["alt"]},VIDEO:{attrs:["alt"]},SOURCE:{attrs:["alt"]},DIV:{attrs:["title"]},SPAN:{attrs:["title"]},P:{attrs:["title"]},META:function(e,t,n){switch(e){case"content":return!(n&&n.name&&"viewport"===n.name.value||n&&n["http-equiv"]&&V(n));case"name":return"viewport"!==t;case"http-equiv":return!V(n);case"charset":return!1;default:return!0}}}[e];return void 0!==o&&("function"==typeof o?o(t,n,r):!(o.attrs.indexOf(t)<0)&&(void 0===o.shouldSuppressAttrValueCheck||o.shouldSuppressAttrValueCheck(n)))}),"hj.privacy.isAttributeSuppressable"),hasPotentialPIIData:hj.tryCatch((function(e,t){var n=!t||y(t)?null:t;return K(e,n)}),"hj.privacy.hasPotentialPIIData"),getSuppressedText:hj.tryCatch((function(e,t){return D.getSuppressedText(e,t)}),"hj.privacy.getSuppressedText"),getSuppressedTextNode:hj.tryCatch((function(e,t){var n=J(e),r=t||X.isRiskyNotAllowlistedOrSuppressedElement(n),o=r||n&&"textarea"===n.type?"full":"partial";return{content:(r=!H(n)&&!!e.textContent&&!E(e.textContent)&&(r||X.hasPotentialPIIData(e.textContent,n)))?D.getSuppressedText(o,e.textContent):e.textContent,shouldSuppressNode:Boolean(r),suppressionType:r?o:"none"}}),"hj.privacy.getSuppressedTextNode"),getSuppressedNodeAttribute:hj.tryCatch((function(e,t,n){var r=t.value,o=t.name,i=n;if("data-hj-suppressed"!==o)return"IMG"!==e.tagName&&"VIDEO"!==e.tagName&&"SOURCE"!==e.tagName||"src"!==o&&"srcset"!==o&&"style"!==o||(i=X.isRiskyNotAllowlistedOrSuppressedElement(e,t),X.isRiskyNotAllowlistedOrSuppressedElement(e,t)&&(o="data-hj-suppressed",r=D.getSuppressedImageNode(e))),!E(r)&&X.isAttributeSuppressable(e.tagName,o,r,e.attributes)&&((i=i||X.isRiskyNotAllowlistedOrSuppressedElement(e))||"META"===e.tagName?r=D.getSuppressedText("full",r):X.hasPotentialPIIData(r)&&(r=D.getSuppressedText("partial",r))),{name:o,value:r,shouldSuppressNode:i}}),"hj.privacy.getSuppressedTextNode"),getSuppressedNode:hj.tryCatch((function(e,t){for(var n={},r=t,o=0;o<e.attributes.length;o++){var i=e.attributes[o],a=X.getSuppressedNodeAttribute(e,i,t);a&&(n[a.name]=a.value,r=r||a.shouldSuppressNode)}return{node:{tagName:e.tagName,attributes:n||{}},shouldSuppressNode:Boolean(r)}}),"hj.privacy.getSuppressedNode"),getTagsWithoutPII:hj.tryCatch((function(e){return e?e.filter((function(e){return!(!e||function(e){var t="string"==typeof e;return!!(m(e)||t&&j(e)||_(e))}(e)&&(hj.log.debug("Tag "+e+" has been removed due to possible PII information included"),1))})):[]}),"hj.privacy.getTagsWithoutPII"),suppressErrorMessage:hj.tryCatch((function(e){var t=e;return hj.settings.anonymize_emails&&(t=t.replace(d,D.textHandler)),t.replace(g,D.textHandler)}),"hj.privacy.suppressErrorMessage")};hj.privacy=X},8564:function(e,t,n){"use strict";n.d(t,{f:function(){return o}});var r={shouldSuppressOnPage:!1},o={get:function(){return r.shouldSuppressOnPage},set:function(e){var t;r.shouldSuppressOnPage=!(null===(t=hj.settings.suppress_all_on_specific_pages)||void 0===t||!t.length)&&hj.targeting.matchUrl(hj.settings.suppress_all_on_specific_pages,e)},reset:function(){r.shouldSuppressOnPage=!1}}},1131:function(e,t,n){"use strict";n.d(t,{t:function(){return y}});var r=n(6366),o=n(8422),i=n(1416),a=n(7698),s=function(e,t){if(!e)return null;var n=new a.f_((new a.f_).getTime()+1e3*e);if(t){var r=new a.f_;r.setHours(23),r.setMinutes(59),r.setSeconds(59),r.setMilliseconds(999),n.setTime(Math.min(n,r))}return n},c=function(e){var t={sameSite:"None",secure:!0};if(e){var n=window.location.hostname;t.domain=(0,i.getMidLevelDomain)(n)}return t};function u(e){var t=this,n=e.key,i=e.supportSubdomains,a=void 0!==i&&i,s=e.ttlSeconds,u=void 0===s?o.jS:s,l=e.shouldSync,h=void 0===l||l,d=e.keepAliveSeconds,f=void 0===d?0:d,g=e.shouldExtendExpiryOnActivity,p=void 0!==g&&g,v=e.shouldExpireAtMidnight,y=void 0!==v&&v,m=e.checkExpiry;this.key=n,this.ttlSeconds=u,this.shouldSync=h,this.keepAliveSeconds=f,this.shouldExpireAtMidnight=y,this.hasExceededCookieMaxDuration=!1,this.isSessionOnly=0===this.ttlSeconds,this.supportSubdomains=a,this.ttlSeconds>0&&(this.activeRefreshTimerId=null,this.keepAliveSeconds>0&&setInterval((function(){return t.refreshExpiryWithThrottling(m)}),1e3*f),p&&(document.addEventListener("click",(function(){return t.refreshExpiryWithThrottling(m)}),!1),document.addEventListener("mousemove",(function(){return t.refreshExpiryWithThrottling(m)}),!1),document.addEventListener("keypress",(function(){return t.refreshExpiryWithThrottling(m)}),!1),document.addEventListener("scroll",(function(){return t.refreshExpiryWithThrottling(m)}),!1),document.addEventListener("visibilitychange",(function(){return t.refreshExpiryWithThrottling(m)}),!1))),this.cookie=r.Z.withAttributes(c(a))}function l(e){var t=e.key;this.key=t}function h(e){u.call(this,e)}u.prototype.getKey=function(){return this.key},u.prototype.get=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).resetExpiry,t=void 0!==e&&e;try{var n=this.cookie.get(this.key)||null;if(!this.isSessionOnly&&this.shouldSync&&(n=this.sync(n)),t&&this.ttlSeconds&&n){var r=s(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,n,{expires:r})}return n}catch(e){return hj.log.debug("Cookie Error: ".concat(e.message),"cookies"),void hj.metrics.count("session-rejection",{tag:{reason:"cookies"},extraTags:{cookie:this.key,message:e.message}})}},u.prototype._setCookie=function(e){var t=s(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,e,{expires:t})},u.prototype._setLocalStorage=function(e){y.canUseLocalStorage()&&window.localStorage.setItem(this.key,e)},u.prototype._getLocalStorage=function(){if(y.canUseLocalStorage())return window.localStorage.getItem(this.key)},u.prototype._removeLocalStorage=function(){y.canUseLocalStorage()&&window.localStorage.removeItem(this.key)},u.prototype.set=function(e,t){this._setCookie(e),!t&&this.shouldSync&&(this.isSessionOnly||this._setLocalStorage(e))},u.prototype.setEncoded=function(e,t){e!==decodeURIComponent(e)&&(e=decodeURIComponent(e)),this._setCookie(e);var n=encodeURIComponent(e);!t&&this.shouldSync&&(this.isSessionOnly||this._setLocalStorage(n))},u.prototype.clear=function(){this.cookie.remove(this.key),this.isSessionOnly||this._removeLocalStorage()},u.prototype.sync=function(e){if(!y.canUseLocalStorage()||!this.shouldSync)return e;var t=this._getLocalStorage(),n=e;return e?this._setLocalStorage(e):t&&!e&&(this.set(t,!0),n=t),n},u.prototype.refreshExpiryWithThrottling=function(e){var t=this;this.activeRefreshTimerId||this.hasExceededCookieMaxDuration||(this.activeRefreshTimerId=setTimeout((function(){t.hasExceededCookieMaxDuration=e&&e(t.clear.bind(t)),t.hasExceededCookieMaxDuration||t.get({resetExpiry:!0}),t.activeRefreshTimerId=null}),1e3*o.E$))},l.prototype.getKey=function(){return this.key},l.prototype.get=function(){return this._getLocalStorage()},l.prototype.set=function(e){this._setLocalStorage(e)},l.prototype.clear=function(){this._removeLocalStorage()},l.prototype._setLocalStorage=function(e){y.canUseLocalStorage()&&window.localStorage.setItem(this.key,e)},l.prototype._getLocalStorage=function(){if(y.canUseLocalStorage())return window.localStorage.getItem(this.key)},l.prototype._removeLocalStorage=function(){y.canUseLocalStorage()&&window.localStorage.removeItem(this.key)},h.prototype=Object.create(u.prototype),h.prototype.constructor=h,h.prototype.exists=function(e){var t=this.get();t=t?t.split(","):[];for(var n=0;n<t.length;n++)if(e.toString()===t[n])return!0;return!1},h.prototype.add=function(e){var t=this.get();(t=t?t.split(","):[]).push(e),this.setEncoded(t.join(","))},h.prototype.remove=function(e){var t=this.get(),n=(t=t?t.split(","):[]).filter((function(t){return t!==e.toString()}));this.setEncoded(n.join(","))},h.prototype.sync=function(e){if(!y.canUseLocalStorage()||!this.shouldSync)return e;var t=window.localStorage.getItem(this.key)||"";e=e?decodeURIComponent(e).split(","):[],t=t?decodeURIComponent(t).split(","):[];var n=e.concat(t),r=n.filter((function(e,t){return n.indexOf(e)===t})).join();return r&&this.setEncoded(r),r};var d=n(1736),f=null,g=null,p=null,v=function(){return window.hjSiteSettings.site_id},y={items:{HAS_CACHED_USER_ATTRIBUTES:new u({key:"_hjHasCachedUserAttributes",ttlSeconds:0}),COOKIE_TEST:new u({key:"_hjCookieTest",ttlSeconds:0}),DEBUG_FLAG:new u({key:"hjDebug",ttlSeconds:0}),HJ_SESSION_USER:new u({key:"_hjSessionUser_".concat(v()),supportSubdomains:!0,shouldSync:!1}),HJ_SESSION:new u({key:"_hjSession_".concat(v()),supportSubdomains:!0,shouldSync:!1,ttlSeconds:o.R0/2,shouldExtendExpiryOnActivity:!0,checkExpiry:d.Wr}),HUBSPOT_UTK:new u({key:"hubspotutk"}),POLL_DONE:new h({key:"_hjDonePolls",supportSubdomains:!0}),POLL_MINIMIZED:new h({key:"_hjMinimizedPolls",supportSubdomains:!0}),SURVEY_INVITES_CLOSED:new h({key:"_hjClosedSurveyInvites"}),USER_ATTRIBUTES_HASH:new u({key:"_hjUserAttributesHash",supportSubdomains:!1,shouldSync:!1,ttlSeconds:2*o.oO,keepAliveSeconds:o.oO/2})},localStorage:{USER_ATTRIBUTES:new l({key:"_hjUserAttributes"})},areCookiesSupported:function(){return f},setCookiesSupported:function(e){f=e},canUseCookies:function(){return null===this.areCookiesSupported()&&this.setCookiesSupported(function(){try{if(!navigator.cookieEnabled)return!1;if(Object.keys(r.Z.get()).length>0)return!0;if(y.items.COOKIE_TEST.set("1"),"1"===y.items.COOKIE_TEST.get())return y.items.COOKIE_TEST.clear(),!0}catch(e){return hj.metrics.count("session-rejection",{tag:{reason:"cookies"}}),!1}}()),this.areCookiesSupported()},canUseLocalStorage:hj.tryCatch((function(){if(null!==g)return g;try{localStorage.setItem("_hjLocalStorageTest",1),localStorage.removeItem("_hjLocalStorageTest"),g=!0}catch(e){g=!1}return g}),"storage.canUseLocalStorage"),canUseSessionStorage:hj.tryCatch((function(){if(null!==p)return p;try{sessionStorage.setItem("_hjSessionStorageTest",1),sessionStorage.removeItem("_hjSessionStorageTest"),p=!0}catch(e){p=!1}return p}),"storage.canUseSessionStorage")};hj.storage=y},2377:function(e,t,n){"use strict";n.r(t);var r,o,i=((o=function(){return r()}).test=r=function(){var e;if(!navigator)return"No User-Agent Provided";if(null!==(e=navigator.userAgentData)&&void 0!==e&&e.mobile)return"mobile";var t=function(e){return navigator.userAgent.match(e)};return t(/GoogleTV|SmartTV|Internet.TV|NetCast|NETTV|AppleTV|boxee|Kylo|Roku|DLNADOC|CE\-HTML/i)||t(/Xbox|PLAYSTATION.3|Wii/i)?"tv":t(/iPad/i)||t(/tablet/i)&&!t(/RX-34/i)||t(/FOLIO/i)||t(/Linux/i)&&t(/Android/i)&&!t(/Fennec|mobi|HTC.Magic|HTCX06HT|Nexus.One|SC-02B|fone.945|Chromebook/i)||t(/Kindle/i)||t(/Mac.OS/i)&&t(/Silk/i)||t(/GT-P10|SC-01C|SHW-M180S|SGH-T849|SCH-I800|SHW-M180L|SPH-P100|SGH-I987|zt180|HTC(.Flyer|\_Flyer)|Sprint.ATP51|ViewPad7|pandigital(sprnova|nova)|Ideos.S7|Dell.Streak.7|Advent.Vega|A101IT|A70BHT|MID7015|Next2|nook/i)||t(/MB511/i)&&t(/RUTEM/i)?"tablet":t(/BOLT|Fennec|Iris|Maemo|Minimo|Mobi|mowser|NetFront|Novarra|Prism|RX-34|Skyfire|Tear|XV6875|XV6975|Google.Wireless.Transcoder/i)||t(/Opera/i)&&t(/Windows.NT.5/i)&&t(/HTC|Xda|Mini|Vario|SAMSUNG\-GT\-i8000|SAMSUNG\-SGH\-i9/i)?"mobile":t(/Windows.(NT|XP|ME|9)/)&&!t(/Phone/i)||t(/Win(9|.9|NT)/i)||t(/Macintosh|PowerPC/i)&&!t(/Silk/i)||t(/Linux/i)&&t(/X11/i)||t(/Solaris|SunOS|BSD/i)||t(/Bot|Crawler|Spider|Yahoo|ia_archiver|Covario-IDS|findlinks|DataparkSearch|larbin|Mediapartners-Google|NG-Search|Snappy|Teoma|Jeeves|TinEye/i)&&!t(/Mobile/i)||t(/\b(CrOS|Chromebook)\b/i)?"desktop":"mobile"},o),a=n(9521),s=n(5547);hj.tryCatch((function(){var e,t,n,r,o;hj.loader.registerModule("UserBehavior",(e={},t=!1,n=function(){var e,t,n={},r={desktop:{time:600,distance:200,clicks:6},mobile:{time:600,distance:200,clicks:6},tablet:{time:600,distance:200,clicks:6},tv:{time:600,distance:200,clicks:6}},o={x:null,y:null,pageX:null,pageY:null},s=0;function c(){s=0,o.x=null,o.y=null,o.pageX=null,o.pageY=null}function u(n){var r,i,u,l,h;if(r=n.clientX,i=n.clientY,u=n.pageX,l=n.pageY,h=o.pageX&&o.pageX!==u||o.pageY&&o.pageY!==l,!(document.documentElement.clientWidth&&r>document.documentElement.clientWidth||document.documentElement.clientHeight&&i>document.documentElement.clientHeight||h||(s++,t&&clearInterval(t),t=setTimeout(c,e.time),function(t,n){var r=Math.abs(t-o.x),i=Math.abs(n-o.y);Math.sqrt(Math.pow(r,2)+Math.pow(i,2))>e.distance&&c()}(n.clientX,n.clientY),o.x=n.clientX,o.y=n.clientY,o.pageX=n.pageX,o.pageY=n.pageY,s!==e.clicks))){var d=null;hj.tryCatch((function(){var e,t,r=(null==n||null===(e=n.composedPath)||void 0===e||null===(t=e.call(n))||void 0===t?void 0:t.length)?n.composedPath()[0]:n.target,o=hj.selector(a.l.getSelectorVersion()).get(hj.hq(r));if(r&&"pageX"in n&&"pageY"in n&&void 0!==o){var i=hj.hq(r).offset(),s=n.pageX-i.left,c=n.pageY-i.top;d={selector:o,pageX:n.pageX,pageY:n.pageY,offsetX:s,offsetY:c}}}),"user-behaviour.rageClick.onClick")(),hj.autotag.rageClick(d)}}return n.listen=function(){e=r[i()],hj.hq(document).on("mousedown.user_behavior_rageclick",u)},n}(),r=function(){var e,t={},n=[];function r(){var e=[],t=Array.prototype.filter.call(hj.hq("form"),(function(t){for(var r=0;r<n.length;r++)if(n[r]===t)return!0;return e.push(t),!0}));e.forEach((function(e){hj.log.debug("Found new form.","autotag",e),hj.hq(e).on("submit.user_behavior_formsubmit",(function(){var t,n;t=e,n=hj.selector(a.l.getSelectorVersion()).get(hj.hq(t)),hj.autotag.formSubmit({selector:n})})),hj.hq(e).on("change",(0,s.IH)((function(){var t,n;t=e,n=hj.selector(a.l.getSelectorVersion()).get(hj.hq(t)),hj.autotag.formInteract({selector:n})})))})),n=t}return t.listen=function(){e=setInterval(r,2e3)},t.stop=function(){clearInterval(e)},t}(),e.listen=function(){hj.url.getParameter("hjAutogeneratedRecording")&&hj.autotag.autogenerated()},o=e,hj.autotag=hj.tryCatch((function(){var e={autogenerated:("autogenerated",function(){a(["autogenerated"])})};function i(e,t,n){return function(r){r&&a(t.reduce((function(t,o){var i=e;return Object.keys(o).forEach((function(e){var t=o[e],a=r[t];null==a&&(a=""),n&&(a=n(t,a)),i+="."+e+":"+a})),t.push(i),t}),[]))}}function a(e,t){hj.log.debug("Sending autotags","autotag",e),hj.behaviorData.tagRecording(e,!0,null!=t?t:null)}return e.formSubmit=function(){a(["formsubmit"],arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)},e.formInteract=function(){a(["forminteract"],arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)},e.rageClick=function(){a(["rageclick"],arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)},e.start=hj.tryCatch((function(){var e,a,s;hj.features.hasFeature("settings.billing_v2")&&!t&&(n.listen(),r.listen(),o.listen(),e=function(e,t){return t.replace(".e:",".E:").replace(".v:",".V:").replace(".c:",".C:")},a=function(e,t){return"string"==typeof t?t.replace(/\.|:/g,"_"):t},s={"poll.show":i("poll.show",[{},{id:"id"}]),"poll.send":i("poll.send",[{},{id:"id",r_id:"response_id"}]),"poll.question":i("poll.q",[{t:"type"},{t:"type",a:"answer"},{t:"type",id:"id"},{qid:"questionUuid",id:"id"},{a:"answer",qid:"questionUuid",id:"id"},{t:"type",a:"answer",id:"id"}],(function(e,t){return"type"!==e?t:{"rating-scale-5":"rating5","rating-scale-7":"rating7","net-promoter-score":"nps","single-close-ended":"singleClose","multiple-close-ended":"multiClose","single-open-ended-multiple-line":"singleOpenMulti","single-open-ended-single-line":"singleOpenSingle"}[t]||t})),"survey.show":i("survey.show",[{},{id:"id"}]),"survey.open":i("survey.open",[{},{id:"id"}]),"exp.go":i("exp.go",[{e:"experimentId",c:"containerId"},{e:"experimentId",v:"variantId",c:"containerId"}],e),"exp.opt":i("exp.opt",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"exp.ub":i("exp.ub",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"exp.abt":i("exp.abt",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"int.ga":i("int.ga",[{a:"action"},{a:"action",c:"category"},{a:"action",c:"category",l:"label"},{a:"action",c:"category",l:"label",v:"value"}],a),"int.mp":i("int.mp",[{event:"event"}],a),"int.hubspot":i("int.hubspot",[{utk:"utk"}])},Object.keys(s).forEach((function(e){hj.event.listen(e,s[e])})),t=!0)}),"user-behavior.autotag.start"),e}),"user-behavior.autotag")(),e.run=Function.prototype,e),!1)}),"user-behavior")()},5547:function(e,t,n){"use strict";n.d(t,{HY:function(){return o},IH:function(){return a},LL:function(){return i},YN:function(){return c},m$:function(){return l},oL:function(){return u},tU:function(){return s}});var r,o=hj.tryCatch((function(e){var t=e||navigator.userAgent;return t.indexOf("MSIE ")>0?document.all&&!document.compatMode?5:document.all&&!window.XMLHttpRequest?6:document.all&&!document.querySelector?7:document.all&&!document.addEventListener?8:document.all&&!window.atob?9:10:-1!==t.indexOf("Trident/")?11:-1!==t.indexOf("Edge/")?12:"notIE"}),"utils"),i=(hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Firefox")>-1}),"utils"),hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Safari")>-1&&-1===e.indexOf("Chrome")}),"utils"),hj.tryCatch((function(e){return e=e||navigator.userAgent,/\b(Safari|iPad|iPhone|iPod)\b/.test(e)&&/WebKit/.test(e)&&!/Edge/.test(e)&&void 0===window.MSStream}),"utils"),hj.tryCatch((function(){return _hjSettings.wsHost||(_hjSettings.wsHost="ws.hotjar.com"),_hjSettings.wsHost}),"utils.get-ws-server")),a=function(e){var t=!1;return function(){if(!t){t=!0;for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return e.apply(null,r)}}},s=function(e){var t={};return function(n){if(!t[n]){t[n]=!0;for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return e.apply(null,o)}}},c=(r=1,function(){return r++}),u=function(e,t){return e.reduce((function(e,n){for(var r=[],o=n.cssRules.length,i=0;i<o;i++){var a;r.push(null===(a=n.cssRules[i])||void 0===a?void 0:a.cssText)}return n.ownerHostNode||(n.ownerHostNode=t,n.sheetId=c()),e.push({id:n.sheetId,rules:r}),e}),[])},l=function(e){return e&&e instanceof Node&&e.ownerDocument===document}},484:function(e,t,n){var r,o,i;!function(a,s){"use strict";o=[n(2444)],void 0===(i="function"==typeof(r=function(e){var t=/(^|@)\S+:\d+/,n=/^\s*at .*(\S+:\d+|\(native\))/m,r=/^(eval@)?(\[native code])?$/;return{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(n))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(e){return!!e.match(n)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var n=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;var o=this.extractLocation(r?r[1]:n),i=r&&n||void 0,a=["eval","<anonymous>"].indexOf(o[0])>-1?void 0:o[0];return new e({functionName:i,fileName:a,lineNumber:o[1],columnNumber:o[2],source:t})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(e){return!e.match(r)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new e({functionName:t});var n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=t.match(n),o=r&&r[1]?r[1]:void 0,i=this.extractLocation(t.replace(n,""));return new e({functionName:o,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:t})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split("\n"),o=[],i=2,a=r.length;i<a;i+=2){var s=n.exec(r[i]);s&&o.push(new e({fileName:s[2],lineNumber:s[1],source:r[i]}))}return o},parseOpera10:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split("\n"),o=[],i=0,a=r.length;i<a;i+=2){var s=n.exec(r[i]);s&&o.push(new e({functionName:s[3]||void 0,fileName:s[2],lineNumber:s[1],source:r[i]}))}return o},parseOpera11:function(n){return n.stack.split("\n").filter((function(e){return!!e.match(t)&&!e.match(/^Error created at/)}),this).map((function(t){var n,r=t.split("@"),o=this.extractLocation(r.pop()),i=r.shift()||"",a=i.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;i.match(/\(([^)]*)\)/)&&(n=i.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var s=void 0===n||"[arguments not available]"===n?void 0:n.split(",");return new e({functionName:a,args:s,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:t})}),this)}}})?r.apply(t,o):r)||(e.exports=i)}()},5687:function(e){"use strict";e.exports=function(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});var n,r="boolean"==typeof t.cycles&&t.cycles,o=t.cmp&&(n=t.cmp,function(e){return function(t,r){var o={key:t,value:e[t]},i={key:r,value:e[r]};return n(o,i)}}),i=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0!==t){if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!=typeof t)return JSON.stringify(t);var n,a;if(Array.isArray(t)){for(a="[",n=0;n<t.length;n++)n&&(a+=","),a+=e(t[n])||"null";return a+"]"}if(null===t)return"null";if(-1!==i.indexOf(t)){if(r)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var s=i.push(t)-1,c=Object.keys(t).sort(o&&o(t));for(a="",n=0;n<c.length;n++){var u=c[n],l=e(t[u]);l&&(a&&(a+=","),a+=JSON.stringify(u)+":"+l)}return i.splice(s,1),"{"+a+"}"}}(e)}},2444:function(e,t){var n,r,o;!function(i,a){"use strict";r=[],void 0===(o="function"==typeof(n=function(){function e(e){return e.charAt(0).toUpperCase()+e.substring(1)}function t(e){return function(){return this[e]}}var n=["isConstructor","isEval","isNative","isToplevel"],r=["columnNumber","lineNumber"],o=["fileName","functionName","source"],i=n.concat(r,o,["args"],["evalOrigin"]);function a(t){if(t)for(var n=0;n<i.length;n++)void 0!==t[i[n]]&&this["set"+e(i[n])](t[i[n]])}a.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof a)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new a(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},a.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),o=e.substring(t+1,n).split(","),i=e.substring(n+1);if(0===i.indexOf("@"))var s=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(i,""),c=s[1],u=s[2],l=s[3];return new a({functionName:r,args:o||void 0,fileName:c,lineNumber:u||void 0,columnNumber:l||void 0})};for(var s=0;s<n.length;s++)a.prototype["get"+e(n[s])]=t(n[s]),a.prototype["set"+e(n[s])]=function(e){return function(t){this[e]=Boolean(t)}}(n[s]);for(var c=0;c<r.length;c++)a.prototype["get"+e(r[c])]=t(r[c]),a.prototype["set"+e(r[c])]=function(e){return function(t){if(n=t,isNaN(parseFloat(n))||!isFinite(n))throw new TypeError(e+" must be a Number");var n;this[e]=Number(t)}}(r[c]);for(var u=0;u<o.length;u++)a.prototype["get"+e(o[u])]=t(o[u]),a.prototype["set"+e(o[u])]=function(e){return function(t){this[e]=String(t)}}(o[u]);return a})?n.apply(t,r):n)||(e.exports=o)}()},6366:function(e,t){"use strict";function n(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var r=function e(t,r){function o(e,o,i){if("undefined"!=typeof document){"number"==typeof(i=n({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in i)i[s]&&(a+="; "+s,!0!==i[s]&&(a+="="+i[s].split(";")[0]));return document.cookie=e+"="+t.write(o,e)+a}}return Object.create({set:o,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var i=n[o].split("="),a=i.slice(1).join("=");try{var s=decodeURIComponent(i[0]);if(r[s]=t.read(a,s),e===s)break}catch(e){}}return e?r[e]:r}},remove:function(e,t){o(e,"",n({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,n({},this.attributes,t))},withConverter:function(t){return e(n({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});t.Z=r}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var o=r.length-1;o>-1&&!e;)e=r[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e}(),function(){n.p=hj.scriptDomain;var e=hj.metrics.time();(0,n(8172).initErrorLogging)({isNotSampled:!0===(0,n(4613).isUserTest)()});var t=n(6946).initVoC;n(4931),n(6682),n(1214),n(5004),n(4416),n(6967),n(6783),n(4005),n(1229),n(3251),n(2377),t(),n(310),hj.metrics.timeEnd("resource-blocking-time",{tag:{resource:"modules-js"},start:e,type:"lab"})}()}();
