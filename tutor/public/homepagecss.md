    --lt-color-gray-100: #f8f9fc;
    --lt-color-gray-200: #f1f3f9;
    --lt-color-gray-300: #dee3ed;
    --lt-color-gray-400: #c2c9d6;
    --lt-color-gray-500: #8f96a3;
    --lt-color-gray-600: #5e636e;
    --lt-color-gray-700: #2f3237;
    --lt-color-gray-800: #1d1e20;
    --lt-color-gray-900: #111213;
    --lt-shadowDefault: 0 2px 6px -1px rgba(0, 0, 0, 0.16), 0 1px 4px -1px rgba(0, 0, 0, 0.04);
    --lt-shadowActive: 0 0 8px -2px rgba(0, 0, 0, 0.1), 0 6px 20px -3px rgba(0, 0, 0, 0.2);
    --lt-color-white: #fff !important;
    --lt-color-black: #111213 !important;
    --lt-color-transparent: rgba(255, 255, 255, 0) !important;
    --lt-color-background-light: var(--lt-color-gray-100) !important;
    --lt-color-background-default: var(--lt-color-gray-200) !important;
    --lt-color-background-dark: var(--lt-color-gray-300) !important;
    --lt-color-border-light: var(--lt-color-gray-200) !important;
    --lt-color-border-default: var(--lt-color-gray-300) !important;
    --lt-color-border-dark: var(--lt-color-gray-400) !important;
    --lt-color-text-very-light: var(--lt-color-gray-500) !important;
    --lt-color-text-light: var(--lt-color-gray-600) !important;
    --lt-color-text-default: var(--lt-color-gray-700) !important;
    --lt-color-text-dark: var(--lt-color-gray-800) !important;
    --lt-color-overlay-default: #fff !important;
    --lt-color-overlay-dark: #fff !important;
    --lt-color-overlay-transparent: rgba(0, 0, 0, 0.1) !important;
    --lt-shadow-website-overlay: 0 0 7px 0 rgba(0, 0, 0, 0.3) !important;
    box-sizing: border-box;
    -webkit-font-smoothing: inherit;

    from here style for body-----

        --lt-color-gray-100: #f8f9fc;
    --lt-color-gray-200: #f1f3f9;
    --lt-color-gray-300: #dee3ed;
    --lt-color-gray-400: #c2c9d6;
    --lt-color-gray-500: #8f96a3;
    --lt-color-gray-600: #5e636e;
    --lt-color-gray-700: #2f3237;
    --lt-color-gray-800: #1d1e20;
    --lt-color-gray-900: #111213;
    --lt-shadowDefault: 0 2px 6px -1px rgba(0, 0, 0, 0.16), 0 1px 4px -1px rgba(0, 0, 0, 0.04);
    --lt-shadowActive: 0 0 8px -2px rgba(0, 0, 0, 0.1), 0 6px 20px -3px rgba(0, 0, 0, 0.2);
    --lt-color-white: #fff !important;
    --lt-color-black: #111213 !important;
    --lt-color-transparent: rgba(255, 255, 255, 0) !important;
    --lt-color-background-light: var(--lt-color-gray-100) !important;
    --lt-color-background-default: var(--lt-color-gray-200) !important;
    --lt-color-background-dark: var(--lt-color-gray-300) !important;
    --lt-color-border-light: var(--lt-color-gray-200) !important;
    --lt-color-border-default: var(--lt-color-gray-300) !important;
    --lt-color-border-dark: var(--lt-color-gray-400) !important;
    --lt-color-text-very-light: var(--lt-color-gray-500) !important;
    --lt-color-text-light: var(--lt-color-gray-600) !important;
    --lt-color-text-default: var(--lt-color-gray-700) !important;
    --lt-color-text-dark: var(--lt-color-gray-800) !important;
    --lt-color-overlay-default: #fff !important;
    --lt-color-overlay-dark: #fff !important;
    --lt-color-overlay-transparent: rgba(0, 0, 0, 0.1) !important;
    --lt-shadow-website-overlay: 0 0 7px 0 rgba(0, 0, 0, 0.3) !important;
    -webkit-font-smoothing: inherit;
    height: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-size: 12px;
    font-family: sans-serif;
    --token-7b040a80-5f57-4f9a-a734-c0f3178785ca: rgb(3, 104, 224);
    --token-15b486ba-2fc9-40ec-b282-d340ae737628: rgb(19, 22, 18);
    --token-b2ddaf18-fb8e-49c5-bfe2-e80a4e95b118: rgb(44, 126, 234);
    --token-840e2253-3db7-4fe4-9c04-053d2f50f134: rgb(168, 168, 168);
    --token-c50781d2-1475-401d-9e71-7a371e78ed14: rgb(112, 167, 240);
    --token-cd156118-158a-47d3-8fb6-822a4bbc99ee: rgb(224, 225, 227);
    --token-a1e13941-d14a-4692-855b-e794eebdfa6f: rgb(229, 232, 232);
    --token-ce5164cd-4223-4bb7-8552-21eb990c41c0: rgb(245, 245, 245);
    --token-36a54893-9e5b-4622-8567-34f1e61e2ee9: rgb(255, 255, 255);
    --token-e3946f44-aa22-4470-a550-0689952f6cfd: rgb(0, 136, 255);
    --token-8699c4f3-d3b7-4764-bab8-47a373c0f26f: rgb(109, 22, 223);
    --token-672a3040-6e9a-4dff-8a40-eed3170f29ae: rgb(90, 90, 91);
    --token-b614b0f1-f502-471d-908b-7ee324a58d01: rgb(0, 24, 182);
    --token-dc74b930-0ddb-4cd5-84c0-a49f71740644: rgb(234, 104, 115);
    --token-d1f3fbd0-f3ba-41ec-a0d6-879c7df665da: rgb(23, 46, 20);
    --token-8e2e3d7a-18d7-4c35-8f04-9fea5742be2b: rgb(69, 105, 64);
    --framer-aspect-ratio-supported: auto;