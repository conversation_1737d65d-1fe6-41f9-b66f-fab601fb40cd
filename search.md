import React, { useState, useEffect } from 'react';

// Common Header Component
const Header = ({ onNavigate }) => (
  <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center border-b border-gray-200">
    <div className="flex items-center space-x-2">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
      </svg>
      <span className="text-xl font-bold text-gray-800">Popless</span>
    </div>
    <div className="flex items-center space-x-4">
      <button onClick={() => onNavigate('profile')} className="text-gray-500 hover:text-gray-700">
        Profile
      </button>
      <button onClick={() => onNavigate('search')} className="text-gray-500 hover:text-gray-700">
        Search
      </button>
      <button className="text-gray-500 hover:text-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      </button>
      <button className="text-gray-500 hover:text-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
      </button>
      <img
        src="https://placehold.co/32x32/E0E0E0/808080?text=User"
        alt="User Profile"
        className="w-8 h-8 rounded-full object-cover"
      />
    </div>
  </header>
);

// Profile Page Component (extracted from previous App)
const ProfilePage = ({ openModal, userId }) => (
  <div className="flex-grow flex items-center justify-center p-4">
    <div className="bg-white shadow-xl rounded-2xl p-6 md:p-8 w-full max-w-6xl flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:space-x-8">
      {/* Left Column - Profile Info */}
      <div className="flex-1">
        <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <span>Education</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
          <span>Maths</span>
        </div>

        <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6 mb-8">
          <img
            src="popless.jpg" // Using the uploaded image
            alt="Jane Cooper"
            className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover shadow-md"
            onError={(e) => { e.target.onerror = null; e.target.src = "https://placehold.co/120x120/E0E0E0/808080?text=Profile"; }}
          />
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1">Jane Cooper</h1>
            <div className="flex items-center space-x-2 text-gray-600 mb-2">
              <span className="text-lg">Maths Tutor</span>
              <span className="text-gray-500 text-sm">High School and College</span>
            </div>
            <div className="flex items-center text-yellow-500 mb-2">
              <span className="text-xl font-bold mr-1">4.74</span>
              <span className="text-gray-500 text-sm">(238 reviews)</span>
              <span className="ml-2 text-gray-400">|</span>
              <button className="ml-2 text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              <button className="ml-2 text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 leading-relaxed max-w-lg">
              Jane has an M.S. in math and is completing her Ph.D. in aerospace engineering from Stanford University. She can help with upper-level high school and college level math, as well as algebra, and precalculus...
              <button className="text-blue-600 hover:underline ml-1">Show more</button>
            </p>
            <div className="flex space-x-4 mt-4">
              <button className="flex items-center text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
                Share
              </button>
              <button className="flex items-center text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                </svg>
                Save
              </button>
              <button onClick={openModal} className="flex items-center text-gray-600 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                Message
              </button>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Specialties</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Maths</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Calculus, Linear algebra, Binomial probability, Rational functions, Finite math, Trigonometry, Polynomials</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">English</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>ACT English, Comprehension, Story telling, Proof reading, Public speaking, Shakespeare</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Finance and markets</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Derivative, Valuation structures</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Statistics</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Advanced regression, Hypothesis testing, Analysis of variance, Confidence intervals, Geometric random variables</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Computer Science</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Python, R, Django, SQL, Postgres, Data manipulation, Machine learning</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Test preparation</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>SAT, MCAT, CFA, GRE</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Economics</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Macroeconomics, Balance of payments</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-6 rounded-xl shadow-inner">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Meet your tutor, Jane</h2>
          <p className="text-gray-700 mb-2">
            <span className="font-semibold">Host on Popless</span> since 2021
          </p>
          <ul className="text-gray-700 space-y-1">
            <li className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              238 reviews
            </li>
            <li className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Identity verified
            </li>
            <li className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              4 hr response time
            </li>
          </ul>
          <p className="text-gray-700 mt-4">
            Languages spoken: <span className="font-semibold">English, Spanish, German, and French</span>
          </p>
        </div>
      </div>

      {/* Right Column - Schedule & Booking */}
      <div className="lg:w-96 p-6 bg-white border border-gray-200 rounded-xl shadow-lg flex-shrink-0">
        <div className="flex justify-between items-center mb-4">
          <span className="text-lg font-semibold text-gray-800">Session $55 / 60 minutes</span>
          <button className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
        </div>

        <div className="text-gray-600 mb-4">
          <p>Sunday, May 1 · 8:15 AM - 9:15 AM (PST)</p>
        </div>

        {/* Schedule Grid */}
        <div className="grid grid-cols-7 gap-1 text-center text-sm mb-6">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="font-medium text-gray-700">{day}</div>
          ))}
          {/* Dummy dates matching image more closely */}
          {[...Array(3)].map((_, i) => <div key={`empty-${i}`} className="p-1"></div>)} {/* Empty cells for spacing */}
          {[1, 2, 3, 4, 5, 6, 7].map((date, i) => (
            <div key={date} className={`p-1 rounded-md ${date === 1 ? 'bg-blue-500 text-white' : 'text-gray-700'}`}>
              {date}
            </div>
          ))}
        </div>

        {/* Time Slots */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>7:00 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>7:15 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>7:30 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>7:45 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>8:00 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
          <div className="flex items-center justify-between text-gray-700 border border-gray-200 rounded-lg p-2">
            <span>8:15 AM</span>
            <span className="font-semibold text-gray-900">$55</span>
          </div>
        </div>

        <button
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md mb-4"
        >
          Select and review
        </button>

        <p className="text-gray-500 text-xs text-center">
          Cancel up to 24 hours before the start time for a full refund.
        </p>

        {userId && (
          <div className="mt-4 text-center text-sm text-gray-500">
            Your User ID: <span className="font-mono break-all">{userId}</span>
          </div>
        )}
      </div>
    </div>
  </div>
);

// Search Page Component
const SearchPage = () => {
  const categories = [
    "All categories", "Art and design", "College mentorship", "Interview preparation",
    "College tutoring", "Technology", "Writing"
  ];
  const subcategories = ["Mathematics"];
  const specialties = ["SAT Prep", "AP Calculus", "AP Algebra"];
  const availability = ["Today", "Tomorrow", "Next 3 days", "Next 7 days", "Custom"];
  const durations = ["60 minutes", "30 minutes", "15 minutes"];
  const prices = ["$0 - $100", "$100 and above"];

  const tutors = [
    {
      id: 1,
      name: "Isabella M.",
      title: "Expert highschool and AP Calculus tutor",
      response: "97% Response Time",
      rating: "4.9 (56 reviews)",
      description: "The only thing I'm more passionate about than math, statistics, and data science is helping others learn. I recently completed three consecutive semesters as a teaching assistant for a masters level course in...",
      tags: ["Calculus I", "Calculus II", "Geometry", "R Programming", "Advanced regression"]
    },
    {
      id: 2,
      name: "Charlie S.",
      title: "Enthusiastic Math and Physics tutor and professor",
      response: "95% Response Time",
      rating: "4.8 (223 reviews)",
      description: "Hey there! I am a PhD in Applied Mathematics with a passion for teaching and tutoring. I've spent the last part of a decade honing my craft, first as a tutor and teaching assistant at Penn State University and later...",
      tags: ["Algebra", "Calculus I", "Calculus II", "AP Statistics", "R", "Python", "Year"]
    },
    {
      id: 3,
      name: "Chelsea V.",
      title: "M.S. in Engineering focused on SAT / ACT Math",
      response: "98% Response Time",
      rating: "4.8 (232 reviews)",
      description: "I've helped hundreds of students starting with Pre-Algebra through AP Calculus. My goal is to help any student understand the materials, and prepare for Math, GRE, SAT, ACT, PSAT, SSAT, ISEE, HSPT and oth...",
      tags: ["SAT Prep", "ACT Math", "AP Algebra", "GRE", "Linear Algebra", "Logarithms"]
    },
    {
      id: 4,
      name: "Dr. Emma T.",
      title: "Math PhD Advisor - University of Michigan",
      response: "99% Response Time",
      rating: "4.6 (23 reviews)",
      description: "I've been tutoring and teaching AP Calculus and all levels of math for more than 20 years. I teach math full-time and have taught at both the high school and college level. I am also a math content creator for...",
      tags: ["AP Calculus", "Linear Algebra", "Differential Equations", "Probability", "Statistics"]
    }
  ];

  return (
    <div className="flex-grow flex p-4">
      {/* Filter Sidebar */}
      <div className="w-64 bg-white rounded-xl shadow-lg p-6 mr-6 flex-shrink-0">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Filter by</h2>

        {/* Category Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Category</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <ul className="space-y-2 text-gray-700">
            {categories.map(cat => (
              <li key={cat}>
                <label className="inline-flex items-center">
                  <input type="radio" name="category" className="form-radio text-blue-600 rounded-full" />
                  <span className="ml-2">{cat}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>

        {/* Subcategory Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Subcategory</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <ul className="space-y-2 text-gray-700">
            {subcategories.map(subcat => (
              <li key={subcat}>
                <label className="inline-flex items-center">
                  <input type="radio" name="subcategory" className="form-radio text-blue-600 rounded-full" />
                  <span className="ml-2">{subcat}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>

        {/* Specialties Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Specialties</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <div className="flex flex-wrap gap-2">
            {specialties.map(spec => (
              <span key={spec} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full flex items-center">
                {spec}
                <button className="ml-1 text-blue-800 hover:text-blue-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            ))}
            <input
              type="text"
              placeholder="Search specialties"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Availability Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Availability</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <ul className="space-y-2 text-gray-700">
            {availability.map(avail => (
              <li key={avail}>
                <label className="inline-flex items-center">
                  <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                  <span className="ml-2">{avail}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>

        {/* Duration Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Duration</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <ul className="space-y-2 text-gray-700">
            {durations.map(duration => (
              <li key={duration}>
                <label className="inline-flex items-center">
                  <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                  <span className="ml-2">{duration}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>

        {/* Price Filter */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Price</h3>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          <ul className="space-y-2 text-gray-700">
            {prices.map(price => (
              <li key={price}>
                <label className="inline-flex items-center">
                  <input type="checkbox" className="form-checkbox text-blue-600 rounded" />
                  <span className="ml-2">{price}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 bg-white rounded-xl shadow-lg p-6">
        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          {['Explore', 'Prior Tutors', 'Favorites (12)'].map(tab => (
            <button
              key={tab}
              className={`py-2 px-4 -mb-px text-lg font-medium ${tab === 'Explore' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600 hover:text-gray-800'}`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Search Bar */}
        <div className="relative mb-6">
          <input
            type="text"
            placeholder="college level math"
            className="w-full p-3 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <p className="text-gray-500 text-sm mb-6">Advanced search</p>

        {/* Tutor Listings */}
        <div className="space-y-6">
          {tutors.map(tutor => (
            <div key={tutor.id} className="bg-gray-50 p-6 rounded-xl shadow-sm flex items-start space-x-4">
              <img
                src={`https://placehold.co/80x80/E0E0E0/808080?text=${tutor.name.split(' ')[0]}`}
                alt={tutor.name}
                className="w-20 h-20 rounded-full object-cover shadow-md"
              />
              <div className="flex-1">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-xl font-bold text-gray-900">{tutor.name}</h3>
                  <button className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-semibold hover:bg-blue-200 transition duration-200">
                    Message
                  </button>
                </div>
                <p className="text-gray-700 mb-1">{tutor.title}</p>
                <div className="flex items-center text-gray-600 text-sm mb-2">
                  <span className="mr-2">{tutor.response}</span>
                  <span className="text-yellow-500 mr-1">★</span>
                  <span>{tutor.rating}</span>
                </div>
                <p className="text-gray-700 leading-relaxed text-sm mb-3">
                  {tutor.description}
                </p>
                <div className="flex flex-wrap gap-2">
                  {tutor.tags.map(tag => (
                    <span key={tag} className="bg-gray-200 text-gray-700 text-xs px-3 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main App component
const App = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('profile'); // 'profile' or 'search'
  const [db, setDb] = useState(null);
  const [auth, setAuth] = useState(null);
  const [userId, setUserId] = useState(null);

  // Initialize Firebase and set up auth listener
  useEffect(() => {
    // Ensure Firebase is initialized only once
    const initializeFirebase = async () => {
      try {
        const { initializeApp } = await import('firebase/app');
        const { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } = await import('firebase/auth');
        const { getFirestore } = await import('firebase/firestore');

        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
        const app = initializeApp(firebaseConfig);
        const firestoreDb = getFirestore(app);
        const firebaseAuth = getAuth(app);

        setDb(firestoreDb);
        setAuth(firebaseAuth);

        // Sign in with custom token if available, otherwise anonymously
        if (typeof __initial_auth_token !== 'undefined') {
          await signInWithCustomToken(firebaseAuth, __initial_auth_token);
        } else {
          await signInAnonymously(firebaseAuth);
        }

        // Set up auth state change listener
        onAuthStateChanged(firebaseAuth, (user) => {
          if (user) {
            setUserId(user.uid);
          } else {
            setUserId(crypto.randomUUID()); // Fallback for unauthenticated users
          }
        });
      } catch (error) {
        console.error("Error initializing Firebase:", error);
      }
    };

    initializeFirebase();
  }, []); // Run only once on component mount

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <div className="min-h-screen bg-gray-100 font-inter flex flex-col">
      <Header onNavigate={setCurrentPage} />

      {currentPage === 'profile' && <ProfilePage openModal={openModal} userId={userId} />}
      {currentPage === 'search' && <SearchPage />}

      {/* Message Modal (remains global as it can be triggered from profile page) */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Send Jane a message</h2>
              <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 mb-4">
              Hey Jane, could you help me with a few equations before I tackle my exams? I'm not super confident I know enough to get the result I'm looking for, which is why I'm reaching out to you.
            </p>
            <p className="text-gray-700 mb-6">
              I'll send through some example questions so you get a better idea of how you could help me. Thank you!
            </p>
            <button
              onClick={closeModal} // In a real app, this would send the message
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition duration-200 shadow-md"
            >
              Send message
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default App;
